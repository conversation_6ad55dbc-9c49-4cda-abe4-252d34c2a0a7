{"Version": 3, "Meta": {"Duration": 5.53, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 63, "TotalSegmentCount": 514, "TotalPointCount": 1399, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Model", "Id": "Opacity", "Segments": [0, 1, 1, 1.13, 1, 2.27, 1, 3.4, 1, 1, 4.1, 1, 4.8, 1, 5.5, 1, 0, 5.53, 1]}, {"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 0.15, 0.7, 2, 1, 0.844, 4.186, 0.989, 7, 1.133, 7, 1, 1.211, 7, 1.289, 7.283, 1.367, 6.75, 1, 1.622, 4.997, 1.878, -2, 2.133, -2, 1, 2.611, -2, 3.089, -2, 3.567, -2, 1, 3.667, -2, 3.767, 2, 3.867, 2, 1, 4.178, 2, 4.489, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0, 0.4, 0, 1, 0.467, 0, 0.533, 27, 0.6, 27, 1, 0.656, 27, 0.711, -11.41, 0.767, -15, 1, 0.911, -24.333, 1.056, -26, 1.2, -26, 1, 1.344, -26, 1.489, -25.361, 1.633, -25.361, 1, 1.756, -25.361, 1.878, -30, 2, -30, 1, 2.111, -30, 2.222, -3, 2.333, -3, 1, 2.744, -3, 3.156, -3, 3.567, -3, 1, 3.689, -3, 3.811, -22, 3.933, -22, 1, 4.089, -22, 4.244, 12, 4.4, 12, 1, 4.556, 12, 4.711, 0, 4.867, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 1, 0.7, 1, 1, 0.844, 1, 0.989, -5, 1.133, -5, 1, 1.211, -5, 1.289, -5.066, 1.367, -4.944, 1, 1.622, -4.546, 1.878, -3, 2.133, -3, 1, 2.611, -3, 3.089, -3, 3.567, -3, 1, 3.667, -3, 3.767, -9, 3.867, -9, 1, 4.022, -9, 4.178, 7, 4.333, 7, 1, 4.489, 7, 4.644, -2, 4.8, -2, 1, 4.989, -2, 5.178, 0, 5.367, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 0.5, 0.7, 0.5, 1, 0.844, 0.5, 0.989, 0.5, 1.133, 0.5, 1, 1.211, 0.5, 1.289, 0.5, 1.367, 0.5, 1, 1.622, 0.5, 1.878, 0.5, 2.133, 0.5, 1, 2.611, 0.5, 3.089, 0.5, 3.567, 0.5, 1, 3.978, 0.5, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamFaceForm", "Segments": [0, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.4, 1, 0.467, 2, 0.533, 2, 1, 0.589, 2, 0.644, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.3, 0, 1.467, 0, 1.633, 0, 1, 1.756, 0, 1.878, 0, 2, 0, 1, 2.044, 0, 2.089, 1.5, 2.133, 1.5, 1, 2.2, 1.5, 2.267, 1.5, 2.333, 1.5, 1, 2.389, 1.5, 2.444, 0, 2.5, 0, 1, 2.533, 0, 2.567, 0, 2.6, 0, 1, 2.644, 0, 2.689, 0.788, 2.733, 1.4, 1, 2.744, 1.553, 2.756, 1.5, 2.767, 1.5, 1, 2.822, 1.5, 2.878, 0, 2.933, 0, 1, 2.944, 0, 2.956, 0, 2.967, 0, 1, 3.011, 0, 3.056, 1.4, 3.1, 1.4, 1, 3.256, 1.4, 3.411, 1.4, 3.567, 1.4, 1, 3.667, 1.4, 3.767, 0, 3.867, 0, 1, 4.022, 0, 4.178, 0, 4.333, 0, 1, 4.489, 0, 4.644, 1, 4.8, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, 1, 0.533, 1, 1, 0.589, 1, 0.644, 1, 0.7, 1, 1, 0.844, 1, 0.989, 1, 1.133, 1, 1, 1.3, 1, 1.467, 1.009, 1.633, 0.913, 1, 1.756, 0.843, 1.878, 0, 2, 0, 1, 2.044, 0, 2.089, 0, 2.133, 0, 1, 2.611, 0, 3.089, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.4, 1, 0.467, 2, 0.533, 2, 1, 0.589, 2, 0.644, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.3, 0, 1.467, 0, 1.633, 0, 1, 1.756, 0, 1.878, 0, 2, 0, 1, 2.044, 0, 2.089, 1.5, 2.133, 1.5, 1, 2.2, 1.5, 2.267, 1.5, 2.333, 1.5, 1, 2.389, 1.5, 2.444, 0, 2.5, 0, 1, 2.533, 0, 2.567, 0, 2.6, 0, 1, 2.644, 0, 2.689, 0.788, 2.733, 1.4, 1, 2.744, 1.553, 2.756, 1.5, 2.767, 1.5, 1, 2.822, 1.5, 2.878, 0, 2.933, 0, 1, 2.944, 0, 2.956, 0, 2.967, 0, 1, 3.011, 0, 3.056, 1.4, 3.1, 1.4, 1, 3.256, 1.4, 3.411, 1.4, 3.567, 1.4, 1, 3.667, 1.4, 3.767, 0, 3.867, 0, 1, 4.022, 0, 4.178, 0, 4.333, 0, 1, 4.489, 0, 4.644, 1, 4.8, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, 1, 0.533, 1, 1, 0.589, 1, 0.644, 1, 0.7, 1, 1, 0.844, 1, 0.989, 1, 1.133, 1, 1, 1.3, 1, 1.467, 1.009, 1.633, 0.913, 1, 1.756, 0.843, 1.878, 0, 2, 0, 1, 2.044, 0, 2.089, 0, 2.133, 0, 1, 2.611, 0, 3.089, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.211, 0, 1.289, 0, 1.367, 0, 1, 1.622, 0, 1.878, 0, 2.133, 0, 1, 2.611, 0, 3.089, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, -0.6, 0.533, -0.6, 1, 0.589, -0.6, 0.644, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.211, 0, 1.289, 0, 1.367, 0, 1, 1.622, 0, 1.878, 0, 2.133, 0, 1, 2.611, 0, 3.089, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.211, 0, 1.289, 0, 1.367, 0, 1, 1.622, 0, 1.878, 0, 2.133, 0, 1, 2.611, 0, 3.089, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, 0.1, 0.533, 0.1, 1, 0.589, 0.1, 0.644, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.3, 0, 1.467, 0, 1.633, 0, 1, 1.756, 0, 1.878, 0, 2, 0, 1, 2.044, 0, 2.089, 0, 2.133, 0, 1, 2.2, 0, 2.267, 0, 2.333, 0, 1, 2.389, 0, 2.444, -0.1, 2.5, -0.1, 1, 2.533, -0.1, 2.567, -0.1, 2.6, -0.1, 1, 2.644, -0.1, 2.689, 0, 2.733, 0, 1, 2.744, 0, 2.756, 0, 2.767, 0, 1, 2.822, 0, 2.878, -0.1, 2.933, -0.1, 1, 2.944, -0.1, 2.956, -0.1, 2.967, -0.1, 1, 3.011, -0.1, 3.056, 0, 3.1, 0, 1, 3.256, 0, 3.411, 0, 3.567, 0, 1, 3.667, 0, 3.767, -0.2, 3.867, -0.2, 1, 4.022, -0.2, 4.178, -0.2, 4.333, -0.2, 1, 4.489, -0.2, 4.644, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, 0.2, 0.533, 0.2, 1, 0.589, 0.2, 0.644, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.3, 0, 1.467, 0, 1.633, 0, 1, 1.756, 0, 1.878, 0, 2, 0, 1, 2.044, 0, 2.089, 0, 2.133, 0, 1, 2.2, 0, 2.267, 0, 2.333, 0, 1, 2.389, 0, 2.444, -0.1, 2.5, -0.1, 1, 2.533, -0.1, 2.567, -0.1, 2.6, -0.1, 1, 2.644, -0.1, 2.689, 0, 2.733, 0, 1, 2.744, 0, 2.756, 0, 2.767, 0, 1, 2.822, 0, 2.878, -0.1, 2.933, -0.1, 1, 2.944, -0.1, 2.956, -0.1, 2.967, -0.1, 1, 3.011, -0.1, 3.056, 0, 3.1, 0, 1, 3.256, 0, 3.411, 0, 3.567, 0, 1, 3.667, 0, 3.767, -0.2, 3.867, -0.2, 1, 4.022, -0.2, 4.178, -0.2, 4.333, -0.2, 1, 4.489, -0.2, 4.644, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, 0.7, 0.533, 0.7, 1, 0.589, 0.7, 0.644, -0.6, 0.7, -0.6, 1, 0.844, -0.6, 0.989, -0.6, 1.133, -0.6, 1, 1.3, -0.6, 1.467, -0.564, 1.633, -0.564, 1, 1.756, -0.564, 1.878, -0.7, 2, -0.7, 1, 2.044, -0.7, 2.089, 0.7, 2.133, 0.7, 1, 2.2, 0.7, 2.267, 0.7, 2.333, 0.7, 1, 2.389, 0.7, 2.444, 0.6, 2.5, 0.6, 1, 2.533, 0.6, 2.567, 0.6, 2.6, 0.6, 1, 2.644, 0.6, 2.689, 0.7, 2.733, 0.7, 1, 2.744, 0.7, 2.756, 0.7, 2.767, 0.7, 1, 2.811, 0.7, 2.856, 0.6, 2.9, 0.6, 1, 2.922, 0.6, 2.944, 0.6, 2.967, 0.6, 1, 3.011, 0.6, 3.056, 0.7, 3.1, 0.7, 1, 3.256, 0.7, 3.411, 0.7, 3.567, 0.7, 1, 3.667, 0.7, 3.767, -0.5, 3.867, -0.5, 1, 4.178, -0.5, 4.489, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, 0.7, 0.533, 0.7, 1, 0.589, 0.7, 0.644, -0.6, 0.7, -0.6, 1, 0.844, -0.6, 0.989, -0.6, 1.133, -0.6, 1, 1.3, -0.6, 1.467, -0.564, 1.633, -0.564, 1, 1.756, -0.564, 1.878, -0.7, 2, -0.7, 1, 2.044, -0.7, 2.089, 0.7, 2.133, 0.7, 1, 2.2, 0.7, 2.267, 0.7, 2.333, 0.7, 1, 2.389, 0.7, 2.444, 0.6, 2.5, 0.6, 1, 2.533, 0.6, 2.567, 0.6, 2.6, 0.6, 1, 2.644, 0.6, 2.689, 0.7, 2.733, 0.7, 1, 2.744, 0.7, 2.756, 0.7, 2.767, 0.7, 1, 2.811, 0.7, 2.856, 0.6, 2.9, 0.6, 1, 2.922, 0.6, 2.944, 0.6, 2.967, 0.6, 1, 3.011, 0.6, 3.056, 0.7, 3.1, 0.7, 1, 3.256, 0.7, 3.411, 0.7, 3.567, 0.7, 1, 3.667, 0.7, 3.767, -0.5, 3.867, -0.5, 1, 4.178, -0.5, 4.489, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, 0.4, 0.533, 0.4, 1, 0.589, 0.4, 0.644, -0.4, 0.7, -0.4, 1, 0.844, -0.4, 0.989, -0.4, 1.133, -0.4, 1, 1.3, -0.4, 1.467, -0.378, 1.633, -0.378, 1, 1.756, -0.378, 1.878, -0.378, 2, -0.378, 1, 2.044, -0.378, 2.089, 0.4, 2.133, 0.4, 1, 2.611, 0.4, 3.089, 0.4, 3.567, 0.4, 1, 3.667, 0.4, 3.767, -0.4, 3.867, -0.4, 1, 4.178, -0.4, 4.489, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, 0.4, 0.533, 0.4, 1, 0.589, 0.4, 0.644, -0.4, 0.7, -0.4, 1, 0.844, -0.4, 0.989, -0.4, 1.133, -0.4, 1, 1.3, -0.4, 1.467, -0.378, 1.633, -0.378, 1, 1.756, -0.378, 1.878, -0.378, 2, -0.378, 1, 2.044, -0.378, 2.089, 0.4, 2.133, 0.4, 1, 2.611, 0.4, 3.089, 0.4, 3.567, 0.4, 1, 3.978, 0.4, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, 0.5, 0.533, 0.5, 1, 0.589, 0.5, 0.644, -0.4, 0.7, -0.4, 1, 0.844, -0.4, 0.989, -0.4, 1.133, -0.4, 1, 1.3, -0.4, 1.467, -0.378, 1.633, -0.378, 1, 1.756, -0.378, 1.878, -0.378, 2, -0.378, 1, 2.044, -0.378, 2.089, 0.4, 2.133, 0.4, 1, 2.611, 0.4, 3.089, 0.4, 3.567, 0.4, 1, 3.667, 0.4, 3.767, 0, 3.867, 0, 1, 4.178, 0, 4.489, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, 0.5, 0.533, 0.5, 1, 0.589, 0.5, 0.644, -0.4, 0.7, -0.4, 1, 0.844, -0.4, 0.989, -0.4, 1.133, -0.4, 1, 1.3, -0.4, 1.467, -0.378, 1.633, -0.378, 1, 1.756, -0.378, 1.878, -0.378, 2, -0.378, 1, 2.044, -0.378, 2.089, 0.4, 2.133, 0.4, 1, 2.611, 0.4, 3.089, 0.4, 3.567, 0.4, 1, 3.667, 0.4, 3.767, 0, 3.867, 0, 1, 4.178, 0, 4.489, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, 0.4, 0.533, 0.4, 1, 0.589, 0.4, 0.644, -0.8, 0.7, -0.8, 1, 0.844, -0.8, 0.989, -0.8, 1.133, -0.8, 1, 1.3, -0.8, 1.467, -0.767, 1.633, -0.767, 1, 1.756, -0.767, 1.878, -0.767, 2, -0.767, 1, 2.044, -0.767, 2.089, 0.4, 2.133, 0.4, 1, 2.611, 0.4, 3.089, 0.4, 3.567, 0.4, 1, 3.667, 0.4, 3.767, -0.3, 3.867, -0.3, 1, 4.178, -0.3, 4.489, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, 0.4, 0.533, 0.4, 1, 0.589, 0.4, 0.644, -0.8, 0.7, -0.8, 1, 0.844, -0.8, 0.989, -0.8, 1.133, -0.8, 1, 1.3, -0.8, 1.467, -0.767, 1.633, -0.767, 1, 1.756, -0.767, 1.878, -0.767, 2, -0.767, 1, 2.044, -0.767, 2.089, 0.4, 2.133, 0.4, 1, 2.611, 0.4, 3.089, 0.4, 3.567, 0.4, 1, 3.667, 0.4, 3.767, -0.3, 3.867, -0.3, 1, 4.178, -0.3, 4.489, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.456, 1, 0.578, -0.6, 0.7, -0.6, 1, 0.844, -0.6, 0.989, -0.6, 1.133, -0.6, 1, 1.3, -0.6, 1.467, -0.6, 1.633, -0.6, 1, 1.756, -0.6, 1.878, -0.6, 2, -0.6, 1, 2.044, -0.6, 2.089, -0.6, 2.133, -0.6, 1, 2.611, -0.6, 3.089, -0.6, 3.567, -0.6, 1, 3.667, -0.6, 3.767, -0.005, 3.867, 0.2, 1, 4.178, 0.839, 4.489, 1, 4.8, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, 0.3, 0.533, 0.3, 1, 0.589, 0.3, 0.644, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.3, 0, 1.467, 0.006, 1.633, 0.006, 1, 1.756, 0.006, 1.878, 0.006, 2, 0.006, 1, 2.044, 0.006, 2.089, 0.2, 2.133, 0.2, 1, 2.611, 0.2, 3.089, 0.2, 3.567, 0.2, 1, 3.667, 0.2, 3.767, 0, 3.867, 0, 1, 4.178, 0, 4.489, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamScarf", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.211, 0, 1.289, 0, 1.367, 0, 1, 1.622, 0, 1.878, 0, 2.133, 0, 1, 2.611, 0, 3.089, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 7, 0.7, 7, 1, 0.844, 7, 0.989, 7, 1.133, 7, 1, 1.211, 7, 1.289, 7.224, 1.367, 6.805, 1, 1.622, 5.431, 1.878, 0, 2.133, 0, 1, 2.611, 0, 3.089, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.4, 0, 0.467, 5, 0.533, 5, 1, 0.589, 5, 0.644, -6, 0.7, -6, 1, 0.844, -6, 0.989, -5.999, 1.133, -5.953, 1, 1.211, -5.928, 1.289, -5.922, 1.367, -5.445, 1, 1.622, -3.877, 1.878, 0, 2.133, 0, 1, 2.611, 0, 3.089, 0, 3.567, 0, 1, 3.667, 0, 3.767, -4, 3.867, -4, 1, 4.022, -4, 4.178, 4, 4.333, 4, 1, 4.489, 4, 4.644, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, -4, 0.7, -4, 1, 0.844, -4, 0.989, -4, 1.133, -4, 1, 1.211, -4, 1.289, -4.13, 1.367, -3.889, 1, 1.622, -3.096, 1.878, 0, 2.133, 0, 1, 2.611, 0, 3.089, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyUpper", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 8.063, 0.7, 9, 1, 0.844, 10.107, 0.989, 10, 1.133, 10, 1, 1.367, 10, 1.6, 10.026, 1.833, 9.364, 1, 1.978, 8.954, 2.122, 0, 2.267, 0, 1, 2.7, 0, 3.133, 0, 3.567, 0, 1, 3.667, 0, 3.767, 1.903, 3.867, 3, 1, 4.022, 4.706, 4.178, 5, 4.333, 5, 1, 4.489, 5, 4.644, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.211, 0, 1.289, 0, 1.367, 0, 1, 1.622, 0, 1.878, 0, 2.133, 0, 1, 2.611, 0, 3.089, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.211, 0, 1.289, 0, 1.367, 0, 1, 1.622, 0, 1.878, 0, 2.133, 0, 1, 2.611, 0, 3.089, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.4, 1, 0.467, 0.281, 0.533, 0.2, 1, 0.589, 0.133, 0.644, 0.124, 0.7, 0.1, 1, 0.867, 0.027, 1.033, 0, 1.2, 0, 1, 1.322, 0, 1.444, -0.006, 1.567, 0.008, 1, 1.8, 0.036, 2.033, 0.3, 2.267, 0.3, 1, 2.4, 0.3, 2.533, 0.264, 2.667, 0.264, 1, 2.967, 0.264, 3.267, 0.27, 3.567, 0.3, 1, 3.667, 0.31, 3.767, 0.4, 3.867, 0.4, 1, 4.022, 0.4, 4.178, 0.4, 4.333, 0.4, 1, 4.489, 0.4, 4.644, 1, 4.8, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 1, 1, 0.111, 1, 0.222, 1, 0.333, 1, 1, 0.4, 1, 0.467, 0.281, 0.533, 0.2, 1, 0.589, 0.133, 0.644, 0.124, 0.7, 0.1, 1, 0.867, 0.027, 1.033, 0, 1.2, 0, 1, 1.322, 0, 1.444, -0.006, 1.567, 0.008, 1, 1.8, 0.036, 2.033, 0.3, 2.267, 0.3, 1, 2.4, 0.3, 2.533, 0.263, 2.667, 0.263, 1, 2.967, 0.263, 3.267, 0.268, 3.567, 0.3, 1, 3.667, 0.311, 3.767, 0.4, 3.867, 0.4, 1, 4.022, 0.4, 4.178, 0.4, 4.333, 0.4, 1, 4.489, 0.4, 4.644, 1, 4.8, 1, 0, 5.533, 1]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, 5, 1, 0.111, 5, 0.222, 5, 0.333, 5, 1, 0.456, 5, 0.578, 0, 0.7, 0, 1, 0.867, 0, 1.033, 0, 1.2, 0, 1, 1.322, 0, 1.444, 0, 1.567, 0, 1, 1.8, 0, 2.033, 0, 2.267, 0, 1, 2.7, 0, 3.133, 0, 3.567, 0, 1, 3.978, 0, 4.389, 5, 4.8, 5, 0, 5.533, 5]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 0, 0.7, 0, 1, 0.867, 0, 1.033, 0, 1.2, 0, 1, 1.322, 0, 1.444, 0, 1.567, 0, 1, 1.8, 0, 2.033, 0, 2.267, 0, 1, 2.7, 0, 3.133, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHandChangeR", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 0, 0.7, 0, 1, 0.889, 0, 1.078, 0, 1.267, 0, 1, 1.4, 0, 1.533, 0, 1.667, 0, 1, 1.9, 0, 2.133, 0, 2.367, 0, 1, 2.767, 0, 3.167, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHandAngleR", "Segments": [0, -0.1, 1, 0.111, -0.1, 0.222, -0.1, 0.333, -0.1, 1, 0.456, -0.1, 0.578, 0, 0.7, 0, 1, 0.889, 0, 1.078, 0, 1.267, 0, 1, 1.4, 0, 1.533, 0, 1.667, 0, 1, 1.9, 0, 2.133, 0.4, 2.367, 0.4, 1, 2.767, 0.4, 3.167, 0.4, 3.567, 0.4, 1, 3.667, 0.4, 3.767, -0.8, 3.867, -0.8, 1, 4.022, -0.8, 4.178, -0.629, 4.333, -0.4, 1, 4.489, -0.171, 4.644, -0.1, 4.8, -0.1, 0, 5.533, -0.1]}, {"Target": "Parameter", "Id": "ParamHandDhangeL", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 0, 0.7, 0, 1, 0.889, 0, 1.078, 0, 1.267, 0, 1, 1.4, 0, 1.533, 0, 1.667, 0, 1, 1.9, 0, 2.133, 0, 2.367, 0, 1, 2.767, 0, 3.167, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHandAngleL", "Segments": [0, 0.1, 1, 0.111, 0.1, 0.222, 0.1, 0.333, 0.1, 1, 0.456, 0.1, 0.578, 0, 0.7, 0, 1, 0.889, 0, 1.078, 0, 1.267, 0, 1, 1.4, 0, 1.533, 0, 1.667, 0, 1, 1.9, 0, 2.133, -0.4, 2.367, -0.4, 1, 2.767, -0.4, 3.167, -0.4, 3.567, -0.4, 1, 3.667, -0.4, 3.767, 1, 3.867, 1, 1, 4.022, 1, 4.178, 0.967, 4.333, 0.7, 1, 4.489, 0.433, 4.644, 0.1, 4.8, 0.1, 0, 5.533, 0.1]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.211, 0, 1.289, 0, 1.367, 0, 1, 1.622, 0, 1.878, 0, 2.133, 0, 1, 2.611, 0, 3.089, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.211, 0, 1.289, 0, 1.367, 0, 1, 1.622, 0, 1.878, 0, 2.133, 0, 1, 2.611, 0, 3.089, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0, 1, 0.456, 0, 0.578, 0, 0.7, 0, 1, 0.844, 0, 0.989, 0, 1.133, 0, 1, 1.211, 0, 1.289, 0, 1.367, 0, 1, 1.622, 0, 1.878, 0, 2.133, 0, 1, 2.611, 0, 3.089, 0, 3.567, 0, 1, 3.978, 0, 4.389, 0, 4.8, 0, 0, 5.533, 0]}, {"Target": "PartOpacity", "Id": "Part01Core", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01Hoho001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01Brow001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01Tear", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01EyeBall001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01Eye001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01Nose001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01Mouth001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01Face001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01Ear001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01Neck001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01HairFront001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01HairSide001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01HairBack001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmRB001", "Segments": [0, 0, 2, 3.4, 0, 2, 5.5, 0, 0, 5.53, 0]}, {"Target": "PartOpacity", "Id": "Part01ArmLB001", "Segments": [0, 0, 2, 3.4, 0, 2, 5.5, 0, 0, 5.53, 0]}, {"Target": "PartOpacity", "Id": "Part01ArmRA001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01ArmLA001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01Body001", "Segments": [0, 1, 2, 3.4, 1, 2, 5.5, 1, 0, 5.53, 1]}, {"Target": "PartOpacity", "Id": "Part01Sketch", "Segments": [0, 0, 2, 3.4, 0, 2, 5.5, 0, 0, 5.53, 0]}]}