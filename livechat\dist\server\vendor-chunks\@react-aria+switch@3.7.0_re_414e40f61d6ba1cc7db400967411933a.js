"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+switch@3.7.0_re_414e40f61d6ba1cc7db400967411933a";
exports.ids = ["vendor-chunks/@react-aria+switch@3.7.0_re_414e40f61d6ba1cc7db400967411933a"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+switch@3.7.0_re_414e40f61d6ba1cc7db400967411933a/node_modules/@react-aria/switch/dist/useSwitch.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+switch@3.7.0_re_414e40f61d6ba1cc7db400967411933a/node_modules/@react-aria/switch/dist/useSwitch.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSwitch: () => (/* binding */ $b418ec0c85c52f27$export$d853f7095ae95f88)\n/* harmony export */ });\n/* harmony import */ var _react_aria_toggle__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-aria/toggle */ \"(ssr)/./node_modules/.pnpm/@react-aria+toggle@3.11.1_r_af7ffcc31744239ab923968bfc78c11f/node_modules/@react-aria/toggle/dist/useToggle.mjs\");\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \nfunction $b418ec0c85c52f27$export$d853f7095ae95f88(props, state, ref) {\n    let { labelProps: labelProps, inputProps: inputProps, isSelected: isSelected, isPressed: isPressed, isDisabled: isDisabled, isReadOnly: isReadOnly } = (0, _react_aria_toggle__WEBPACK_IMPORTED_MODULE_0__.useToggle)(props, state, ref);\n    return {\n        labelProps: labelProps,\n        inputProps: {\n            ...inputProps,\n            role: 'switch',\n            checked: isSelected\n        },\n        isSelected: isSelected,\n        isPressed: isPressed,\n        isDisabled: isDisabled,\n        isReadOnly: isReadOnly\n    };\n}\n\n\n\n//# sourceMappingURL=useSwitch.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+switch@3.7.0_re_414e40f61d6ba1cc7db400967411933a/node_modules/@react-aria/switch/dist/useSwitch.mjs\n");

/***/ })

};
;