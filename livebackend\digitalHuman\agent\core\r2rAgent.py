# -*- coding: utf-8 -*-
'''
@File    :   r2rAgent.py
<AUTHOR>   AI Assistant
'''

import json
import asyncio
from typing import AsyncGenerator, Dict, Any
from ..builder import AGENTS
from ..agentBase import BaseAgent
from digitalHuman.protocol import *
from digitalHuman.utils import logger

try:
    from r2r import R2RClient
    R2R_AVAILABLE = True
except ImportError:
    R2R_AVAILABLE = False
    logger.warning("R2R package not available. Please install with: pip install r2r")

__all__ = ["R2RAgent"]

@AGENTS.register("R2R")
class R2RAgent(BaseAgent):
    """
    R2R RAG Agent implementation
    Provides Retrieval-Augmented Generation capabilities using R2R backend
    """
    
    def __init__(self, config, engine_type):
        super().__init__(config, engine_type)
        if not R2R_AVAILABLE:
            raise ImportError("R2R package not installed. Please install with: pip install r2r>=0.4.0")
    
    async def run(
        self, 
        user: UserDesc,
        input: TextMessage, 
        streaming: bool = True,
        conversation_id: str = "",
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Execute R2R agent conversation
        
        Args:
            user: User description
            input: Text message from user
            streaming: Whether to use streaming response
            conversation_id: Conversation ID for multi-turn dialogue
            **kwargs: Additional parameters from configuration
        
        Yields:
            str: Server-sent events formatted strings
        """
        try:
            if not isinstance(input, TextMessage):
                raise RuntimeError("R2R Agent only supports TextMessage")
            
            # Parameter validation and extraction
            parameters = self.checkParameter(**kwargs)
            base_url = parameters.get("base_url", "http://localhost:7272")
            api_key = parameters.get("api_key", "")
            search_limit = int(parameters.get("search_limit", 10))
            use_hybrid_search = bool(parameters.get("use_hybrid_search", True))
            use_vector_search = bool(parameters.get("use_vector_search", True))
            temperature = float(parameters.get("temperature", 0.1))
            max_tokens = int(parameters.get("max_tokens", 1024))
            top_p = float(parameters.get("top_p", 1.0))
            top_k = int(parameters.get("top_k", 100))
            
            logger.info(f"[R2RAgent] Initializing with base_url: {base_url}")
            
            # Create R2R client
            client = R2RClient(base_url)
            
            # Handle authentication if API key is provided
            if api_key:
                try:
                    # Note: R2R authentication might need to be implemented based on the specific setup
                    logger.info("[R2RAgent] API key provided, authentication may be required")
                except Exception as auth_error:
                    logger.warning(f"[R2RAgent] Authentication warning: {auth_error}")
            
            # Conversation ID management
            conversation_id_required = not bool(conversation_id)
            if conversation_id_required:
                conversation_id = await self.createConversation()
                yield eventStreamConversationId(conversation_id)
                logger.info(f"[R2RAgent] Created new conversation: {conversation_id}")
            
            # Prepare search settings
            search_settings = {
                "limit": search_limit,
                "use_hybrid_search": use_hybrid_search,
                "use_vector_search": use_vector_search,
                "filters": {}
            }
            
            # Prepare RAG generation configuration
            rag_generation_config = {
                "stream": True,
                "temperature": temperature,
                "max_tokens_to_sample": max_tokens,
                "top_p": top_p,
                "top_k": top_k
            }
            
            logger.info(f"[R2RAgent] Processing message: {input.data[:100]}...")
            
            # Prepare message for R2R
            message = {
                "role": "user",
                "content": input.data
            }
            
            # Call R2R agent with streaming
            try:
                logger.info(f"[R2RAgent] Calling R2R agent with streaming enabled")

                # Call R2R agent - this returns an iterable for streaming responses
                def call_r2r_agent():
                    return client.retrieval.agent(
                        message=message,
                        search_settings=search_settings,
                        rag_generation_config=rag_generation_config,
                        conversation_id=conversation_id if not conversation_id_required else None
                    )

                # Execute the R2R call in a thread pool
                stream_response = await asyncio.get_event_loop().run_in_executor(
                    None, call_r2r_agent
                )

                logger.info(f"[R2RAgent] Got stream response: {type(stream_response)}")

                # Process streaming response - R2R returns an iterable of text chunks
                full_response = ""

                # Iterate through the streaming response synchronously in executor
                def process_r2r_stream():
                    chunks = []
                    try:
                        logger.info(f"[R2RAgent] Starting to iterate through R2R stream")
                        for chunk in stream_response:
                            logger.debug(f"[R2RAgent] Received chunk: {repr(chunk)}")
                            if chunk and isinstance(chunk, str):
                                chunks.append(chunk)
                            elif chunk:
                                # Convert non-string chunks to string
                                chunk_str = str(chunk)
                                chunks.append(chunk_str)
                                logger.debug(f"[R2RAgent] Converted chunk to string: {chunk_str[:50]}...")
                        logger.info(f"[R2RAgent] Finished iterating, got {len(chunks)} chunks")
                        return chunks
                    except Exception as e:
                        logger.error(f"[R2RAgent] Error iterating stream: {e}")
                        return [f"Error processing R2R response: {str(e)}"]

                # Get all chunks from the stream
                chunks = await asyncio.get_event_loop().run_in_executor(None, process_r2r_stream)

                # Yield each chunk
                for chunk in chunks:
                    if chunk:
                        full_response += chunk
                        yield eventStreamText(chunk)
                        logger.debug(f"[R2RAgent] Yielded chunk: {chunk[:50]}...")

                logger.info(f"[R2RAgent] Completed response, total length: {len(full_response)}")
                yield eventStreamDone()

            except Exception as r2r_error:
                logger.error(f"[R2RAgent] R2R API error: {r2r_error}")
                yield eventStreamError(f"R2R API error: {str(r2r_error)}")
                
        except Exception as e:
            logger.error(f"[R2RAgent] Exception: {e}", exc_info=True)
            yield eventStreamError(str(e))
    




