"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@heroui+use-draggable@2.1.8_7ebba94f7b1e5de09faa8df341790198";
exports.ids = ["vendor-chunks/@heroui+use-draggable@2.1.8_7ebba94f7b1e5de09faa8df341790198"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@heroui+use-draggable@2.1.8_7ebba94f7b1e5de09faa8df341790198/node_modules/@heroui/use-draggable/dist/index.mjs":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@heroui+use-draggable@2.1.8_7ebba94f7b1e5de09faa8df341790198/node_modules/@heroui/use-draggable/dist/index.mjs ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDraggable: () => (/* binding */ useDraggable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f4c4e47fdd38dbbc2317f9013c56c4ca/node_modules/@react-aria/interactions/dist/useMove.mjs\");\n// src/index.ts\n\n\nfunction useDraggable(props) {\n  const { targetRef, isDisabled = false, canOverflow = false } = props;\n  const boundary = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({ minLeft: 0, minTop: 0, maxLeft: 0, maxTop: 0 });\n  let transform = { offsetX: 0, offsetY: 0 };\n  const onMoveStart = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    var _a, _b, _c, _d, _e;\n    const { offsetX, offsetY } = transform;\n    const targetRect = (_a = targetRef == null ? void 0 : targetRef.current) == null ? void 0 : _a.getBoundingClientRect();\n    const targetLeft = (_b = targetRect == null ? void 0 : targetRect.left) != null ? _b : 0;\n    const targetTop = (_c = targetRect == null ? void 0 : targetRect.top) != null ? _c : 0;\n    const targetWidth = (_d = targetRect == null ? void 0 : targetRect.width) != null ? _d : 0;\n    const targetHeight = (_e = targetRect == null ? void 0 : targetRect.height) != null ? _e : 0;\n    const clientWidth = document.documentElement.clientWidth;\n    const clientHeight = document.documentElement.clientHeight;\n    const minLeft = -targetLeft + offsetX;\n    const minTop = -targetTop + offsetY;\n    const maxLeft = clientWidth - targetLeft - targetWidth + offsetX;\n    const maxTop = clientHeight - targetTop - targetHeight + offsetY;\n    boundary.current = {\n      minLeft,\n      minTop,\n      maxLeft,\n      maxTop\n    };\n  }, [transform, targetRef == null ? void 0 : targetRef.current]);\n  const onMove = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      if (isDisabled) {\n        return;\n      }\n      const { offsetX, offsetY } = transform;\n      const { minLeft, minTop, maxLeft, maxTop } = boundary.current;\n      let moveX = offsetX + e.deltaX;\n      let moveY = offsetY + e.deltaY;\n      if (!canOverflow) {\n        moveX = Math.min(Math.max(moveX, minLeft), maxLeft);\n        moveY = Math.min(Math.max(moveY, minTop), maxTop);\n      }\n      transform = {\n        offsetX: moveX,\n        offsetY: moveY\n      };\n      if (targetRef == null ? void 0 : targetRef.current) {\n        targetRef.current.style.transform = `translate(${moveX}px, ${moveY}px)`;\n      }\n    },\n    [isDisabled, transform, boundary.current, canOverflow, targetRef == null ? void 0 : targetRef.current]\n  );\n  const { moveProps } = (0,_react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__.useMove)({\n    onMoveStart,\n    onMove\n  });\n  const preventDefault = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((e) => {\n    e.preventDefault();\n  }, []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!isDisabled) {\n      document.body.addEventListener(\"touchmove\", preventDefault, { passive: false });\n    }\n    return () => {\n      document.body.removeEventListener(\"touchmove\", preventDefault);\n    };\n  }, [isDisabled]);\n  return {\n    moveProps: {\n      ...moveProps,\n      style: { cursor: !isDisabled ? \"move\" : void 0 }\n    }\n  };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@heroui+use-draggable@2.1.8_7ebba94f7b1e5de09faa8df341790198/node_modules/@heroui/use-draggable/dist/index.mjs\n");

/***/ })

};
;