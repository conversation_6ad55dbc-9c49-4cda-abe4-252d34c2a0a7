/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "/_error";
exports.ids = ["/_error"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./node_modules\\next\\dist\\pages\\_error.js */ \"./node_modules/next/dist/pages/_error.js\");\n/* harmony import */ var _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/_error\",\n        pathname: \"/_error\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _node_modules_next_dist_pages_error_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/config/chatConfig.ts":
/*!**********************************!*\
  !*** ./src/config/chatConfig.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultChatConfig: () => (/* binding */ defaultChatConfig),\n/* harmony export */   getDeploymentUrl: () => (/* binding */ getDeploymentUrl),\n/* harmony export */   loadChatConfig: () => (/* binding */ loadChatConfig)\n/* harmony export */ });\n/**\n * Default configuration values\n */ const defaultChatConfig = {\n    server: {\n        apiUrl: \"http://localhost:7272\",\n        useHttps: false,\n        apiVersion: \"v3\",\n        timeout: 30000\n    },\n    app: {\n        appName: \"ARChat\",\n        appDescription: \"Agent-powered R2R chat application\",\n        version: \"1.0.0\",\n        defaultMode: \"rag_agent\",\n        conversationHistoryLimit: 10\n    },\n    vectorSearch: {\n        enabled: true,\n        searchLimit: 10,\n        searchFilters: \"{}\",\n        indexMeasure: \"cosine_distance\",\n        includeMetadatas: false,\n        probes: undefined,\n        efSearch: undefined\n    },\n    hybridSearch: {\n        enabled: false,\n        fullTextWeight: undefined,\n        semanticWeight: undefined,\n        fullTextLimit: undefined,\n        rrfK: undefined\n    },\n    graphSearch: {\n        enabled: true,\n        kgSearchLevel: null,\n        maxCommunityDescriptionLength: 100,\n        localSearchLimits: {},\n        maxLlmQueries: undefined\n    },\n    ragGeneration: {\n        temperature: 0.1,\n        topP: 1.0,\n        topK: 100,\n        maxTokensToSample: 1024,\n        kgTemperature: 0.1,\n        kgTopP: 1.0,\n        kgTopK: 100,\n        kgMaxTokensToSample: 1024\n    }\n};\n/**\n * Load configuration from public/config.json with fallback to defaults\n */ const loadChatConfig = async ()=>{\n    try {\n        const response = await fetch(\"/config.json\");\n        if (!response.ok) {\n            console.warn(\"Failed to load config.json, using default configuration\");\n            return defaultChatConfig;\n        }\n        const config = await response.json();\n        // Merge with defaults to ensure all required fields are present\n        return {\n            server: {\n                ...defaultChatConfig.server,\n                ...config.server\n            },\n            app: {\n                ...defaultChatConfig.app,\n                ...config.app\n            },\n            vectorSearch: {\n                ...defaultChatConfig.vectorSearch,\n                ...config.vectorSearch\n            },\n            hybridSearch: {\n                ...defaultChatConfig.hybridSearch,\n                ...config.hybridSearch\n            },\n            graphSearch: {\n                ...defaultChatConfig.graphSearch,\n                ...config.graphSearch\n            },\n            ragGeneration: {\n                ...defaultChatConfig.ragGeneration,\n                ...config.ragGeneration\n            }\n        };\n    } catch (error) {\n        console.error(\"Error loading configuration:\", error);\n        return defaultChatConfig;\n    }\n};\n/**\n * Get the deployment URL from configuration\n */ const getDeploymentUrl = (config)=>{\n    const cfg = config || defaultChatConfig;\n    // If apiUrl already includes protocol, use it as-is\n    if (cfg.server.apiUrl.startsWith(\"http://\") || cfg.server.apiUrl.startsWith(\"https://\")) {\n        return cfg.server.apiUrl;\n    }\n    // Otherwise, construct URL with protocol\n    const protocol = cfg.server.useHttps ? \"https\" : \"http\";\n    return `${protocol}://${cfg.server.apiUrl}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/config/chatConfig.ts\n");

/***/ }),

/***/ "./src/context/UserContext.tsx":
/*!*************************************!*\
  !*** ./src/context/UserContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! r2r-js */ \"r2r-js\");\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(r2r_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../config/chatConfig */ \"./src/config/chatConfig.ts\");\n\n\n\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    pipeline: null,\n    setPipeline: ()=>{},\n    selectedModel: \"null\",\n    setSelectedModel: ()=>{},\n    isAuthenticated: false,\n    login: async ()=>({\n            success: false,\n            userRole: \"user\"\n        }),\n    loginWithToken: async ()=>({\n            success: false,\n            userRole: \"user\"\n        }),\n    logout: async ()=>{},\n    unsetCredentials: async ()=>{},\n    register: async ()=>{},\n    authState: {\n        isAuthenticated: false,\n        email: null,\n        userRole: null,\n        userId: null\n    },\n    getClient: ()=>null,\n    client: null,\n    viewMode: \"user\",\n    setViewMode: ()=>{},\n    isSuperUser: ()=>false,\n    createUser: async ()=>{\n        throw new Error(\"createUser is not implemented in the default context\");\n    },\n    deleteUser: async ()=>{\n        throw new Error(\"deleteUser is not implemented in the default context\");\n    },\n    updateUser: async ()=>{\n        throw new Error(\"updateUser is not implemented in the default context\");\n    }\n});\nconst UserProvider = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"user\"); // Default to user mode\n    const [pipeline, setPipeline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        if (false) {}\n        return null;\n    });\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        if (false) {}\n        return \"null\";\n    });\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isAuthenticated: false,\n        email: null,\n        userRole: null,\n        userId: null\n    });\n    const isSuperUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return authState.userRole === \"admin\" && viewMode === \"admin\";\n    }, [\n        authState.userRole,\n        viewMode\n    ]);\n    const [lastLoginTime, setLastLoginTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const login = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (email, password, instanceUrl)=>{\n        const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_3__.r2rClient(instanceUrl);\n        try {\n            const tokens = await newClient.users.login({\n                email: email,\n                password: password\n            });\n            localStorage.setItem(\"chatAccessToken\", tokens.results.accessToken.token);\n            localStorage.setItem(\"chatRefreshToken\", tokens.results.refreshToken.token);\n            newClient.setTokens(tokens.results.accessToken.token, tokens.results.refreshToken.token);\n            setClient(newClient);\n            // Get user info\n            const userInfo = await newClient.users.me();\n            if (!userInfo.results) {\n                throw new Error(\"Failed to get user information\");\n            }\n            // Check user role\n            let userRole = \"user\";\n            try {\n                await newClient.system.settings();\n                userRole = \"admin\";\n            } catch (error) {\n                if (error instanceof Error && \"status\" in error && error.status === 403) {\n                // User doesn't have admin access, keep as \"user\"\n                } else {\n                    console.error(\"Unexpected error when checking user role:\", error);\n                }\n            }\n            setAuthState({\n                isAuthenticated: true,\n                email: userInfo.results.email || email,\n                userRole: userRole,\n                userId: userInfo.results.id\n            });\n            // Store pipeline\n            const newPipeline = {\n                deploymentUrl: instanceUrl\n            };\n            setPipeline(newPipeline);\n            localStorage.setItem(\"pipeline\", JSON.stringify(newPipeline));\n            setLastLoginTime(Date.now());\n            // Redirect to chat page\n            router.push(\"/chat\");\n            return {\n                success: true,\n                userRole\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            throw error;\n        }\n    }, [\n        router\n    ]);\n    const loginWithToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (token, instanceUrl)=>{\n        const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_3__.r2rClient(instanceUrl);\n        try {\n            const result = await newClient.users.loginWithToken({\n                accessToken: token\n            });\n            const userInfo = await newClient.users.me();\n            localStorage.setItem(\"chatAccessToken\", result.accessToken.token);\n            newClient.setTokens(result.accessToken.token, \"\");\n            setClient(newClient);\n            let userRole = \"user\";\n            try {\n                await newClient.system.settings();\n                userRole = \"admin\";\n            } catch (error) {\n                if (error instanceof Error && \"status\" in error && error.status === 403) {\n                // User doesn't have admin access\n                } else {\n                    console.error(\"Unexpected error when checking user role:\", error);\n                }\n            }\n            setAuthState({\n                isAuthenticated: true,\n                email: userInfo.results?.email || \"\",\n                userRole: userRole,\n                userId: userInfo.results?.id || \"\"\n            });\n            const newPipeline = {\n                deploymentUrl: instanceUrl\n            };\n            setPipeline(newPipeline);\n            localStorage.setItem(\"pipeline\", JSON.stringify(newPipeline));\n            return {\n                success: true,\n                userRole\n            };\n        } catch (error) {\n            console.error(\"Token login error:\", error);\n            throw error;\n        }\n    }, []);\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            if (client) {\n                await client.users.logout();\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            // Clear all stored data\n            localStorage.removeItem(\"chatAccessToken\");\n            localStorage.removeItem(\"chatRefreshToken\");\n            localStorage.removeItem(\"pipeline\");\n            setAuthState({\n                isAuthenticated: false,\n                email: null,\n                userRole: null,\n                userId: null\n            });\n            setClient(null);\n            setPipeline(null);\n            // Redirect to login\n            router.push(\"/auth/login\");\n        }\n    }, [\n        client,\n        router\n    ]);\n    const unsetCredentials = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        localStorage.removeItem(\"chatAccessToken\");\n        localStorage.removeItem(\"chatRefreshToken\");\n        setAuthState({\n            isAuthenticated: false,\n            email: null,\n            userRole: null,\n            userId: null\n        });\n        setClient(null);\n    }, []);\n    const register = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (email, password, instanceUrl)=>{\n        const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_3__.r2rClient(instanceUrl);\n        try {\n            await newClient.users.create({\n                email: email,\n                password: password\n            });\n            // After successful registration, log in\n            await login(email, password, instanceUrl);\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            throw error;\n        }\n    }, [\n        login\n    ]);\n    const getClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return client;\n    }, [\n        client\n    ]);\n    const createUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (email, password, name)=>{\n        if (!client) {\n            throw new Error(\"No client available\");\n        }\n        const result = await client.users.create({\n            email,\n            password,\n            name\n        });\n        return {\n            id: result.results.id,\n            email: result.results.email,\n            name: result.results.name,\n            role: \"user\"\n        };\n    }, [\n        client\n    ]);\n    const deleteUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (userId)=>{\n        if (!client) {\n            throw new Error(\"No client available\");\n        }\n        // Note: R2R delete user API might require additional parameters\n        // For now, we'll implement a basic version\n        try {\n            await client.users.delete({\n                id: userId,\n                password: \"\"\n            });\n        } catch (error) {\n            console.error(\"Delete user error:\", error);\n            throw error;\n        }\n    }, [\n        client\n    ]);\n    const updateUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (userId, updates)=>{\n        if (!client) {\n            throw new Error(\"No client available\");\n        }\n        const result = await client.users.update({\n            id: userId,\n            ...updates\n        });\n        return {\n            id: result.results.id,\n            email: result.results.email,\n            name: result.results.name,\n            role: updates.role || \"user\"\n        };\n    }, [\n        client\n    ]);\n    // Initialize authentication state on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                const config = await (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_4__.loadChatConfig)();\n                const deploymentUrl = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_4__.getDeploymentUrl)(config);\n                // Check for stored tokens\n                const accessToken = localStorage.getItem(\"chatAccessToken\");\n                const refreshToken = localStorage.getItem(\"chatRefreshToken\");\n                if (accessToken) {\n                    const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_3__.r2rClient(deploymentUrl);\n                    newClient.setTokens(accessToken, refreshToken || \"\");\n                    try {\n                        // Verify token is still valid\n                        const userInfo = await newClient.users.me();\n                        if (userInfo.results) {\n                            setClient(newClient);\n                            // Check user role\n                            let userRole = \"user\";\n                            try {\n                                await newClient.system.settings();\n                                userRole = \"admin\";\n                            } catch (error) {\n                            // User doesn't have admin access\n                            }\n                            setAuthState({\n                                isAuthenticated: true,\n                                email: userInfo.results.email || \"\",\n                                userRole: userRole,\n                                userId: userInfo.results.id\n                            });\n                            // Set pipeline if not already set\n                            if (!pipeline) {\n                                const newPipeline = {\n                                    deploymentUrl\n                                };\n                                setPipeline(newPipeline);\n                                localStorage.setItem(\"pipeline\", JSON.stringify(newPipeline));\n                            }\n                        }\n                    } catch (error) {\n                        // Token is invalid, clear it\n                        localStorage.removeItem(\"chatAccessToken\");\n                        localStorage.removeItem(\"chatRefreshToken\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Auth initialization error:\", error);\n            } finally{\n                setIsReady(true);\n            }\n        };\n        initializeAuth();\n    }, [\n        pipeline\n    ]);\n    // Save selected model to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        selectedModel\n    ]);\n    const contextValue = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo(()=>({\n            pipeline,\n            setPipeline,\n            selectedModel,\n            setSelectedModel,\n            isAuthenticated: authState.isAuthenticated,\n            authState,\n            login,\n            loginWithToken,\n            logout,\n            unsetCredentials,\n            register,\n            getClient,\n            client,\n            viewMode,\n            setViewMode,\n            isSuperUser,\n            createUser,\n            deleteUser,\n            updateUser\n        }), [\n        pipeline,\n        selectedModel,\n        authState,\n        client,\n        viewMode,\n        isSuperUser,\n        login,\n        loginWithToken,\n        logout,\n        unsetCredentials,\n        register,\n        getClient,\n        createUser,\n        deleteUser,\n        updateUser\n    ]);\n    if (!isReady) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\context\\\\UserContext.tsx\",\n        lineNumber: 467,\n        columnNumber: 5\n    }, undefined);\n};\nconst useUser = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUser must be used within a UserProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/UserContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"dark\",\n        enableSystem: true,\n        disableTransitionOnChange: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_UserContext__WEBPACK_IMPORTED_MODULE_2__.UserProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQzRDO0FBQ1M7QUFDdkI7QUFFZixTQUFTRSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQzVELHFCQUNFLDhEQUFDSixzREFBYUE7UUFDWkssV0FBVTtRQUNWQyxjQUFhO1FBQ2JDLFlBQVk7UUFDWkMseUJBQXlCO2tCQUV6Qiw0RUFBQ1AsOERBQVlBO3NCQUNYLDRFQUFDRTtnQkFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7O0FBSWhDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXJjaGF0Ly4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJztcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tICduZXh0LXRoZW1lcyc7XG5pbXBvcnQgeyBVc2VyUHJvdmlkZXIgfSBmcm9tICdAL2NvbnRleHQvVXNlckNvbnRleHQnO1xuaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcyc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IEFwcFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFRoZW1lUHJvdmlkZXJcbiAgICAgIGF0dHJpYnV0ZT1cImNsYXNzXCJcbiAgICAgIGRlZmF1bHRUaGVtZT1cImRhcmtcIlxuICAgICAgZW5hYmxlU3lzdGVtXG4gICAgICBkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlXG4gICAgPlxuICAgICAgPFVzZXJQcm92aWRlcj5cbiAgICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgICAgPC9Vc2VyUHJvdmlkZXI+XG4gICAgPC9UaGVtZVByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRoZW1lUHJvdmlkZXIiLCJVc2VyUHJvdmlkZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJhdHRyaWJ1dGUiLCJkZWZhdWx0VGhlbWUiLCJlbmFibGVTeXN0ZW0iLCJkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next-themes":
/*!******************************!*\
  !*** external "next-themes" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-themes");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "r2r-js":
/*!*************************!*\
  !*** external "r2r-js" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("r2r-js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F_error&preferredRegion=&absolutePagePath=.%2Fnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();