"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ricky0123+vad-react@0.0.28_7d5f929687bd30d0bc73424fc17e8a68";
exports.ids = ["vendor-chunks/@ricky0123+vad-react@0.0.28_7d5f929687bd30d0bc73424fc17e8a68"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-react@0.0.28_7d5f929687bd30d0bc73424fc17e8a68/node_modules/@ricky0123/vad-react/dist/index.js":
/*!*****************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-react@0.0.28_7d5f929687bd30d0bc73424fc17e8a68/node_modules/@ricky0123/vad-react/dist/index.js ***!
  \*****************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.useMicVAD = exports.getDefaultReactRealTimeVADOptions = exports.utils = void 0;\nconst vad_web_1 = __webpack_require__(/*! @ricky0123/vad-web */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/index.js\");\nconst react_1 = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\"));\nvar vad_web_2 = __webpack_require__(/*! @ricky0123/vad-web */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/index.js\");\nObject.defineProperty(exports, \"utils\", ({ enumerable: true, get: function () { return vad_web_2.utils; } }));\nconst defaultReactOptions = {\n    startOnLoad: true,\n    userSpeakingThreshold: 0.6,\n};\nconst getDefaultReactRealTimeVADOptions = (model) => {\n    return {\n        ...(0, vad_web_1.getDefaultRealTimeVADOptions)(model),\n        ...defaultReactOptions,\n    };\n};\nexports.getDefaultReactRealTimeVADOptions = getDefaultReactRealTimeVADOptions;\nconst reactOptionKeys = Object.keys(defaultReactOptions);\nconst vadOptionKeys = Object.keys((0, vad_web_1.getDefaultRealTimeVADOptions)(\"v5\"));\nconst _filter = (keys, obj) => {\n    return keys.reduce((acc, key) => {\n        acc[key] = obj[key];\n        return acc;\n    }, {});\n};\nfunction useOptions(options) {\n    const model = options.model ?? vad_web_1.DEFAULT_MODEL;\n    options = { ...(0, exports.getDefaultReactRealTimeVADOptions)(model), ...options };\n    const reactOptions = _filter(reactOptionKeys, options);\n    const vadOptions = _filter(vadOptionKeys, options);\n    return [reactOptions, vadOptions];\n}\nfunction useEventCallback(fn) {\n    const ref = react_1.default.useRef(fn);\n    // we copy a ref to the callback scoped to the current state/props on each render\n    useIsomorphicLayoutEffect(() => {\n        ref.current = fn;\n    });\n    return react_1.default.useCallback((...args) => ref.current.apply(void 0, args), []);\n}\nfunction useMicVAD(options) {\n    const [reactOptions, vadOptions] = useOptions(options);\n    const [userSpeaking, updateUserSpeaking] = (0, react_1.useReducer)((state, isSpeechProbability) => isSpeechProbability > reactOptions.userSpeakingThreshold, false);\n    const [loading, setLoading] = (0, react_1.useState)(true);\n    const [errored, setErrored] = (0, react_1.useState)(false);\n    const [listening, setListening] = (0, react_1.useState)(false);\n    const [vad, setVAD] = (0, react_1.useState)(null);\n    const userOnFrameProcessed = useEventCallback(vadOptions.onFrameProcessed);\n    vadOptions.onFrameProcessed = useEventCallback((probs, frame) => {\n        updateUserSpeaking(probs.isSpeech);\n        userOnFrameProcessed(probs, frame);\n    });\n    const { onSpeechEnd, onSpeechStart, onVADMisfire } = vadOptions;\n    const _onSpeechEnd = useEventCallback(onSpeechEnd);\n    const _onSpeechStart = useEventCallback(onSpeechStart);\n    const _onVADMisfire = useEventCallback(onVADMisfire);\n    vadOptions.onSpeechEnd = _onSpeechEnd;\n    vadOptions.onSpeechStart = _onSpeechStart;\n    vadOptions.onVADMisfire = _onVADMisfire;\n    (0, react_1.useEffect)(() => {\n        let myvad;\n        let canceled = false;\n        const setup = async () => {\n            try {\n                myvad = await vad_web_1.MicVAD.new(vadOptions);\n                if (canceled) {\n                    myvad.destroy();\n                    return;\n                }\n            }\n            catch (e) {\n                setLoading(false);\n                if (e instanceof Error) {\n                    setErrored(e.message);\n                }\n                else {\n                    setErrored(String(e));\n                }\n                return;\n            }\n            setVAD(myvad);\n            setLoading(false);\n            if (reactOptions.startOnLoad) {\n                myvad?.start();\n                setListening(true);\n            }\n        };\n        setup().catch((e) => {\n            console.log(\"Well that didn't work\");\n        });\n        return function cleanUp() {\n            myvad?.destroy();\n            canceled = true;\n            if (!loading && !errored) {\n                setListening(false);\n            }\n        };\n    }, []);\n    const pause = () => {\n        if (!loading && !errored) {\n            vad?.pause();\n            setListening(false);\n        }\n    };\n    const start = () => {\n        if (!loading && !errored) {\n            vad?.start();\n            setListening(true);\n        }\n    };\n    const toggle = () => {\n        if (listening) {\n            pause();\n        }\n        else {\n            start();\n        }\n    };\n    return {\n        listening,\n        errored,\n        loading,\n        userSpeaking,\n        pause,\n        start,\n        toggle,\n    };\n}\nexports.useMicVAD = useMicVAD;\nconst useIsomorphicLayoutEffect = typeof window !== \"undefined\" &&\n    typeof window.document !== \"undefined\" &&\n    typeof window.document.createElement !== \"undefined\"\n    ? react_1.default.useLayoutEffect\n    : react_1.default.useEffect;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-react@0.0.28_7d5f929687bd30d0bc73424fc17e8a68/node_modules/@ricky0123/vad-react/dist/index.js\n");

/***/ })

};
;