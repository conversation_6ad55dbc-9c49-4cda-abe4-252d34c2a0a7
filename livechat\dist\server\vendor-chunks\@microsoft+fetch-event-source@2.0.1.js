"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@microsoft+fetch-event-source@2.0.1";
exports.ids = ["vendor-chunks/@microsoft+fetch-event-source@2.0.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/fetch.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/fetch.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EventStreamContentType: () => (/* binding */ EventStreamContentType),\n/* harmony export */   fetchEventSource: () => (/* binding */ fetchEventSource)\n/* harmony export */ });\n/* harmony import */ var _parse__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parse */ \"(ssr)/./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/parse.js\");\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n};\r\n\r\nconst EventStreamContentType = 'text/event-stream';\r\nconst DefaultRetryInterval = 1000;\r\nconst LastEventId = 'last-event-id';\r\nfunction fetchEventSource(input, _a) {\r\n    var { signal: inputSignal, headers: inputHeaders, onopen: inputOnOpen, onmessage, onclose, onerror, openWhenHidden, fetch: inputFetch } = _a, rest = __rest(_a, [\"signal\", \"headers\", \"onopen\", \"onmessage\", \"onclose\", \"onerror\", \"openWhenHidden\", \"fetch\"]);\r\n    return new Promise((resolve, reject) => {\r\n        const headers = Object.assign({}, inputHeaders);\r\n        if (!headers.accept) {\r\n            headers.accept = EventStreamContentType;\r\n        }\r\n        let curRequestController;\r\n        function onVisibilityChange() {\r\n            curRequestController.abort();\r\n            if (!document.hidden) {\r\n                create();\r\n            }\r\n        }\r\n        if (!openWhenHidden) {\r\n            document.addEventListener('visibilitychange', onVisibilityChange);\r\n        }\r\n        let retryInterval = DefaultRetryInterval;\r\n        let retryTimer = 0;\r\n        function dispose() {\r\n            document.removeEventListener('visibilitychange', onVisibilityChange);\r\n            window.clearTimeout(retryTimer);\r\n            curRequestController.abort();\r\n        }\r\n        inputSignal === null || inputSignal === void 0 ? void 0 : inputSignal.addEventListener('abort', () => {\r\n            dispose();\r\n            resolve();\r\n        });\r\n        const fetch = inputFetch !== null && inputFetch !== void 0 ? inputFetch : window.fetch;\r\n        const onopen = inputOnOpen !== null && inputOnOpen !== void 0 ? inputOnOpen : defaultOnOpen;\r\n        async function create() {\r\n            var _a;\r\n            curRequestController = new AbortController();\r\n            try {\r\n                const response = await fetch(input, Object.assign(Object.assign({}, rest), { headers, signal: curRequestController.signal }));\r\n                await onopen(response);\r\n                await (0,_parse__WEBPACK_IMPORTED_MODULE_0__.getBytes)(response.body, (0,_parse__WEBPACK_IMPORTED_MODULE_0__.getLines)((0,_parse__WEBPACK_IMPORTED_MODULE_0__.getMessages)(id => {\r\n                    if (id) {\r\n                        headers[LastEventId] = id;\r\n                    }\r\n                    else {\r\n                        delete headers[LastEventId];\r\n                    }\r\n                }, retry => {\r\n                    retryInterval = retry;\r\n                }, onmessage)));\r\n                onclose === null || onclose === void 0 ? void 0 : onclose();\r\n                dispose();\r\n                resolve();\r\n            }\r\n            catch (err) {\r\n                if (!curRequestController.signal.aborted) {\r\n                    try {\r\n                        const interval = (_a = onerror === null || onerror === void 0 ? void 0 : onerror(err)) !== null && _a !== void 0 ? _a : retryInterval;\r\n                        window.clearTimeout(retryTimer);\r\n                        retryTimer = window.setTimeout(create, interval);\r\n                    }\r\n                    catch (innerErr) {\r\n                        dispose();\r\n                        reject(innerErr);\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        create();\r\n    });\r\n}\r\nfunction defaultOnOpen(response) {\r\n    const contentType = response.headers.get('content-type');\r\n    if (!(contentType === null || contentType === void 0 ? void 0 : contentType.startsWith(EventStreamContentType))) {\r\n        throw new Error(`Expected content-type to be ${EventStreamContentType}, Actual: ${contentType}`);\r\n    }\r\n}\r\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/fetch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/parse.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/parse.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getBytes: () => (/* binding */ getBytes),\n/* harmony export */   getLines: () => (/* binding */ getLines),\n/* harmony export */   getMessages: () => (/* binding */ getMessages)\n/* harmony export */ });\nasync function getBytes(stream, onChunk) {\r\n    const reader = stream.getReader();\r\n    let result;\r\n    while (!(result = await reader.read()).done) {\r\n        onChunk(result.value);\r\n    }\r\n}\r\nfunction getLines(onLine) {\r\n    let buffer;\r\n    let position;\r\n    let fieldLength;\r\n    let discardTrailingNewline = false;\r\n    return function onChunk(arr) {\r\n        if (buffer === undefined) {\r\n            buffer = arr;\r\n            position = 0;\r\n            fieldLength = -1;\r\n        }\r\n        else {\r\n            buffer = concat(buffer, arr);\r\n        }\r\n        const bufLength = buffer.length;\r\n        let lineStart = 0;\r\n        while (position < bufLength) {\r\n            if (discardTrailingNewline) {\r\n                if (buffer[position] === 10) {\r\n                    lineStart = ++position;\r\n                }\r\n                discardTrailingNewline = false;\r\n            }\r\n            let lineEnd = -1;\r\n            for (; position < bufLength && lineEnd === -1; ++position) {\r\n                switch (buffer[position]) {\r\n                    case 58:\r\n                        if (fieldLength === -1) {\r\n                            fieldLength = position - lineStart;\r\n                        }\r\n                        break;\r\n                    case 13:\r\n                        discardTrailingNewline = true;\r\n                    case 10:\r\n                        lineEnd = position;\r\n                        break;\r\n                }\r\n            }\r\n            if (lineEnd === -1) {\r\n                break;\r\n            }\r\n            onLine(buffer.subarray(lineStart, lineEnd), fieldLength);\r\n            lineStart = position;\r\n            fieldLength = -1;\r\n        }\r\n        if (lineStart === bufLength) {\r\n            buffer = undefined;\r\n        }\r\n        else if (lineStart !== 0) {\r\n            buffer = buffer.subarray(lineStart);\r\n            position -= lineStart;\r\n        }\r\n    };\r\n}\r\nfunction getMessages(onId, onRetry, onMessage) {\r\n    let message = newMessage();\r\n    const decoder = new TextDecoder();\r\n    return function onLine(line, fieldLength) {\r\n        if (line.length === 0) {\r\n            onMessage === null || onMessage === void 0 ? void 0 : onMessage(message);\r\n            message = newMessage();\r\n        }\r\n        else if (fieldLength > 0) {\r\n            const field = decoder.decode(line.subarray(0, fieldLength));\r\n            const valueOffset = fieldLength + (line[fieldLength + 1] === 32 ? 2 : 1);\r\n            const value = decoder.decode(line.subarray(valueOffset));\r\n            switch (field) {\r\n                case 'data':\r\n                    message.data = message.data\r\n                        ? message.data + '\\n' + value\r\n                        : value;\r\n                    break;\r\n                case 'event':\r\n                    message.event = value;\r\n                    break;\r\n                case 'id':\r\n                    onId(message.id = value);\r\n                    break;\r\n                case 'retry':\r\n                    const retry = parseInt(value, 10);\r\n                    if (!isNaN(retry)) {\r\n                        onRetry(message.retry = retry);\r\n                    }\r\n                    break;\r\n            }\r\n        }\r\n    };\r\n}\r\nfunction concat(a, b) {\r\n    const res = new Uint8Array(a.length + b.length);\r\n    res.set(a);\r\n    res.set(b, a.length);\r\n    return res;\r\n}\r\nfunction newMessage() {\r\n    return {\r\n        data: '',\r\n        event: '',\r\n        id: '',\r\n        retry: undefined,\r\n    };\r\n}\r\n//# sourceMappingURL=parse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/parse.js\n");

/***/ })

};
;