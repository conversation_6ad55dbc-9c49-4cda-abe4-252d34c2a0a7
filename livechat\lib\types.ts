export interface User {
  id: string;
  email: string;
  name?: string;
  role: "admin" | "user";
}

export interface AuthState {
  isAuthenticated: boolean;
  email: string | null;
  userRole: "admin" | "user" | null;
  userId: string | null;
  token?: string;
}

export interface LoginResult {
  success: boolean;
  userRole: "admin" | "user";
}

export interface Pipeline {
  id: string;
  name: string;
  description?: string;
}

export interface ChatConfig {
  server: ServerConfig;
  app: AppConfig;
  vectorSearch: VectorSearchConfig;
  hybridSearch: HybridSearchConfig;
  graphSearch: GraphSearchConfig;
  ragGeneration: RagGenerationConfig;
}

export interface ServerConfig {
  apiUrl: string;
  useHttps?: boolean;
  apiVersion?: string;
  timeout?: number;
}

export interface AppConfig {
  appName: string;
  appDescription: string;
  version: string;
  defaultMode: 'rag' | 'rag_agent';
  conversationHistoryLimit?: number;
}

export interface VectorSearchConfig {
  enabled: boolean;
  searchLimit: number;
  searchFilters: string;
  indexMeasure: 'cosine_distance' | 'l2_distance' | 'max_inner_product';
  includeMetadatas: boolean;
  probes?: number;
  efSearch?: number;
}

export interface HybridSearchConfig {
  enabled: boolean;
  fullTextWeight?: number;
  semanticWeight?: number;
  fullTextLimit?: number;
  rrfK?: number;
}

export interface GraphSearchConfig {
  enabled: boolean;
  kgSearchLevel?: number | null;
  maxCommunityDescriptionLength: number;
  localSearchLimits: Record<string, number>;
  maxLlmQueries?: number;
}

export interface RagGenerationConfig {
  temperature: number;
  topP: number;
  topK: number;
  maxTokensToSample: number;
  kgTemperature: number;
  kgTopP: number;
  kgTopK: number;
  kgMaxTokensToSample: number;
}

export interface Pipeline {
  deploymentUrl: string;
  name?: string;
  description?: string;
}