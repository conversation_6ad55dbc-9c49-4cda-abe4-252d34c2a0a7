(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[78],{69811:e=>{"use strict";e.exports="object"==typeof self?self.FormData:window.FormData},75664:(e,t,r)=>{"use strict";r.r(t);var n=r(76512),i={};for(let e in n)"default"!==e&&(i[e]=()=>n[e]);r.d(t,i)},7460:e=>{!function(){var t={675:function(e,t){"use strict";t.byteLength=function(e){var t=l(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,o=l(e),s=o[0],a=o[1],u=new i((s+a)*3/4-a),c=0,f=a>0?s-4:s;for(r=0;r<f;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],u[c++]=t>>16&255,u[c++]=t>>8&255,u[c++]=255&t;return 2===a&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,u[c++]=255&t),1===a&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,u[c++]=t>>8&255,u[c++]=255&t),u},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,o=[],s=0,a=n-i;s<a;s+=16383)o.push(function(e,t,n){for(var i,o=[],s=t;s<n;s+=3)o.push(r[(i=(e[s]<<16&0xff0000)+(e[s+1]<<8&65280)+(255&e[s+2]))>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return o.join("")}(e,s,s+16383>a?a:s+16383));return 1===i?o.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===i&&o.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),o.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,a=o.length;s<a;++s)r[s]=o[s],n[o.charCodeAt(s)]=s;function l(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},72:function(e,t,r){"use strict";var n=r(675),i=r(783),o="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function s(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,a.prototype),t}function a(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return c(e)}return l(e,t,r)}function l(e,t,r){if("string"==typeof e)return function(e,t){if(("string"!=typeof t||""===t)&&(t="utf8"),!a.isEncoding(t))throw TypeError("Unknown encoding: "+t);var r=0|h(e,t),n=s(r),i=n.write(e,t);return i!==r&&(n=n.slice(0,i)),n}(e,t);if(ArrayBuffer.isView(e))return f(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(S(e,ArrayBuffer)||e&&S(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(S(e,SharedArrayBuffer)||e&&S(e.buffer,SharedArrayBuffer)))return function(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),a.prototype),n}(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var n=e.valueOf&&e.valueOf();if(null!=n&&n!==e)return a.from(n,t,r);var i=function(e){if(a.isBuffer(e)){var t,r=0|d(e.length),n=s(r);return 0===n.length||e.copy(n,0,0,r),n}return void 0!==e.length?"number"!=typeof e.length||(t=e.length)!=t?s(0):f(e):"Buffer"===e.type&&Array.isArray(e.data)?f(e.data):void 0}(e);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return a.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function u(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function c(e){return u(e),s(e<0?0:0|d(e))}function f(e){for(var t=e.length<0?0:0|d(e.length),r=s(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}function d(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function h(e,t){if(a.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||S(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return _(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return j(e).length;default:if(i)return n?-1:_(e).length;t=(""+t).toLowerCase(),i=!0}}function p(e,t,r){var i,o,s=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=C[e[o]];return i}(this,t,r);case"utf8":case"utf-8":return b(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}(this,t,r);case"base64":return i=t,o=r,0===i&&o===this.length?n.fromByteArray(this):n.fromByteArray(this.slice(i,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}(this,t,r);default:if(s)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),s=!0}}function m(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function y(e,t,r,n,i){var o;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(o=r=+r)!=o&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return -1;r=e.length-1}else if(r<0){if(!i)return -1;r=0}if("string"==typeof t&&(t=a.from(t,n)),a.isBuffer(t))return 0===t.length?-1:g(e,t,r,n,i);if("number"==typeof t)return(t&=255,"function"==typeof Uint8Array.prototype.indexOf)?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):g(e,[t],r,n,i);throw TypeError("val must be string, number or Buffer")}function g(e,t,r,n,i){var o,s=1,a=e.length,l=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;s=2,a/=2,l/=2,r/=2}function u(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(i){var c=-1;for(o=r;o<a;o++)if(u(e,o)===u(t,-1===c?0:o-c)){if(-1===c&&(c=o),o-c+1===l)return c*s}else -1!==c&&(o-=o-c),c=-1}else for(r+l>a&&(r=a-l),o=r;o>=0;o--){for(var f=!0,d=0;d<l;d++)if(u(e,o+d)!==u(t,d)){f=!1;break}if(f)return o}return -1}function b(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,s,a,l,u=e[i],c=null,f=u>239?4:u>223?3:u>191?2:1;if(i+f<=r)switch(f){case 1:u<128&&(c=u);break;case 2:(192&(o=e[i+1]))==128&&(l=(31&u)<<6|63&o)>127&&(c=l);break;case 3:o=e[i+1],s=e[i+2],(192&o)==128&&(192&s)==128&&(l=(15&u)<<12|(63&o)<<6|63&s)>2047&&(l<55296||l>57343)&&(c=l);break;case 4:o=e[i+1],s=e[i+2],a=e[i+3],(192&o)==128&&(192&s)==128&&(192&a)==128&&(l=(15&u)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&l<1114112&&(c=l)}null===c?(c=65533,f=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=f}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);for(var r="",n=0;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=4096));return r}(n)}function v(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function w(e,t,r,n,i,o){if(!a.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function O(e,t,r,n,i,o){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function E(e,t,r,n,o){return t=+t,r>>>=0,o||O(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,n,23,4),r+4}function T(e,t,r,n,o){return t=+t,r>>>=0,o||O(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,n,52,8),r+8}t.Buffer=a,t.SlowBuffer=function(e){return+e!=e&&(e=0),a.alloc(+e)},t.INSPECT_MAX_BYTES=50,t.kMaxLength=0x7fffffff,a.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(e,t,r){return l(e,t,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(e,t,r){return(u(e),e<=0)?s(e):void 0!==t?"string"==typeof r?s(e).fill(t,r):s(e).fill(t):s(e)},a.allocUnsafe=function(e){return c(e)},a.allocUnsafeSlow=function(e){return c(e)},a.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==a.prototype},a.compare=function(e,t){if(S(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),S(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(e)||!a.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},a.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return a.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=a.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var o=e[r];if(S(o,Uint8Array)&&(o=a.from(o)),!a.isBuffer(o))throw TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},a.byteLength=h,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)m(this,t,t+1);return this},a.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)m(this,t,t+3),m(this,t+1,t+2);return this},a.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)m(this,t,t+7),m(this,t+1,t+6),m(this,t+2,t+5),m(this,t+3,t+4);return this},a.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?b(this,0,e):p.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(e){if(!a.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===a.compare(this,e)},a.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},o&&(a.prototype[o]=a.prototype.inspect),a.prototype.compare=function(e,t,r,n,i){if(S(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var o=i-n,s=r-t,l=Math.min(o,s),u=this.slice(n,i),c=e.slice(t,r),f=0;f<l;++f)if(u[f]!==c[f]){o=u[f],s=c[f];break}return o<s?-1:s<o?1:0},a.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},a.prototype.indexOf=function(e,t,r){return y(this,e,t,r,!0)},a.prototype.lastIndexOf=function(e,t,r){return y(this,e,t,r,!1)},a.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,o,s,a,l,u,c,f,d,h,p,m,y=this.length-t;if((void 0===r||r>y)&&(r=y),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var g=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a=parseInt(t.substr(2*s,2),16);if(a!=a)break;e[r+s]=a}return s}(this,e,t,r);case"utf8":case"utf-8":return l=t,u=r,k(_(e,this.length-l),this,l,u);case"ascii":return c=t,f=r,k(x(e),this,c,f);case"latin1":case"binary":return i=this,o=e,s=t,a=r,k(x(o),i,s,a);case"base64":return d=t,h=r,k(j(e),this,d,h);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return p=t,m=r,k(function(e,t){for(var r,n,i=[],o=0;o<e.length&&!((t-=2)<0);++o)n=(r=e.charCodeAt(o))>>8,i.push(r%256),i.push(n);return i}(e,this.length-p),this,p,m);default:if(g)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),g=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||v(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},a.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||v(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},a.prototype.readUInt8=function(e,t){return e>>>=0,t||v(e,1,this.length),this[e]},a.prototype.readUInt16LE=function(e,t){return e>>>=0,t||v(e,2,this.length),this[e]|this[e+1]<<8},a.prototype.readUInt16BE=function(e,t){return e>>>=0,t||v(e,2,this.length),this[e]<<8|this[e+1]},a.prototype.readUInt32LE=function(e,t){return e>>>=0,t||v(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},a.prototype.readUInt32BE=function(e,t){return e>>>=0,t||v(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},a.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||v(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},a.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||v(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},a.prototype.readInt8=function(e,t){return(e>>>=0,t||v(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},a.prototype.readInt16LE=function(e,t){e>>>=0,t||v(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt16BE=function(e,t){e>>>=0,t||v(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},a.prototype.readInt32LE=function(e,t){return e>>>=0,t||v(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},a.prototype.readInt32BE=function(e,t){return e>>>=0,t||v(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},a.prototype.readFloatLE=function(e,t){return e>>>=0,t||v(e,4,this.length),i.read(this,e,!0,23,4)},a.prototype.readFloatBE=function(e,t){return e>>>=0,t||v(e,4,this.length),i.read(this,e,!1,23,4)},a.prototype.readDoubleLE=function(e,t){return e>>>=0,t||v(e,8,this.length),i.read(this,e,!0,52,8)},a.prototype.readDoubleBE=function(e,t){return e>>>=0,t||v(e,8,this.length),i.read(this,e,!1,52,8)},a.prototype.writeUIntLE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,e,t,r,i,0)}var o=1,s=0;for(this[t]=255&e;++s<r&&(o*=256);)this[t+s]=e/o&255;return t+r},a.prototype.writeUIntBE=function(e,t,r,n){if(e=+e,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,e,t,r,i,0)}var o=r-1,s=1;for(this[t+o]=255&e;--o>=0&&(s*=256);)this[t+o]=e/s&255;return t+r},a.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,1,255,0),this[t]=255&e,t+1},a.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},a.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,e,t,r,i-1,-i)}var o=0,s=1,a=0;for(this[t]=255&e;++o<r&&(s*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+r},a.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,e,t,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[t+o]=255&e;--o>=0&&(s*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+r},a.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},a.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},a.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||w(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeFloatLE=function(e,t,r){return E(this,e,t,!0,r)},a.prototype.writeFloatBE=function(e,t,r){return E(this,e,t,!1,r)},a.prototype.writeDoubleLE=function(e,t,r){return T(this,e,t,!0,r)},a.prototype.writeDoubleBE=function(e,t,r){return T(this,e,t,!1,r)},a.prototype.copy=function(e,t,r,n){if(!a.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i=n-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,n);else if(this===e&&r<t&&t<n)for(var o=i-1;o>=0;--o)e[o+t]=this[o+r];else Uint8Array.prototype.set.call(e,this.subarray(r,n),t);return i},a.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var i,o=e.charCodeAt(0);("utf8"===n&&o<128||"latin1"===n)&&(e=o)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var s=a.isBuffer(e)?e:a.from(e,n),l=s.length;if(0===l)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=s[i%l]}return this};var R=/[^+/0-9A-Za-z-_]/g;function _(e,t){t=t||1/0;for(var r,n=e.length,i=null,o=[],s=0;s<n;++s){if((r=e.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319||s+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return o}function x(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function j(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(R,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function k(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length)&&!(i>=e.length);++i)t[i+r]=e[i];return i}function S(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var C=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)t[n+i]=e[r]+e[i];return t}()},783:function(e,t){t.read=function(e,t,r,n,i){var o,s,a=8*i-n-1,l=(1<<a)-1,u=l>>1,c=-7,f=r?i-1:0,d=r?-1:1,h=e[t+f];for(f+=d,o=h&(1<<-c)-1,h>>=-c,c+=a;c>0;o=256*o+e[t+f],f+=d,c-=8);for(s=o&(1<<-c)-1,o>>=-c,c+=n;c>0;s=256*s+e[t+f],f+=d,c-=8);if(0===o)o=1-u;else{if(o===l)return s?NaN:1/0*(h?-1:1);s+=Math.pow(2,n),o-=u}return(h?-1:1)*s*Math.pow(2,o-n)},t.write=function(e,t,r,n,i,o){var s,a,l,u=8*o-i-1,c=(1<<u)-1,f=c>>1,d=23===i?5960464477539062e-23:0,h=n?0:o-1,p=n?1:-1,m=t<0||0===t&&1/t<0?1:0;for(isNaN(t=Math.abs(t))||t===1/0?(a=isNaN(t)?1:0,s=c):(s=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-s))<1&&(s--,l*=2),s+f>=1?t+=d/l:t+=d*Math.pow(2,1-f),t*l>=2&&(s++,l/=2),s+f>=c?(a=0,s=c):s+f>=1?(a=(t*l-1)*Math.pow(2,i),s+=f):(a=t*Math.pow(2,f-1)*Math.pow(2,i),s=0));i>=8;e[r+h]=255&a,h+=p,a/=256,i-=8);for(s=s<<i|a,u+=i;u>0;e[r+h]=255&s,h+=p,s/=256,u-=8);e[r+h-p]|=128*m}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var o=r[e]={exports:{}},s=!0;try{t[e](o,o.exports,n),s=!1}finally{s&&delete r[e]}return o.exports}n.ab="//";var i=n(72);e.exports=i}()},11843:e=>{!function(){"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};t.endianness=function(){return"LE"},t.hostname=function(){return"undefined"!=typeof location?location.hostname:""},t.loadavg=function(){return[]},t.uptime=function(){return 0},t.freemem=function(){return Number.MAX_VALUE},t.totalmem=function(){return Number.MAX_VALUE},t.cpus=function(){return[]},t.type=function(){return"Browser"},t.release=function(){return"undefined"!=typeof navigator?navigator.appVersion:""},t.networkInterfaces=t.getNetworkInterfaces=function(){return{}},t.arch=function(){return"javascript"},t.platform=function(){return"browser"},t.tmpdir=t.tmpDir=function(){return"/tmp"},t.EOL="\n",t.homedir=function(){return"/"},e.exports=t}()},23596:e=>{!function(){"use strict";var t={114:function(e){function t(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}function r(e,t){for(var r,n="",i=0,o=-1,s=0,a=0;a<=e.length;++a){if(a<e.length)r=e.charCodeAt(a);else if(47===r)break;else r=47;if(47===r){if(o===a-1||1===s);else if(o!==a-1&&2===s){if(n.length<2||2!==i||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2)){if(n.length>2){var l=n.lastIndexOf("/");if(l!==n.length-1){-1===l?(n="",i=0):i=(n=n.slice(0,l)).length-1-n.lastIndexOf("/"),o=a,s=0;continue}}else if(2===n.length||1===n.length){n="",i=0,o=a,s=0;continue}}t&&(n.length>0?n+="/..":n="..",i=2)}else n.length>0?n+="/"+e.slice(o+1,a):n=e.slice(o+1,a),i=a-o-1;o=a,s=0}else 46===r&&-1!==s?++s:s=-1}return n}var n={resolve:function(){for(var e,n,i="",o=!1,s=arguments.length-1;s>=-1&&!o;s--)s>=0?n=arguments[s]:(void 0===e&&(e=""),n=e),t(n),0!==n.length&&(i=n+"/"+i,o=47===n.charCodeAt(0));return(i=r(i,!o),o)?i.length>0?"/"+i:"/":i.length>0?i:"."},normalize:function(e){if(t(e),0===e.length)return".";var n=47===e.charCodeAt(0),i=47===e.charCodeAt(e.length-1);return(0!==(e=r(e,!n)).length||n||(e="."),e.length>0&&i&&(e+="/"),n)?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0==arguments.length)return".";for(var e,r=0;r<arguments.length;++r){var i=arguments[r];t(i),i.length>0&&(void 0===e?e=i:e+="/"+i)}return void 0===e?".":n.normalize(e)},relative:function(e,r){if(t(e),t(r),e===r||(e=n.resolve(e))===(r=n.resolve(r)))return"";for(var i=1;i<e.length&&47===e.charCodeAt(i);++i);for(var o=e.length,s=o-i,a=1;a<r.length&&47===r.charCodeAt(a);++a);for(var l=r.length-a,u=s<l?s:l,c=-1,f=0;f<=u;++f){if(f===u){if(l>u){if(47===r.charCodeAt(a+f))return r.slice(a+f+1);if(0===f)return r.slice(a+f)}else s>u&&(47===e.charCodeAt(i+f)?c=f:0===f&&(c=0));break}var d=e.charCodeAt(i+f);if(d!==r.charCodeAt(a+f))break;47===d&&(c=f)}var h="";for(f=i+c+1;f<=o;++f)(f===o||47===e.charCodeAt(f))&&(0===h.length?h+="..":h+="/..");return h.length>0?h+r.slice(a+c):(a+=c,47===r.charCodeAt(a)&&++a,r.slice(a))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var r=e.charCodeAt(0),n=47===r,i=-1,o=!0,s=e.length-1;s>=1;--s)if(47===(r=e.charCodeAt(s))){if(!o){i=s;break}}else o=!1;return -1===i?n?"/":".":n&&1===i?"//":e.slice(0,i)},basename:function(e,r){if(void 0!==r&&"string"!=typeof r)throw TypeError('"ext" argument must be a string');t(e);var n,i=0,o=-1,s=!0;if(void 0!==r&&r.length>0&&r.length<=e.length){if(r.length===e.length&&r===e)return"";var a=r.length-1,l=-1;for(n=e.length-1;n>=0;--n){var u=e.charCodeAt(n);if(47===u){if(!s){i=n+1;break}}else -1===l&&(s=!1,l=n+1),a>=0&&(u===r.charCodeAt(a)?-1==--a&&(o=n):(a=-1,o=l))}return i===o?o=l:-1===o&&(o=e.length),e.slice(i,o)}for(n=e.length-1;n>=0;--n)if(47===e.charCodeAt(n)){if(!s){i=n+1;break}}else -1===o&&(s=!1,o=n+1);return -1===o?"":e.slice(i,o)},extname:function(e){t(e);for(var r=-1,n=0,i=-1,o=!0,s=0,a=e.length-1;a>=0;--a){var l=e.charCodeAt(a);if(47===l){if(!o){n=a+1;break}continue}-1===i&&(o=!1,i=a+1),46===l?-1===r?r=a:1!==s&&(s=1):-1!==r&&(s=-1)}return -1===r||-1===i||0===s||1===s&&r===i-1&&r===n+1?"":e.slice(r,i)},format:function(e){var t,r;if(null===e||"object"!=typeof e)throw TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return t=e.dir||e.root,r=e.base||(e.name||"")+(e.ext||""),t?t===e.root?t+r:t+"/"+r:r},parse:function(e){t(e);var r,n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var i=e.charCodeAt(0),o=47===i;o?(n.root="/",r=1):r=0;for(var s=-1,a=0,l=-1,u=!0,c=e.length-1,f=0;c>=r;--c){if(47===(i=e.charCodeAt(c))){if(!u){a=c+1;break}continue}-1===l&&(u=!1,l=c+1),46===i?-1===s?s=c:1!==f&&(f=1):-1!==s&&(f=-1)}return -1===s||-1===l||0===f||1===f&&s===l-1&&s===a+1?-1!==l&&(0===a&&o?n.base=n.name=e.slice(1,l):n.base=n.name=e.slice(a,l)):(0===a&&o?(n.name=e.slice(1,s),n.base=e.slice(1,l)):(n.name=e.slice(a,s),n.base=e.slice(a,l)),n.ext=e.slice(s,l)),a>0?n.dir=e.slice(0,a-1):o&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};n.posix=n,e.exports=n}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var o=r[e]={exports:{}},s=!0;try{t[e](o,o.exports,n),s=!1}finally{s&&delete r[e]}return o.exports}n.ab="//";var i=n(114);e.exports=i}()},8291:function(e,t,r){"use strict";var n=r(26332),i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.BaseClient=void 0;let o=i(r(17144)),s=i(r(69811)),a=r(8713);"undefined"==typeof window&&r(2208);class l{constructor(e="http://localhost:7272",t="",r=!0,i=!1){this.baseUrl=`${e}${t}`,this.accessToken=null,this.apiKey=n.env.R2R_API_KEY||null,this.projectName=null,this.refreshToken=null,this.anonymousTelemetry=r,this.enableAutoRefresh=i,this.axiosInstance=o.default.create({baseURL:this.baseUrl,headers:{"Content-Type":"application/json"}})}async _makeRequest(e,t,r={},n="v3"){let i=Object.assign(Object.assign({method:e,url:`/${n}/${t}`,headers:Object.assign({},r.headers),params:r.params},r),{responseType:r.responseType||"json"});if(i.headers=i.headers||{},r.params&&(i.paramsSerializer=e=>Object.entries(e).map(([e,t])=>Array.isArray(t)?t.map(t=>`${encodeURIComponent(e)}=${encodeURIComponent(t)}`).join("&"):`${encodeURIComponent(e)}=${encodeURIComponent(String(t))}`).join("&")),r.data&&(void 0!==s.default&&r.data instanceof s.default?(i.data=r.data,delete i.headers["Content-Type"]):"object"==typeof r.data?"application/x-www-form-urlencoded"===i.headers["Content-Type"]?i.data=Object.keys(r.data).map(e=>`${encodeURIComponent(e)}=${encodeURIComponent(r.data[e])}`).join("&"):(i.data=JSON.stringify(r.data),"DELETE"!==e?i.headers["Content-Type"]="application/json":(i.headers["Content-Type"]="application/json",i.data=JSON.stringify(r.data))):i.data=r.data),this.accessToken&&this.apiKey)throw Error("Cannot have both access token and api key.");if(this.apiKey&&!["register","login","verify_email","health"].includes(t)?i.headers["x-api-key"]=this.apiKey:this.accessToken&&!["register","login","verify_email","health"].includes(t)&&(i.headers.Authorization=`Bearer ${this.accessToken}`),this.projectName&&(i.headers["x-project-name"]=this.projectName),"stream"===r.responseType)return this.handleStreamingRequest(e,n,t,i);try{let e=await this.axiosInstance.request(i);if("blob"===r.responseType)return e.data;if("arraybuffer"===r.responseType){if(r.returnFullResponse)return e;return e.data}return r.returnFullResponse?Object.assign(Object.assign({},e),{data:(0,a.ensureCamelCase)(e.data)}):(0,a.ensureCamelCase)(e.data)}catch(e){throw o.default.isAxiosError(e)&&e.response&&function(e){let t;if(e.status<400)return;let r=(0,a.ensureCamelCase)(e.data);throw t="object"==typeof r&&null!==r?r.message||r.detail&&r.detail.message||"string"==typeof r.detail&&r.detail||JSON.stringify(r):String(r),Error(`Status ${e.status}: ${t}`)}(e.response),e}}async handleStreamingRequest(e,t,r,n){var i;let o={};Object.entries(n.headers||{}).forEach(([e,t])=>{"string"==typeof t&&(o[e]=t)});try{let s=await fetch(`${this.baseUrl}/${t}/${r}`,{method:e,headers:o,body:n.data});if(!s.ok){let e=await s.json().catch(()=>({}));throw Error(`HTTP error! status: ${s.status}: ${(0,a.ensureCamelCase)(e).message||"Unknown error"}`)}let l=new TransformStream({transform(e,t){t.enqueue(e)}}),u=null===(i=s.body)||void 0===i?void 0:i.pipeThrough(l);if(!u)throw Error("No response body received from stream");return u}catch(e){throw console.error("Streaming request failed:",e),e}}_ensureAuthenticated(){if(!this.accessToken)throw Error("Not authenticated. Please login first.")}setTokens(e,t){this.accessToken=e,this.refreshToken=t}setApiKey(e){if(!e)throw Error("API key is required");this.apiKey=e}setProjectName(e){if(!e)throw Error("Project name is required");this.projectName=e}unsetProjectName(){this.projectName=null}}t.BaseClient=l},90959:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.r2rClient=void 0;var o=r(37966);Object.defineProperty(t,"r2rClient",{enumerable:!0,get:function(){return o.r2rClient}}),i(r(71706),t)},37966:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.r2rClient=void 0;let i=n(r(17144)),o=r(8291),s=r(51740),a=r(88559),l=r(29762),u=r(584),c=r(32295),f=r(40191),d=r(60621),h=r(32466),p=r(85483),m=r(13550);"undefined"==typeof window&&r(2208);class y extends o.BaseClient{constructor(e,t=!0,r={}){super(e,"",t,r.enableAutoRefresh),this.chunks=new s.ChunksClient(this),this.collections=new a.CollectionsClient(this),this.conversations=new l.ConversationsClient(this),this.documents=new u.DocumentsClient(this),this.graphs=new c.GraphsClient(this),this.indices=new f.IndiciesClient(this),this.prompts=new d.PromptsClient(this),this.retrieval=new h.RetrievalClient(this),this.system=new p.SystemClient(this),this.users=new m.UsersClient(this),this.axiosInstance=i.default.create({baseURL:this.baseUrl,headers:{"Content-Type":"application/json"}}),this.getTokensCallback=r.getTokensCallback,this.setTokensCallback=r.setTokensCallback,this.onRefreshFailedCallback=r.onRefreshFailedCallback,this.axiosInstance.interceptors.request.use(e=>{var t;let r=null===(t=this.getTokensCallback)||void 0===t?void 0:t.call(this),n=(null==r?void 0:r.accessToken)||null;return n&&(e.headers.Authorization=`Bearer ${n}`),e},e=>(console.error("[r2rClient] Request interceptor error:",e),Promise.reject(e))),this.setupResponseInterceptor()}setupResponseInterceptor(){this.axiosInstance.interceptors.response.use(e=>e,async e=>{var t,r,n,i,o,s;let a=null===(t=e.response)||void 0===t?void 0:t.status,l=null===(r=e.config)||void 0===r?void 0:r.url,u=null===(n=e.response)||void 0===n?void 0:n.data;if(null==l?void 0:l.includes("/v3/users/refresh-token"))return console.error("[r2rClient] Refresh call itself returned 401/403 => logging out"),null===(i=this.onRefreshFailedCallback)||void 0===i||i.call(this),Promise.reject(e);let c=!!(null==u?void 0:u.error_code)&&"TOKEN_EXPIRED"===u.error_code.toUpperCase(),f=((null==u?void 0:u.message)||"").toLowerCase(),d=f.includes("invalid token")||f.includes("token expired")||f.includes("credentials");if((401===a||403===a)&&this.getTokensCallback&&(c||d)){let{refreshToken:t}=this.getTokensCallback();if(!t)return console.error("[r2rClient] No refresh token found => logout"),null===(o=this.onRefreshFailedCallback)||void 0===o||o.call(this),Promise.reject(e);try{let t=await this.users.refreshAccessToken(),r=t.results.accessToken.token,n=t.results.refreshToken.token;if(this.setTokens(r,n),e.config)return e.config.headers.Authorization=`Bearer ${r}`,this.axiosInstance.request(e.config);console.warn("[r2rClient] No request config found to retry. Possibly manual re-fetch needed")}catch(e){return console.error("[r2rClient] Refresh attempt failed => logging out. Error was:",e),null===(s=this.onRefreshFailedCallback)||void 0===s||s.call(this),Promise.reject(e)}}return Promise.reject(e)})}makeRequest(e,t,r={}){return this._makeRequest(e,t,r,"v3")}getRefreshToken(){return this.refreshToken}setTokens(e,t){var r;super.setTokens(e||"",t||""),null===(r=this.setTokensCallback)||void 0===r||r.call(this,e,t)}}t.r2rClient=y,t.default=y},71706:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.IndexMeasure=void 0,function(e){e.COSINE_DISTANCE="cosine_distance",e.L2_DISTANCE="l2_distance",e.MAX_INNER_PRODUCT="max_inner_product"}(r||(t.IndexMeasure=r={}))},8713:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(13062),t),i(r(85872),t)},13062:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ensureCamelCase=function e(t){if(!n(t))return t;if(Array.isArray(t))return t.map(t=>e(t));if(!r(t))return t;try{let n={};for(let o of[...Object.getOwnPropertyNames(t),...Object.getOwnPropertySymbols(t)]){let s=Object.getOwnPropertyDescriptor(t,o);if("symbol"==typeof o)Object.defineProperty(n,o,s);else{let s=i(o.toString()),a=t[o];if(r(a)){let t=e(a);n[s]=t,Object.getOwnPropertySymbols(a).forEach(e=>{let r=Object.getOwnPropertyDescriptor(a,e);Object.defineProperty(t,e,r)})}else Array.isArray(a)?n[s]=a.map(t=>e(t)):n[s]=a}}return n}catch(e){throw Error(`Failed to transform to camelCase: ${e instanceof Error?e.message:"Unknown error"}`)}},t.ensureSnakeCase=function e(t){if(!n(t))return t;if(Array.isArray(t))return t.map(t=>e(t));if(!r(t))return t;try{let n={},i=Object.getOwnPropertyDescriptors(t);for(let s of[...Object.getOwnPropertyNames(t),...Object.getOwnPropertySymbols(t)]){let{value:t}=i[s];if("symbol"==typeof s){if(r(t)){let r=e(t);Object.defineProperty(n,s,{enumerable:!0,configurable:!0,writable:!0,value:r})}else n[s]=t}else{let i=o(s.toString());if(r(t)){let r=e(t);n[i]=r,Object.getOwnPropertySymbols(t).forEach(e=>{Object.defineProperty(r,e,Object.assign(Object.assign({},Object.getOwnPropertyDescriptor(t,e)),{value:t[e]}))})}else Array.isArray(t)?n[i]=t.map(t=>e(t)):n[i]=t}}return n}catch(e){throw Error(`Failed to transform to snake_case: ${e instanceof Error?e.message:"Unknown error"}`)}};let r=e=>"object"==typeof e&&null!==e&&!Array.isArray(e)&&!(e instanceof Date)&&!(e instanceof Map)&&!(e instanceof Set)&&!(e instanceof Error)&&!(e instanceof RegExp),n=e=>null!=e,i=e=>{let t=e.match(/^(_+)/),r=t?t[1]:"",n=e.slice(r.length);return n?r+n.split("_").map((e,t)=>0===t?e.toLowerCase():e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(""):e},o=e=>{let t=e.match(/^(_+)/),r=t?t[1]:"",n=e.slice(r.length);return n?r+n.replace(/([A-Z]+)([A-Z][a-z])/g,"$1_$2").replace(/([a-z\d])([A-Z])/g,"$1_$2").toLowerCase():e}},85872:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.downloadBlob=function(e,t){let r=window.URL.createObjectURL(e),n=document.createElement("a");n.href=r,n.download=t,document.body.appendChild(n),n.click(),document.body.removeChild(n),window.URL.revokeObjectURL(r)}},51740:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ChunksClient=void 0;let n=r(8713);class i{constructor(e){this.client=e}async create(e){return this.client.makeRequest("POST","chunks",{data:{raw_chunks:(0,n.ensureSnakeCase)(e.chunks),runWithOrchestration:e.runWithOrchestration}})}async update(e){return this.client.makeRequest("POST",`chunks/${e.id}`,{data:e})}async retrieve(e){return this.client.makeRequest("GET",`chunks/${e.id}`)}async delete(e){return this.client.makeRequest("DELETE",`chunks/${e.id}`)}async list(e){var t,r;let n={offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t?t:0,limit:null!==(r=null==e?void 0:e.limit)&&void 0!==r?r:100};return(null==e?void 0:e.includeVectors)&&(n.include_vectors=e.includeVectors),(null==e?void 0:e.metadataFilters)&&(n.metadata_filters=e.metadataFilters),this.client.makeRequest("GET","chunks",{params:n})}}t.ChunksClient=i},88559:(e,t,r)=>{"use strict";let n;var i=r(26332),o=r(7460).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),t.CollectionsClient=void 0;let s=r(8713);"undefined"==typeof window&&(n=r(3767));class a{constructor(e){this.client=e}async create(e){return this.client.makeRequest("POST","collections",{data:e})}async list(e){var t,r;let n={offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t?t:0,limit:null!==(r=null==e?void 0:e.limit)&&void 0!==r?r:100};return(null==e?void 0:e.ids)&&e.ids.length>0&&(n.ids=e.ids),(null==e?void 0:e.ownerOnly)&&(n.owner_only=e.ownerOnly),this.client.makeRequest("GET","collections",{params:n})}async retrieve(e){return this.client.makeRequest("GET",`collections/${e.id}`)}async update(e){let t=Object.assign(Object.assign(Object.assign({},e.name&&{name:e.name}),e.description&&{description:e.description}),void 0!==e.generateDescription&&{generate_description:e.generateDescription});return this.client.makeRequest("POST",`collections/${e.id}`,{data:t})}async delete(e){return this.client.makeRequest("DELETE",`collections/${e.id}`)}async listDocuments(e){var t,r;let n={offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t?t:0,limit:null!==(r=null==e?void 0:e.limit)&&void 0!==r?r:100};return this.client.makeRequest("GET",`collections/${e.id}/documents`,{params:n})}async addDocument(e){return this.client.makeRequest("POST",`collections/${e.id}/documents/${e.documentId}`)}async removeDocument(e){return this.client.makeRequest("DELETE",`collections/${e.id}/documents/${e.documentId}`)}async listUsers(e){var t,r;let n={offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t?t:0,limit:null!==(r=null==e?void 0:e.limit)&&void 0!==r?r:100};return this.client.makeRequest("GET",`collections/${e.id}/users`,{params:n})}async addUser(e){return this.client.makeRequest("POST",`collections/${e.id}/users/${e.userId}`)}async removeUser(e){return this.client.makeRequest("DELETE",`collections/${e.id}/users/${e.userId}`)}async extract(e){let t=Object.assign(Object.assign({},e.settings&&{settings:e.settings}),void 0!==e.runWithOrchestration&&{run_with_orchestration:e.runWithOrchestration});return this.client.makeRequest("POST",`collections/${e.collectionId}/extract`,{data:t})}async export(e={}){var t;let r={include_header:null===(t=e.includeHeader)||void 0===t||t};e.columns&&(r.columns=e.columns),e.filters&&(r.filters=e.filters);let s=await this.client.makeRequest("POST","collections/export",{data:r,responseType:"arraybuffer",headers:{Accept:"text/csv"}});if(e.outputPath&&void 0!==i){await n.promises.writeFile(e.outputPath,o.from(s));return}return new Blob([s],{type:"text/csv"})}async exportToFile(e){let t=await this.export(e);t instanceof Blob&&(0,s.downloadBlob)(t,e.filename)}async retrieveByName(e){let t={};return e.ownerId&&(t.owner_id=e.ownerId),this.client.makeRequest("GET",`collections/name/${e.name}`,{params:t})}}t.CollectionsClient=a},29762:(e,t,r)=>{"use strict";let n;var i=r(26332),o=r(7460).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),t.ConversationsClient=void 0;let s=r(8713);"undefined"==typeof window&&(n=r(3767));class a{constructor(e){this.client=e}async create(e){let t=Object.assign({},(null==e?void 0:e.name)&&{name:null==e?void 0:e.name});return this.client.makeRequest("POST","conversations",{data:t})}async list(e){var t,r;let n={offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t?t:0,limit:null!==(r=null==e?void 0:e.limit)&&void 0!==r?r:100};return(null==e?void 0:e.ids)&&e.ids.length>0&&(n.ids=e.ids),this.client.makeRequest("GET","conversations",{params:n})}async retrieve(e){return this.client.makeRequest("GET",`conversations/${e.id}`)}async update(e){let t={name:e.name};return this.client.makeRequest("POST",`conversations/${e.id}`,{data:t})}async delete(e){return this.client.makeRequest("DELETE",`conversations/${e.id}`)}async addMessage(e){let t=Object.assign(Object.assign({content:e.content,role:e.role},e.parentID&&{parentID:e.parentID}),e.metadata&&{metadata:e.metadata});return this.client.makeRequest("POST",`conversations/${e.id}/messages`,{data:t})}async updateMessage(e){let t=Object.assign(Object.assign({},e.content&&{content:e.content}),e.metadata&&{metadata:e.metadata});return this.client.makeRequest("POST",`conversations/${e.id}/messages/${e.messageID}`,{data:t})}async export(e={}){var t;let r={include_header:null===(t=e.includeHeader)||void 0===t||t};e.columns&&(r.columns=e.columns),e.filters&&(r.filters=e.filters);let s=await this.client.makeRequest("POST","conversations/export",{data:r,responseType:"arraybuffer",headers:{Accept:"text/csv"}});if(e.outputPath&&void 0!==i){await n.promises.writeFile(e.outputPath,o.from(s));return}return new Blob([s],{type:"text/csv"})}async exportToFile(e){let t=await this.export(e);t instanceof Blob&&(0,s.downloadBlob)(t,e.filename)}async exportMessages(e={}){var t;let r={include_header:null===(t=e.includeHeader)||void 0===t||t};e.columns&&(r.columns=e.columns),e.filters&&(r.filters=e.filters);let s=await this.client.makeRequest("POST","conversations/export_messages",{data:r,responseType:"arraybuffer",headers:{Accept:"text/csv"}});if(e.outputPath&&void 0!==i){await n.promises.writeFile(e.outputPath,o.from(s));return}return new Blob([s],{type:"text/csv"})}async exportMessagesToFile(e){let t=await this.exportMessages(e);t instanceof Blob&&(0,s.downloadBlob)(t,e.filename)}}t.ConversationsClient=a},584:function(e,t,r){"use strict";let n;var i,o=r(7460).Buffer,s=r(26332),a=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var i=Object.getOwnPropertyDescriptor(t,r);(!i||("get"in i?!t.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,i)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),l=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),u=this&&this.__importStar||(i=function(e){return(i=Object.getOwnPropertyNames||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[t.length]=r);return t})(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r=i(e),n=0;n<r.length;n++)"default"!==r[n]&&a(t,e,r[n]);return l(t,e),t}),c=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.DocumentsClient=void 0;let f=c(r(69811)),d=r(8713),h=r(8713);"undefined"==typeof window&&(n=r(3767));let p=c(r(17144)),m=u(r(11843)),y=u(r(23596)),g=r(92515);class b{constructor(e){this.client=e}async create(e){var t,r,i;let s=[e.file,e.raw_text,e.chunks,e.s3Url].filter(e=>void 0!==e).length;if(0===s)throw Error("Either file, raw_text, chunks, or s3Url must be provided");if(s>1)throw Error("Only one of file, raw_text, chunks, or s3Url may be provided");let a=new f.default,l=null,u=async e=>{let t=(e,t)=>{a.append("file",e,t)};if("string"==typeof e){if("undefined"==typeof window){if((await n.promises.stat(e)).isDirectory())throw Error("Directories are not supported in create()");t(n.createReadStream(e),e.split("/").pop()||"")}else throw console.warn("File path provided in browser environment. This is not supported. Use a File object instead."),Error("File paths are not supported in the browser. Use a File object.")}else if(e instanceof File)t(e,e.name);else if("path"in e&&"name"in e){if("undefined"==typeof window)t(n.createReadStream(e.path),e.name);else throw console.warn("File path object provided in browser environment. This is not supported. Use a File object instead."),Error("File path objects are not supported in the browser. Use a File object.")}};if(e.file)await u(e.file);else if(e.raw_text)a.append("raw_text",e.raw_text);else if(e.chunks)a.append("chunks",JSON.stringify(e.chunks));else if(e.s3Url)try{let t,r,i;if("undefined"==typeof window){t=await p.default.get(e.s3Url,{responseType:"arraybuffer"}),r=o.from(t.data),i=e.s3Url.split("?")[0].split("/").pop()||"s3_file";let s=m.tmpdir();l=y.join(s,`r2r_s3_${Date.now()}_${i}`),await n.promises.writeFile(l,r),a.append("file",n.createReadStream(l),i)}else{if(!(t=await fetch(e.s3Url)).ok)throw Error(`Failed to download file from S3 URL: ${t.status}`);let r=await t.blob();i=e.s3Url.split("?")[0].split("/").pop()||"s3_file";let n=new File([r],i,{type:r.type});a.append("file",n,i)}}catch(e){throw Error(`Failed to download file from S3 URL: ${e.message}`)}e.id&&a.append("id",e.id),e.metadata&&a.append("metadata",JSON.stringify(e.metadata)),e.ingestionConfig&&a.append("ingestion_config",JSON.stringify((0,h.ensureSnakeCase)(e.ingestionConfig))),(null===(t=e.collectionIds)||void 0===t?void 0:t.length)&&a.append("collection_ids",JSON.stringify(e.collectionIds)),void 0!==e.runWithOrchestration&&a.append("run_with_orchestration",String(e.runWithOrchestration)),e.ingestionMode&&a.append("ingestion_mode",e.ingestionMode);try{return this.client.makeRequest("POST","documents",{data:a,headers:null!==(i=null===(r=a.getHeaders)||void 0===r?void 0:r.call(a))&&void 0!==i?i:{"Content-Type":"multipart/form-data"},transformRequest:[(e,t)=>e]})}finally{if(l&&"undefined"==typeof window)try{n.existsSync(l)&&await n.promises.unlink(l)}catch(e){console.error("Error cleaning up temporary file:",e)}}}async appendMetadata(e){return this.client.makeRequest("PATCH",`documents/${e.id}/metadata`,{data:e.metadata})}async replaceMetadata(e){return this.client.makeRequest("PUT",`documents/${e.id}/metadata`,{data:e.metadata})}async retrieve(e){return this.client.makeRequest("GET",`documents/${e.id}`)}async list(e){var t,r,n,i;let o={offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t?t:0,limit:null!==(r=null==e?void 0:e.limit)&&void 0!==r?r:100,include_summary_embeddings:null!==(n=null==e?void 0:e.includeSummaryEmbeddings)&&void 0!==n&&n};return(null===(i=null==e?void 0:e.ids)||void 0===i?void 0:i.length)&&(o.ids=e.ids),(null==e?void 0:e.ownerOnly)&&(o.owner_only=e.ownerOnly),this.client.makeRequest("GET","documents",{params:o})}async download(e){var t;let r=await this.client.makeRequest("GET",`documents/${e.id}/download`,{responseType:"arraybuffer",returnFullResponse:!0});if(!r.data)throw Error("No data received in response");let n=(null===(t=r.headers)||void 0===t?void 0:t["content-type"])||"application/octet-stream";if(r.data instanceof Blob)return r.data;if(r.data instanceof ArrayBuffer||"string"==typeof r.data)return new Blob([r.data],{type:n});try{return new Blob([JSON.stringify(r.data)],{type:n})}catch(e){return console.error("Could not convert response data to Blob:",e),new Blob([],{type:n})}}async export(e={}){var t;let r={include_header:null===(t=e.includeHeader)||void 0===t||t};e.columns&&(r.columns=e.columns),e.filters&&(r.filters=e.filters);let i=await this.client.makeRequest("POST","documents/export",{data:r,responseType:"arraybuffer",headers:{Accept:"text/csv"},returnFullResponse:!1});if(e.outputPath&&void 0!==s&&(null==n?void 0:n.promises)){await n.promises.writeFile(e.outputPath,o.from(i));return}return new Blob([i],{type:"text/csv"})}async exportEntities(e){var t;let r={include_header:null===(t=e.includeHeader)||void 0===t||t};e.columns&&(r.columns=e.columns),e.filters&&(r.filters=e.filters);let i=await this.client.makeRequest("POST",`documents/${e.id}/entities/export`,{data:r,responseType:"arraybuffer",headers:{Accept:"text/csv"},returnFullResponse:!1});if(e.outputPath&&void 0!==s&&(null==n?void 0:n.promises)){await n.promises.writeFile(e.outputPath,o.from(i));return}return new Blob([i],{type:"text/csv"})}async exportEntitiesToFile(e){if("undefined"==typeof window){console.warn("exportEntitiesToFile is intended for browser environments only.");return}let t=await this.exportEntities({id:e.id,columns:e.columns,filters:e.filters,includeHeader:e.includeHeader});t instanceof Blob?(0,d.downloadBlob)(t,e.filename):console.error("Expected a Blob but received void. Did you accidentally provide an outputPath in a browser context?")}async exportRelationships(e){var t;let r={include_header:null===(t=e.includeHeader)||void 0===t||t};e.columns&&(r.columns=e.columns),e.filters&&(r.filters=e.filters);let i=await this.client.makeRequest("POST",`documents/${e.id}/relationships/export`,{data:r,responseType:"arraybuffer",headers:{Accept:"text/csv"},returnFullResponse:!1});if(e.outputPath&&void 0!==s&&(null==n?void 0:n.promises)){await n.promises.writeFile(e.outputPath,o.from(i));return}return new Blob([i],{type:"text/csv"})}async exportRelationshipsToFile(e){if("undefined"==typeof window){console.warn("exportRelationshipsToFile is intended for browser environments only.");return}let t=await this.exportRelationships({id:e.id,columns:e.columns,filters:e.filters,includeHeader:e.includeHeader});t instanceof Blob?(0,d.downloadBlob)(t,e.filename):console.error("Expected a Blob but received void. Did you accidentally provide an outputPath in a browser context?")}async downloadZip(e){var t;let r={};(null===(t=e.documentIds)||void 0===t?void 0:t.length)&&(r.document_ids=e.documentIds),e.startDate&&(r.start_date=e.startDate.toISOString()),e.endDate&&(r.end_date=e.endDate.toISOString());let i=await this.client.makeRequest("GET","documents/download_zip",{params:r,responseType:"arraybuffer",headers:{Accept:"application/zip"},returnFullResponse:!1});if(e.outputPath&&void 0!==s&&(null==n?void 0:n.promises)){await n.promises.writeFile(e.outputPath,o.from(i));return}return new Blob([i],{type:"application/zip"})}async downloadZipToFile(e){if("undefined"==typeof window){console.warn("downloadZipToFile is intended for browser environments only.");return}let t=await this.downloadZip({documentIds:e.documentIds,startDate:e.startDate,endDate:e.endDate});t instanceof Blob?(0,d.downloadBlob)(t,e.filename):console.error("Expected a Blob but received void. Did you accidentally provide an outputPath in a browser context?")}async exportToFile(e){if("undefined"==typeof window){console.warn("exportToFile is intended for browser environments only.");return}let t=await this.export({columns:e.columns,filters:e.filters,includeHeader:e.includeHeader});t instanceof Blob?(0,d.downloadBlob)(t,e.filename):console.error("Expected a Blob but received void. Did you accidentally provide an outputPath in a browser context?")}async delete(e){return this.client.makeRequest("DELETE",`documents/${e.id}`)}async listChunks(e){var t,r,n;let i={include_vectors:null!==(t=e.includeVectors)&&void 0!==t&&t,offset:null!==(r=e.offset)&&void 0!==r?r:0,limit:null!==(n=e.limit)&&void 0!==n?n:100};return this.client.makeRequest("GET",`documents/${e.id}/chunks`,{params:i})}async listCollections(e){var t,r;let n={offset:null!==(t=e.offset)&&void 0!==t?t:0,limit:null!==(r=e.limit)&&void 0!==r?r:100};return this.client.makeRequest("GET",`documents/${e.id}/collections`,{params:n})}async deleteByFilter(e){return this.client.makeRequest("DELETE","documents/by-filter",{data:e.filters})}async extract(e){let t={};return e.settings&&(t.settings=e.settings),void 0!==e.runWithOrchestration&&(t.run_with_orchestration=e.runWithOrchestration),this.client.makeRequest("POST",`documents/${e.id}/extract`,{data:t})}async listEntities(e){var t,r,n;let i={offset:null!==(t=e.offset)&&void 0!==t?t:0,limit:null!==(r=e.limit)&&void 0!==r?r:100,include_embeddings:null!==(n=e.includeEmbeddings)&&void 0!==n&&n};return this.client.makeRequest("GET",`documents/${e.id}/entities`,{params:i})}async listRelationships(e){var t,r,n,i;let o={offset:null!==(t=e.offset)&&void 0!==t?t:0,limit:null!==(r=e.limit)&&void 0!==r?r:100};return(null===(n=e.entityNames)||void 0===n?void 0:n.length)&&(o.entity_names=e.entityNames),(null===(i=e.relationshipTypes)||void 0===i?void 0:i.length)&&(o.relationship_types=e.relationshipTypes),this.client.makeRequest("GET",`documents/${e.id}/relationships`,{params:o})}async deduplicate(e){let t={};return e.settings&&(t.settings=e.settings),void 0!==e.runWithOrchestration&&(t.run_with_orchestration=e.runWithOrchestration),this.client.makeRequest("POST",`documents/${e.id}/deduplicate`,{data:t})}async search(e){var t,r;let n={query:e.query,search_mode:null!==(t=e.searchMode)&&void 0!==t?t:"custom",search_settings:null!==(r=e.searchSettings)&&void 0!==r?r:{}};return this.client.makeRequest("POST","documents/search",{data:n})}async createSample(e){let t;if("undefined"!=typeof window||!n||!p.default||!m||!y)throw Error("createSample method requires a Node.js environment with 'fs', 'axios', 'os', 'path', 'uuid' modules.");let r="https://raw.githubusercontent.com/SciPhi-AI/R2R/main/py/core/examples/data/DeepSeek_R1.pdf",i=new URL(r).pathname.split("/").pop()||"sample.pdf",s=m.tmpdir(),a=y.join(s,`r2r_sample_${Date.now()}_${i}`);try{let s=await p.default.get(r,{responseType:"arraybuffer"});await n.promises.writeFile(a,o.from(s.data));let l=(0,g.v5)(r,"6ba7b810-9dad-11d1-80b4-00c04fd430c8");t=await this.create({file:a,metadata:{title:i},id:l,ingestionMode:null==e?void 0:e.ingestionMode})}catch(e){throw console.error("Error during createSample:",e),e}finally{try{await n.promises.unlink(a)}catch(e){console.error(`Failed to delete temporary file ${a}:`,e)}}return t}}t.DocumentsClient=b},32295:(e,t,r)=>{"use strict";let n;var i=r(26332),o=r(7460).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),t.GraphsClient=void 0;let s=r(8713);"undefined"==typeof window&&(n=r(3767));class a{constructor(e){this.client=e}async list(e){var t,r;let n={offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t?t:0,limit:null!==(r=null==e?void 0:e.limit)&&void 0!==r?r:100};return(null==e?void 0:e.collectionIds)&&e.collectionIds.length>0&&(n.collectionIds=e.collectionIds),this.client.makeRequest("GET","graphs",{params:n})}async retrieve(e){return this.client.makeRequest("GET",`graphs/${e.collectionId}`)}async reset(e){return this.client.makeRequest("POST",`graphs/${e.collectionId}/reset`)}async update(e){let t=Object.assign(Object.assign({},e.name&&{name:e.name}),e.description&&{description:e.description});return this.client.makeRequest("POST",`graphs/${e.collectionId}`,{data:t})}async createEntity(e){let t=Object.assign(Object.assign(Object.assign({name:e.name},e.description&&{description:e.description}),e.category&&{category:e.category}),e.metadata&&{metadata:e.metadata});return this.client.makeRequest("POST",`graphs/${e.collectionId}/entities`,{data:t})}async listEntities(e){var t,r;let n={offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t?t:0,limit:null!==(r=null==e?void 0:e.limit)&&void 0!==r?r:100};return this.client.makeRequest("GET",`graphs/${e.collectionId}/entities`,{params:n})}async getEntity(e){return this.client.makeRequest("GET",`graphs/${e.collectionId}/entities/${e.entityId}`)}async updateEntity(e){let t=Object.assign(Object.assign(Object.assign(Object.assign({},e.name&&{name:e.name}),e.description&&{description:e.description}),e.category&&{category:e.category}),e.metadata&&{metadata:e.metadata});return this.client.makeRequest("POST",`graphs/${e.collectionId}/entities/${e.entityId}`,{data:t})}async removeEntity(e){return this.client.makeRequest("DELETE",`graphs/${e.collectionId}/entities/${e.entityId}`)}async createRelationship(e){let t=Object.assign(Object.assign({subject:e.subject,subject_id:e.subjectId,predicate:e.predicate,object:e.object,object_id:e.objectId,description:e.description},e.weight&&{weight:e.weight}),e.metadata&&{metadata:e.metadata});return this.client.makeRequest("POST",`graphs/${e.collectionId}/relationships`,{data:t})}async listRelationships(e){var t,r;let n={offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t?t:0,limit:null!==(r=null==e?void 0:e.limit)&&void 0!==r?r:100};return this.client.makeRequest("GET",`graphs/${e.collectionId}/relationships`,{params:n})}async getRelationship(e){return this.client.makeRequest("GET",`graphs/${e.collectionId}/relationships/${e.relationshipId}`)}async updateRelationship(e){let t=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},e.subject&&{subject:e.subject}),e.subjectId&&{subject_id:e.subjectId}),e.predicate&&{predicate:e.predicate}),e.object&&{object:e.object}),e.objectId&&{object_id:e.objectId}),e.description&&{description:e.description}),e.weight&&{weight:e.weight}),e.metadata&&{metadata:e.metadata});return this.client.makeRequest("POST",`graphs/${e.collectionId}/relationships/${e.relationshipId}`,{data:t})}async removeRelationship(e){return this.client.makeRequest("DELETE",`graphs/${e.collectionId}/relationships/${e.relationshipId}`)}async exportEntities(e){var t;let r={include_header:null===(t=e.includeHeader)||void 0===t||t};e.columns&&(r.columns=e.columns),e.filters&&(r.filters=e.filters);let s=await this.client.makeRequest("POST",`graphs/${e.collectionId}/entities/export`,{data:r,responseType:"arraybuffer",headers:{Accept:"text/csv"}});if(e.outputPath&&void 0!==i){await n.promises.writeFile(e.outputPath,o.from(s));return}return new Blob([s],{type:"text/csv"})}async exportEntitiesToFile(e){let t=await this.exportEntities(e);t instanceof Blob&&(0,s.downloadBlob)(t,e.filename)}async exportRelationships(e){var t;let r={include_header:null===(t=e.includeHeader)||void 0===t||t};e.columns&&(r.columns=e.columns),e.filters&&(r.filters=e.filters);let s=await this.client.makeRequest("POST",`graphs/${e.collectionId}/relationships/export`,{data:r,responseType:"arraybuffer",headers:{Accept:"text/csv"}});if(e.outputPath&&void 0!==i){await n.promises.writeFile(e.outputPath,o.from(s));return}return new Blob([s],{type:"text/csv"})}async exportRelationshipsToFile(e){let t=await this.exportRelationships(e);t instanceof Blob&&(0,s.downloadBlob)(t,e.filename)}async exportCommunities(e){var t;let r={include_header:null===(t=e.includeHeader)||void 0===t||t};e.columns&&(r.columns=e.columns),e.filters&&(r.filters=e.filters);let s=await this.client.makeRequest("POST",`graphs/${e.collectionId}/communities/export`,{data:r,responseType:"arraybuffer",headers:{Accept:"text/csv"}});if(e.outputPath&&void 0!==i){await n.promises.writeFile(e.outputPath,o.from(s));return}return new Blob([s],{type:"text/csv"})}async exportCommunitiesToFile(e){let t=await this.exportRelationships(e);t instanceof Blob&&(0,s.downloadBlob)(t,e.filename)}async createCommunity(e){let t=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({name:e.name},e.summary&&{summary:e.summary}),e.findings&&{findings:e.findings}),e.rating&&{rating:e.rating}),e.ratingExplanation&&{rating_explanation:e.ratingExplanation}),e.attributes&&{attributes:e.attributes});return this.client.makeRequest("POST",`graphs/${e.collectionId}/communities`,{data:t})}async listCommunities(e){var t,r;let n={offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t?t:0,limit:null!==(r=null==e?void 0:e.limit)&&void 0!==r?r:100};return this.client.makeRequest("GET",`graphs/${e.collectionId}/communities`,{params:n})}async getCommunity(e){return this.client.makeRequest("GET",`graphs/${e.collectionId}/communities/${e.communityId}`)}async updateCommunity(e){let t=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},e.name&&{name:e.name}),e.summary&&{summary:e.summary}),e.findings&&{findings:e.findings}),e.rating&&{rating:e.rating}),e.ratingExplanation&&{rating_explanation:e.ratingExplanation}),e.attributes&&{attributes:e.attributes});return this.client.makeRequest("POST",`graphs/${e.collectionId}/communities/${e.communityId}`,{data:t})}async deleteCommunity(e){return this.client.makeRequest("DELETE",`graphs/${e.collectionId}/communities/${e.communityId}`)}async pull(e){return this.client.makeRequest("POST",`graphs/${e.collectionId}/pull`)}async removeDocument(e){return this.client.makeRequest("DELETE",`graphs/${e.collectionId}/documents/${e.documentId}`)}async buildCommunities(e){return this.client.makeRequest("POST",`graphs/${e.collectionId}/communities/build`)}}t.GraphsClient=a},40191:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.IndiciesClient=void 0;class r{constructor(e){this.client=e}async create(e){let t=Object.assign({config:e.config},void 0!==e.runWithOrchestration&&{run_with_orchestration:e.runWithOrchestration});return this.client.makeRequest("POST","indices",{data:t})}async list(e){var t,r;let n={offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t?t:0,limit:null!==(r=null==e?void 0:e.limit)&&void 0!==r?r:100};return(null==e?void 0:e.filters)&&(n.filters=e.filters),this.client.makeRequest("GET","indices",{params:n})}async retrieve(e){return this.client.makeRequest("GET",`indices/${e.indexName}/${e.tableName}`)}async delete(e){return this.client.makeRequest("DELETE",`indices/${e.indexName}/${e.tableName}`)}}t.IndiciesClient=r},60621:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.PromptsClient=void 0;class r{constructor(e){this.client=e}async create(e){return this.client.makeRequest("POST","prompts",{data:e})}async list(){return this.client.makeRequest("GET","prompts")}async retrieve(e){let t=Object.assign(Object.assign({},e.inputs&&{inputs:e.inputs}),e.promptOverride&&{promptOverride:e.promptOverride});return this.client.makeRequest("POST",`prompts/${e.name}`,{params:t})}async update(e){let t={name:e.name};return e.template&&(t.template=e.template),e.inputTypes&&(t.inputTypes=e.inputTypes),this.client.makeRequest("PUT",`prompts/${e.name}`,{data:t})}async delete(e){return this.client.makeRequest("DELETE",`prompts/${e.name}`)}}t.PromptsClient=r},32466:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.RetrievalClient=void 0;let n=r(8713);class i{constructor(e){this.client=e}async search(e){let t=Object.assign(Object.assign({query:e.query},e.searchSettings&&{search_settings:(0,n.ensureSnakeCase)(e.searchSettings)}),e.searchMode&&{search_mode:e.searchMode});return await this.client.makeRequest("POST","retrieval/search",{data:t})}async rag(e){let t=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({query:e.query},e.searchMode&&{search_mode:e.searchMode}),e.searchSettings&&{search_settings:(0,n.ensureSnakeCase)(e.searchSettings)}),e.ragGenerationConfig&&{rag_generation_config:(0,n.ensureSnakeCase)(e.ragGenerationConfig)}),e.taskPrompt&&{task_prompt:e.taskPrompt}),void 0!==e.includeTitleIfAvailable&&{include_title_if_available:e.includeTitleIfAvailable}),e.includeWebSearch&&{include_web_search:e.includeWebSearch});return e.ragGenerationConfig&&e.ragGenerationConfig.stream?this.streamRag(t):await this.client.makeRequest("POST","retrieval/rag",{data:t})}async streamRag(e){return this.client.makeRequest("POST","retrieval/rag",{data:e,headers:{"Content-Type":"application/json"},responseType:"stream"})}async agent(e){let t=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},e.message&&{message:e.message}),e.messages&&{messages:e.messages}),e.searchMode&&{search_mode:e.searchMode}),e.ragGenerationConfig&&{rag_generation_config:(0,n.ensureSnakeCase)(e.ragGenerationConfig)}),e.researchGenerationConfig&&{research_generation_config:(0,n.ensureSnakeCase)(e.researchGenerationConfig)}),e.searchSettings&&{search_settings:(0,n.ensureSnakeCase)(e.searchSettings)}),e.taskPrompt&&{task_prompt:e.taskPrompt}),typeof e.includeTitleIfAvailable&&{include_title_if_available:e.includeTitleIfAvailable}),e.conversationId&&{conversation_id:e.conversationId}),e.maxToolContextLength&&{max_tool_context_length:e.maxToolContextLength}),e.tools&&{tools:e.tools}),e.ragTools&&{rag_tools:e.ragTools}),e.researchTools&&{research_tools:e.researchTools}),(e.useSystemContext,{use_system_context:e.useSystemContext})),e.mode&&{mode:e.mode}),e.needsInitialConversationName&&{needsInitialConversationName:e.needsInitialConversationName}),r=!1;return(e.ragGenerationConfig&&e.ragGenerationConfig.stream?r=!0:e.researchGenerationConfig&&"research"===e.mode&&e.researchGenerationConfig.stream&&(r=!0),r)?this.streamAgent(t):await this.client.makeRequest("POST","retrieval/agent",{data:t})}async streamAgent(e){return this.client.makeRequest("POST","retrieval/agent",{data:e,headers:{"Content-Type":"application/json"},responseType:"stream"})}async completion(e){let t=Object.assign({messages:e.messages},e.generationConfig&&{generation_config:e.generationConfig});return e.generationConfig&&e.generationConfig.stream?this.streamCompletion(t):await this.client.makeRequest("POST","retrieval/completion",{data:t})}async streamCompletion(e){return this.client.makeRequest("POST","retrieval/completion",{data:e,headers:{"Content-Type":"application/json"},responseType:"stream"})}async embedding(e){return await this.client.makeRequest("POST","retrieval/embedding",{data:e.text})}}t.RetrievalClient=i},85483:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.SystemClient=void 0;class r{constructor(e){this.client=e}async health(){return await this.client.makeRequest("GET","health")}async settings(){return await this.client.makeRequest("GET","system/settings")}async status(){return await this.client.makeRequest("GET","system/status")}}t.SystemClient=r},13550:(e,t,r)=>{"use strict";let n;var i=r(26332),o=r(7460).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),t.UsersClient=void 0;let s=r(8713);"undefined"==typeof window&&(n=r(3767));class a{constructor(e){this.client=e}async create(e){let t=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},e.email&&{email:e.email}),e.password&&{password:e.password}),e.name&&{name:e.name}),e.bio&&{bio:e.bio}),e.profilePicture&&{profile_picture:e.profilePicture}),void 0!==e.isVerified&&{is_verified:e.isVerified});return this.client.makeRequest("POST","users",{data:t})}async sendVerificationEmail(e){return this.client.makeRequest("POST","users/send-verification-email",{data:e.email,headers:{"Content-Type":"text/plain"}})}async delete(e){return this.client.makeRequest("DELETE",`users/${e.id}`,{data:{password:e.password}})}async verifyEmail(e){return this.client.makeRequest("POST","users/verify-email",{data:e})}async login(e){let t=await this.client.makeRequest("POST","users/login",{data:{username:e.email,password:e.password},headers:{"Content-Type":"application/x-www-form-urlencoded"}});return(null==t?void 0:t.results)&&this.client.setTokens(t.results.accessToken.token,t.results.refreshToken.token),t}async loginWithToken(e){this.client.setTokens(e.accessToken,null);try{return await this.client.makeRequest("GET","users/me"),{results:{access_token:{token:e.accessToken,token_type:"access_token"}}}}catch(e){throw this.client.setTokens(null,null),Error("Invalid token provided")}}async logout(){let e=await this.client.makeRequest("POST","users/logout");return this.client.setTokens(null,null),e}async refreshAccessToken(){let e=this.client.getRefreshToken();if(!e)throw Error("No refresh token available. Please login again.");let t=await this.client.makeRequest("POST","users/refresh-token",{data:e,headers:{"Content-Type":"application/x-www-form-urlencoded"}});if(null==t?void 0:t.results)this.client.setTokens(t.results.accessToken.token,t.results.refreshToken.token);else throw Error("Invalid response structure");return t}async changePassword(e){return this.client.makeRequest("POST","users/change-password",{data:e})}async requestPasswordReset(e){return this.client.makeRequest("POST","users/request-password-reset",{data:e,headers:{"Content-Type":"text/plain"}})}async resetPassword(e){return this.client.makeRequest("POST","users/reset-password",{data:e})}async list(e){var t,r;let n={offset:null!==(t=null==e?void 0:e.offset)&&void 0!==t?t:0,limit:null!==(r=null==e?void 0:e.limit)&&void 0!==r?r:100};return(null==e?void 0:e.ids)&&(n.ids=e.ids),this.client.makeRequest("GET","users",{params:n})}async retrieve(e){return this.client.makeRequest("GET",`users/${e.id}`)}async me(){return this.client.makeRequest("GET","users/me")}async update(e){let t=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},e.email&&{email:e.email}),void 0!==e.isSuperuser&&{is_superuser:e.isSuperuser}),e.name&&{name:e.name}),e.bio&&{bio:e.bio}),e.profilePicture&&{profile_picture:e.profilePicture}),e.metadata&&{metadata:e.metadata});return this.client.makeRequest("POST",`users/${e.id}`,{data:t})}async listCollections(e){var t,r;let n={offset:null!==(t=e.offset)&&void 0!==t?t:0,limit:null!==(r=e.limit)&&void 0!==r?r:100};return this.client.makeRequest("GET",`users/${e.id}/collections`,{params:n})}async addToCollection(e){return this.client.makeRequest("POST",`users/${e.id}/collections/${e.collectionId}`)}async removeFromCollection(e){return this.client.makeRequest("DELETE",`users/${e.id}/collections/${e.collectionId}`)}async export(e={}){var t;let r={include_header:null===(t=e.includeHeader)||void 0===t||t};e.columns&&(r.columns=e.columns),e.filters&&(r.filters=e.filters);let s=await this.client.makeRequest("POST","users/export",{data:r,responseType:"arraybuffer",headers:{Accept:"text/csv"}});if(e.outputPath&&void 0!==i){await n.promises.writeFile(e.outputPath,o.from(s));return}return new Blob([s],{type:"text/csv"})}async exportToFile(e){let t=await this.export(e);t instanceof Blob&&(0,s.downloadBlob)(t,e.filename)}async createApiKey(e){let t=Object.assign(Object.assign({},e.name&&{name:e.name}),e.description&&{description:e.description});return this.client.makeRequest("POST",`users/${e.id}/api-keys`,{data:t})}async listApiKeys(e){return this.client.makeRequest("GET",`users/${e.id}/api-keys`)}async deleteApiKey(e){return this.client.makeRequest("DELETE",`users/${e.id}/api-keys/${e.keyId}`)}async getLimits(e){return this.client.makeRequest("GET",`users/${e.id}/limits`)}async oauthGoogleAuthorize(){return this.client.makeRequest("GET","users/oauth/google/authorize")}async oauthGithubAuthorize(){return this.client.makeRequest("GET","users/oauth/github/authorize")}async oauthGoogleCallback(e){return this.client.makeRequest("GET","users/oauth/google/callback",{params:{code:e.code,state:e.state}})}async oauthGithubCallback(e){return this.client.makeRequest("GET","users/oauth/github/callback",{params:{code:e.code,state:e.state}})}}t.UsersClient=a},92515:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"MAX",{enumerable:!0,get:function(){return n.default}}),Object.defineProperty(t,"NIL",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(t,"parse",{enumerable:!0,get:function(){return o.default}}),Object.defineProperty(t,"stringify",{enumerable:!0,get:function(){return s.default}}),Object.defineProperty(t,"v1",{enumerable:!0,get:function(){return a.default}}),Object.defineProperty(t,"v1ToV6",{enumerable:!0,get:function(){return l.default}}),Object.defineProperty(t,"v3",{enumerable:!0,get:function(){return u.default}}),Object.defineProperty(t,"v4",{enumerable:!0,get:function(){return c.default}}),Object.defineProperty(t,"v5",{enumerable:!0,get:function(){return f.default}}),Object.defineProperty(t,"v6",{enumerable:!0,get:function(){return d.default}}),Object.defineProperty(t,"v6ToV1",{enumerable:!0,get:function(){return h.default}}),Object.defineProperty(t,"v7",{enumerable:!0,get:function(){return p.default}}),Object.defineProperty(t,"validate",{enumerable:!0,get:function(){return m.default}}),Object.defineProperty(t,"version",{enumerable:!0,get:function(){return y.default}});var n=g(r(69937)),i=g(r(10900)),o=g(r(86192)),s=g(r(26022)),a=g(r(98444)),l=g(r(13589)),u=g(r(79482)),c=g(r(23371)),f=g(r(55464)),d=g(r(94625)),h=g(r(10793)),p=g(r(49574)),m=g(r(96015)),y=g(r(87055));function g(e){return e&&e.__esModule?e:{default:e}}},69937:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default="ffffffff-ffff-ffff-ffff-ffffffffffff"},95711:(e,t)=>{"use strict";function r(e){return(e+64>>>9<<4)+14+1}function n(e,t){var r=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(r>>16)<<16|65535&r}function i(e,t,r,i,o,s){var a;return n((a=n(n(t,e),n(i,s)))<<o|a>>>32-o,r)}function o(e,t,r,n,o,s,a){return i(t&r|~t&n,e,t,o,s,a)}function s(e,t,r,n,o,s,a){return i(t&n|r&~n,e,t,o,s,a)}function a(e,t,r,n,o,s,a){return i(t^r^n,e,t,o,s,a)}function l(e,t,r,n,o,s,a){return i(r^(t|~n),e,t,o,s,a)}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default=function(e){if("string"==typeof e){var t=unescape(encodeURIComponent(e));e=new Uint8Array(t.length);for(var i=0;i<t.length;++i)e[i]=t.charCodeAt(i)}return function(e){for(var t=[],r=32*e.length,n="0123456789abcdef",i=0;i<r;i+=8){var o=e[i>>5]>>>i%32&255,s=parseInt(n.charAt(o>>>4&15)+n.charAt(15&o),16);t.push(s)}return t}(function(e,t){e[t>>5]|=128<<t%32,e[r(t)-1]=t;for(var i=0x67452301,u=-0x10325477,c=-0x67452302,f=0x10325476,d=0;d<e.length;d+=16){var h=i,p=u,m=c,y=f;i=o(i,u,c,f,e[d],7,-0x28955b88),f=o(f,i,u,c,e[d+1],12,-0x173848aa),c=o(c,f,i,u,e[d+2],17,0x242070db),u=o(u,c,f,i,e[d+3],22,-0x3e423112),i=o(i,u,c,f,e[d+4],7,-0xa83f051),f=o(f,i,u,c,e[d+5],12,0x4787c62a),c=o(c,f,i,u,e[d+6],17,-0x57cfb9ed),u=o(u,c,f,i,e[d+7],22,-0x2b96aff),i=o(i,u,c,f,e[d+8],7,0x698098d8),f=o(f,i,u,c,e[d+9],12,-0x74bb0851),c=o(c,f,i,u,e[d+10],17,-42063),u=o(u,c,f,i,e[d+11],22,-0x76a32842),i=o(i,u,c,f,e[d+12],7,0x6b901122),f=o(f,i,u,c,e[d+13],12,-0x2678e6d),c=o(c,f,i,u,e[d+14],17,-0x5986bc72),u=o(u,c,f,i,e[d+15],22,0x49b40821),i=s(i,u,c,f,e[d+1],5,-0x9e1da9e),f=s(f,i,u,c,e[d+6],9,-0x3fbf4cc0),c=s(c,f,i,u,e[d+11],14,0x265e5a51),u=s(u,c,f,i,e[d],20,-0x16493856),i=s(i,u,c,f,e[d+5],5,-0x29d0efa3),f=s(f,i,u,c,e[d+10],9,0x2441453),c=s(c,f,i,u,e[d+15],14,-0x275e197f),u=s(u,c,f,i,e[d+4],20,-0x182c0438),i=s(i,u,c,f,e[d+9],5,0x21e1cde6),f=s(f,i,u,c,e[d+14],9,-0x3cc8f82a),c=s(c,f,i,u,e[d+3],14,-0xb2af279),u=s(u,c,f,i,e[d+8],20,0x455a14ed),i=s(i,u,c,f,e[d+13],5,-0x561c16fb),f=s(f,i,u,c,e[d+2],9,-0x3105c08),c=s(c,f,i,u,e[d+7],14,0x676f02d9),u=s(u,c,f,i,e[d+12],20,-0x72d5b376),i=a(i,u,c,f,e[d+5],4,-378558),f=a(f,i,u,c,e[d+8],11,-0x788e097f),c=a(c,f,i,u,e[d+11],16,0x6d9d6122),u=a(u,c,f,i,e[d+14],23,-0x21ac7f4),i=a(i,u,c,f,e[d+1],4,-0x5b4115bc),f=a(f,i,u,c,e[d+4],11,0x4bdecfa9),c=a(c,f,i,u,e[d+7],16,-0x944b4a0),u=a(u,c,f,i,e[d+10],23,-0x41404390),i=a(i,u,c,f,e[d+13],4,0x289b7ec6),f=a(f,i,u,c,e[d],11,-0x155ed806),c=a(c,f,i,u,e[d+3],16,-0x2b10cf7b),u=a(u,c,f,i,e[d+6],23,0x4881d05),i=a(i,u,c,f,e[d+9],4,-0x262b2fc7),f=a(f,i,u,c,e[d+12],11,-0x1924661b),c=a(c,f,i,u,e[d+15],16,0x1fa27cf8),u=a(u,c,f,i,e[d+2],23,-0x3b53a99b),i=l(i,u,c,f,e[d],6,-0xbd6ddbc),f=l(f,i,u,c,e[d+7],10,0x432aff97),c=l(c,f,i,u,e[d+14],15,-0x546bdc59),u=l(u,c,f,i,e[d+5],21,-0x36c5fc7),i=l(i,u,c,f,e[d+12],6,0x655b59c3),f=l(f,i,u,c,e[d+3],10,-0x70f3336e),c=l(c,f,i,u,e[d+10],15,-1051523),u=l(u,c,f,i,e[d+1],21,-0x7a7ba22f),i=l(i,u,c,f,e[d+8],6,0x6fa87e4f),f=l(f,i,u,c,e[d+15],10,-0x1d31920),c=l(c,f,i,u,e[d+6],15,-0x5cfebcec),u=l(u,c,f,i,e[d+13],21,0x4e0811a1),i=l(i,u,c,f,e[d+4],6,-0x8ac817e),f=l(f,i,u,c,e[d+11],10,-0x42c50dcb),c=l(c,f,i,u,e[d+2],15,0x2ad7d2bb),u=l(u,c,f,i,e[d+9],21,-0x14792c6f),i=n(i,h),u=n(u,p),c=n(c,m),f=n(f,y)}return[i,u,c,f]}(function(e){if(0===e.length)return[];for(var t=8*e.length,n=new Uint32Array(r(t)),i=0;i<t;i+=8)n[i>>5]|=(255&e[i/8])<<i%32;return n}(e),8*e.length))}},99358:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r="undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto);t.default={randomUUID:r}},10900:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default="00000000-0000-0000-0000-000000000000"},86192:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(r(96015));t.default=function(e){if(!(0,n.default)(e))throw TypeError("Invalid UUID");var t,r=new Uint8Array(16);return r[0]=(t=parseInt(e.slice(0,8),16))>>>24,r[1]=t>>>16&255,r[2]=t>>>8&255,r[3]=255&t,r[4]=(t=parseInt(e.slice(9,13),16))>>>8,r[5]=255&t,r[6]=(t=parseInt(e.slice(14,18),16))>>>8,r[7]=255&t,r[8]=(t=parseInt(e.slice(19,23),16))>>>8,r[9]=255&t,r[10]=(t=parseInt(e.slice(24,36),16))/0x10000000000&255,r[11]=t/0x100000000&255,r[12]=t>>>24&255,r[13]=t>>>16&255,r[14]=t>>>8&255,r[15]=255&t,r}},47748:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-8][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000|ffffffff-ffff-ffff-ffff-ffffffffffff)$/i},35438:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){if(!r&&!(r="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return r(n)};var r,n=new Uint8Array(16)},54536:(e,t)=>{"use strict";function r(e,t){return e<<t|e>>>32-t}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.default=function(e){var t=[0x5a827999,0x6ed9eba1,0x8f1bbcdc,0xca62c1d6],n=[0x67452301,0xefcdab89,0x98badcfe,0x10325476,0xc3d2e1f0];if("string"==typeof e){var i=unescape(encodeURIComponent(e));e=[];for(var o=0;o<i.length;++o)e.push(i.charCodeAt(o))}else Array.isArray(e)||(e=Array.prototype.slice.call(e));e.push(128);for(var s=Math.ceil((e.length/4+2)/16),a=Array(s),l=0;l<s;++l){for(var u=new Uint32Array(16),c=0;c<16;++c)u[c]=e[64*l+4*c]<<24|e[64*l+4*c+1]<<16|e[64*l+4*c+2]<<8|e[64*l+4*c+3];a[l]=u}a[s-1][14]=(e.length-1)*8/0x100000000,a[s-1][14]=Math.floor(a[s-1][14]),a[s-1][15]=(e.length-1)*8&0xffffffff;for(var f=0;f<s;++f){for(var d=new Uint32Array(80),h=0;h<16;++h)d[h]=a[f][h];for(var p=16;p<80;++p)d[p]=r(d[p-3]^d[p-8]^d[p-14]^d[p-16],1);for(var m=n[0],y=n[1],g=n[2],b=n[3],v=n[4],w=0;w<80;++w){var O=Math.floor(w/20),E=r(m,5)+function(e,t,r,n){switch(e){case 0:return t&r^~t&n;case 1:case 3:return t^r^n;case 2:return t&r^t&n^r&n}}(O,y,g,b)+v+t[O]+d[w]>>>0;v=b,b=g,g=r(y,30)>>>0,y=m,m=E}n[0]=n[0]+m>>>0,n[1]=n[1]+y>>>0,n[2]=n[2]+g>>>0,n[3]=n[3]+b>>>0,n[4]=n[4]+v>>>0}return[n[0]>>24&255,n[0]>>16&255,n[0]>>8&255,255&n[0],n[1]>>24&255,n[1]>>16&255,n[1]>>8&255,255&n[1],n[2]>>24&255,n[2]>>16&255,n[2]>>8&255,255&n[2],n[3]>>24&255,n[3]>>16&255,n[3]>>8&255,255&n[3],n[4]>>24&255,n[4]>>16&255,n[4]>>8&255,255&n[4]]}},26022:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,t.unsafeStringify=s;for(var n=function(e){return e&&e.__esModule?e:{default:e}}(r(96015)),i=[],o=0;o<256;++o)i.push((o+256).toString(16).slice(1));function s(e,t=0){return(i[e[t+0]]+i[e[t+1]]+i[e[t+2]]+i[e[t+3]]+"-"+i[e[t+4]]+i[e[t+5]]+"-"+i[e[t+6]]+i[e[t+7]]+"-"+i[e[t+8]]+i[e[t+9]]+"-"+i[e[t+10]]+i[e[t+11]]+i[e[t+12]]+i[e[t+13]]+i[e[t+14]]+i[e[t+15]]).toLowerCase()}t.default=function(e,t=0){var r=s(e,t);if(!(0,n.default)(r))throw TypeError("Stringified UUID is invalid");return r}},98444:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,i,o=function(e){return e&&e.__esModule?e:{default:e}}(r(35438)),s=r(26022),a=0,l=0;t.default=function(e,t,r){var u=t&&r||0,c=t||Array(16),f=(e=e||{}).node,d=e.clockseq;if(e._v6||(f||(f=n),null!=d||(d=i)),null==f||null==d){var h=e.random||(e.rng||o.default)();null!=f||(f=[h[0],h[1],h[2],h[3],h[4],h[5]],n||e._v6||(f[0]|=1,n=f)),null!=d||(d=(h[6]<<8|h[7])&16383,void 0!==i||e._v6||(i=d))}var p=void 0!==e.msecs?e.msecs:Date.now(),m=void 0!==e.nsecs?e.nsecs:l+1,y=p-a+(m-l)/1e4;if(y<0&&void 0===e.clockseq&&(d=d+1&16383),(y<0||p>a)&&void 0===e.nsecs&&(m=0),m>=1e4)throw Error("uuid.v1(): Can't create more than 10M uuids/sec");a=p,l=m,i=d;var g=((0xfffffff&(p+=122192928e5))*1e4+m)%0x100000000;c[u++]=g>>>24&255,c[u++]=g>>>16&255,c[u++]=g>>>8&255,c[u++]=255&g;var b=p/0x100000000*1e4&0xfffffff;c[u++]=b>>>8&255,c[u++]=255&b,c[u++]=b>>>24&15|16,c[u++]=b>>>16&255,c[u++]=d>>>8|128,c[u++]=255&d;for(var v=0;v<6;++v)c[u+v]=f[v];return t||(0,s.unsafeStringify)(c)}},13589:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t=function(e,t=!1){return Uint8Array.of((15&e[6])<<4|e[7]>>4&15,(15&e[7])<<4|(240&e[4])>>4,(15&e[4])<<4|(240&e[5])>>4,(15&e[5])<<4|(240&e[0])>>4,(15&e[0])<<4|(240&e[1])>>4,(15&e[1])<<4|(240&e[2])>>4,96|15&e[2],e[3],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])}("string"==typeof e?(0,n.default)(e):e);return"string"==typeof e?(0,i.unsafeStringify)(t):t};var n=function(e){return e&&e.__esModule?e:{default:e}}(r(86192)),i=r(26022)},79482:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(r(80249)),i=o(r(95711));function o(e){return e&&e.__esModule?e:{default:e}}var s=(0,n.default)("v3",48,i.default);t.default=s},80249:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.URL=t.DNS=void 0,t.default=function(e,t,r){function a(e,o,s,a){if("string"==typeof e&&(e=function(e){e=unescape(encodeURIComponent(e));for(var t=[],r=0;r<e.length;++r)t.push(e.charCodeAt(r));return t}(e)),"string"==typeof o&&(o=(0,i.default)(o)),(null===(l=o)||void 0===l?void 0:l.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");var l,u=new Uint8Array(16+e.length);if(u.set(o),u.set(e,o.length),(u=r(u))[6]=15&u[6]|t,u[8]=63&u[8]|128,s){a=a||0;for(var c=0;c<16;++c)s[a+c]=u[c];return s}return(0,n.unsafeStringify)(u)}try{a.name=e}catch(e){}return a.DNS=o,a.URL=s,a};var n=r(26022),i=function(e){return e&&e.__esModule?e:{default:e}}(r(86192)),o=t.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",s=t.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8"},23371:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=s(r(99358)),i=s(r(35438)),o=r(26022);function s(e){return e&&e.__esModule?e:{default:e}}t.default=function(e,t,r){if(n.default.randomUUID&&!t&&!e)return n.default.randomUUID();var s=(e=e||{}).random||(e.rng||i.default)();if(s[6]=15&s[6]|64,s[8]=63&s[8]|128,t){r=r||0;for(var a=0;a<16;++a)t[r+a]=s[a];return t}return(0,o.unsafeStringify)(s)}},55464:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(r(80249)),i=o(r(54536));function o(e){return e&&e.__esModule?e:{default:e}}var s=(0,n.default)("v5",80,i.default);t.default=s},94625:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e={},t,r=0){var s=(0,i.default)(l(l({},e),{},{_v6:!0}),new Uint8Array(16));if(s=(0,o.default)(s),t){for(var a=0;a<16;a++)t[r+a]=s[a];return t}return(0,n.unsafeStringify)(s)};var n=r(26022),i=s(r(98444)),o=s(r(13589));function s(e){return e&&e.__esModule?e:{default:e}}function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i;n=t,i=r[t],(n=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(n))in e?Object.defineProperty(e,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}},10793:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){var t,r=(t="string"==typeof e?(0,n.default)(e):e,Uint8Array.of((15&t[3])<<4|t[4]>>4&15,(15&t[4])<<4|(240&t[5])>>4,(15&t[5])<<4|15&t[6],t[7],(15&t[1])<<4|(240&t[2])>>4,(15&t[2])<<4|(240&t[3])>>4,16|(240&t[0])>>4,(15&t[0])<<4|(240&t[1])>>4,t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15]));return"string"==typeof e?(0,i.unsafeStringify)(r):r};var n=function(e){return e&&e.__esModule?e:{default:e}}(r(86192)),i=r(26022)},49574:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(r(35438)),i=r(26022),o=null,s=null,a=0;t.default=function(e,t,r){e=e||{};var l=t&&r||0,u=t||new Uint8Array(16),c=e.random||(e.rng||n.default)(),f=void 0!==e.msecs?e.msecs:Date.now(),d=void 0!==e.seq?e.seq:null,h=s,p=o;return f>a&&void 0===e.msecs&&(a=f,null!==d&&(h=null,p=null)),null!==d&&(d>0x7fffffff&&(d=0x7fffffff),h=d>>>19&4095,p=524287&d),(null===h||null===p)&&(h=(h=127&c[6])<<8|c[7],p=(p=(p=63&c[8])<<8|c[9])<<5|c[10]>>>3),f+1e4>a&&null===d?++p>524287&&(p=0,++h>4095&&(h=0,a++)):a=f,s=h,o=p,u[l++]=a/0x10000000000&255,u[l++]=a/0x100000000&255,u[l++]=a/0x1000000&255,u[l++]=a/65536&255,u[l++]=a/256&255,u[l++]=255&a,u[l++]=h>>>4&15|112,u[l++]=255&h,u[l++]=p>>>13&63|128,u[l++]=p>>>5&255,u[l++]=p<<3&255|7&c[10],u[l++]=c[11],u[l++]=c[12],u[l++]=c[13],u[l++]=c[14],u[l++]=c[15],t||(0,i.unsafeStringify)(u)}},96015:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(r(47748));t.default=function(e){return"string"==typeof e&&n.default.test(e)}},87055:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=function(e){return e&&e.__esModule?e:{default:e}}(r(96015));t.default=function(e){if(!(0,n.default)(e))throw TypeError("Invalid UUID");return parseInt(e.slice(14,15),16)}},17144:(e,t,r)=>{"use strict";let n;var i,o,s,a=r(26332),l=r(7460).Buffer;function u(e,t){return function(){return e.apply(t,arguments)}}let{toString:c}=Object.prototype,{getPrototypeOf:f}=Object,{iterator:d,toStringTag:h}=Symbol,p=(e=>t=>{let r=c.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),m=e=>(e=e.toLowerCase(),t=>p(t)===e),y=e=>t=>typeof t===e,{isArray:g}=Array,b=y("undefined"),v=m("ArrayBuffer"),w=y("string"),O=y("function"),E=y("number"),T=e=>null!==e&&"object"==typeof e,R=e=>{if("object"!==p(e))return!1;let t=f(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(h in e)&&!(d in e)},_=m("Date"),x=m("File"),j=m("Blob"),k=m("FileList"),S=m("URLSearchParams"),[C,P,A,U]=["ReadableStream","Request","Response","Headers"].map(m);function B(e,t,{allOwnKeys:r=!1}={}){let n,i;if(null!=e){if("object"!=typeof e&&(e=[e]),g(e))for(n=0,i=e.length;n<i;n++)t.call(null,e[n],n,e);else{let i;let o=r?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;for(n=0;n<s;n++)i=o[n],t.call(null,e[i],i,e)}}}function I(e,t){let r;t=t.toLowerCase();let n=Object.keys(e),i=n.length;for(;i-- >0;)if(t===(r=n[i]).toLowerCase())return r;return null}let q="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:r.g,N=e=>!b(e)&&e!==q,L=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&f(Uint8Array)),D=m("HTMLFormElement"),M=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),$=m("RegExp"),F=(e,t)=>{let r=Object.getOwnPropertyDescriptors(e),n={};B(r,(r,i)=>{let o;!1!==(o=t(r,i,e))&&(n[i]=o||r)}),Object.defineProperties(e,n)},G=m("AsyncFunction"),z=(i="function"==typeof setImmediate,o=O(q.postMessage),i?setImmediate:o?((e,t)=>(q.addEventListener("message",({source:r,data:n})=>{r===q&&n===e&&t.length&&t.shift()()},!1),r=>{t.push(r),q.postMessage(e,"*")}))(`axios@${Math.random()}`,[]):e=>setTimeout(e)),H="undefined"!=typeof queueMicrotask?queueMicrotask.bind(q):void 0!==a&&a.nextTick||z;var W={isArray:g,isArrayBuffer:v,isBuffer:function(e){return null!==e&&!b(e)&&null!==e.constructor&&!b(e.constructor)&&O(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||O(e.append)&&("formdata"===(t=p(e))||"object"===t&&O(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&v(e.buffer)},isString:w,isNumber:E,isBoolean:e=>!0===e||!1===e,isObject:T,isPlainObject:R,isReadableStream:C,isRequest:P,isResponse:A,isHeaders:U,isUndefined:b,isDate:_,isFile:x,isBlob:j,isRegExp:$,isFunction:O,isStream:e=>T(e)&&O(e.pipe),isURLSearchParams:S,isTypedArray:L,isFileList:k,forEach:B,merge:function e(){let{caseless:t}=N(this)&&this||{},r={},n=(n,i)=>{let o=t&&I(r,i)||i;R(r[o])&&R(n)?r[o]=e(r[o],n):R(n)?r[o]=e({},n):g(n)?r[o]=n.slice():r[o]=n};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&B(arguments[e],n);return r},extend:(e,t,r,{allOwnKeys:n}={})=>(B(t,(t,n)=>{r&&O(t)?e[n]=u(t,r):e[n]=t},{allOwnKeys:n}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},toFlatObject:(e,t,r,n)=>{let i,o,s;let a={};if(t=t||{},null==e)return t;do{for(o=(i=Object.getOwnPropertyNames(e)).length;o-- >0;)s=i[o],(!n||n(s,e,t))&&!a[s]&&(t[s]=e[s],a[s]=!0);e=!1!==r&&f(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},kindOf:p,kindOfTest:m,endsWith:(e,t,r)=>{e=String(e),(void 0===r||r>e.length)&&(r=e.length),r-=t.length;let n=e.indexOf(t,r);return -1!==n&&n===r},toArray:e=>{if(!e)return null;if(g(e))return e;let t=e.length;if(!E(t))return null;let r=Array(t);for(;t-- >0;)r[t]=e[t];return r},forEachEntry:(e,t)=>{let r;let n=(e&&e[d]).call(e);for(;(r=n.next())&&!r.done;){let n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let r;let n=[];for(;null!==(r=e.exec(t));)n.push(r);return n},isHTMLForm:D,hasOwnProperty:M,hasOwnProp:M,reduceDescriptors:F,freezeMethods:e=>{F(e,(t,r)=>{if(O(e)&&-1!==["arguments","caller","callee"].indexOf(r))return!1;if(O(e[r])){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},toObjectSet:(e,t)=>{let r={};return(e=>{e.forEach(e=>{r[e]=!0})})(g(e)?e:String(e).split(t)),r},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,t,r){return t.toUpperCase()+r}),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:I,global:q,isContextDefined:N,isSpecCompliantForm:function(e){return!!(e&&O(e.append)&&"FormData"===e[h]&&e[d])},toJSONObject:e=>{let t=Array(10),r=(e,n)=>{if(T(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[n]=e;let i=g(e)?[]:{};return B(e,(e,t)=>{let o=r(e,n+1);b(o)||(i[t]=o)}),t[n]=void 0,i}}return e};return r(e,0)},isAsyncFn:G,isThenable:e=>e&&(T(e)||O(e))&&O(e.then)&&O(e.catch),setImmediate:z,asap:H,isIterable:e=>null!=e&&O(e[d])};function J(e,t,r,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}W.inherits(J,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:W.toJSONObject(this.config),code:this.code,status:this.status}}});let V=J.prototype,K={};function X(e){return W.isPlainObject(e)||W.isArray(e)}function Z(e){return W.endsWith(e,"[]")?e.slice(0,-2):e}function Y(e,t,r){return e?e.concat(t).map(function(e,t){return e=Z(e),!r&&t?"["+e+"]":e}).join(r?".":""):t}["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{K[e]={value:e}}),Object.defineProperties(J,K),Object.defineProperty(V,"isAxiosError",{value:!0}),J.from=(e,t,r,n,i,o)=>{let s=Object.create(V);return W.toFlatObject(e,s,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),J.call(s,e.message,t,r,n,i),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};let Q=W.toFlatObject(W,{},null,function(e){return/^is[A-Z]/.test(e)});function ee(e,t,r){if(!W.isObject(e))throw TypeError("target must be an object");t=t||new FormData;let n=(r=W.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,t){return!W.isUndefined(t[e])})).metaTokens,i=r.visitor||c,o=r.dots,s=r.indexes,a=(r.Blob||"undefined"!=typeof Blob&&Blob)&&W.isSpecCompliantForm(t);if(!W.isFunction(i))throw TypeError("visitor must be a function");function u(e){if(null===e)return"";if(W.isDate(e))return e.toISOString();if(W.isBoolean(e))return e.toString();if(!a&&W.isBlob(e))throw new J("Blob is not supported. Use a Buffer instead.");return W.isArrayBuffer(e)||W.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):l.from(e):e}function c(e,r,i){let a=e;if(e&&!i&&"object"==typeof e){if(W.endsWith(r,"{}"))r=n?r:r.slice(0,-2),e=JSON.stringify(e);else{var l;if(W.isArray(e)&&(l=e,W.isArray(l)&&!l.some(X))||(W.isFileList(e)||W.endsWith(r,"[]"))&&(a=W.toArray(e)))return r=Z(r),a.forEach(function(e,n){W.isUndefined(e)||null===e||t.append(!0===s?Y([r],n,o):null===s?r:r+"[]",u(e))}),!1}}return!!X(e)||(t.append(Y(i,r,o),u(e)),!1)}let f=[],d=Object.assign(Q,{defaultVisitor:c,convertValue:u,isVisitable:X});if(!W.isObject(e))throw TypeError("data must be an object");return!function e(r,n){if(!W.isUndefined(r)){if(-1!==f.indexOf(r))throw Error("Circular reference detected in "+n.join("."));f.push(r),W.forEach(r,function(r,o){!0===(!(W.isUndefined(r)||null===r)&&i.call(t,r,W.isString(o)?o.trim():o,n,d))&&e(r,n?n.concat(o):[o])}),f.pop()}}(e),t}function et(e){let t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return t[e]})}function er(e,t){this._pairs=[],e&&ee(e,this,t)}let en=er.prototype;function ei(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function eo(e,t,r){let n;if(!t)return e;let i=r&&r.encode||ei;W.isFunction(r)&&(r={serialize:r});let o=r&&r.serialize;if(n=o?o(t,r):W.isURLSearchParams(t)?t.toString():new er(t,r).toString(i)){let t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+n}return e}en.append=function(e,t){this._pairs.push([e,t])},en.toString=function(e){let t=e?function(t){return e.call(this,t,et)}:et;return this._pairs.map(function(e){return t(e[0])+"="+t(e[1])},"").join("&")};class es{constructor(){this.handlers=[]}use(e,t,r){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){W.forEach(this.handlers,function(t){null!==t&&e(t)})}}var ea={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},el="undefined"!=typeof URLSearchParams?URLSearchParams:er,eu="undefined"!=typeof FormData?FormData:null,ec="undefined"!=typeof Blob?Blob:null;let ef="undefined"!=typeof window&&"undefined"!=typeof document,ed="object"==typeof navigator&&navigator||void 0,eh=ef&&(!ed||0>["ReactNative","NativeScript","NS"].indexOf(ed.product)),ep="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,em=ef&&window.location.href||"http://localhost";var ey={...Object.freeze({__proto__:null,hasBrowserEnv:ef,hasStandardBrowserWebWorkerEnv:ep,hasStandardBrowserEnv:eh,navigator:ed,origin:em}),isBrowser:!0,classes:{URLSearchParams:el,FormData:eu,Blob:ec},protocols:["http","https","file","blob","url","data"]};function eg(e){if(W.isFormData(e)&&W.isFunction(e.entries)){let t={};return W.forEachEntry(e,(e,r)=>{!function e(t,r,n,i){let o=t[i++];if("__proto__"===o)return!0;let s=Number.isFinite(+o),a=i>=t.length;return(o=!o&&W.isArray(n)?n.length:o,a)?W.hasOwnProp(n,o)?n[o]=[n[o],r]:n[o]=r:(n[o]&&W.isObject(n[o])||(n[o]=[]),e(t,r,n[o],i)&&W.isArray(n[o])&&(n[o]=function(e){let t,r;let n={},i=Object.keys(e),o=i.length;for(t=0;t<o;t++)n[r=i[t]]=e[r];return n}(n[o]))),!s}(W.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),r,t,0)}),t}return null}let eb={transitional:ea,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){let r;let n=t.getContentType()||"",i=n.indexOf("application/json")>-1,o=W.isObject(e);if(o&&W.isHTMLForm(e)&&(e=new FormData(e)),W.isFormData(e))return i?JSON.stringify(eg(e)):e;if(W.isArrayBuffer(e)||W.isBuffer(e)||W.isStream(e)||W.isFile(e)||W.isBlob(e)||W.isReadableStream(e))return e;if(W.isArrayBufferView(e))return e.buffer;if(W.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1){var s,a;return(s=e,a=this.formSerializer,ee(s,new ey.classes.URLSearchParams,Object.assign({visitor:function(e,t,r,n){return ey.isNode&&W.isBuffer(e)?(this.append(t,e.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)}},a))).toString()}if((r=W.isFileList(e))||n.indexOf("multipart/form-data")>-1){let t=this.env&&this.env.FormData;return ee(r?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||i?(t.setContentType("application/json",!1),function(e,t,r){if(W.isString(e))try{return(0,JSON.parse)(e),W.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){let t=this.transitional||eb.transitional,r=t&&t.forcedJSONParsing,n="json"===this.responseType;if(W.isResponse(e)||W.isReadableStream(e))return e;if(e&&W.isString(e)&&(r&&!this.responseType||n)){let r=t&&t.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!r&&n){if("SyntaxError"===e.name)throw J.from(e,J.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:ey.classes.FormData,Blob:ey.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};W.forEach(["delete","get","head","post","put","patch"],e=>{eb.headers[e]={}});let ev=W.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]);var ew=e=>{let t,r,n;let i={};return e&&e.split("\n").forEach(function(e){n=e.indexOf(":"),t=e.substring(0,n).trim().toLowerCase(),r=e.substring(n+1).trim(),!t||i[t]&&ev[t]||("set-cookie"===t?i[t]?i[t].push(r):i[t]=[r]:i[t]=i[t]?i[t]+", "+r:r)}),i};let eO=Symbol("internals");function eE(e){return e&&String(e).trim().toLowerCase()}function eT(e){return!1===e||null==e?e:W.isArray(e)?e.map(eT):String(e)}let eR=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function e_(e,t,r,n,i){if(W.isFunction(n))return n.call(this,t,r);if(i&&(t=r),W.isString(t)){if(W.isString(n))return -1!==t.indexOf(n);if(W.isRegExp(n))return n.test(t)}}class ex{constructor(e){e&&this.set(e)}set(e,t,r){let n=this;function i(e,t,r){let i=eE(t);if(!i)throw Error("header name must be a non-empty string");let o=W.findKey(n,i);o&&void 0!==n[o]&&!0!==r&&(void 0!==r||!1===n[o])||(n[o||t]=eT(e))}let o=(e,t)=>W.forEach(e,(e,r)=>i(e,r,t));if(W.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(W.isString(e)&&(e=e.trim())&&!eR(e))o(ew(e),t);else if(W.isObject(e)&&W.isIterable(e)){let r={},n,i;for(let t of e){if(!W.isArray(t))throw TypeError("Object iterator must return a key-value pair");r[i=t[0]]=(n=r[i])?W.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}o(r,t)}else null!=e&&i(t,e,r);return this}get(e,t){if(e=eE(e)){let r=W.findKey(this,e);if(r){let e=this[r];if(!t)return e;if(!0===t)return function(e){let t;let r=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;t=n.exec(e);)r[t[1]]=t[2];return r}(e);if(W.isFunction(t))return t.call(this,e,r);if(W.isRegExp(t))return t.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=eE(e)){let r=W.findKey(this,e);return!!(r&&void 0!==this[r]&&(!t||e_(this,this[r],r,t)))}return!1}delete(e,t){let r=this,n=!1;function i(e){if(e=eE(e)){let i=W.findKey(r,e);i&&(!t||e_(r,r[i],i,t))&&(delete r[i],n=!0)}}return W.isArray(e)?e.forEach(i):i(e),n}clear(e){let t=Object.keys(this),r=t.length,n=!1;for(;r--;){let i=t[r];(!e||e_(this,this[i],i,e,!0))&&(delete this[i],n=!0)}return n}normalize(e){let t=this,r={};return W.forEach(this,(n,i)=>{let o=W.findKey(r,i);if(o){t[o]=eT(n),delete t[i];return}let s=e?i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,t,r)=>t.toUpperCase()+r):String(i).trim();s!==i&&delete t[i],t[s]=eT(n),r[s]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let t=Object.create(null);return W.forEach(this,(r,n)=>{null!=r&&!1!==r&&(t[n]=e&&W.isArray(r)?r.join(", "):r)}),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,t])=>e+": "+t).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){let r=new this(e);return t.forEach(e=>r.set(e)),r}static accessor(e){let t=(this[eO]=this[eO]={accessors:{}}).accessors,r=this.prototype;function n(e){let n=eE(e);t[n]||(!function(e,t){let r=W.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(e,r,i){return this[n].call(this,t,e,r,i)},configurable:!0})})}(r,e),t[n]=!0)}return W.isArray(e)?e.forEach(n):n(e),this}}function ej(e,t){let r=this||eb,n=t||r,i=ex.from(n.headers),o=n.data;return W.forEach(e,function(e){o=e.call(r,o,i.normalize(),t?t.status:void 0)}),i.normalize(),o}function ek(e){return!!(e&&e.__CANCEL__)}function eS(e,t,r){J.call(this,null==e?"canceled":e,J.ERR_CANCELED,t,r),this.name="CanceledError"}function eC(e,t,r){let n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new J("Request failed with status code "+r.status,[J.ERR_BAD_REQUEST,J.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}ex.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),W.reduceDescriptors(ex.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[r]=e}}}),W.freezeMethods(ex),W.inherits(eS,J,{__CANCEL__:!0});let eP=(e,t,r=3)=>{let n=0,i=function(e,t){let r;let n=Array(e=e||10),i=Array(e),o=0,s=0;return t=void 0!==t?t:1e3,function(a){let l=Date.now(),u=i[s];r||(r=l),n[o]=a,i[o]=l;let c=s,f=0;for(;c!==o;)f+=n[c++],c%=e;if((o=(o+1)%e)===s&&(s=(s+1)%e),l-r<t)return;let d=u&&l-u;return d?Math.round(1e3*f/d):void 0}}(50,250);return function(e,t){let r,n,i=0,o=1e3/t,s=(t,o=Date.now())=>{i=o,r=null,n&&(clearTimeout(n),n=null),e.apply(null,t)};return[(...e)=>{let t=Date.now(),a=t-i;a>=o?s(e,t):(r=e,n||(n=setTimeout(()=>{n=null,s(r)},o-a)))},()=>r&&s(r)]}(r=>{let o=r.loaded,s=r.lengthComputable?r.total:void 0,a=o-n,l=i(a);n=o,e({loaded:o,total:s,progress:s?o/s:void 0,bytes:a,rate:l||void 0,estimated:l&&s&&o<=s?(s-o)/l:void 0,event:r,lengthComputable:null!=s,[t?"download":"upload"]:!0})},r)},eA=(e,t)=>{let r=null!=e;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},eU=e=>(...t)=>W.asap(()=>e(...t));var eB=ey.hasStandardBrowserEnv?((e,t)=>r=>(r=new URL(r,ey.origin),e.protocol===r.protocol&&e.host===r.host&&(t||e.port===r.port)))(new URL(ey.origin),ey.navigator&&/(msie|trident)/i.test(ey.navigator.userAgent)):()=>!0,eI=ey.hasStandardBrowserEnv?{write(e,t,r,n,i,o){let s=[e+"="+encodeURIComponent(t)];W.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),W.isString(n)&&s.push("path="+n),W.isString(i)&&s.push("domain="+i),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(e){let t=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function eq(e,t,r){let n=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(n||!1==r)?t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e:t}let eN=e=>e instanceof ex?{...e}:e;function eL(e,t){t=t||{};let r={};function n(e,t,r,n){return W.isPlainObject(e)&&W.isPlainObject(t)?W.merge.call({caseless:n},e,t):W.isPlainObject(t)?W.merge({},t):W.isArray(t)?t.slice():t}function i(e,t,r,i){return W.isUndefined(t)?W.isUndefined(e)?void 0:n(void 0,e,r,i):n(e,t,r,i)}function o(e,t){if(!W.isUndefined(t))return n(void 0,t)}function s(e,t){return W.isUndefined(t)?W.isUndefined(e)?void 0:n(void 0,e):n(void 0,t)}function a(r,i,o){return o in t?n(r,i):o in e?n(void 0,r):void 0}let l={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:a,headers:(e,t,r)=>i(eN(e),eN(t),r,!0)};return W.forEach(Object.keys(Object.assign({},e,t)),function(n){let o=l[n]||i,s=o(e[n],t[n],n);W.isUndefined(s)&&o!==a||(r[n]=s)}),r}var eD=e=>{let t;let r=eL({},e),{data:n,withXSRFToken:i,xsrfHeaderName:o,xsrfCookieName:s,headers:a,auth:l}=r;if(r.headers=a=ex.from(a),r.url=eo(eq(r.baseURL,r.url,r.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&a.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),W.isFormData(n)){if(ey.hasStandardBrowserEnv||ey.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(t=a.getContentType())){let[e,...r]=t?t.split(";").map(e=>e.trim()).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...r].join("; "))}}if(ey.hasStandardBrowserEnv&&(i&&W.isFunction(i)&&(i=i(r)),i||!1!==i&&eB(r.url))){let e=o&&s&&eI.read(s);e&&a.set(o,e)}return r},eM="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(t,r){let n,i,o,s,a;let l=eD(e),u=l.data,c=ex.from(l.headers).normalize(),{responseType:f,onUploadProgress:d,onDownloadProgress:h}=l;function p(){s&&s(),a&&a(),l.cancelToken&&l.cancelToken.unsubscribe(n),l.signal&&l.signal.removeEventListener("abort",n)}let m=new XMLHttpRequest;function y(){if(!m)return;let n=ex.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());eC(function(e){t(e),p()},function(e){r(e),p()},{data:f&&"text"!==f&&"json"!==f?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:n,config:e,request:m}),m=null}m.open(l.method.toUpperCase(),l.url,!0),m.timeout=l.timeout,"onloadend"in m?m.onloadend=y:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(y)},m.onabort=function(){m&&(r(new J("Request aborted",J.ECONNABORTED,e,m)),m=null)},m.onerror=function(){r(new J("Network Error",J.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=l.timeout?"timeout of "+l.timeout+"ms exceeded":"timeout exceeded",n=l.transitional||ea;l.timeoutErrorMessage&&(t=l.timeoutErrorMessage),r(new J(t,n.clarifyTimeoutError?J.ETIMEDOUT:J.ECONNABORTED,e,m)),m=null},void 0===u&&c.setContentType(null),"setRequestHeader"in m&&W.forEach(c.toJSON(),function(e,t){m.setRequestHeader(t,e)}),W.isUndefined(l.withCredentials)||(m.withCredentials=!!l.withCredentials),f&&"json"!==f&&(m.responseType=l.responseType),h&&([o,a]=eP(h,!0),m.addEventListener("progress",o)),d&&m.upload&&([i,s]=eP(d),m.upload.addEventListener("progress",i),m.upload.addEventListener("loadend",s)),(l.cancelToken||l.signal)&&(n=t=>{m&&(r(!t||t.type?new eS(null,e,m):t),m.abort(),m=null)},l.cancelToken&&l.cancelToken.subscribe(n),l.signal&&(l.signal.aborted?n():l.signal.addEventListener("abort",n)));let g=function(e){let t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(l.url);if(g&&-1===ey.protocols.indexOf(g)){r(new J("Unsupported protocol "+g+":",J.ERR_BAD_REQUEST,e));return}m.send(u||null)})},e$=(e,t)=>{let{length:r}=e=e?e.filter(Boolean):[];if(t||r){let r,n=new AbortController,i=function(e){if(!r){r=!0,s();let t=e instanceof Error?e:this.reason;n.abort(t instanceof J?t:new eS(t instanceof Error?t.message:t))}},o=t&&setTimeout(()=>{o=null,i(new J(`timeout ${t} of ms exceeded`,J.ETIMEDOUT))},t),s=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)}),e=null)};e.forEach(e=>e.addEventListener("abort",i));let{signal:a}=n;return a.unsubscribe=()=>W.asap(s),a}};let eF=function*(e,t){let r,n=e.byteLength;if(!t||n<t){yield e;return}let i=0;for(;i<n;)r=i+t,yield e.slice(i,r),i=r},eG=async function*(e,t){for await(let r of ez(e))yield*eF(r,t)},ez=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let t=e.getReader();try{for(;;){let{done:e,value:r}=await t.read();if(e)break;yield r}}finally{await t.cancel()}},eH=(e,t,r,n)=>{let i;let o=eG(e,t),s=0,a=e=>{!i&&(i=!0,n&&n(e))};return new ReadableStream({async pull(e){try{let{done:t,value:n}=await o.next();if(t){a(),e.close();return}let i=n.byteLength;if(r){let e=s+=i;r(e)}e.enqueue(new Uint8Array(n))}catch(e){throw a(e),e}},cancel:e=>(a(e),o.return())},{highWaterMark:2})},eW="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,eJ=eW&&"function"==typeof ReadableStream,eV=eW&&("function"==typeof TextEncoder?(n=new TextEncoder,e=>n.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),eK=(e,...t)=>{try{return!!e(...t)}catch(e){return!1}},eX=eJ&&eK(()=>{let e=!1,t=new Request(ey.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),eZ=eJ&&eK(()=>W.isReadableStream(new Response("").body)),eY={stream:eZ&&(e=>e.body)};eW&&(s=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{eY[e]||(eY[e]=W.isFunction(s[e])?t=>t[e]():(t,r)=>{throw new J(`Response type '${e}' is not supported`,J.ERR_NOT_SUPPORT,r)})}));let eQ=async e=>{if(null==e)return 0;if(W.isBlob(e))return e.size;if(W.isSpecCompliantForm(e)){let t=new Request(ey.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return W.isArrayBufferView(e)||W.isArrayBuffer(e)?e.byteLength:(W.isURLSearchParams(e)&&(e+=""),W.isString(e))?(await eV(e)).byteLength:void 0},e0=async(e,t)=>{let r=W.toFiniteNumber(e.getContentLength());return null==r?eQ(t):r},e1={http:null,xhr:eM,fetch:eW&&(async e=>{let t,r,{url:n,method:i,data:o,signal:s,cancelToken:a,timeout:l,onDownloadProgress:u,onUploadProgress:c,responseType:f,headers:d,withCredentials:h="same-origin",fetchOptions:p}=eD(e);f=f?(f+"").toLowerCase():"text";let m=e$([s,a&&a.toAbortSignal()],l),y=m&&m.unsubscribe&&(()=>{m.unsubscribe()});try{if(c&&eX&&"get"!==i&&"head"!==i&&0!==(r=await e0(d,o))){let e,t=new Request(n,{method:"POST",body:o,duplex:"half"});if(W.isFormData(o)&&(e=t.headers.get("content-type"))&&d.setContentType(e),t.body){let[e,n]=eA(r,eP(eU(c)));o=eH(t.body,65536,e,n)}}W.isString(h)||(h=h?"include":"omit");let s="credentials"in Request.prototype;t=new Request(n,{...p,signal:m,method:i.toUpperCase(),headers:d.normalize().toJSON(),body:o,duplex:"half",credentials:s?h:void 0});let a=await fetch(t,p),l=eZ&&("stream"===f||"response"===f);if(eZ&&(u||l&&y)){let e={};["status","statusText","headers"].forEach(t=>{e[t]=a[t]});let t=W.toFiniteNumber(a.headers.get("content-length")),[r,n]=u&&eA(t,eP(eU(u),!0))||[];a=new Response(eH(a.body,65536,r,()=>{n&&n(),y&&y()}),e)}f=f||"text";let g=await eY[W.findKey(eY,f)||"text"](a,e);return!l&&y&&y(),await new Promise((r,n)=>{eC(r,n,{data:g,headers:ex.from(a.headers),status:a.status,statusText:a.statusText,config:e,request:t})})}catch(r){if(y&&y(),r&&"TypeError"===r.name&&/Load failed|fetch/i.test(r.message))throw Object.assign(new J("Network Error",J.ERR_NETWORK,e,t),{cause:r.cause||r});throw J.from(r,r&&r.code,e,t)}})};W.forEach(e1,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}});let e2=e=>`- ${e}`,e5=e=>W.isFunction(e)||null===e||!1===e;var e4={getAdapter:e=>{let t,r;let{length:n}=e=W.isArray(e)?e:[e],i={};for(let o=0;o<n;o++){let n;if(r=t=e[o],!e5(t)&&void 0===(r=e1[(n=String(t)).toLowerCase()]))throw new J(`Unknown adapter '${n}'`);if(r)break;i[n||"#"+o]=r}if(!r){let e=Object.entries(i).map(([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build"));throw new J("There is no suitable adapter to dispatch the request "+(n?e.length>1?"since :\n"+e.map(e2).join("\n"):" "+e2(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r}};function e6(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new eS(null,e)}function e8(e){return e6(e),e.headers=ex.from(e.headers),e.data=ej.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),e4.getAdapter(e.adapter||eb.adapter)(e).then(function(t){return e6(e),t.data=ej.call(e,e.transformResponse,t),t.headers=ex.from(t.headers),t},function(t){return!ek(t)&&(e6(e),t&&t.response&&(t.response.data=ej.call(e,e.transformResponse,t.response),t.response.headers=ex.from(t.response.headers))),Promise.reject(t)})}let e3="1.10.0",e7={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{e7[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});let e9={};e7.transitional=function(e,t,r){function n(e,t){return"[Axios v"+e3+"] Transitional option '"+e+"'"+t+(r?". "+r:"")}return(r,i,o)=>{if(!1===e)throw new J(n(i," has been removed"+(t?" in "+t:"")),J.ERR_DEPRECATED);return t&&!e9[i]&&(e9[i]=!0,console.warn(n(i," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(r,i,o)}},e7.spelling=function(e){return(t,r)=>(console.warn(`${r} is likely a misspelling of ${e}`),!0)};var te={assertOptions:function(e,t,r){if("object"!=typeof e)throw new J("options must be an object",J.ERR_BAD_OPTION_VALUE);let n=Object.keys(e),i=n.length;for(;i-- >0;){let o=n[i],s=t[o];if(s){let t=e[o],r=void 0===t||s(t,o,e);if(!0!==r)throw new J("option "+o+" must be "+r,J.ERR_BAD_OPTION_VALUE);continue}if(!0!==r)throw new J("Unknown option "+o,J.ERR_BAD_OPTION)}},validators:e7};let tt=te.validators;class tr{constructor(e){this.defaults=e||{},this.interceptors={request:new es,response:new es}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=Error();let r=t.stack?t.stack.replace(/^.+\n/,""):"";try{e.stack?r&&!String(e.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+r):e.stack=r}catch(e){}}throw e}}_request(e,t){let r,n;"string"==typeof e?(t=t||{}).url=e:t=e||{};let{transitional:i,paramsSerializer:o,headers:s}=t=eL(this.defaults,t);void 0!==i&&te.assertOptions(i,{silentJSONParsing:tt.transitional(tt.boolean),forcedJSONParsing:tt.transitional(tt.boolean),clarifyTimeoutError:tt.transitional(tt.boolean)},!1),null!=o&&(W.isFunction(o)?t.paramsSerializer={serialize:o}:te.assertOptions(o,{encode:tt.function,serialize:tt.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),te.assertOptions(t,{baseUrl:tt.spelling("baseURL"),withXsrfToken:tt.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let a=s&&W.merge(s.common,s[t.method]);s&&W.forEach(["delete","get","head","post","put","patch","common"],e=>{delete s[e]}),t.headers=ex.concat(a,s);let l=[],u=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(t))&&(u=u&&e.synchronous,l.unshift(e.fulfilled,e.rejected))});let c=[];this.interceptors.response.forEach(function(e){c.push(e.fulfilled,e.rejected)});let f=0;if(!u){let e=[e8.bind(this),void 0];for(e.unshift.apply(e,l),e.push.apply(e,c),n=e.length,r=Promise.resolve(t);f<n;)r=r.then(e[f++],e[f++]);return r}n=l.length;let d=t;for(f=0;f<n;){let e=l[f++],t=l[f++];try{d=e(d)}catch(e){t.call(this,e);break}}try{r=e8.call(this,d)}catch(e){return Promise.reject(e)}for(f=0,n=c.length;f<n;)r=r.then(c[f++],c[f++]);return r}getUri(e){return eo(eq((e=eL(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}W.forEach(["delete","get","head","options"],function(e){tr.prototype[e]=function(t,r){return this.request(eL(r||{},{method:e,url:t,data:(r||{}).data}))}}),W.forEach(["post","put","patch"],function(e){function t(t){return function(r,n,i){return this.request(eL(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:r,data:n}))}}tr.prototype[e]=t(),tr.prototype[e+"Form"]=t(!0)});class tn{constructor(e){let t;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){t=e});let r=this;this.promise.then(e=>{if(!r._listeners)return;let t=r._listeners.length;for(;t-- >0;)r._listeners[t](e);r._listeners=null}),this.promise.then=e=>{let t;let n=new Promise(e=>{r.subscribe(e),t=e}).then(e);return n.cancel=function(){r.unsubscribe(t)},n},e(function(e,n,i){r.reason||(r.reason=new eS(e,n,i),t(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){let e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new tn(function(t){e=t}),cancel:e}}}let ti={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ti).forEach(([e,t])=>{ti[t]=e});let to=function e(t){let r=new tr(t),n=u(tr.prototype.request,r);return W.extend(n,tr.prototype,r,{allOwnKeys:!0}),W.extend(n,r,null,{allOwnKeys:!0}),n.create=function(r){return e(eL(t,r))},n}(eb);to.Axios=tr,to.CanceledError=eS,to.CancelToken=tn,to.isCancel=ek,to.VERSION=e3,to.toFormData=ee,to.AxiosError=J,to.Cancel=to.CanceledError,to.all=function(e){return Promise.all(e)},to.spread=function(e){return function(t){return e.apply(null,t)}},to.isAxiosError=function(e){return W.isObject(e)&&!0===e.isAxiosError},to.mergeConfig=eL,to.AxiosHeaders=ex,to.formToJSON=e=>eg(W.isHTMLForm(e)?new FormData(e):e),to.getAdapter=e4.getAdapter,to.HttpStatusCode=ti,to.default=to,e.exports=to}}]);