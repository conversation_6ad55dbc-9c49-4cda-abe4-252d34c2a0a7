"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+dialog@3.5.22_r_053f8c3b10292eb2809decb7e5ed7738";
exports.ids = ["vendor-chunks/@react-aria+dialog@3.5.22_r_053f8c3b10292eb2809decb7e5ed7738"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+dialog@3.5.22_r_053f8c3b10292eb2809decb7e5ed7738/node_modules/@react-aria/dialog/dist/useDialog.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+dialog@3.5.22_r_053f8c3b10292eb2809decb7e5ed7738/node_modules/@react-aria/dialog/dist/useDialog.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDialog: () => (/* binding */ $40df3f8667284809$export$d55e7ee900f34e93)\n/* harmony export */ });\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/useId.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/filterDOMProps.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f4c4e47fdd38dbbc2317f9013c56c4ca/node_modules/@react-aria/interactions/dist/focusSafely.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_aria_overlays__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/overlays */ \"(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.26.0_f683e2f5d8a049041921877a4d68e6f4/node_modules/@react-aria/overlays/dist/Overlay.mjs\");\n\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n\nfunction $40df3f8667284809$export$d55e7ee900f34e93(props, ref) {\n    let { role: role = 'dialog' } = props;\n    let titleId = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useSlotId)();\n    titleId = props['aria-label'] ? undefined : titleId;\n    let isRefocusing = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Focus the dialog itself on mount, unless a child element is already focused.\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (ref.current && !ref.current.contains(document.activeElement)) {\n            (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_2__.focusSafely)(ref.current);\n            // Safari on iOS does not move the VoiceOver cursor to the dialog\n            // or announce that it has opened until it has rendered. A workaround\n            // is to wait for half a second, then blur and re-focus the dialog.\n            let timeout = setTimeout(()=>{\n                if (document.activeElement === ref.current) {\n                    isRefocusing.current = true;\n                    if (ref.current) {\n                        ref.current.blur();\n                        (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_2__.focusSafely)(ref.current);\n                    }\n                    isRefocusing.current = false;\n                }\n            }, 500);\n            return ()=>{\n                clearTimeout(timeout);\n            };\n        }\n    }, [\n        ref\n    ]);\n    (0, _react_aria_overlays__WEBPACK_IMPORTED_MODULE_3__.useOverlayFocusContain)();\n    // We do not use aria-modal due to a Safari bug which forces the first focusable element to be focused\n    // on mount when inside an iframe, no matter which element we programmatically focus.\n    // See https://bugs.webkit.org/show_bug.cgi?id=211934.\n    // useModal sets aria-hidden on all elements outside the dialog, so the dialog will behave as a modal\n    // even without aria-modal on the dialog itself.\n    return {\n        dialogProps: {\n            ...(0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__.filterDOMProps)(props, {\n                labelable: true\n            }),\n            role: role,\n            tabIndex: -1,\n            'aria-labelledby': props['aria-labelledby'] || titleId,\n            // Prevent blur events from reaching useOverlay, which may cause\n            // popovers to close. Since focus is contained within the dialog,\n            // we don't want this to occur due to the above useEffect.\n            onBlur: (e)=>{\n                if (isRefocusing.current) e.stopPropagation();\n            }\n        },\n        titleProps: {\n            id: titleId\n        }\n    };\n}\n\n\n\n//# sourceMappingURL=useDialog.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+dialog@3.5.22_r_053f8c3b10292eb2809decb7e5ed7738/node_modules/@react-aria/dialog/dist/useDialog.mjs\n");

/***/ })

};
;