"use strict";exports.id=393,exports.ids=[393],exports.modules={98690:(t,e,i)=>{i.d(e,{P:()=>s});var r=i(8649);class s{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}then(t,e){return Promise.all(this.animations).then(t).catch(e)}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>(0,r.J)()&&i.attachTimeline?i.attachTimeline(t):e(i));return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}},88798:(t,e,i)=>{i.d(e,{m:()=>w});var r=i(45399),s=i(31526),n=i(18338),a=i(68012),o=i(34929),l=i(23851),u=i(36433),h=i(10651),d=i(97432),p=i(27805),c=i(50692),m=i(14963),f=i(59291),v=i(19266),g=i(6391),y=i(98497);let b={anticipate:r.b,backInOut:s.ZZ,circInOut:n.tn};class w extends h.i{constructor(t){super(t);let{name:e,motionValue:i,element:r,keyframes:s}=this.options;this.resolver=new a.K(s,(t,e)=>this.onKeyframesResolved(t,e),e,i,r),this.resolver.scheduleResolve()}initPlayback(t,e){var i,r;let{duration:s=300,times:n,ease:a,type:o,motionValue:l,name:h,startTime:p}=this.options;if(!(null===(i=l.owner)||void 0===i?void 0:i.current))return!1;if("string"==typeof a&&(0,g.n)()&&a in b&&(a=b[a]),r=this.options,(0,u.W)(r.type)||"spring"===r.type||!(0,m.yL)(r.ease)){let{onComplete:e,onUpdate:i,motionValue:r,element:l,...u}=this.options,h=function(t,e){let i=new d.o({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),r={done:!1,value:t[0]},s=[],n=0;for(;!r.done&&n<2e4;)s.push((r=i.sample(n)).value),n+=10;return{times:void 0,keyframes:s,duration:n-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),s=h.duration,n=h.times,a=h.ease,o="keyframes"}let y=(0,c.R)(l.owner.current,h,t,{...this.options,duration:s,times:n,ease:a});return y.startTime=null!=p?p:this.calcStartTime(),this.pendingTimeline?((0,f.v)(y,this.pendingTimeline),this.pendingTimeline=void 0):y.onfinish=()=>{let{onComplete:i}=this.options;l.set((0,v.X)(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:y,duration:s,times:n,type:o,ease:a,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return(0,l.X)(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return(0,l.X)(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=(0,l.f)(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return o.l;let{animation:i}=e;(0,f.v)(i,t)}else this.pendingTimeline=t;return o.l}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:r,type:s,ease:n,times:a}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:o,element:u,...h}=this.options,p=new d.o({...h,keyframes:i,duration:r,type:s,ease:n,times:a,isGenerator:!0}),c=(0,l.f)(this.time);t.setWithVelocity(p.sample(c-10).value,p.sample(c).value,10)}let{onStop:o}=this.options;o&&o(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:s,damping:n,type:a}=t;return(0,y.B)()&&i&&p.M.has(i)&&e&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate&&!r&&"mirror"!==s&&0!==n&&"inertia"!==a}}},10651:(t,e,i)=>{i.d(e,{i:()=>d});var r=i(9468),s=i(84801),n=i(41210),a=i(48148),o=i(36433),l=i(9915);let u=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(l.f.test(t)||"0"===t)&&!t.startsWith("url("));var h=i(19266);class d{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:a="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=r.k.now(),this.options={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:a,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(0,s.q)(),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=r.k.now(),this.hasAttemptedResolve=!0;let{name:i,type:s,velocity:l,delay:d,onComplete:p,onUpdate:c,isGenerator:m}=this.options;if(!m&&!function(t,e,i,r){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let n=t[t.length-1],l=u(s,e),h=u(n,e);return(0,a.$)(l===h,`You are trying to animate ${e} from "${s}" to "${n}". ${s} is not an animatable value - to enable this animation set ${s} to a value animatable to ${n} via the \`style\` property.`),!!l&&!!h&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||(0,o.W)(i))&&r)}(t,i,s,l)){if(n.d.current||!d){null==c||c((0,h.X)(t,this.options,e)),null==p||p(),this.resolveFinishedPromise();return}this.options.duration=0}let f=this.initPlayback(t,e);!1!==f&&(this._resolved={keyframes:t,finalKeyframe:e,...f},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}},97432:(t,e,i)=>{i.d(e,{o:()=>w,L:()=>S});var r=i(84801),s=i(36221),n=i(8935),a=i(93790),o=i(10651),l=i(95021),u=i(47349),h=i(13575),d=i(23851),p=i(80590),c=i(9468),m=i(39846);let f=t=>{let e=({timestamp:e})=>t(e);return{start:()=>m.Gt.update(e,!0),stop:()=>(0,m.WG)(e),now:()=>m.uv.isProcessing?m.uv.timestamp:c.k.now()}};var v=i(19266),g=i(36433);let y={decay:n.B,inertia:n.B,tween:a.i,keyframes:a.i,spring:s.o},b=t=>t/100;class w extends o.i{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:s,keyframes:n}=this.options,a=(null==s?void 0:s.KeyframeResolver)||r.h;this.resolver=new a(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i;let{type:r="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:o,velocity:d=0}=this.options,p=(0,g.W)(r)?r:y[r]||a.i;p!==a.i&&"number"!=typeof t[0]&&(e=(0,l.F)(b,(0,u.j)(t[0],t[1])),t=[0,100]);let c=p({...this.options,keyframes:t});"mirror"===o&&(i=p({...this.options,keyframes:[...t].reverse(),velocity:-d})),null===c.calculatedDuration&&(c.calculatedDuration=(0,h.t)(c));let{calculatedDuration:m}=c,f=m+n;return{generator:c,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:m,resolvedDuration:f,totalDuration:f*(s+1)-n}}onPostResolved(){let{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:r,generator:s,mirroredGenerator:n,mapPercentToKeyframes:a,keyframes:o,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return s.next(0);let{delay:d,repeat:c,repeatType:m,repeatDelay:f,onUpdate:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let y=this.currentTime-d*(this.speed>=0?1:-1),b=this.speed>=0?y<0:y>u;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let w=this.currentTime,S=s;if(c){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===m?(i=1-i,f&&(i-=f/h)):"mirror"===m&&(S=n)),w=(0,p.q)(0,1,i)*h}let A=b?{done:!1,value:o[0]}:S.next(w);a&&(A.value=a(A.value));let{done:V}=A;b||null===l||(V=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let P=null===this.holdTime&&("finished"===this.state||"running"===this.state&&V);return P&&void 0!==r&&(A.value=(0,v.X)(o,this.options,r)),g&&g(A.value),P&&this.finish(),A}get duration(){let{resolved:t}=this;return t?(0,d.X)(t.calculatedDuration):0}get time(){return(0,d.X)(this.currentTime)}set time(t){t=(0,d.f)(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,d.X)(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=f,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let r=this.driver.now();null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=r):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}function S(t){return new w(t)}},27805:(t,e,i)=>{i.d(e,{M:()=>r});let r=new Set(["opacity","clipPath","filter","transform"])},14963:(t,e,i)=>{i.d(e,{yL:()=>o,TU:()=>function t(e,i){if(e)return"function"==typeof e&&(0,a.n)()?n(e,i):(0,r.D)(e)?l(e):Array.isArray(e)?e.map(e=>t(e,i)||u.easeOut):u[e]}});var r=i(23139),s=i(92948);let n=(t,e)=>{let i="",r=Math.max(Math.round(e/10),2);for(let e=0;e<r;e++)i+=t((0,s.q)(0,r-1,e))+", ";return`linear(${i.substring(0,i.length-2)})`};var a=i(6391);function o(t){return!!("function"==typeof t&&(0,a.n)()||!t||"string"==typeof t&&(t in u||(0,a.n)())||(0,r.D)(t)||Array.isArray(t)&&t.every(o))}let l=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,u={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:l([0,.65,.55,1]),circOut:l([.55,0,1,.45]),backIn:l([.31,.01,.66,-.59]),backOut:l([.33,1.53,.69,.99])}},50692:(t,e,i)=>{i.d(e,{R:()=>s});var r=i(14963);function s(t,e,i,{delay:n=0,duration:a=300,repeat:o=0,repeatType:l="loop",ease:u="easeInOut",times:h}={}){let d={[e]:i};h&&(d.offset=h);let p=(0,r.TU)(u,a);return Array.isArray(p)&&(d.easing=p),t.animate(d,{delay:n,duration:a,easing:Array.isArray(p)?"linear":p,fill:"both",iterations:o+1,direction:"reverse"===l?"alternate":"normal"})}},59291:(t,e,i)=>{i.d(e,{v:()=>r});function r(t,e){t.timeline=e,t.onfinish=null}},19266:(t,e,i)=>{i.d(e,{X:()=>s});let r=t=>null!==t;function s(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(r),a=e&&"loop"!==i&&e%2==1?0:n.length-1;return a&&void 0!==s?s:n[a]}},6391:(t,e,i)=>{i.d(e,{n:()=>n});var r=i(49907);let s={linearEasing:void 0},n=function(t,e){let i=(0,r.p)(t);return()=>{var t;return null!==(t=s[e])&&void 0!==t?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing")},98497:(t,e,i)=>{i.d(e,{B:()=>r});let r=(0,i(49907).p)(()=>Object.hasOwnProperty.call(Element.prototype,"animate"))},8935:(t,e,i)=>{i.d(e,{B:()=>n});var r=i(36221),s=i(88600);function n({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:a=10,bounceStiffness:o=500,modifyTarget:l,min:u,max:h,restDelta:d=.5,restSpeed:p}){let c,m;let f=t[0],v={done:!1,value:f},g=t=>void 0!==u&&t<u||void 0!==h&&t>h,y=t=>void 0===u?h:void 0===h?u:Math.abs(u-t)<Math.abs(h-t)?u:h,b=i*e,w=f+b,S=void 0===l?w:l(w);S!==w&&(b=S-f);let A=t=>-b*Math.exp(-t/n),V=t=>S+A(t),P=t=>{let e=A(t),i=V(t);v.done=Math.abs(e)<=d,v.value=v.done?S:i},T=t=>{g(v.value)&&(c=t,m=(0,r.o)({keyframes:[v.value,y(v.value)],velocity:(0,s.Y)(V,t,v.value),damping:a,stiffness:o,restDelta:d,restSpeed:p}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return(m||void 0!==c||(e=!0,P(t),T(t)),void 0!==c&&t>=c)?m.next(t-c):(e||P(t),v)}}}},93790:(t,e,i)=>{i.d(e,{i:()=>l});var r=i(27789),s=i(94533),n=i(86081),a=i(81650),o=i(71605);function l({duration:t=300,keyframes:e,times:i,ease:l="easeInOut"}){let u=(0,s.h)(l)?l.map(n.K):(0,n.K)(l),h={done:!1,value:e[0]},d=(i&&i.length===e.length?i:(0,o.Z)(e)).map(e=>e*t),p=(0,a.G)(d,e,{ease:Array.isArray(u)?u:e.map(()=>u||r.am).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(h.value=p(e),h.done=e>=t,h)}}},19346:(t,e,i)=>{i.d(e,{aU:()=>o,pX:()=>a});var r=i(48148),s=i(80590),n=i(23851);function a({duration:t=800,bounce:e=.25,velocity:i=0,mass:a=1}){let l,u;(0,r.$)(t<=(0,n.f)(10),"Spring duration must be 10 seconds or less");let h=1-e;h=(0,s.q)(.05,1,h),t=(0,s.q)(.01,10,(0,n.X)(t)),h<1?(l=e=>{let r=e*h,s=r*t;return .001-(r-i)/o(e,h)*Math.exp(-s)},u=e=>{let r=e*h*t,s=Math.pow(h,2)*Math.pow(e,2)*t,n=Math.exp(-r),a=o(Math.pow(e,2),h);return(r*i+i-s)*n*(-l(e)+.001>0?-1:1)/a}):(l=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),u=e=>t*t*(i-e)*Math.exp(-e*t));let d=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(l,u,5/t);if(t=(0,n.f)(t),isNaN(d))return{stiffness:100,damping:10,duration:t};{let e=Math.pow(d,2)*a;return{stiffness:e,damping:2*h*Math.sqrt(a*e),duration:t}}}function o(t,e){return t*Math.sqrt(1-e*e)}},36221:(t,e,i)=>{i.d(e,{o:()=>u});var r=i(23851),s=i(88600),n=i(19346);let a=["duration","bounce"],o=["stiffness","damping","mass"];function l(t,e){return e.some(e=>void 0!==t[e])}function u({keyframes:t,restDelta:e,restSpeed:i,...u}){let h;let d=t[0],p=t[t.length-1],c={done:!1,value:d},{stiffness:m,damping:f,mass:v,duration:g,velocity:y,isResolvedFromDuration:b}=function(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!l(t,o)&&l(t,a)){let i=(0,n.pX)(t);(e={...e,...i,mass:1}).isResolvedFromDuration=!0}return e}({...u,velocity:-(0,r.X)(u.velocity||0)}),w=y||0,S=f/(2*Math.sqrt(m*v)),A=p-d,V=(0,r.X)(Math.sqrt(m/v)),P=5>Math.abs(A);if(i||(i=P?.01:2),e||(e=P?.005:.5),S<1){let t=(0,n.aU)(V,S);h=e=>p-Math.exp(-S*V*e)*((w+S*V*A)/t*Math.sin(t*e)+A*Math.cos(t*e))}else if(1===S)h=t=>p-Math.exp(-V*t)*(A+(w+V*A)*t);else{let t=V*Math.sqrt(S*S-1);h=e=>{let i=Math.exp(-S*V*e),r=Math.min(t*e,300);return p-i*((w+S*V*A)*Math.sinh(r)+t*A*Math.cosh(r))/t}}return{calculatedDuration:b&&g||null,next:t=>{let n=h(t);if(b)c.done=t>=g;else{let a=0;S<1&&(a=0===t?(0,r.f)(w):(0,s.Y)(h,t,n));let o=Math.abs(a)<=i,l=Math.abs(p-n)<=e;c.done=o&&l}return c.value=c.done?p:n,c}}}},13575:(t,e,i)=>{i.d(e,{Y:()=>r,t:()=>s});let r=2e4;function s(t){let e=0,i=t.next(e);for(;!i.done&&e<r;)e+=50,i=t.next(e);return e>=r?1/0:e}},36433:(t,e,i)=>{i.d(e,{W:()=>r});function r(t){return"function"==typeof t}},88600:(t,e,i)=>{i.d(e,{Y:()=>s});var r=i(67531);function s(t,e,i){let s=Math.max(e-5,0);return(0,r.f)(i-t(s),e-s)}},19977:(t,e,i)=>{i.d(e,{f:()=>y});var r=i(23851),s=i(92086);let n={type:"spring",stiffness:500,damping:25,restSpeed:10},a=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),o={type:"keyframes",duration:.8},l={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},u=(t,{keyframes:e})=>e.length>2?o:s.f.has(t)?t.startsWith("scale")?a(e[1]):n:l;var h=i(50368),d=i(60682),p=i(41210),c=i(19266),m=i(39846),f=i(88798),v=i(97432),g=i(98690);let y=(t,e,i,s={},n,a)=>o=>{let l=(0,h.r)(s,t)||{},y=l.delay||s.delay||0,{elapsed:b=0}=s;b-=(0,r.f)(y);let w={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-b,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{o(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:a?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:s,repeat:n,repeatType:a,repeatDelay:o,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(l)&&(w={...w,...u(t,w)}),w.duration&&(w.duration=(0,r.f)(w.duration)),w.repeatDelay&&(w.repeatDelay=(0,r.f)(w.repeatDelay)),void 0!==w.from&&(w.keyframes[0]=w.from);let S=!1;if(!1!==w.type&&(0!==w.duration||w.repeatDelay)||(w.duration=0,0!==w.delay||(S=!0)),(p.d.current||d.W.skipAnimations)&&(S=!0,w.duration=0,w.delay=0),S&&!a&&void 0!==e.get()){let t=(0,c.X)(w.keyframes,l);if(void 0!==t)return m.Gt.update(()=>{w.onUpdate(t),w.onComplete()}),new g.P([])}return!a&&f.m.supports(w)?new f.m(w):new v.o(w)}},81484:(t,e,i)=>{i.d(e,{$:()=>h});var r=i(92086),s=i(19977),n=i(35400),a=i(50368),o=i(49388),l=i(1660),u=i(39846);function h(t,e,{delay:i=0,transitionOverride:d,type:p}={}){var c;let{transition:m=t.getDefaultTransition(),transitionEnd:f,...v}=e;d&&(m=d);let g=[],y=p&&t.animationState&&t.animationState.getState()[p];for(let e in v){let n=t.getValue(e,null!==(c=t.latestValues[e])&&void 0!==c?c:null),h=v[e];if(void 0===h||y&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(y,e))continue;let d={delay:i,...(0,a.r)(m||{},e)},p=!1;if(window.MotionHandoffAnimation){let i=(0,o.P)(t);if(i){let t=window.MotionHandoffAnimation(i,e,u.Gt);null!==t&&(d.startTime=t,p=!0)}}(0,l.g)(t,e),n.start((0,s.f)(e,n,h,t.shouldReduceMotion&&r.f.has(e)?{type:!1}:d,t,p));let f=n.animation;f&&g.push(f)}return f&&Promise.all(g).then(()=>{u.Gt.update(()=>{f&&(0,n.U)(t,f)})}),g}},4296:(t,e,i)=>{i.d(e,{_:()=>o});var r=i(89403),s=i(81484);function n(t,e,i={}){var o;let l=(0,r.K)(t,e,"exit"===i.type?null===(o=t.presenceContext)||void 0===o?void 0:o.custom:void 0),{transition:u=t.getDefaultTransition()||{}}=l||{};i.transitionOverride&&(u=i.transitionOverride);let h=l?()=>Promise.all((0,s.$)(t,l,i)):()=>Promise.resolve(),d=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:l}=u;return function(t,e,i=0,r=0,s=1,o){let l=[],u=(t.variantChildren.size-1)*r,h=1===s?(t=0)=>t*r:(t=0)=>u-t*r;return Array.from(t.variantChildren).sort(a).forEach((t,r)=>{t.notify("AnimationStart",e),l.push(n(t,e,{...o,delay:i+h(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(l)}(t,e,s+r,o,l,i)}:()=>Promise.resolve(),{when:p}=u;if(!p)return Promise.all([h(),d(i.delay)]);{let[t,e]="beforeChildren"===p?[h,d]:[d,h];return t().then(()=>e())}}function a(t,e){return t.sortNodePosition(e)}function o(t,e,i={}){let a;if(t.notify("AnimationStart",e),Array.isArray(e))a=Promise.all(e.map(e=>n(t,e,i)));else if("string"==typeof e)a=n(t,e,i);else{let n="function"==typeof e?(0,r.K)(t,e,i.custom):e;a=Promise.all((0,s.$)(t,n,i))}return a.then(()=>{t.notify("AnimationComplete",e)})}},49388:(t,e,i)=>{i.d(e,{P:()=>s});var r=i(67494);function s(t){return t.props[r.n]}},50368:(t,e,i)=>{i.d(e,{r:()=>r});function r(t,e){return t?t[e]||t.default||t:void 0}},45399:(t,e,i)=>{i.d(e,{b:()=>s});var r=i(31526);let s=t=>(t*=2)<1?.5*(0,r.dg)(t):.5*(2-Math.pow(2,-10*(t-1)))},31526:(t,e,i)=>{i.d(e,{Sz:()=>a,ZZ:()=>l,dg:()=>o});var r=i(95009),s=i(32447),n=i(29224);let a=(0,r.A)(.33,1.53,.69,.99),o=(0,n.G)(a),l=(0,s.V)(o)},18338:(t,e,i)=>{i.d(e,{po:()=>n,tn:()=>o,yT:()=>a});var r=i(32447),s=i(29224);let n=t=>1-Math.sin(Math.acos(t)),a=(0,s.G)(n),o=(0,r.V)(n)},95009:(t,e,i)=>{i.d(e,{A:()=>n});var r=i(34929);let s=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function n(t,e,i,n){if(t===e&&i===n)return r.l;let a=e=>(function(t,e,i,r,n){let a,o;let l=0;do(a=s(o=e+(i-e)/2,r,n)-t)>0?i=o:e=o;while(Math.abs(a)>1e-7&&++l<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:s(a(t),e,n)}},27789:(t,e,i)=>{i.d(e,{a6:()=>s,am:()=>a,vT:()=>n});var r=i(95009);let s=(0,r.A)(.42,0,1,1),n=(0,r.A)(0,0,.58,1),a=(0,r.A)(.42,0,.58,1)},32447:(t,e,i)=>{i.d(e,{V:()=>r});let r=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2},29224:(t,e,i)=>{i.d(e,{G:()=>r});let r=t=>e=>1-t(1-e)},23139:(t,e,i)=>{i.d(e,{D:()=>r});let r=t=>Array.isArray(t)&&"number"==typeof t[0]},94533:(t,e,i)=>{i.d(e,{h:()=>r});let r=t=>Array.isArray(t)&&"number"!=typeof t[0]},86081:(t,e,i)=>{i.d(e,{K:()=>p});var r=i(48148),s=i(95009),n=i(34929),a=i(27789),o=i(18338),l=i(31526),u=i(45399),h=i(23139);let d={linear:n.l,easeIn:a.a6,easeInOut:a.am,easeOut:a.vT,circIn:o.po,circInOut:o.tn,circOut:o.yT,backIn:l.dg,backInOut:l.ZZ,backOut:l.Sz,anticipate:u.b},p=t=>{if((0,h.D)(t)){(0,r.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,a]=t;return(0,s.A)(e,i,n,a)}return"string"==typeof t?((0,r.V)(void 0!==d[t],`Invalid easing type '${t}'`),d[t]):t}},68124:(t,e,i)=>{i.d(e,{k:()=>r});function r(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}},16927:(t,e,i)=>{i.d(e,{h:()=>n});var r=i(68124),s=i(28110);function n(t,e,i,n){return(0,r.k)(t,e,(0,s.F)(i),n)}},28110:(t,e,i)=>{i.d(e,{F:()=>n,e:()=>s});var r=i(12140);function s(t,e="page"){return{point:{x:t[`${e}X`],y:t[`${e}Y`]}}}let n=t=>e=>(0,r.M)(e)&&t(e,s(e))},12140:(t,e,i)=>{i.d(e,{M:()=>r});let r=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary},9468:(t,e,i)=>{let r;i.d(e,{k:()=>o});var s=i(60682),n=i(39846);function a(){r=void 0}let o={now:()=>(void 0===r&&o.set(n.uv.isProcessing||s.W.useManualTiming?n.uv.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(a)}}},18064:(t,e,i)=>{function r(t){let e=null;return()=>null===e&&(e=t,()=>{e=null})}i.d(e,{D3:()=>o,nQ:()=>a});let s=r("dragHorizontal"),n=r("dragVertical");function a(t){let e=!1;if("y"===t)e=n();else if("x"===t)e=s();else{let t=s(),i=n();t&&i?e=()=>{t(),i()}:(t&&t(),i&&i())}return e}function o(){let t=a(!0);return!t||(t(),!1)}},32310:(t,e,i)=>{i.d(e,{X:()=>r});class r{constructor(t){this.isMounted=!1,this.node=t}update(){}}},38070:(t,e,i)=>{i.d(e,{W:()=>b});var r=i(83960),s=i(32182);function n(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}var a=i(52289),o=i(89403),l=i(60282),u=i(4296);let h=l._.length,d=[...l.U].reverse(),p=l.U.length;function c(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function m(){return{animate:c(!0),whileInView:c(),whileHover:c(),whileTap:c(),whileDrag:c(),whileFocus:c(),exit:c()}}var f=i(32310);class v extends f.X{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(0,u._)(t,e,i))),i=m(),c=!0,f=e=>(i,r)=>{var s;let n=(0,o.K)(t,r,"exit"===e?null===(s=t.presenceContext)||void 0===s?void 0:s.custom:void 0);if(n){let{transition:t,transitionEnd:e,...r}=n;i={...i,...r,...e}}return i};function v(o){let{props:u}=t,m=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<h;t++){let r=l._[t],s=e.props[r];((0,a.w)(s)||!1===s)&&(i[r]=s)}return i}(t.parent)||{},v=[],g=new Set,y={},b=1/0;for(let e=0;e<p;e++){var w;let l=d[e],h=i[l],p=void 0!==u[l]?u[l]:m[l],S=(0,a.w)(p),A=l===o?h.isActive:null;!1===A&&(b=e);let V=p===m[l]&&p!==u[l]&&S;if(V&&c&&t.manuallyAnimateOnMount&&(V=!1),h.protectedKeys={...y},!h.isActive&&null===A||!p&&!h.prevProp||(0,r.N)(p)||"boolean"==typeof p)continue;let P=(w=h.prevProp,"string"==typeof p?p!==w:!!Array.isArray(p)&&!n(p,w)),T=P||l===o&&h.isActive&&!V&&S||e>b&&S,x=!1,M=Array.isArray(p)?p:[p],F=M.reduce(f(l),{});!1===A&&(F={});let{prevResolvedValues:k={}}=h,C={...k,...F},E=e=>{T=!0,g.has(e)&&(x=!0,g.delete(e)),h.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in C){let e=F[t],i=k[t];if(!y.hasOwnProperty(t))((0,s.p)(e)&&(0,s.p)(i)?n(e,i):e===i)?void 0!==e&&g.has(t)?E(t):h.protectedKeys[t]=!0:null!=e?E(t):g.add(t)}h.prevProp=p,h.prevResolvedValues=F,h.isActive&&(y={...y,...F}),c&&t.blockInitialAnimation&&(T=!1);let O=!(V&&P)||x;T&&O&&v.push(...M.map(t=>({animation:t,options:{type:l}})))}if(g.size){let e={};g.forEach(i=>{let r=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=null!=r?r:null}),v.push({animation:e})}let S=!!v.length;return c&&(!1===u.initial||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(S=!1),c=!1,S?e(v):Promise.resolve()}return{animateChanges:v,setActive:function(e,r){var s;if(i[e].isActive===r)return Promise.resolve();null===(s=t.variantChildren)||void 0===s||s.forEach(t=>{var i;return null===(i=t.animationState)||void 0===i?void 0:i.setActive(e,r)}),i[e].isActive=r;let n=v(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=m(),c=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();(0,r.N)(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}let g=0;class y extends f.X{constructor(){super(...arguments),this.id=g++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}let b={animation:{Feature:v},exit:{Feature:y}}},29086:(t,e,i)=>{i.d(e,{n:()=>V});var r=i(16927),s=i(95021),n=i(18064),a=i(32310),o=i(39846);function l(t,e){let i=e?"onHoverStart":"onHoverEnd";return(0,r.h)(t.current,e?"pointerenter":"pointerleave",(r,s)=>{if("touch"===r.pointerType||(0,n.D3)())return;let a=t.getProps();t.animationState&&a.whileHover&&t.animationState.setActive("whileHover",e);let l=a[i];l&&o.Gt.postRender(()=>l(r,s))},{passive:!t.getProps()[i]})}class u extends a.X{mount(){this.unmount=(0,s.F)(l(this.node,!0),l(this.node,!1))}unmount(){}}var h=i(68124);class d extends a.X{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,s.F)((0,h.k)(this.node.current,"focus",()=>this.onFocus()),(0,h.k)(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var p=i(28110);let c=(t,e)=>!!e&&(t===e||c(t,e.parentElement));var m=i(34929);function f(t,e){if(!e)return;let i=new PointerEvent("pointer"+t);e(i,(0,p.e)(i))}class v extends a.X{constructor(){super(...arguments),this.removeStartListeners=m.l,this.removeEndListeners=m.l,this.removeAccessibleListeners=m.l,this.startPointerPress=(t,e)=>{if(this.isPressing)return;this.removeEndListeners();let i=this.node.getProps(),n=(0,r.h)(window,"pointerup",(t,e)=>{if(!this.checkPressEnd())return;let{onTap:i,onTapCancel:r,globalTapTarget:s}=this.node.getProps(),n=s||c(this.node.current,t.target)?i:r;n&&o.Gt.update(()=>n(t,e))},{passive:!(i.onTap||i.onPointerUp)}),a=(0,r.h)(window,"pointercancel",(t,e)=>this.cancelPress(t,e),{passive:!(i.onTapCancel||i.onPointerCancel)});this.removeEndListeners=(0,s.F)(n,a),this.startPress(t,e)},this.startAccessiblePress=()=>{let t=(0,h.k)(this.node.current,"keydown",t=>{"Enter"!==t.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=(0,h.k)(this.node.current,"keyup",t=>{"Enter"===t.key&&this.checkPressEnd()&&f("up",(t,e)=>{let{onTap:i}=this.node.getProps();i&&o.Gt.postRender(()=>i(t,e))})}),f("down",(t,e)=>{this.startPress(t,e)}))}),e=(0,h.k)(this.node.current,"blur",()=>{this.isPressing&&f("cancel",(t,e)=>this.cancelPress(t,e))});this.removeAccessibleListeners=(0,s.F)(t,e)}}startPress(t,e){this.isPressing=!0;let{onTapStart:i,whileTap:r}=this.node.getProps();r&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),i&&o.Gt.postRender(()=>i(t,e))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!(0,n.D3)()}cancelPress(t,e){if(!this.checkPressEnd())return;let{onTapCancel:i}=this.node.getProps();i&&o.Gt.postRender(()=>i(t,e))}mount(){let t=this.node.getProps(),e=(0,r.h)(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),i=(0,h.k)(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=(0,s.F)(e,i)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let g=new WeakMap,y=new WeakMap,b=t=>{let e=g.get(t.target);e&&e(t)},w=t=>{t.forEach(b)},S={some:0,all:1};class A extends a.X{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:s}=t,n={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:S[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;y.has(i)||y.set(i,{});let r=y.get(i),s=JSON.stringify(e);return r[s]||(r[s]=new IntersectionObserver(w,{root:t,...e})),r[s]}(e);return g.set(t,i),r.observe(t),()=>{g.delete(t),r.unobserve(t)}}(this.node.current,n,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),n=e?i:r;n&&n(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let V={inView:{Feature:A},tap:{Feature:v},focus:{Feature:d},hover:{Feature:u}}},84226:(t,e,i)=>{function r({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function s({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function n(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}i.d(e,{FY:()=>r,bS:()=>n,pA:()=>s})},91327:(t,e,i)=>{i.d(e,{OU:()=>u,Ql:()=>h,Ww:()=>p,hq:()=>n,o4:()=>l});var r=i(77031),s=i(13072);function n(t,e,i){return i+e*(t-i)}function a(t,e,i,r,s){return void 0!==s&&(t=r+s*(t-r)),r+i*(t-r)+e}function o(t,e=0,i=1,r,s){t.min=a(t.min,e,i,r,s),t.max=a(t.max,e,i,r,s)}function l(t,{x:e,y:i}){o(t.x,e.translate,e.scale,e.originPoint),o(t.y,i.translate,i.scale,i.originPoint)}function u(t,e,i,r=!1){let n,a;let o=i.length;if(o){e.x=e.y=1;for(let u=0;u<o;u++){a=(n=i[u]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&p(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),a&&(e.x*=a.x.scale,e.y*=a.y.scale,l(t,a)),r&&(0,s.HD)(n.latestValues)&&p(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}function h(t,e){t.min=t.min+e,t.max=t.max+e}function d(t,e,i,s,n=.5){let a=(0,r.k)(t.min,t.max,n);o(t,e,i,a,s)}function p(t,e){d(t.x,e.x,e.scaleX,e.scale,e.originX),d(t.y,e.y,e.scaleY,e.scale,e.originY)}},34848:(t,e,i)=>{i.d(e,{ge:()=>a,xU:()=>s});let r=()=>({translate:0,scale:1,origin:0,originPoint:0}),s=()=>({x:r(),y:r()}),n=()=>({min:0,max:0}),a=()=>({x:n(),y:n()})},13072:(t,e,i)=>{function r(t){return void 0===t||1===t}function s({scale:t,scaleX:e,scaleY:i}){return!r(t)||!r(e)||!r(i)}function n(t){return s(t)||a(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function a(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}i.d(e,{HD:()=>n,vF:()=>a,vk:()=>s})},45631:(t,e,i)=>{i.d(e,{L:()=>a,m:()=>n});var r=i(84226),s=i(91327);function n(t,e){return(0,r.FY)((0,r.bS)(t.getBoundingClientRect(),e))}function a(t,e,i){let r=n(t,i),{scroll:a}=e;return a&&((0,s.Ql)(r.x,a.offset.x),(0,s.Ql)(r.y,a.offset.y)),r}},19781:(t,e,i)=>{i.d(e,{B:()=>M});var r=i(92021),s=i(98126),n=i(200),a=i(91607),o=i(94222),l=i(92086),u=i(36195),h=i(17311),d=i(85378),p=i(83273),c=i(84801),m=i(28454),f=i(96394),v=i(40737),g=i(9915),y=i(77653),b=i(4531);let w=[...y.T,v.y,g.f],S=t=>w.find((0,b.w)(t));var A=i(83846),V=i(34848),P=i(9468),T=i(39846);let x=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class M{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:s,visualState:n},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=c.h,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=P.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,T.Gt.render(this.render,!1,!0))};let{latestValues:l,renderState:h}=n;this.latestValues=l,this.baseTarget={...l},this.initialValues=e.initial?{...l}:{},this.renderState=h,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!s,this.isControllingVariants=(0,u.e)(e),this.isVariantNode=(0,u.O)(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:d,...p}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in p){let e=p[t];void 0!==l[t]&&(0,o.S)(e)&&e.set(l[t],!1)}}mount(t){this.current=t,p.C.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),s.r.current||(0,r.U)(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||s.O.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in p.C.delete(this.current),this.projection&&this.projection.unmount(),(0,T.WG)(this.notifyUpdate),(0,T.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=l.f.has(t),s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&T.Gt.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),n=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),n(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in d.B){let e=d.B[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):(0,V.ge)()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<x.length;e++){let i=x[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let s=e[r],n=i[r];if((0,o.S)(s))t.addValue(r,s);else if((0,o.S)(n))t.addValue(r,(0,a.OQ)(s,{owner:t}));else if(n!==s){if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(r);t.addValue(r,(0,a.OQ)(void 0!==e?e:s,{owner:t}))}}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,a.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let r=void 0===this.latestValues[t]&&this.current?null!==(i=this.getBaseTargetFromProps(this.props,t))&&void 0!==i?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=r&&("string"==typeof r&&((0,m.i)(r)||(0,f.$)(r))?r=parseFloat(r):!S(r)&&g.f.test(e)&&(r=(0,A.J)(t,e)),this.setBaseTarget(t,(0,o.S)(r)?r.get():r)),(0,o.S)(r)?r.get():r}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i;let{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let s=(0,h.a)(this.props,r,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);s&&(i=s[t])}if(r&&void 0!==i)return i;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||(0,o.S)(s)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new n.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}},68012:(t,e,i)=>{i.d(e,{K:()=>m});var r=i(96394),s=i(48148),n=i(28454),a=i(31354);let o=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;var l=i(98478),u=i(77653),h=i(84801),d=i(9915),p=i(83846);let c=new Set(["auto","none","0"]);class m extends h.h{constructor(t,e,i,r,s){super(t,e,i,r,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&(r=r.trim(),(0,a.p)(r))){let l=function t(e,i,r=1){(0,s.V)(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[l,u]=function(t){let e=o.exec(t);if(!e)return[,];let[,i,r,s]=e;return[`--${null!=i?i:r}`,s]}(e);if(!l)return;let h=window.getComputedStyle(i).getPropertyValue(l);if(h){let t=h.trim();return(0,n.i)(t)?parseFloat(t):t}return(0,a.p)(u)?t(u,i,r+1):u}(r,e.current);void 0!==l&&(t[i]=l),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!l.$y.has(i)||2!==t.length)return;let[r,h]=t,d=(0,u.n)(r),p=(0,u.n)(h);if(d!==p){if((0,l.E4)(d)&&(0,l.E4)(p))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||(0,r.$)(s))&&i.push(e)}i.length&&function(t,e,i){let r,s=0;for(;s<t.length&&!r;){let e=t[s];"string"==typeof e&&!c.has(e)&&(0,d.V)(e).values.length&&(r=t[s]),s++}if(r&&i)for(let s of e)t[s]=(0,p.J)(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=l.Hr[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:r}=this;if(!e||!e.current)return;let s=e.getValue(i);s&&s.jump(this.measuredOrigin,!1);let n=r.length-1,a=r[n];r[n]=l.Hr[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==a&&void 0===this.finalKeyframe&&(this.finalKeyframe=a),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}},50965:(t,e,i)=>{i.d(e,{b:()=>n});var r=i(19781),s=i(68012);class n extends r.B{constructor(){super(...arguments),this.KeyframeResolver=s.K}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}}},51569:(t,e,i)=>{i.d(e,{J:()=>o});var r=i(423),s=i(69217),n=i(71061),a=i(792);let o=(t,e)=>(0,a.Q)(t)?new n.l(e):new s.M(e,{allowProjection:t!==r.Fragment})},76393:(t,e,i)=>{i.d(e,{l:()=>n});var r=i(38070),s=i(29086);let n={renderer:i(51569).J,...r.W,...s.n}},8649:(t,e,i)=>{i.d(e,{J:()=>r});let r=(0,i(49907).p)(()=>void 0!==window.ScrollTimeline)},98478:(t,e,i)=>{i.d(e,{$y:()=>a,E4:()=>o,Hr:()=>c,W9:()=>p});var r=i(92086),s=i(94242),n=i(82075);let a=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),o=t=>t===s.ai||t===n.px,l=(t,e)=>parseFloat(t.split(", ")[e]),u=(t,e)=>(i,{transform:r})=>{if("none"===r||!r)return 0;let s=r.match(/^matrix3d\((.+)\)$/u);if(s)return l(s[1],e);{let e=r.match(/^matrix\((.+)\)$/u);return e?l(e[1],t):0}},h=new Set(["x","y","z"]),d=r.U.filter(t=>!h.has(t));function p(t){let e=[];return d.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(i.startsWith("scale")?1:0))}),e}let c={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:u(4,13),y:u(5,14)};c.translateX=c.x,c.translateY=c.y},83846:(t,e,i)=>{i.d(e,{J:()=>a});var r=i(9915),s=i(67414),n=i(84733);function a(t,e){let i=(0,n.D)(t);return i!==s.p&&(i=r.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}},84733:(t,e,i)=>{i.d(e,{D:()=>a});var r=i(40737),s=i(67414);let n={...i(9837).W,color:r.y,backgroundColor:r.y,outlineColor:r.y,fill:r.y,stroke:r.y,borderColor:r.y,borderTopColor:r.y,borderRightColor:r.y,borderBottomColor:r.y,borderLeftColor:r.y,filter:s.p,WebkitFilter:s.p},a=t=>n[t]},77653:(t,e,i)=>{i.d(e,{T:()=>a,n:()=>o});var r=i(94242),s=i(82075),n=i(4531);let a=[r.ai,s.px,s.KN,s.uj,s.vw,s.vh,{test:t=>"auto"===t,parse:t=>t}],o=t=>a.find((0,n.w)(t))},4531:(t,e,i)=>{i.d(e,{w:()=>r});let r=t=>e=>e.test(t)},69217:(t,e,i)=>{i.d(e,{M:()=>p});var r=i(35629),s=i(31354),n=i(92086),a=i(56036),o=i(67386),l=i(84733),u=i(45631),h=i(50965),d=i(94222);class p extends h.b{constructor(){super(...arguments),this.type="html",this.renderInstance=o.e}readValueFromInstance(t,e){if(n.f.has(e)){let t=(0,l.D)(e);return t&&t.default||0}{let i=window.getComputedStyle(t),r=((0,s.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return(0,u.m)(t,e)}build(t,e,i){(0,r.O)(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return(0,a.x)(t,e,i)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,d.S)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}},83273:(t,e,i)=>{i.d(e,{C:()=>r});let r=new WeakMap},71061:(t,e,i)=>{i.d(e,{l:()=>c});var r=i(83787),s=i(50965),n=i(89357),a=i(42260),o=i(15337),l=i(92086),u=i(82193),h=i(84733),d=i(34848),p=i(21127);class c extends s.b{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=d.ge}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(l.f.has(e)){let t=(0,h.D)(e);return t&&t.default||0}return e=o.e.has(e)?e:(0,a.I)(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return(0,r.x)(t,e,i)}build(t,e,i){(0,n.B)(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,r){(0,u.d)(t,e,i,r)}mount(t){this.isSVGTag=(0,p.n)(t.tagName),super.mount(t)}}},84801:(t,e,i)=>{i.d(e,{h:()=>d,q:()=>h});var r=i(98478),s=i(39846);let n=new Set,a=!1,o=!1;function l(){if(o){let t=Array.from(n).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=(0,r.W9)(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var r;null===(r=t.getValue(e))||void 0===r||r.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}o=!1,a=!1,n.forEach(t=>t.complete()),n.clear()}function u(){n.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(o=!0)})}function h(){u(),l()}class d{constructor(t,e,i,r,s,n=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=s,this.isAsync=n}scheduleResolve(){this.isScheduled=!0,this.isAsync?(n.add(this),a||(a=!0,s.Gt.read(u),s.Gt.resolveKeyframes(l))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;for(let s=0;s<t.length;s++)if(null===t[s]){if(0===s){let s=null==r?void 0:r.get(),n=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let r=i.readValue(e,n);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=n),r&&void 0===s&&r.set(t[0])}else t[s]=t[s-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),n.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,n.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}},89403:(t,e,i)=>{i.d(e,{K:()=>s});var r=i(17311);function s(t,e,i){let s=t.getProps();return(0,r.a)(s,e,void 0!==i?i:s.custom,t)}},35400:(t,e,i)=>{i.d(e,{U:()=>a});var r=i(14211),s=i(91607),n=i(89403);function a(t,e){let{transitionEnd:i={},transition:a={},...o}=(0,n.K)(t,e)||{};for(let e in o={...o,...i}){let i=(0,r.K)(o[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,s.OQ)(i))}}},80212:(t,e,i)=>{function r(t,e){-1===t.indexOf(e)&&t.push(e)}function s(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}function n([...t],e,i){let r=e<0?t.length+e:e;if(r>=0&&r<t.length){let r=i<0?t.length+i:i,[s]=t.splice(e,1);t.splice(r,0,s)}return t}i.d(e,{Ai:()=>s,Kq:()=>r,Pe:()=>n})},81650:(t,e,i)=>{i.d(e,{G:()=>u});var r=i(48148),s=i(80590),n=i(95021),a=i(92948),o=i(34929),l=i(47349);function u(t,e,{clamp:i=!0,ease:h,mixer:d}={}){let p=t.length;if((0,r.V)(p===e.length,"Both input and output ranges must be the same length"),1===p)return()=>e[0];if(2===p&&t[0]===t[1])return()=>e[1];t[0]>t[p-1]&&(t=[...t].reverse(),e=[...e].reverse());let c=function(t,e,i){let r=[],s=i||l.j,a=t.length-1;for(let i=0;i<a;i++){let a=s(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||o.l:e;a=(0,n.F)(t,a)}r.push(a)}return r}(e,h,d),m=c.length,f=e=>{let i=0;if(m>1)for(;i<t.length-2&&!(e<t[i+1]);i++);let r=(0,a.q)(t[i],t[i+1],e);return c[i](r)};return i?e=>f((0,s.q)(t[0],t[p-1],e)):f}},28454:(t,e,i)=>{i.d(e,{i:()=>r});let r=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t)},96394:(t,e,i)=>{i.d(e,{$:()=>r});let r=t=>/^0[^.\s]+$/u.test(t)},49907:(t,e,i)=>{i.d(e,{p:()=>r});function r(t){let e;return()=>(void 0===e&&(e=t()),e)}},47349:(t,e,i)=>{i.d(e,{j:()=>T});var r=i(77031),s=i(48148);function n(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var a=i(19042),o=i(30321),l=i(52077);function u(t,e){return i=>i>0?e:t}let h=(t,e,i)=>{let r=t*t,s=i*(e*e-r)+r;return s<0?0:Math.sqrt(s)},d=[a.u,o.B,l.V],p=t=>d.find(e=>e.test(t));function c(t){let e=p(t);if((0,s.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===l.V&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let s=0,a=0,o=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,l=2*i-r;s=n(l,r,t+1/3),a=n(l,r,t),o=n(l,r,t-1/3)}else s=a=o=i;return{red:Math.round(255*s),green:Math.round(255*a),blue:Math.round(255*o),alpha:r}}(i)),i}let m=(t,e)=>{let i=c(t),s=c(e);if(!i||!s)return u(t,e);let n={...i};return t=>(n.red=h(i.red,s.red,t),n.green=h(i.green,s.green,t),n.blue=h(i.blue,s.blue,t),n.alpha=(0,r.k)(i.alpha,s.alpha,t),o.B.transform(n))};var f=i(95021),v=i(40737),g=i(9915),y=i(31354);let b=new Set(["none","hidden"]);function w(t,e){return i=>(0,r.k)(t,e,i)}function S(t){return"number"==typeof t?w:"string"==typeof t?(0,y.p)(t)?u:v.y.test(t)?m:P:Array.isArray(t)?A:"object"==typeof t?v.y.test(t)?m:V:u}function A(t,e){let i=[...t],r=i.length,s=t.map((t,i)=>S(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=s[e](t);return i}}function V(t,e){let i={...t,...e},r={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(r[s]=S(t[s])(t[s],e[s]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let P=(t,e)=>{let i=g.f.createTransformer(e),r=(0,g.V)(t),n=(0,g.V)(e);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?b.has(t)&&!n.values.length||b.has(e)&&!r.values.length?function(t,e){return b.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,f.F)(A(function(t,e){var i;let r=[],s={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let a=e.types[n],o=t.indexes[a][s[a]],l=null!==(i=t.values[o])&&void 0!==i?i:0;r[n]=l,s[a]++}return r}(r,n),n.values),i):((0,s.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),u(t,e))};function T(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,r.k)(t,e,i):S(t)(t,e)}},77031:(t,e,i)=>{i.d(e,{k:()=>r});let r=(t,e,i)=>t+(e-t)*i},71605:(t,e,i)=>{i.d(e,{Z:()=>s});var r=i(10585);function s(t){let e=[0];return(0,r.f)(e,t.length-1),e}},10585:(t,e,i)=>{i.d(e,{f:()=>n});var r=i(77031),s=i(92948);function n(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let a=(0,s.q)(0,e,n);t.push((0,r.k)(i,1,a))}}},95021:(t,e,i)=>{i.d(e,{F:()=>s});let r=(t,e)=>i=>e(t(i)),s=(...t)=>t.reduce(r)},92948:(t,e,i)=>{i.d(e,{q:()=>r});let r=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r}},92021:(t,e,i)=>{i.d(e,{U:()=>n});var r=i(72274),s=i(98126);function n(){if(s.r.current=!0,r.B){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>s.O.current=t.matches;t.addListener(e),e()}else s.O.current=!1}}},98126:(t,e,i)=>{i.d(e,{O:()=>r,r:()=>s});let r={current:null},s={current:!1}},200:(t,e,i)=>{i.d(e,{v:()=>s});var r=i(80212);class s{constructor(){this.subscriptions=[]}add(t){return(0,r.Kq)(this.subscriptions,t),()=>(0,r.Ai)(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r){if(1===r)this.subscriptions[0](t,e,i);else for(let s=0;s<r;s++){let r=this.subscriptions[s];r&&r(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},23851:(t,e,i)=>{i.d(e,{X:()=>s,f:()=>r});let r=t=>1e3*t,s=t=>t/1e3},41210:(t,e,i)=>{i.d(e,{d:()=>r});let r={current:!1}},67531:(t,e,i)=>{i.d(e,{f:()=>r});function r(t,e){return e?1e3/e*t:0}},91607:(t,e,i)=>{i.d(e,{KG:()=>u,OQ:()=>h,bt:()=>l});var r=i(200),s=i(67531),n=i(9468),a=i(39846);let o=t=>!isNaN(parseFloat(t)),l={current:void 0};class u{constructor(t,e={}){this.version="11.11.17",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=n.k.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=n.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=o(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new r.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),a.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return l.current&&l.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=n.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,s.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function h(t,e){return new u(t,e)}},19042:(t,e,i)=>{i.d(e,{u:()=>s});var r=i(30321);let s={test:(0,i(90983).$)("#"),parse:function(t){let e="",i="",r="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,r+=r,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:s?parseInt(s,16)/255:1}},transform:r.B.transform}},52077:(t,e,i)=>{i.d(e,{V:()=>o});var r=i(94242),s=i(82075),n=i(83868),a=i(90983);let o={test:(0,a.$)("hsl","hue"),parse:(0,a.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:a=1})=>"hsla("+Math.round(t)+", "+s.KN.transform((0,n.a)(e))+", "+s.KN.transform((0,n.a)(i))+", "+(0,n.a)(r.X4.transform(a))+")"}},40737:(t,e,i)=>{i.d(e,{y:()=>a});var r=i(19042),s=i(52077),n=i(30321);let a={test:t=>n.B.test(t)||r.u.test(t)||s.V.test(t),parse:t=>n.B.test(t)?n.B.parse(t):s.V.test(t)?s.V.parse(t):r.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?n.B.transform(t):s.V.transform(t)}},30321:(t,e,i)=>{i.d(e,{B:()=>u});var r=i(80590),s=i(94242),n=i(83868),a=i(90983);let o=t=>(0,r.q)(0,255,t),l={...s.ai,transform:t=>Math.round(o(t))},u={test:(0,a.$)("rgb","red"),parse:(0,a.q)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+l.transform(t)+", "+l.transform(e)+", "+l.transform(i)+", "+(0,n.a)(s.X4.transform(r))+")"}},90983:(t,e,i)=>{i.d(e,{$:()=>n,q:()=>a});var r=i(16755);let s=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,n=(t,e)=>i=>!!("string"==typeof i&&s.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),a=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[n,a,o,l]=s.match(r.S);return{[t]:parseFloat(n),[e]:parseFloat(a),[i]:parseFloat(o),alpha:void 0!==l?parseFloat(l):1}}},67414:(t,e,i)=>{i.d(e,{p:()=>l});var r=i(9915),s=i(16755);let n=new Set(["brightness","contrast","saturate","opacity"]);function a(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(s.S)||[];if(!r)return t;let a=i.replace(r,""),o=n.has(e)?1:0;return r!==i&&(o*=100),e+"("+o+a+")"}let o=/\b([a-z-]*)\(.*?\)/gu,l={...r.f,getAnimatableNone:t=>{let e=t.match(o);return e?e.map(a).join(" "):t}}},9915:(t,e,i)=>{i.d(e,{V:()=>h,f:()=>m});var r=i(40737);let s=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var n=i(16755),a=i(83868);let o="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function h(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},n=[],a=0,h=e.replace(u,t=>(r.y.test(t)?(s.color.push(a),n.push(l),i.push(r.y.parse(t))):t.startsWith("var(")?(s.var.push(a),n.push("var"),i.push(t)):(s.number.push(a),n.push(o),i.push(parseFloat(t))),++a,"${}")).split("${}");return{values:i,split:h,indexes:s,types:n}}function d(t){return h(t).values}function p(t){let{split:e,types:i}=h(t),s=e.length;return t=>{let n="";for(let u=0;u<s;u++)if(n+=e[u],void 0!==t[u]){let e=i[u];e===o?n+=(0,a.a)(t[u]):e===l?n+=r.y.transform(t[u]):n+=t[u]}return n}}let c=t=>"number"==typeof t?0:t,m={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(n.S))||void 0===e?void 0:e.length)||0)+((null===(i=t.match(s))||void 0===i?void 0:i.length)||0)>0},parse:d,createTransformer:p,getAnimatableNone:function(t){let e=d(t);return p(t)(e.map(c))}}},16755:(t,e,i)=>{i.d(e,{S:()=>r});let r=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},83868:(t,e,i)=>{i.d(e,{a:()=>r});let r=t=>Math.round(1e5*t)/1e5},1660:(t,e,i)=>{i.d(e,{g:()=>s});var r=i(94222);function s(t,e){let i=t.getValue("willChange");if((0,r.S)(i)&&i.add)return i.add(e)}}};