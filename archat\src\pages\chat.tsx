import React, { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/router';
import { useUser } from '@/context/UserContext';
import { Message, Conversation } from '@/types';
import { loadChatConfig } from '@/config/chatConfig';
import ChatHeader from '@/components/chat/ChatHeader';
import MessageList from '@/components/chat/MessageList';
import ChatInput from '@/components/chat/ChatInput';
import ConversationSidebar from '@/components/chat/ConversationSidebar';
import { generateId } from '@/lib/utils';

const ChatPage: React.FC = () => {
  const router = useRouter();
  const { isAuthenticated, getClient, authState } = useUser();
  
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [mode] = useState<'rag' | 'rag_agent'>('rag_agent'); // Fixed to Agent Mode
  
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login');
    }
  }, [isAuthenticated, router]);

  // Auto-scroll to bottom when new messages are added
  useEffect(() => {
    const scrollToBottom = () => {
      const messagesContainer = document.getElementById('messages-container');
      if (messagesContainer) {
        messagesContainer.scrollTo({
          top: messagesContainer.scrollHeight,
          behavior: 'smooth'
        });
      }
    };

    // Small delay to ensure DOM is updated
    const timeoutId = setTimeout(scrollToBottom, 100);
    return () => clearTimeout(timeoutId);
  }, [messages]);

  const handleSendMessage = async (content: string) => {
    if (!content.trim() || isLoading) return;

    const client = getClient();
    if (!client) {
      setError('No authenticated client available');
      return;
    }

    // Create user message
    const userMessage: Message = {
      id: generateId(),
      content: content.trim(),
      role: 'user',
      timestamp: new Date(),
    };

    // Add user message to the conversation
    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);
    setError(null);

    // Create assistant message placeholder
    const assistantMessage: Message = {
      id: generateId(),
      content: '',
      role: 'assistant',
      timestamp: new Date(),
    };

    try {
      // Load configuration for chat settings
      const config = await loadChatConfig();

      setMessages(prev => [...prev, assistantMessage]);

      // Prepare search settings
      const searchSettings = {
        vectorSearchSettings: {
          enabled: config.vectorSearch.enabled,
          searchLimit: config.vectorSearch.searchLimit,
          searchFilters: JSON.parse(config.vectorSearch.searchFilters),
          indexMeasure: config.vectorSearch.indexMeasure,
          includeMetadatas: config.vectorSearch.includeMetadatas,
          probes: config.vectorSearch.probes,
          efSearch: config.vectorSearch.efSearch,
        },
        hybridSearchSettings: config.hybridSearch.enabled ? {
          fullTextWeight: config.hybridSearch.fullTextWeight,
          semanticWeight: config.hybridSearch.semanticWeight,
          fullTextLimit: config.hybridSearch.fullTextLimit,
          rrfK: config.hybridSearch.rrfK,
        } : undefined,
        kgSearchSettings: config.graphSearch.enabled ? {
          kgSearchLevel: config.graphSearch.kgSearchLevel,
          maxCommunityDescriptionLength: config.graphSearch.maxCommunityDescriptionLength,
          localSearchLimits: config.graphSearch.localSearchLimits,
          maxLlmQueries: config.graphSearch.maxLlmQueries,
        } : undefined,
      };

      // Prepare RAG generation config
      const ragGenerationConfig = {
        stream: true, // 启用流式响应
        temperature: config.ragGeneration.temperature,
        topP: config.ragGeneration.topP,
        topK: config.ragGeneration.topK,
        maxTokensToSample: config.ragGeneration.maxTokensToSample,
        kgTemperature: config.ragGeneration.kgTemperature,
        kgTopP: config.ragGeneration.kgTopP,
        kgTopK: config.ragGeneration.kgTopK,
        kgMaxTokensToSample: config.ragGeneration.kgMaxTokensToSample,
      };

      // Use agent mode (fixed to rag_agent)
      const streamResponse = await client.retrieval.agent({
        message: {
          role: 'user',
          content: content.trim(),
        },
        ragGenerationConfig,
        searchSettings,
        conversationId: currentConversationId || undefined,
      });

      // Handle streaming response
      const reader = streamResponse.getReader();
      const decoder = new TextDecoder();
      let buffer = '';
      let fullResponse = '';

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });

          // Process complete SSE events from the buffer
          const events = buffer.split('\n\n');
          buffer = events.pop() || ''; // Keep the last potentially incomplete event in the buffer

          for (const event of events) {
            if (!event.trim()) continue;

            const lines = event.split('\n');
            const eventType = lines[0].startsWith('event: ') ? lines[0].slice(7) : '';
            const dataLine = lines.find((line) => line.startsWith('data: '));

            if (!dataLine) continue;

            const jsonStr = dataLine.slice(6).trim();

            // Check for stream completion marker
            if (jsonStr === '[DONE]') {
              console.log('Stream completed');
              break;
            }

            // Skip empty data
            if (!jsonStr) continue;

            try {
              const eventData = JSON.parse(jsonStr);

              // Handle search results event
              if (eventType === 'search_results') {
                console.log('Search results received');
                // Handle search results if needed
              }
              // Handle message events with content delta
              else if (eventType === 'message' && eventData.delta && eventData.delta.content) {
                const contentItems = eventData.delta.content;
                for (const item of contentItems) {
                  if (item.type === 'text' && item.payload && item.payload.value) {
                    fullResponse += item.payload.value;
                  }
                }

                // Update the assistant message with streaming content
                setMessages(prev =>
                  prev.map(msg =>
                    msg.id === assistantMessage.id
                      ? { ...msg, content: fullResponse }
                      : msg
                  )
                );
              }
            } catch (err) {
              console.error('Error parsing SSE event data:', err, 'Raw data:', jsonStr);
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      // If this is a new conversation, we might get a conversation ID back
      // For now, we'll generate one if we don't have one
      if (!currentConversationId) {
        setCurrentConversationId(generateId());
      }

    } catch (error) {
      console.error('Error sending message:', error);
      setError(error instanceof Error ? error.message : 'Failed to send message');
      
      // Remove the assistant message placeholder on error
      setMessages(prev => prev.filter(msg => msg.id !== assistantMessage.id));
    } finally {
      setIsLoading(false);
    }
  };

  const handleConversationSelect = (conversationId: string, conversationMessages: Message[]) => {
    setCurrentConversationId(conversationId);
    setMessages(conversationMessages);
    // 不自动关闭侧边栏，让用户手动控制
  };

  const handleNewChat = () => {
    setMessages([]);
    setCurrentConversationId(null);
    setError(null);
    // 不自动关闭侧边栏，让用户手动控制
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleMessageEdit = (messageId: string, newContent: string) => {
    setMessages(prev => 
      prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, content: newContent }
          : msg
      )
    );
  };

  const handleMessageDelete = (messageId: string) => {
    setMessages(prev => prev.filter(msg => msg.id !== messageId));
  };

  if (!isAuthenticated) {
    return null; // Will redirect to login
  }

  return (
    <div className="flex h-screen max-h-screen">
      {/* Conversation History Sidebar */}
      <ConversationSidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        onToggle={toggleSidebar}
        onConversationSelect={handleConversationSelect}
        onNewChat={handleNewChat}
        currentConversationId={currentConversationId}
      />

      {/* Main Chat Area */}
      <div className={`flex flex-col w-full h-screen max-h-screen transition-all duration-200 ease-in-out ${
        sidebarOpen ? 'md:max-w-[calc(100%-260px)]' : 'max-w-full'
      }`}>
        {/* Header */}
        <div className="flex-shrink-0">
          <ChatHeader
            onSidebarToggle={toggleSidebar}
            sidebarOpen={sidebarOpen}
            mode={mode}
          />
        </div>

        {/* Scrollable Messages Area */}
        <div className="flex-1 overflow-hidden relative">
          <div
            className="absolute inset-0 overflow-y-auto scrollbar-thin"
            id="messages-container"
          >
            <MessageList
              messages={messages}
              isLoading={isLoading}
              onMessageEdit={handleMessageEdit}
              onMessageDelete={handleMessageDelete}
            />
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Input Area */}
        <div className="flex-shrink-0">
          <ChatInput
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            error={error}
            onClearError={() => setError(null)}
          />
        </div>
      </div>
    </div>
  );
};

export default ChatPage;
