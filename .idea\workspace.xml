<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1e5394b5-758d-490c-b426-bf117fe53592" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
        <option value="JavaScript File" />
      </list>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2yrESLMhmoYIKNp7ZoYaFnbIFDR" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;JavaScript Debug.Unnamed.executor&quot;: &quot;Debug&quot;,
    &quot;Node.js.r2rsdkjsTest.js.executor&quot;: &quot;Debug&quot;,
    &quot;Python.r2rsdkTest.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/OpenSource/Speech/CosyVoice&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;D:\\LLM\\Learning\\ai_coding\\anythingchat\\archat\\node_modules\\prettier&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\LLM\\Learning\\ai_coding\\anythingchat\\archat\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\LLM\Learning\ai_coding\anythingchat\frontend" />
    </key>
  </component>
  <component name="RunManager" selected="Python.r2rsdkTest">
    <configuration name="Unnamed" type="JavascriptDebugType" nameIsGenerated="true" uri="http://localhost:3008">
      <method v="2" />
    </configuration>
    <configuration name="r2rsdkjsTest.js" type="NodeJSConfigurationType" temporary="true" nameIsGenerated="true" path-to-js-file="$PROJECT_DIR$/frontend/r2rsdkjsTest.js" working-dir="$PROJECT_DIR$/backend">
      <method v="2" />
    </configuration>
    <configuration name="r2rsdkTest" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="anythingchat" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/backend" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/backend/r2rsdkTest.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="JavaScript Debug.Unnamed" />
      <item itemvalue="Node.js.r2rsdkjsTest.js" />
      <item itemvalue="Python.r2rsdkTest" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.r2rsdkTest" />
        <item itemvalue="Node.js.r2rsdkjsTest.js" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-822845ee3bb5-JavaScript-PY-243.23654.177" />
        <option value="bundled-python-sdk-91d3a02ef49d-43b77aa2d136-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.23654.177" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1e5394b5-758d-490c-b426-bf117fe53592" name="Changes" comment="" />
      <created>1750584290966</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750584290966</updated>
      <workItem from="1750584292076" duration="5349000" />
      <workItem from="1750602118068" duration="1264000" />
      <workItem from="1750648261904" duration="974000" />
      <workItem from="1751065689291" duration="81000" />
      <workItem from="1751065789537" duration="175000" />
      <workItem from="1751109336717" duration="681000" />
      <workItem from="1751240807503" duration="1806000" />
      <workItem from="1751299498953" duration="475000" />
      <workItem from="1751357337938" duration="1750000" />
      <workItem from="1751380317528" duration="6556000" />
      <workItem from="1751414313807" duration="4965000" />
      <workItem from="1751422849118" duration="7870000" />
      <workItem from="1751549047543" duration="2611000" />
      <workItem from="1751598816189" duration="1248000" />
      <workItem from="1751643347557" duration="870000" />
      <workItem from="1751813143905" duration="2071000" />
      <workItem from="1751900112437" duration="684000" />
      <workItem from="1751901097585" duration="2080000" />
      <workItem from="1752293530461" duration="152000" />
      <workItem from="1752736400489" duration="4488000" />
      <workItem from="1752809303437" duration="1877000" />
      <workItem from="1752852870230" duration="1434000" />
      <workItem from="1752889090372" duration="6596000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/backend/r2rsdkTest.py</url>
          <line>4</line>
          <option name="timeStamp" value="2" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/backend/r2rsdkTest.py</url>
          <line>9</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/chatfrontend/src/context/UserContext.tsx</url>
          <line>131</line>
          <option name="timeStamp" value="6" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/frontend/r2rsdkjsTest.js</url>
          <line>3</line>
          <option name="timeStamp" value="7" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/frontend/r2rsdkjsTest.js</url>
          <line>1</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/frontend/src/context/UserContext.tsx</url>
          <line>128</line>
          <option name="timeStamp" value="9" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/archat/src/context/UserContext.tsx</url>
          <line>102</line>
          <option name="timeStamp" value="14" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/archat/src/context/UserContext.tsx</url>
          <line>108</line>
          <option name="timeStamp" value="15" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/anythingchat$r2rsdkTest.coverage" NAME="r2rsdkTest Coverage Results" MODIFIED="1752896000094" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/backend" />
  </component>
</project>