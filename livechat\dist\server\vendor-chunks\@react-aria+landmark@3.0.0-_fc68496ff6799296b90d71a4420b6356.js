"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+landmark@3.0.0-_fc68496ff6799296b90d71a4420b6356";
exports.ids = ["vendor-chunks/@react-aria+landmark@3.0.0-_fc68496ff6799296b90d71a4420b6356"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+landmark@3.0.0-_fc68496ff6799296b90d71a4420b6356/node_modules/@react-aria/landmark/dist/useLandmark.mjs":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+landmark@3.0.0-_fc68496ff6799296b90d71a4420b6356/node_modules/@react-aria/landmark/dist/useLandmark.mjs ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createLandmarkController: () => (/* binding */ $a86207c5d7f7e1fb$export$f50151dbd51cd1d9),\n/* harmony export */   useLandmark: () => (/* binding */ $a86207c5d7f7e1fb$export$4cc632584fd87fae)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/useLayoutEffect.mjs\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/.pnpm/use-sync-external-store@1.4_06df1f58d3499d54e9960e81cb270864/node_modules/use-sync-external-store/shim/index.js\");\n\n\n\n\n/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n// Increment this version number whenever the\n// LandmarkManagerApi or Landmark interfaces change.\nconst $a86207c5d7f7e1fb$var$LANDMARK_API_VERSION = 1;\n// Symbol under which the singleton landmark manager instance is attached to the document.\nconst $a86207c5d7f7e1fb$var$landmarkSymbol = Symbol.for('react-aria-landmark-manager');\nfunction $a86207c5d7f7e1fb$var$subscribe(fn) {\n    document.addEventListener('react-aria-landmark-manager-change', fn);\n    return ()=>document.removeEventListener('react-aria-landmark-manager-change', fn);\n}\nfunction $a86207c5d7f7e1fb$var$getLandmarkManager() {\n    if (typeof document === 'undefined') return null;\n    // Reuse an existing instance if it has the same or greater version.\n    let instance = document[$a86207c5d7f7e1fb$var$landmarkSymbol];\n    if (instance && instance.version >= $a86207c5d7f7e1fb$var$LANDMARK_API_VERSION) return instance;\n    // Otherwise, create a new instance and dispatch an event so anything using the existing\n    // instance updates and re-registers their landmarks with the new one.\n    document[$a86207c5d7f7e1fb$var$landmarkSymbol] = new $a86207c5d7f7e1fb$var$LandmarkManager();\n    document.dispatchEvent(new CustomEvent('react-aria-landmark-manager-change'));\n    return document[$a86207c5d7f7e1fb$var$landmarkSymbol];\n}\n// Subscribes a React component to the current landmark manager instance.\nfunction $a86207c5d7f7e1fb$var$useLandmarkManager() {\n    return (0, use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)($a86207c5d7f7e1fb$var$subscribe, $a86207c5d7f7e1fb$var$getLandmarkManager, $a86207c5d7f7e1fb$var$getLandmarkManager);\n}\nclass $a86207c5d7f7e1fb$var$LandmarkManager {\n    setupIfNeeded() {\n        if (this.isListening) return;\n        document.addEventListener('keydown', this.f6Handler, {\n            capture: true\n        });\n        document.addEventListener('focusin', this.focusinHandler, {\n            capture: true\n        });\n        document.addEventListener('focusout', this.focusoutHandler, {\n            capture: true\n        });\n        this.isListening = true;\n    }\n    teardownIfNeeded() {\n        if (!this.isListening || this.landmarks.length > 0 || this.refCount > 0) return;\n        document.removeEventListener('keydown', this.f6Handler, {\n            capture: true\n        });\n        document.removeEventListener('focusin', this.focusinHandler, {\n            capture: true\n        });\n        document.removeEventListener('focusout', this.focusoutHandler, {\n            capture: true\n        });\n        this.isListening = false;\n    }\n    focusLandmark(landmark, direction) {\n        var _this_landmarks_find_focus, _this_landmarks_find;\n        (_this_landmarks_find = this.landmarks.find((l)=>l.ref.current === landmark)) === null || _this_landmarks_find === void 0 ? void 0 : (_this_landmarks_find_focus = _this_landmarks_find.focus) === null || _this_landmarks_find_focus === void 0 ? void 0 : _this_landmarks_find_focus.call(_this_landmarks_find, direction);\n    }\n    /**\n   * Return set of landmarks with a specific role.\n   */ getLandmarksByRole(role) {\n        return new Set(this.landmarks.filter((l)=>l.role === role));\n    }\n    /**\n   * Return first landmark with a specific role.\n   */ getLandmarkByRole(role) {\n        return this.landmarks.find((l)=>l.role === role);\n    }\n    addLandmark(newLandmark) {\n        this.setupIfNeeded();\n        if (this.landmarks.find((landmark)=>landmark.ref === newLandmark.ref) || !newLandmark.ref.current) return;\n        if (this.landmarks.filter((landmark)=>landmark.role === 'main').length > 1) console.error('Page can contain no more than one landmark with the role \"main\".');\n        if (this.landmarks.length === 0) {\n            this.landmarks = [\n                newLandmark\n            ];\n            this.checkLabels(newLandmark.role);\n            return;\n        }\n        // Binary search to insert new landmark based on position in document relative to existing landmarks.\n        // https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n        let start = 0;\n        let end = this.landmarks.length - 1;\n        while(start <= end){\n            let mid = Math.floor((start + end) / 2);\n            let comparedPosition = newLandmark.ref.current.compareDocumentPosition(this.landmarks[mid].ref.current);\n            let isNewAfterExisting = Boolean(comparedPosition & Node.DOCUMENT_POSITION_PRECEDING || comparedPosition & Node.DOCUMENT_POSITION_CONTAINS);\n            if (isNewAfterExisting) start = mid + 1;\n            else end = mid - 1;\n        }\n        this.landmarks.splice(start, 0, newLandmark);\n        this.checkLabels(newLandmark.role);\n    }\n    updateLandmark(landmark) {\n        let index = this.landmarks.findIndex((l)=>l.ref === landmark.ref);\n        if (index >= 0) {\n            this.landmarks[index] = {\n                ...this.landmarks[index],\n                ...landmark\n            };\n            this.checkLabels(this.landmarks[index].role);\n        }\n    }\n    removeLandmark(ref) {\n        this.landmarks = this.landmarks.filter((landmark)=>landmark.ref !== ref);\n        this.teardownIfNeeded();\n    }\n    /**\n   * Warn if there are 2+ landmarks with the same role but no label.\n   * Labels for landmarks with the same role must also be unique.\n   *\n   * See https://www.w3.org/WAI/ARIA/apg/practices/landmark-regions/.\n   */ checkLabels(role) {\n        let landmarksWithRole = this.getLandmarksByRole(role);\n        if (landmarksWithRole.size > 1) {\n            let duplicatesWithoutLabel = [\n                ...landmarksWithRole\n            ].filter((landmark)=>!landmark.label);\n            if (duplicatesWithoutLabel.length > 0) console.warn(`Page contains more than one landmark with the '${role}' role. If two or more landmarks on a page share the same role, all must be labeled with an aria-label or aria-labelledby attribute: `, duplicatesWithoutLabel.map((landmark)=>landmark.ref.current));\n            else {\n                let labels = [\n                    ...landmarksWithRole\n                ].map((landmark)=>landmark.label);\n                let duplicateLabels = labels.filter((item, index)=>labels.indexOf(item) !== index);\n                duplicateLabels.forEach((label)=>{\n                    console.warn(`Page contains more than one landmark with the '${role}' role and '${label}' label. If two or more landmarks on a page share the same role, they must have unique labels: `, [\n                        ...landmarksWithRole\n                    ].filter((landmark)=>landmark.label === label).map((landmark)=>landmark.ref.current));\n                });\n            }\n        }\n    }\n    /**\n   * Get the landmark that is the closest parent in the DOM.\n   * Returns undefined if no parent is a landmark.\n   */ closestLandmark(element) {\n        let landmarkMap = new Map(this.landmarks.map((l)=>[\n                l.ref.current,\n                l\n            ]));\n        let currentElement = element;\n        while(currentElement && !landmarkMap.has(currentElement) && currentElement !== document.body && currentElement.parentElement)currentElement = currentElement.parentElement;\n        return landmarkMap.get(currentElement);\n    }\n    /**\n   * Gets the next landmark, in DOM focus order, or previous if backwards is specified.\n   * If last landmark, next should be the first landmark.\n   * If not inside a landmark, will return first landmark.\n   * Returns undefined if there are no landmarks.\n   */ getNextLandmark(element, { backward: backward }) {\n        var _this_landmarks_nextLandmarkIndex_ref_current;\n        let currentLandmark = this.closestLandmark(element);\n        let nextLandmarkIndex = backward ? this.landmarks.length - 1 : 0;\n        if (currentLandmark) nextLandmarkIndex = this.landmarks.indexOf(currentLandmark) + (backward ? -1 : 1);\n        let wrapIfNeeded = ()=>{\n            // When we reach the end of the landmark sequence, fire a custom event that can be listened for by applications.\n            // If this event is canceled, we return immediately. This can be used to implement landmark navigation across iframes.\n            if (nextLandmarkIndex < 0) {\n                if (!element.dispatchEvent(new CustomEvent('react-aria-landmark-navigation', {\n                    detail: {\n                        direction: 'backward'\n                    },\n                    bubbles: true,\n                    cancelable: true\n                }))) return true;\n                nextLandmarkIndex = this.landmarks.length - 1;\n            } else if (nextLandmarkIndex >= this.landmarks.length) {\n                if (!element.dispatchEvent(new CustomEvent('react-aria-landmark-navigation', {\n                    detail: {\n                        direction: 'forward'\n                    },\n                    bubbles: true,\n                    cancelable: true\n                }))) return true;\n                nextLandmarkIndex = 0;\n            }\n            if (nextLandmarkIndex < 0 || nextLandmarkIndex >= this.landmarks.length) return true;\n            return false;\n        };\n        if (wrapIfNeeded()) return undefined;\n        // Skip over hidden landmarks.\n        let i = nextLandmarkIndex;\n        while((_this_landmarks_nextLandmarkIndex_ref_current = this.landmarks[nextLandmarkIndex].ref.current) === null || _this_landmarks_nextLandmarkIndex_ref_current === void 0 ? void 0 : _this_landmarks_nextLandmarkIndex_ref_current.closest('[aria-hidden=true]')){\n            nextLandmarkIndex += backward ? -1 : 1;\n            if (wrapIfNeeded()) return undefined;\n            if (nextLandmarkIndex === i) break;\n        }\n        return this.landmarks[nextLandmarkIndex];\n    }\n    /**\n   * Look at next landmark. If an element was previously focused inside, restore focus there.\n   * If not, focus the landmark itself.\n   * If no landmarks at all, or none with focusable elements, don't move focus.\n   */ f6Handler(e) {\n        if (e.key === 'F6') {\n            // If alt key pressed, focus main landmark, otherwise navigate forward or backward based on shift key.\n            let handled = e.altKey ? this.focusMain() : this.navigate(e.target, e.shiftKey);\n            if (handled) {\n                e.preventDefault();\n                e.stopPropagation();\n            }\n        }\n    }\n    focusMain() {\n        let main = this.getLandmarkByRole('main');\n        if (main && main.ref.current && document.contains(main.ref.current)) {\n            this.focusLandmark(main.ref.current, 'forward');\n            return true;\n        }\n        return false;\n    }\n    navigate(from, backward) {\n        let nextLandmark = this.getNextLandmark(from, {\n            backward: backward\n        });\n        if (!nextLandmark) return false;\n        // If something was previously focused in the next landmark, then return focus to it\n        if (nextLandmark.lastFocused) {\n            let lastFocused = nextLandmark.lastFocused;\n            if (document.body.contains(lastFocused)) {\n                lastFocused.focus();\n                return true;\n            }\n        }\n        // Otherwise, focus the landmark itself\n        if (nextLandmark.ref.current && document.contains(nextLandmark.ref.current)) {\n            this.focusLandmark(nextLandmark.ref.current, backward ? 'backward' : 'forward');\n            return true;\n        }\n        return false;\n    }\n    /**\n   * Sets lastFocused for a landmark, if focus is moved within that landmark.\n   * Lets the last focused landmark know it was blurred if something else is focused.\n   */ focusinHandler(e) {\n        let currentLandmark = this.closestLandmark(e.target);\n        if (currentLandmark && currentLandmark.ref.current !== e.target) this.updateLandmark({\n            ref: currentLandmark.ref,\n            lastFocused: e.target\n        });\n        let previousFocusedElement = e.relatedTarget;\n        if (previousFocusedElement) {\n            let closestPreviousLandmark = this.closestLandmark(previousFocusedElement);\n            if (closestPreviousLandmark && closestPreviousLandmark.ref.current === previousFocusedElement) closestPreviousLandmark.blur();\n        }\n    }\n    /**\n   * Track if the focus is lost to the body. If it is, do cleanup on the landmark that last had focus.\n   */ focusoutHandler(e) {\n        let previousFocusedElement = e.target;\n        let nextFocusedElement = e.relatedTarget;\n        // the === document seems to be a jest thing for focus to go there on generic blur event such as landmark.blur();\n        // browsers appear to send focus instead to document.body and the relatedTarget is null when that happens\n        if (!nextFocusedElement || nextFocusedElement === document) {\n            let closestPreviousLandmark = this.closestLandmark(previousFocusedElement);\n            if (closestPreviousLandmark && closestPreviousLandmark.ref.current === previousFocusedElement) closestPreviousLandmark.blur();\n        }\n    }\n    createLandmarkController() {\n        let instance = this;\n        instance.refCount++;\n        instance.setupIfNeeded();\n        return {\n            navigate (direction, opts) {\n                let element = (opts === null || opts === void 0 ? void 0 : opts.from) || document.activeElement;\n                return instance.navigate(element, direction === 'backward');\n            },\n            focusNext (opts) {\n                let element = (opts === null || opts === void 0 ? void 0 : opts.from) || document.activeElement;\n                return instance.navigate(element, false);\n            },\n            focusPrevious (opts) {\n                let element = (opts === null || opts === void 0 ? void 0 : opts.from) || document.activeElement;\n                return instance.navigate(element, true);\n            },\n            focusMain () {\n                return instance.focusMain();\n            },\n            dispose () {\n                if (instance) {\n                    instance.refCount--;\n                    instance.teardownIfNeeded();\n                    instance = null;\n                }\n            }\n        };\n    }\n    registerLandmark(landmark) {\n        if (this.landmarks.find((l)=>l.ref === landmark.ref)) this.updateLandmark(landmark);\n        else this.addLandmark(landmark);\n        return ()=>this.removeLandmark(landmark.ref);\n    }\n    constructor(){\n        this.landmarks = [];\n        this.isListening = false;\n        this.refCount = 0;\n        this.version = $a86207c5d7f7e1fb$var$LANDMARK_API_VERSION;\n        this.f6Handler = this.f6Handler.bind(this);\n        this.focusinHandler = this.focusinHandler.bind(this);\n        this.focusoutHandler = this.focusoutHandler.bind(this);\n    }\n}\nfunction $a86207c5d7f7e1fb$export$f50151dbd51cd1d9() {\n    // Get the current landmark manager and create a controller using it.\n    let instance = $a86207c5d7f7e1fb$var$getLandmarkManager();\n    let controller = instance === null || instance === void 0 ? void 0 : instance.createLandmarkController();\n    let unsubscribe = $a86207c5d7f7e1fb$var$subscribe(()=>{\n        // If the landmark manager changes, dispose the old\n        // controller and create a new one.\n        controller === null || controller === void 0 ? void 0 : controller.dispose();\n        instance = $a86207c5d7f7e1fb$var$getLandmarkManager();\n        controller = instance === null || instance === void 0 ? void 0 : instance.createLandmarkController();\n    });\n    // Return a wrapper that proxies requests to the current controller instance.\n    return {\n        navigate (direction, opts) {\n            return controller.navigate(direction, opts);\n        },\n        focusNext (opts) {\n            return controller.focusNext(opts);\n        },\n        focusPrevious (opts) {\n            return controller.focusPrevious(opts);\n        },\n        focusMain () {\n            return controller.focusMain();\n        },\n        dispose () {\n            controller === null || controller === void 0 ? void 0 : controller.dispose();\n            unsubscribe();\n            controller = undefined;\n            instance = null;\n        }\n    };\n}\nfunction $a86207c5d7f7e1fb$export$4cc632584fd87fae(props, ref) {\n    const { role: role, 'aria-label': ariaLabel, 'aria-labelledby': ariaLabelledby, focus: focus } = props;\n    let manager = $a86207c5d7f7e1fb$var$useLandmarkManager();\n    let label = ariaLabel || ariaLabelledby;\n    let [isLandmarkFocused, setIsLandmarkFocused] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    let defaultFocus = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setIsLandmarkFocused(true);\n    }, [\n        setIsLandmarkFocused\n    ]);\n    let blur = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setIsLandmarkFocused(false);\n    }, [\n        setIsLandmarkFocused\n    ]);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        if (manager) return manager.registerLandmark({\n            ref: ref,\n            label: label,\n            role: role,\n            focus: focus || defaultFocus,\n            blur: blur\n        });\n    }, [\n        manager,\n        label,\n        ref,\n        role,\n        focus,\n        defaultFocus,\n        blur\n    ]);\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _ref_current;\n        if (isLandmarkFocused) (_ref_current = ref.current) === null || _ref_current === void 0 ? void 0 : _ref_current.focus();\n    }, [\n        isLandmarkFocused,\n        ref\n    ]);\n    return {\n        landmarkProps: {\n            role: role,\n            tabIndex: isLandmarkFocused ? -1 : undefined,\n            'aria-label': ariaLabel,\n            'aria-labelledby': ariaLabelledby\n        }\n    };\n}\n\n\n\n//# sourceMappingURL=useLandmark.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+landmark@3.0.0-_fc68496ff6799296b90d71a4420b6356/node_modules/@react-aria/landmark/dist/useLandmark.mjs\n");

/***/ })

};
;