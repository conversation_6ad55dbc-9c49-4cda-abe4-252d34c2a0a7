"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+overlays@3.26.1_881a4771d98afc507628262737cf6450";
exports.ids = ["vendor-chunks/@react-aria+overlays@3.26.1_881a4771d98afc507628262737cf6450"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.26.1_881a4771d98afc507628262737cf6450/node_modules/@react-aria/overlays/dist/ariaHideOutside.mjs":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+overlays@3.26.1_881a4771d98afc507628262737cf6450/node_modules/@react-aria/overlays/dist/ariaHideOutside.mjs ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ariaHideOutside: () => (/* binding */ $5e3802645cc19319$export$1c3ebcada18427bf),\n/* harmony export */   keepVisible: () => (/* binding */ $5e3802645cc19319$export$1020fa7f77e17884)\n/* harmony export */ });\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // Keeps a ref count of all hidden elements. Added to when hiding an element, and\n// subtracted from when showing it again. When it reaches zero, aria-hidden is removed.\nlet $5e3802645cc19319$var$refCountMap = new WeakMap();\nlet $5e3802645cc19319$var$observerStack = [];\nfunction $5e3802645cc19319$export$1c3ebcada18427bf(targets, root = document.body) {\n    let visibleNodes = new Set(targets);\n    let hiddenNodes = new Set();\n    let walk = (root)=>{\n        // Keep live announcer and top layer elements (e.g. toasts) visible.\n        for (let element of root.querySelectorAll('[data-live-announcer], [data-react-aria-top-layer]'))visibleNodes.add(element);\n        let acceptNode = (node)=>{\n            // Skip this node and its children if it is one of the target nodes, or a live announcer.\n            // Also skip children of already hidden nodes, as aria-hidden is recursive. An exception is\n            // made for elements with role=\"row\" since VoiceOver on iOS has issues hiding elements with role=\"row\".\n            // For that case we want to hide the cells inside as well (https://bugs.webkit.org/show_bug.cgi?id=222623).\n            if (visibleNodes.has(node) || node.parentElement && hiddenNodes.has(node.parentElement) && node.parentElement.getAttribute('role') !== 'row') return NodeFilter.FILTER_REJECT;\n            // Skip this node but continue to children if one of the targets is inside the node.\n            for (let target of visibleNodes){\n                if (node.contains(target)) return NodeFilter.FILTER_SKIP;\n            }\n            return NodeFilter.FILTER_ACCEPT;\n        };\n        let walker = document.createTreeWalker(root, NodeFilter.SHOW_ELEMENT, {\n            acceptNode: acceptNode\n        });\n        // TreeWalker does not include the root.\n        let acceptRoot = acceptNode(root);\n        if (acceptRoot === NodeFilter.FILTER_ACCEPT) hide(root);\n        if (acceptRoot !== NodeFilter.FILTER_REJECT) {\n            let node = walker.nextNode();\n            while(node != null){\n                hide(node);\n                node = walker.nextNode();\n            }\n        }\n    };\n    let hide = (node)=>{\n        var _refCountMap_get;\n        let refCount = (_refCountMap_get = $5e3802645cc19319$var$refCountMap.get(node)) !== null && _refCountMap_get !== void 0 ? _refCountMap_get : 0;\n        // If already aria-hidden, and the ref count is zero, then this element\n        // was already hidden and there's nothing for us to do.\n        if (node.getAttribute('aria-hidden') === 'true' && refCount === 0) return;\n        if (refCount === 0) node.setAttribute('aria-hidden', 'true');\n        hiddenNodes.add(node);\n        $5e3802645cc19319$var$refCountMap.set(node, refCount + 1);\n    };\n    // If there is already a MutationObserver listening from a previous call,\n    // disconnect it so the new on takes over.\n    if ($5e3802645cc19319$var$observerStack.length) $5e3802645cc19319$var$observerStack[$5e3802645cc19319$var$observerStack.length - 1].disconnect();\n    walk(root);\n    let observer = new MutationObserver((changes)=>{\n        for (let change of changes){\n            if (change.type !== 'childList' || change.addedNodes.length === 0) continue;\n            // If the parent element of the added nodes is not within one of the targets,\n            // and not already inside a hidden node, hide all of the new children.\n            if (![\n                ...visibleNodes,\n                ...hiddenNodes\n            ].some((node)=>node.contains(change.target))) {\n                for (let node of change.removedNodes)if (node instanceof Element) {\n                    visibleNodes.delete(node);\n                    hiddenNodes.delete(node);\n                }\n                for (let node of change.addedNodes){\n                    if ((node instanceof HTMLElement || node instanceof SVGElement) && (node.dataset.liveAnnouncer === 'true' || node.dataset.reactAriaTopLayer === 'true')) visibleNodes.add(node);\n                    else if (node instanceof Element) walk(node);\n                }\n            }\n        }\n    });\n    observer.observe(root, {\n        childList: true,\n        subtree: true\n    });\n    let observerWrapper = {\n        visibleNodes: visibleNodes,\n        hiddenNodes: hiddenNodes,\n        observe () {\n            observer.observe(root, {\n                childList: true,\n                subtree: true\n            });\n        },\n        disconnect () {\n            observer.disconnect();\n        }\n    };\n    $5e3802645cc19319$var$observerStack.push(observerWrapper);\n    return ()=>{\n        observer.disconnect();\n        for (let node of hiddenNodes){\n            let count = $5e3802645cc19319$var$refCountMap.get(node);\n            if (count == null) continue;\n            if (count === 1) {\n                node.removeAttribute('aria-hidden');\n                $5e3802645cc19319$var$refCountMap.delete(node);\n            } else $5e3802645cc19319$var$refCountMap.set(node, count - 1);\n        }\n        // Remove this observer from the stack, and start the previous one.\n        if (observerWrapper === $5e3802645cc19319$var$observerStack[$5e3802645cc19319$var$observerStack.length - 1]) {\n            $5e3802645cc19319$var$observerStack.pop();\n            if ($5e3802645cc19319$var$observerStack.length) $5e3802645cc19319$var$observerStack[$5e3802645cc19319$var$observerStack.length - 1].observe();\n        } else $5e3802645cc19319$var$observerStack.splice($5e3802645cc19319$var$observerStack.indexOf(observerWrapper), 1);\n    };\n}\nfunction $5e3802645cc19319$export$1020fa7f77e17884(element) {\n    let observer = $5e3802645cc19319$var$observerStack[$5e3802645cc19319$var$observerStack.length - 1];\n    if (observer && !observer.visibleNodes.has(element)) {\n        observer.visibleNodes.add(element);\n        return ()=>{\n            observer.visibleNodes.delete(element);\n        };\n    }\n}\n\n\n\n//# sourceMappingURL=ariaHideOutside.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.26.1_881a4771d98afc507628262737cf6450/node_modules/@react-aria/overlays/dist/ariaHideOutside.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.26.1_881a4771d98afc507628262737cf6450/node_modules/@react-aria/overlays/dist/useCloseOnScroll.mjs":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+overlays@3.26.1_881a4771d98afc507628262737cf6450/node_modules/@react-aria/overlays/dist/useCloseOnScroll.mjs ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   onCloseMap: () => (/* binding */ $dd149f63282afbbf$export$f6211563215e3b37),\n/* harmony export */   useCloseOnScroll: () => (/* binding */ $dd149f63282afbbf$export$18fc8428861184da)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \nconst $dd149f63282afbbf$export$f6211563215e3b37 = new WeakMap();\nfunction $dd149f63282afbbf$export$18fc8428861184da(opts) {\n    let { triggerRef: triggerRef, isOpen: isOpen, onClose: onClose } = opts;\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!isOpen || onClose === null) return;\n        let onScroll = (e)=>{\n            // Ignore if scrolling an scrollable region outside the trigger's tree.\n            let target = e.target;\n            // window is not a Node and doesn't have contain, but window contains everything\n            if (!triggerRef.current || target instanceof Node && !target.contains(triggerRef.current)) return;\n            // Ignore scroll events on any input or textarea as the cursor position can cause it to scroll\n            // such as in a combobox. Clicking the dropdown button places focus on the input, and if the\n            // text inside the input extends beyond the 'end', then it will scroll so the cursor is visible at the end.\n            if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) return;\n            let onCloseHandler = onClose || $dd149f63282afbbf$export$f6211563215e3b37.get(triggerRef.current);\n            if (onCloseHandler) onCloseHandler();\n        };\n        window.addEventListener('scroll', onScroll, true);\n        return ()=>{\n            window.removeEventListener('scroll', onScroll, true);\n        };\n    }, [\n        isOpen,\n        onClose,\n        triggerRef\n    ]);\n}\n\n\n\n//# sourceMappingURL=useCloseOnScroll.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.26.1_881a4771d98afc507628262737cf6450/node_modules/@react-aria/overlays/dist/useCloseOnScroll.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.26.1_881a4771d98afc507628262737cf6450/node_modules/@react-aria/overlays/dist/useOverlayTrigger.mjs":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+overlays@3.26.1_881a4771d98afc507628262737cf6450/node_modules/@react-aria/overlays/dist/useOverlayTrigger.mjs ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOverlayTrigger: () => (/* binding */ $628037886ba31236$export$f9d5c8beee7d008d)\n/* harmony export */ });\n/* harmony import */ var _useCloseOnScroll_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./useCloseOnScroll.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.26.1_881a4771d98afc507628262737cf6450/node_modules/@react-aria/overlays/dist/useCloseOnScroll.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/useId.mjs\");\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\nfunction $628037886ba31236$export$f9d5c8beee7d008d(props, state, ref) {\n    let { type: type } = props;\n    let { isOpen: isOpen } = state;\n    // Backward compatibility. Share state close function with useOverlayPosition so it can close on scroll\n    // without forcing users to pass onClose.\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (ref && ref.current) (0, _useCloseOnScroll_mjs__WEBPACK_IMPORTED_MODULE_1__.onCloseMap).set(ref.current, state.close);\n    });\n    // Aria 1.1 supports multiple values for aria-haspopup other than just menus.\n    // https://www.w3.org/TR/wai-aria-1.1/#aria-haspopup\n    // However, we only add it for menus for now because screen readers often\n    // announce it as a menu even for other values.\n    let ariaHasPopup = undefined;\n    if (type === 'menu') ariaHasPopup = true;\n    else if (type === 'listbox') ariaHasPopup = 'listbox';\n    let overlayId = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.useId)();\n    return {\n        triggerProps: {\n            'aria-haspopup': ariaHasPopup,\n            'aria-expanded': isOpen,\n            'aria-controls': isOpen ? overlayId : undefined,\n            onPress: state.toggle\n        },\n        overlayProps: {\n            id: overlayId\n        }\n    };\n}\n\n\n\n//# sourceMappingURL=useOverlayTrigger.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.26.1_881a4771d98afc507628262737cf6450/node_modules/@react-aria/overlays/dist/useOverlayTrigger.mjs\n");

/***/ })

};
;