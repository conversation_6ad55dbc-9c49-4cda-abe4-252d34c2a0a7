(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{3767:()=>{},2208:()=>{},21949:(e,t,r)=>{Promise.resolve().then(r.bind(r,40744)),Promise.resolve().then(r.bind(r,84825)),Promise.resolve().then(r.t.bind(r,16040,23)),Promise.resolve().then(r.t.bind(r,20983,23))},40744:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>g});var l=r(12389),s=r(58148),o=r(63828),n=r(73290),a=r(28523),i=r(41473),c=r(79864),u=r(17567),d=e=>{let{children:t,navigate:r,disableAnimation:d,useHref:h,disableRipple:m=!1,skipFramerMotionAnimations:v=d,reducedMotion:g="never",validationBehavior:k,locale:f="en-US",labelPlacement:w,defaultDates:p,createCalendar:y,spinnerVariant:S,...x}=e,I=t;r&&(I=(0,l.jsx)(n.pg,{navigate:r,useHref:h,children:I}));let b=(0,i.useMemo)(()=>(d&&v&&(c.W.skipAnimations=!0),{createCalendar:y,defaultDates:p,disableAnimation:d,disableRipple:m,validationBehavior:k,labelPlacement:w,spinnerVariant:S}),[y,null==p?void 0:p.maxDate,null==p?void 0:p.minDate,d,m,k,w,S]);return(0,l.jsx)(s.n,{value:b,children:(0,l.jsx)(o.C,{locale:f,children:(0,l.jsx)(u.x,{reducedMotion:g,children:(0,l.jsx)(a.so,{...x,children:I})})})})},h=r(25140),m=r(75664),v=r(86037);function g(e){let{children:t}=e,r=(0,m.useRouter)();return(0,l.jsxs)(d,{navigate:r.push,children:[(0,l.jsx)(h.tE,{placement:"top-right",toastProps:{timeout:3e3}}),(0,l.jsx)(v.v,{children:t})]})}},86037:(e,t,r)=>{"use strict";r.d(t,{J:()=>a,v:()=>i});var l=r(12389),s=r(41473),o=r(90959);let n=(0,s.createContext)({pipeline:null,setPipeline:()=>{},selectedModel:"null",setSelectedModel:()=>{},isAuthenticated:!1,login:async()=>({success:!1,userRole:"user"}),loginWithToken:async()=>({success:!1,userRole:"user"}),logout:async()=>{},unsetCredentials:async()=>{},register:async()=>{},authState:{isAuthenticated:!1,email:null,userRole:null,userId:null},getClient:()=>null,client:null,viewMode:"user",setViewMode:()=>{},isSuperUser:()=>!1,createUser:async()=>{throw Error("createUser is not implemented in the default context")}}),a=()=>{let e=(0,s.useContext)(n);if(!e)throw Error("useUser must be used within a UserProvider");return e},i=e=>{let{children:t}=e,[a,i]=(0,s.useState)(null),[c,u]=(0,s.useState)(null),[d,h]=(0,s.useState)("null"),[m,v]=(0,s.useState)("user"),[g,k]=(0,s.useState)({isAuthenticated:!1,email:null,userRole:null,userId:null}),f=(0,s.useCallback)(()=>"admin"===g.userRole&&"admin"===m,[g.userRole,m]),[w,p]=(0,s.useState)(null),y=(0,s.useCallback)(async(e,t,r)=>{let l=new o.r2rClient(r);try{var s,n,a,c;let r=await l.users.login({email:e,password:t}),o=(null===(s=r.results.accessToken)||void 0===s?void 0:s.token)||(null===(n=r.results.access_token)||void 0===n?void 0:n.token),u=(null===(a=r.results.refreshToken)||void 0===a?void 0:a.token)||(null===(c=r.results.refresh_token)||void 0===c?void 0:c.token);if(!o)throw Error("No access token received from server");localStorage.setItem("livechatAccessToken",o),u&&localStorage.setItem("livechatRefreshToken",u),l.setTokens(o,u||""),i(l);let d=await l.users.me();if(!d.results)throw Error("Failed to get user information");let h="user";try{await l.system.settings(),h="admin"}catch(e){e instanceof Error&&"status"in e&&403===e.status||console.error("Unexpected error when checking user role:",e)}return k({isAuthenticated:!0,email:d.results.email,userRole:h,userId:d.results.id}),p(Date.now()),{success:!0,userRole:h}}catch(e){throw console.error("Login error:",e),e}},[]),S=(0,s.useCallback)(async(e,t)=>{let r=new o.r2rClient(t);try{r.setTokens(e,"");let t=await r.users.me();if(!t.results)throw Error("Failed to get user information");localStorage.setItem("livechatAccessToken",e),i(r);let l="user";try{await r.system.settings(),l="admin"}catch(e){e instanceof Error&&"status"in e&&403===e.status||console.error("Unexpected error when checking user role:",e)}return k({isAuthenticated:!0,email:t.results.email,userRole:l,userId:t.results.id}),{success:!0,userRole:l}}catch(e){throw console.error("Token login error:",e),localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),e}},[]),x=(0,s.useCallback)(async()=>{try{a&&await a.users.logout()}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),i(null),k({isAuthenticated:!1,email:null,userRole:null,userId:null}),p(null)}},[a]),I=(0,s.useCallback)(async()=>{localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),i(null),k({isAuthenticated:!1,email:null,userRole:null,userId:null}),p(null)},[]),b=(0,s.useCallback)(async(e,t,r)=>{let l=new o.r2rClient(r);try{await l.users.create({email:e,password:t}),await y(e,t,r)}catch(e){throw console.error("Registration error:",e),e}},[y]),C=(0,s.useCallback)(()=>a,[a]),A=(0,s.useCallback)(async function(e,t){if(arguments.length>2&&void 0!==arguments[2]&&arguments[2],!a)throw Error("No authenticated client available");try{await a.users.create({email:e,password:t})}catch(e){throw console.error("Create user error:",e),e}},[a]);return(0,s.useEffect)(()=>{(async()=>{let e=localStorage.getItem("livechatAccessToken");if(e)try{let{loadChatConfig:t,getDeploymentUrl:l}=await r.e(111).then(r.bind(r,12492)),s=await t(),o=l(s);await S(e,o)}catch(e){console.error("Auto-login failed:",e),localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken")}})()},[S]),(0,l.jsx)(n.Provider,{value:{pipeline:c,setPipeline:u,selectedModel:d,setSelectedModel:h,isAuthenticated:g.isAuthenticated,login:y,loginWithToken:S,logout:x,unsetCredentials:I,register:b,authState:g,getClient:C,client:a,viewMode:m,setViewMode:v,isSuperUser:f,createUser:A},children:t})}},84825:(e,t,r)=>{"use strict";function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var l in r)({}).hasOwnProperty.call(r,l)&&(e[l]=r[l])}return e}).apply(null,arguments)}r.d(t,{default:()=>n});var s=r(41473),o=r(36591);function n(e){let{locale:t,...r}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl-docs.vercel.app/docs/configuration#locale");return s.createElement(o.IntlProvider,l({locale:t},r))}},20983:()=>{},16040:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c"}},17567:(e,t,r)=>{"use strict";r.d(t,{x:()=>i});var l=r(12389),s=r(41473),o=r(51432),n=r(43228),a=r(58209);function i(e){let{children:t,isValidProp:r,...i}=e;r&&(0,n.D)(r),(i={...(0,s.useContext)(o.Q),...i}).isStatic=(0,a.M)(()=>i.isStatic);let c=(0,s.useMemo)(()=>i,[JSON.stringify(i.transition),i.transformPagePoint,i.reducedMotion]);return(0,l.jsx)(o.Q.Provider,{value:c,children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[451,142,78,431,809,663,61,358],()=>t(21949)),_N_E=e.O()}]);