"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@heroui+use-image@2.1.7_react@19.0.0-rc-69d4b800-20241021";
exports.ids = ["vendor-chunks/@heroui+use-image@2.1.7_react@19.0.0-rc-69d4b800-20241021"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@heroui+use-image@2.1.7_react@19.0.0-rc-69d4b800-20241021/node_modules/@heroui/use-image/dist/index.mjs":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@heroui+use-image@2.1.7_react@19.0.0-rc-69d4b800-20241021/node_modules/@heroui/use-image/dist/index.mjs ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   shouldShowFallbackImage: () => (/* binding */ shouldShowFallbackImage),\n/* harmony export */   useImage: () => (/* binding */ useImage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _heroui_react_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @heroui/react-utils */ \"(ssr)/./node_modules/.pnpm/@heroui+react-utils@2.1.8_react@19.0.0-rc-69d4b800-20241021/node_modules/@heroui/react-utils/dist/chunk-6UBKM7F3.mjs\");\n/* harmony import */ var _heroui_use_safe_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @heroui/use-safe-layout-effect */ \"(ssr)/./node_modules/.pnpm/@heroui+use-safe-layout-eff_e82c068bf0f21d27b91df3c83af0fa0c/node_modules/@heroui/use-safe-layout-effect/dist/index.mjs\");\n// src/index.ts\n\n\n\nfunction useImage(props = {}) {\n  const { onLoad, onError, ignoreFallback, src, crossOrigin, srcSet, sizes, loading } = props;\n  const isHydrated = (0,_heroui_react_utils__WEBPACK_IMPORTED_MODULE_1__.useIsHydrated)();\n  const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(isHydrated ? new Image() : null);\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"pending\");\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!imageRef.current) return;\n    imageRef.current.onload = (event) => {\n      flush();\n      setStatus(\"loaded\");\n      onLoad == null ? void 0 : onLoad(event);\n    };\n    imageRef.current.onerror = (error) => {\n      flush();\n      setStatus(\"failed\");\n      onError == null ? void 0 : onError(error);\n    };\n  }, [imageRef.current]);\n  const flush = () => {\n    if (imageRef.current) {\n      imageRef.current.onload = null;\n      imageRef.current.onerror = null;\n      imageRef.current = null;\n    }\n  };\n  const load = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (!src) return \"pending\";\n    if (ignoreFallback) return \"loaded\";\n    const img = new Image();\n    img.src = src;\n    if (crossOrigin) img.crossOrigin = crossOrigin;\n    if (srcSet) img.srcset = srcSet;\n    if (sizes) img.sizes = sizes;\n    if (loading) img.loading = loading;\n    imageRef.current = img;\n    if (img.complete && img.naturalWidth) {\n      return \"loaded\";\n    }\n    return \"loading\";\n  }, [src, crossOrigin, srcSet, sizes, onLoad, onError, loading]);\n  (0,_heroui_use_safe_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useSafeLayoutEffect)(() => {\n    if (isHydrated) {\n      setStatus(load());\n    }\n  }, [isHydrated, load]);\n  return ignoreFallback ? \"loaded\" : status;\n}\nvar shouldShowFallbackImage = (status, fallbackStrategy) => status !== \"loaded\" && fallbackStrategy === \"beforeLoadOrError\" || status === \"failed\" && fallbackStrategy === \"onError\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@heroui+use-image@2.1.7_react@19.0.0-rc-69d4b800-20241021/node_modules/@heroui/use-image/dist/index.mjs\n");

/***/ })

};
;