"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171";
exports.ids = ["vendor-chunks/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/FocusScope.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/FocusScope.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ $9bf71ea28793e738$export$20e40289641fbbb6),\n/* harmony export */   createFocusManager: () => (/* binding */ $9bf71ea28793e738$export$c5251b9e124bf29),\n/* harmony export */   focusScopeTree: () => (/* binding */ $9bf71ea28793e738$export$d06fae2ee68b101e),\n/* harmony export */   getFocusableTreeWalker: () => (/* binding */ $9bf71ea28793e738$export$2d6ec8fc375ceafa),\n/* harmony export */   isElementInChildOfActiveScope: () => (/* binding */ $9bf71ea28793e738$export$1258395f99bf9cbf),\n/* harmony export */   useFocusManager: () => (/* binding */ $9bf71ea28793e738$export$10c5169755ce7bd7)\n/* harmony export */ });\n/* harmony import */ var _isElementVisible_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./isElementVisible.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/isElementVisible.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/useLayoutEffect.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/DOMFunctions.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/domHelpers.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/platform.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/isFocusable.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/ShadowTreeWalker.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f4c4e47fdd38dbbc2317f9013c56c4ca/node_modules/@react-aria/interactions/dist/useFocusVisible.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f4c4e47fdd38dbbc2317f9013c56c4ca/node_modules/@react-aria/interactions/dist/focusSafely.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n\nconst $9bf71ea28793e738$var$FocusContext = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createContext(null);\nconst $9bf71ea28793e738$var$RESTORE_FOCUS_EVENT = 'react-aria-focus-scope-restore';\nlet $9bf71ea28793e738$var$activeScope = null;\nfunction $9bf71ea28793e738$export$20e40289641fbbb6(props) {\n    let { children: children, contain: contain, restoreFocus: restoreFocus, autoFocus: autoFocus } = props;\n    let startRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    let endRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    let scopeRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    let { parentNode: parentNode } = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($9bf71ea28793e738$var$FocusContext) || {};\n    // Create a tree node here so we can add children to it even before it is added to the tree.\n    let node = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>new $9bf71ea28793e738$var$TreeNode({\n            scopeRef: scopeRef\n        }), [\n        scopeRef\n    ]);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        // If a new scope mounts outside the active scope, (e.g. DialogContainer launched from a menu),\n        // use the active scope as the parent instead of the parent from context. Layout effects run bottom\n        // up, so if the parent is not yet added to the tree, don't do this. Only the outer-most FocusScope\n        // that is being added should get the activeScope as its parent.\n        let parent = parentNode || $9bf71ea28793e738$export$d06fae2ee68b101e.root;\n        if ($9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(parent.scopeRef) && $9bf71ea28793e738$var$activeScope && !$9bf71ea28793e738$var$isAncestorScope($9bf71ea28793e738$var$activeScope, parent.scopeRef)) {\n            let activeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode($9bf71ea28793e738$var$activeScope);\n            if (activeNode) parent = activeNode;\n        }\n        // Add the node to the parent, and to the tree.\n        parent.addChild(node);\n        $9bf71ea28793e738$export$d06fae2ee68b101e.addNode(node);\n    }, [\n        node,\n        parentNode\n    ]);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        let node = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef);\n        if (node) node.contain = !!contain;\n    }, [\n        contain\n    ]);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        var _startRef_current;\n        // Find all rendered nodes between the sentinels and add them to the scope.\n        let node = (_startRef_current = startRef.current) === null || _startRef_current === void 0 ? void 0 : _startRef_current.nextSibling;\n        let nodes = [];\n        let stopPropagation = (e)=>e.stopPropagation();\n        while(node && node !== endRef.current){\n            nodes.push(node);\n            // Stop custom restore focus event from propagating to parent focus scopes.\n            node.addEventListener($9bf71ea28793e738$var$RESTORE_FOCUS_EVENT, stopPropagation);\n            node = node.nextSibling;\n        }\n        scopeRef.current = nodes;\n        return ()=>{\n            for (let node of nodes)node.removeEventListener($9bf71ea28793e738$var$RESTORE_FOCUS_EVENT, stopPropagation);\n        };\n    }, [\n        children\n    ]);\n    $9bf71ea28793e738$var$useActiveScopeTracker(scopeRef, restoreFocus, contain);\n    $9bf71ea28793e738$var$useFocusContainment(scopeRef, contain);\n    $9bf71ea28793e738$var$useRestoreFocus(scopeRef, restoreFocus, contain);\n    $9bf71ea28793e738$var$useAutoFocus(scopeRef, autoFocus);\n    // This needs to be an effect so that activeScope is updated after the FocusScope tree is complete.\n    // It cannot be a useLayoutEffect because the parent of this node hasn't been attached in the tree yet.\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const activeElement = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getActiveElement)((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined));\n        let scope = null;\n        if ($9bf71ea28793e738$var$isElementInScope(activeElement, scopeRef.current)) {\n            // We need to traverse the focusScope tree and find the bottom most scope that\n            // contains the active element and set that as the activeScope.\n            for (let node of $9bf71ea28793e738$export$d06fae2ee68b101e.traverse())if (node.scopeRef && $9bf71ea28793e738$var$isElementInScope(activeElement, node.scopeRef.current)) scope = node;\n            if (scope === $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef)) $9bf71ea28793e738$var$activeScope = scope.scopeRef;\n        }\n    }, [\n        scopeRef\n    ]);\n    // This layout effect cleanup is so that the tree node is removed synchronously with react before the RAF\n    // in useRestoreFocus cleanup runs.\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        return ()=>{\n            var _focusScopeTree_getTreeNode_parent, _focusScopeTree_getTreeNode;\n            var _focusScopeTree_getTreeNode_parent_scopeRef;\n            // Scope may have been re-parented.\n            let parentScope = (_focusScopeTree_getTreeNode_parent_scopeRef = (_focusScopeTree_getTreeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef)) === null || _focusScopeTree_getTreeNode === void 0 ? void 0 : (_focusScopeTree_getTreeNode_parent = _focusScopeTree_getTreeNode.parent) === null || _focusScopeTree_getTreeNode_parent === void 0 ? void 0 : _focusScopeTree_getTreeNode_parent.scopeRef) !== null && _focusScopeTree_getTreeNode_parent_scopeRef !== void 0 ? _focusScopeTree_getTreeNode_parent_scopeRef : null;\n            if ((scopeRef === $9bf71ea28793e738$var$activeScope || $9bf71ea28793e738$var$isAncestorScope(scopeRef, $9bf71ea28793e738$var$activeScope)) && (!parentScope || $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(parentScope))) $9bf71ea28793e738$var$activeScope = parentScope;\n            $9bf71ea28793e738$export$d06fae2ee68b101e.removeTreeNode(scopeRef);\n        };\n    }, [\n        scopeRef\n    ]);\n    let focusManager = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>$9bf71ea28793e738$var$createFocusManagerForScope(scopeRef), []);\n    let value = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            focusManager: focusManager,\n            parentNode: node\n        }), [\n        node,\n        focusManager\n    ]);\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($9bf71ea28793e738$var$FocusContext.Provider, {\n        value: value\n    }, /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement(\"span\", {\n        \"data-focus-scope-start\": true,\n        hidden: true,\n        ref: startRef\n    }), children, /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement(\"span\", {\n        \"data-focus-scope-end\": true,\n        hidden: true,\n        ref: endRef\n    }));\n}\nfunction $9bf71ea28793e738$export$10c5169755ce7bd7() {\n    var _useContext;\n    return (_useContext = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($9bf71ea28793e738$var$FocusContext)) === null || _useContext === void 0 ? void 0 : _useContext.focusManager;\n}\nfunction $9bf71ea28793e738$var$createFocusManagerForScope(scopeRef) {\n    return {\n        focusNext (opts = {}) {\n            let scope = scopeRef.current;\n            let { from: from, tabbable: tabbable, wrap: wrap, accept: accept } = opts;\n            var _scope_;\n            let node = from || (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getActiveElement)((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.getOwnerDocument)((_scope_ = scope[0]) !== null && _scope_ !== void 0 ? _scope_ : undefined));\n            let sentinel = scope[0].previousElementSibling;\n            let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n                tabbable: tabbable,\n                accept: accept\n            }, scope);\n            walker.currentNode = $9bf71ea28793e738$var$isElementInScope(node, scope) ? node : sentinel;\n            let nextNode = walker.nextNode();\n            if (!nextNode && wrap) {\n                walker.currentNode = sentinel;\n                nextNode = walker.nextNode();\n            }\n            if (nextNode) $9bf71ea28793e738$var$focusElement(nextNode, true);\n            return nextNode;\n        },\n        focusPrevious (opts = {}) {\n            let scope = scopeRef.current;\n            let { from: from, tabbable: tabbable, wrap: wrap, accept: accept } = opts;\n            var _scope_;\n            let node = from || (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getActiveElement)((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.getOwnerDocument)((_scope_ = scope[0]) !== null && _scope_ !== void 0 ? _scope_ : undefined));\n            let sentinel = scope[scope.length - 1].nextElementSibling;\n            let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n                tabbable: tabbable,\n                accept: accept\n            }, scope);\n            walker.currentNode = $9bf71ea28793e738$var$isElementInScope(node, scope) ? node : sentinel;\n            let previousNode = walker.previousNode();\n            if (!previousNode && wrap) {\n                walker.currentNode = sentinel;\n                previousNode = walker.previousNode();\n            }\n            if (previousNode) $9bf71ea28793e738$var$focusElement(previousNode, true);\n            return previousNode;\n        },\n        focusFirst (opts = {}) {\n            let scope = scopeRef.current;\n            let { tabbable: tabbable, accept: accept } = opts;\n            let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n                tabbable: tabbable,\n                accept: accept\n            }, scope);\n            walker.currentNode = scope[0].previousElementSibling;\n            let nextNode = walker.nextNode();\n            if (nextNode) $9bf71ea28793e738$var$focusElement(nextNode, true);\n            return nextNode;\n        },\n        focusLast (opts = {}) {\n            let scope = scopeRef.current;\n            let { tabbable: tabbable, accept: accept } = opts;\n            let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n                tabbable: tabbable,\n                accept: accept\n            }, scope);\n            walker.currentNode = scope[scope.length - 1].nextElementSibling;\n            let previousNode = walker.previousNode();\n            if (previousNode) $9bf71ea28793e738$var$focusElement(previousNode, true);\n            return previousNode;\n        }\n    };\n}\nfunction $9bf71ea28793e738$var$getScopeRoot(scope) {\n    return scope[0].parentElement;\n}\nfunction $9bf71ea28793e738$var$shouldContainFocus(scopeRef) {\n    let scope = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode($9bf71ea28793e738$var$activeScope);\n    while(scope && scope.scopeRef !== scopeRef){\n        if (scope.contain) return false;\n        scope = scope.parent;\n    }\n    return true;\n}\nfunction $9bf71ea28793e738$var$useFocusContainment(scopeRef, contain) {\n    let focusedNode = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(undefined);\n    let raf = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(undefined);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        let scope = scopeRef.current;\n        if (!contain) {\n            // if contain was changed, then we should cancel any ongoing waits to pull focus back into containment\n            if (raf.current) {\n                cancelAnimationFrame(raf.current);\n                raf.current = undefined;\n            }\n            return;\n        }\n        const ownerDocument = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.getOwnerDocument)(scope ? scope[0] : undefined);\n        // Handle the Tab key to contain focus within the scope\n        let onKeyDown = (e)=>{\n            if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !$9bf71ea28793e738$var$shouldContainFocus(scopeRef) || e.isComposing) return;\n            let focusedElement = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getActiveElement)(ownerDocument);\n            let scope = scopeRef.current;\n            if (!scope || !$9bf71ea28793e738$var$isElementInScope(focusedElement, scope)) return;\n            let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n                tabbable: true\n            }, scope);\n            if (!focusedElement) return;\n            walker.currentNode = focusedElement;\n            let nextElement = e.shiftKey ? walker.previousNode() : walker.nextNode();\n            if (!nextElement) {\n                walker.currentNode = e.shiftKey ? scope[scope.length - 1].nextElementSibling : scope[0].previousElementSibling;\n                nextElement = e.shiftKey ? walker.previousNode() : walker.nextNode();\n            }\n            e.preventDefault();\n            if (nextElement) $9bf71ea28793e738$var$focusElement(nextElement, true);\n        };\n        let onFocus = (e)=>{\n            // If focusing an element in a child scope of the currently active scope, the child becomes active.\n            // Moving out of the active scope to an ancestor is not allowed.\n            if ((!$9bf71ea28793e738$var$activeScope || $9bf71ea28793e738$var$isAncestorScope($9bf71ea28793e738$var$activeScope, scopeRef)) && $9bf71ea28793e738$var$isElementInScope((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getEventTarget)(e), scopeRef.current)) {\n                $9bf71ea28793e738$var$activeScope = scopeRef;\n                focusedNode.current = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getEventTarget)(e);\n            } else if ($9bf71ea28793e738$var$shouldContainFocus(scopeRef) && !$9bf71ea28793e738$var$isElementInChildScope((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getEventTarget)(e), scopeRef)) {\n                // If a focus event occurs outside the active scope (e.g. user tabs from browser location bar),\n                // restore focus to the previously focused node or the first tabbable element in the active scope.\n                if (focusedNode.current) focusedNode.current.focus();\n                else if ($9bf71ea28793e738$var$activeScope && $9bf71ea28793e738$var$activeScope.current) $9bf71ea28793e738$var$focusFirstInScope($9bf71ea28793e738$var$activeScope.current);\n            } else if ($9bf71ea28793e738$var$shouldContainFocus(scopeRef)) focusedNode.current = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getEventTarget)(e);\n        };\n        let onBlur = (e)=>{\n            // Firefox doesn't shift focus back to the Dialog properly without this\n            if (raf.current) cancelAnimationFrame(raf.current);\n            raf.current = requestAnimationFrame(()=>{\n                // Patches infinite focus coersion loop for Android Talkback where the user isn't able to move the virtual cursor\n                // if within a containing focus scope. Bug filed against Chrome: https://issuetracker.google.com/issues/384844019.\n                // Note that this means focus can leave focus containing modals due to this, but it is isolated to Chrome Talkback.\n                let modality = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_4__.getInteractionModality)();\n                let shouldSkipFocusRestore = (modality === 'virtual' || modality === null) && (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.isAndroid)() && (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.isChrome)();\n                // Use document.activeElement instead of e.relatedTarget so we can tell if user clicked into iframe\n                let activeElement = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getActiveElement)(ownerDocument);\n                if (!shouldSkipFocusRestore && activeElement && $9bf71ea28793e738$var$shouldContainFocus(scopeRef) && !$9bf71ea28793e738$var$isElementInChildScope(activeElement, scopeRef)) {\n                    $9bf71ea28793e738$var$activeScope = scopeRef;\n                    let target = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getEventTarget)(e);\n                    if (target && target.isConnected) {\n                        var _focusedNode_current;\n                        focusedNode.current = target;\n                        (_focusedNode_current = focusedNode.current) === null || _focusedNode_current === void 0 ? void 0 : _focusedNode_current.focus();\n                    } else if ($9bf71ea28793e738$var$activeScope.current) $9bf71ea28793e738$var$focusFirstInScope($9bf71ea28793e738$var$activeScope.current);\n                }\n            });\n        };\n        ownerDocument.addEventListener('keydown', onKeyDown, false);\n        ownerDocument.addEventListener('focusin', onFocus, false);\n        scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.addEventListener('focusin', onFocus, false));\n        scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.addEventListener('focusout', onBlur, false));\n        return ()=>{\n            ownerDocument.removeEventListener('keydown', onKeyDown, false);\n            ownerDocument.removeEventListener('focusin', onFocus, false);\n            scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.removeEventListener('focusin', onFocus, false));\n            scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.removeEventListener('focusout', onBlur, false));\n        };\n    }, [\n        scopeRef,\n        contain\n    ]);\n    // This is a useLayoutEffect so it is guaranteed to run before our async synthetic blur\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        return ()=>{\n            if (raf.current) cancelAnimationFrame(raf.current);\n        };\n    }, [\n        raf\n    ]);\n}\nfunction $9bf71ea28793e738$var$isElementInAnyScope(element) {\n    return $9bf71ea28793e738$var$isElementInChildScope(element);\n}\nfunction $9bf71ea28793e738$var$isElementInScope(element, scope) {\n    if (!element) return false;\n    if (!scope) return false;\n    return scope.some((node)=>node.contains(element));\n}\nfunction $9bf71ea28793e738$var$isElementInChildScope(element, scope = null) {\n    // If the element is within a top layer element (e.g. toasts), always allow moving focus there.\n    if (element instanceof Element && element.closest('[data-react-aria-top-layer]')) return true;\n    // node.contains in isElementInScope covers child scopes that are also DOM children,\n    // but does not cover child scopes in portals.\n    for (let { scopeRef: s } of $9bf71ea28793e738$export$d06fae2ee68b101e.traverse($9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scope))){\n        if (s && $9bf71ea28793e738$var$isElementInScope(element, s.current)) return true;\n    }\n    return false;\n}\nfunction $9bf71ea28793e738$export$1258395f99bf9cbf(element) {\n    return $9bf71ea28793e738$var$isElementInChildScope(element, $9bf71ea28793e738$var$activeScope);\n}\nfunction $9bf71ea28793e738$var$isAncestorScope(ancestor, scope) {\n    var _focusScopeTree_getTreeNode;\n    let parent = (_focusScopeTree_getTreeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scope)) === null || _focusScopeTree_getTreeNode === void 0 ? void 0 : _focusScopeTree_getTreeNode.parent;\n    while(parent){\n        if (parent.scopeRef === ancestor) return true;\n        parent = parent.parent;\n    }\n    return false;\n}\nfunction $9bf71ea28793e738$var$focusElement(element, scroll = false) {\n    if (element != null && !scroll) try {\n        (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_6__.focusSafely)(element);\n    } catch  {\n    // ignore\n    }\n    else if (element != null) try {\n        element.focus();\n    } catch  {\n    // ignore\n    }\n}\nfunction $9bf71ea28793e738$var$getFirstInScope(scope, tabbable = true) {\n    let sentinel = scope[0].previousElementSibling;\n    let scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n    let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n        tabbable: tabbable\n    }, scope);\n    walker.currentNode = sentinel;\n    let nextNode = walker.nextNode();\n    // If the scope does not contain a tabbable element, use the first focusable element.\n    if (tabbable && !nextNode) {\n        scopeRoot = $9bf71ea28793e738$var$getScopeRoot(scope);\n        walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(scopeRoot, {\n            tabbable: false\n        }, scope);\n        walker.currentNode = sentinel;\n        nextNode = walker.nextNode();\n    }\n    return nextNode;\n}\nfunction $9bf71ea28793e738$var$focusFirstInScope(scope, tabbable = true) {\n    $9bf71ea28793e738$var$focusElement($9bf71ea28793e738$var$getFirstInScope(scope, tabbable));\n}\nfunction $9bf71ea28793e738$var$useAutoFocus(scopeRef, autoFocus) {\n    const autoFocusRef = (0, react__WEBPACK_IMPORTED_MODULE_0__).useRef(autoFocus);\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (autoFocusRef.current) {\n            $9bf71ea28793e738$var$activeScope = scopeRef;\n            const ownerDocument = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined);\n            if (!$9bf71ea28793e738$var$isElementInScope((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getActiveElement)(ownerDocument), $9bf71ea28793e738$var$activeScope.current) && scopeRef.current) $9bf71ea28793e738$var$focusFirstInScope(scopeRef.current);\n        }\n        autoFocusRef.current = false;\n    }, [\n        scopeRef\n    ]);\n}\nfunction $9bf71ea28793e738$var$useActiveScopeTracker(scopeRef, restore, contain) {\n    // tracks the active scope, in case restore and contain are both false.\n    // if either are true, this is tracked in useRestoreFocus or useFocusContainment.\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        if (restore || contain) return;\n        let scope = scopeRef.current;\n        const ownerDocument = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.getOwnerDocument)(scope ? scope[0] : undefined);\n        let onFocus = (e)=>{\n            let target = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getEventTarget)(e);\n            if ($9bf71ea28793e738$var$isElementInScope(target, scopeRef.current)) $9bf71ea28793e738$var$activeScope = scopeRef;\n            else if (!$9bf71ea28793e738$var$isElementInAnyScope(target)) $9bf71ea28793e738$var$activeScope = null;\n        };\n        ownerDocument.addEventListener('focusin', onFocus, false);\n        scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.addEventListener('focusin', onFocus, false));\n        return ()=>{\n            ownerDocument.removeEventListener('focusin', onFocus, false);\n            scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.removeEventListener('focusin', onFocus, false));\n        };\n    }, [\n        scopeRef,\n        restore,\n        contain\n    ]);\n}\nfunction $9bf71ea28793e738$var$shouldRestoreFocus(scopeRef) {\n    let scope = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode($9bf71ea28793e738$var$activeScope);\n    while(scope && scope.scopeRef !== scopeRef){\n        if (scope.nodeToRestore) return false;\n        scope = scope.parent;\n    }\n    return (scope === null || scope === void 0 ? void 0 : scope.scopeRef) === scopeRef;\n}\nfunction $9bf71ea28793e738$var$useRestoreFocus(scopeRef, restoreFocus, contain) {\n    // create a ref during render instead of useLayoutEffect so the active element is saved before a child with autoFocus=true mounts.\n    // eslint-disable-next-line no-restricted-globals\n    const nodeToRestoreRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(typeof document !== 'undefined' ? (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getActiveElement)((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined)) : null);\n    // restoring scopes should all track if they are active regardless of contain, but contain already tracks it plus logic to contain the focus\n    // restoring-non-containing scopes should only care if they become active so they can perform the restore\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        let scope = scopeRef.current;\n        const ownerDocument = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.getOwnerDocument)(scope ? scope[0] : undefined);\n        if (!restoreFocus || contain) return;\n        let onFocus = ()=>{\n            // If focusing an element in a child scope of the currently active scope, the child becomes active.\n            // Moving out of the active scope to an ancestor is not allowed.\n            if ((!$9bf71ea28793e738$var$activeScope || $9bf71ea28793e738$var$isAncestorScope($9bf71ea28793e738$var$activeScope, scopeRef)) && $9bf71ea28793e738$var$isElementInScope((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getActiveElement)(ownerDocument), scopeRef.current)) $9bf71ea28793e738$var$activeScope = scopeRef;\n        };\n        ownerDocument.addEventListener('focusin', onFocus, false);\n        scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.addEventListener('focusin', onFocus, false));\n        return ()=>{\n            ownerDocument.removeEventListener('focusin', onFocus, false);\n            scope === null || scope === void 0 ? void 0 : scope.forEach((element)=>element.removeEventListener('focusin', onFocus, false));\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        scopeRef,\n        contain\n    ]);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        const ownerDocument = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined);\n        if (!restoreFocus) return;\n        // Handle the Tab key so that tabbing out of the scope goes to the next element\n        // after the node that had focus when the scope mounted. This is important when\n        // using portals for overlays, so that focus goes to the expected element when\n        // tabbing out of the overlay.\n        let onKeyDown = (e)=>{\n            if (e.key !== 'Tab' || e.altKey || e.ctrlKey || e.metaKey || !$9bf71ea28793e738$var$shouldContainFocus(scopeRef) || e.isComposing) return;\n            let focusedElement = ownerDocument.activeElement;\n            if (!$9bf71ea28793e738$var$isElementInChildScope(focusedElement, scopeRef) || !$9bf71ea28793e738$var$shouldRestoreFocus(scopeRef)) return;\n            let treeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef);\n            if (!treeNode) return;\n            let nodeToRestore = treeNode.nodeToRestore;\n            // Create a DOM tree walker that matches all tabbable elements\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(ownerDocument.body, {\n                tabbable: true\n            });\n            // Find the next tabbable element after the currently focused element\n            walker.currentNode = focusedElement;\n            let nextElement = e.shiftKey ? walker.previousNode() : walker.nextNode();\n            if (!nodeToRestore || !nodeToRestore.isConnected || nodeToRestore === ownerDocument.body) {\n                nodeToRestore = undefined;\n                treeNode.nodeToRestore = undefined;\n            }\n            // If there is no next element, or it is outside the current scope, move focus to the\n            // next element after the node to restore to instead.\n            if ((!nextElement || !$9bf71ea28793e738$var$isElementInChildScope(nextElement, scopeRef)) && nodeToRestore) {\n                walker.currentNode = nodeToRestore;\n                // Skip over elements within the scope, in case the scope immediately follows the node to restore.\n                do nextElement = e.shiftKey ? walker.previousNode() : walker.nextNode();\n                while ($9bf71ea28793e738$var$isElementInChildScope(nextElement, scopeRef));\n                e.preventDefault();\n                e.stopPropagation();\n                if (nextElement) $9bf71ea28793e738$var$focusElement(nextElement, true);\n                else // If there is no next element and the nodeToRestore isn't within a FocusScope (i.e. we are leaving the top level focus scope)\n                // then move focus to the body.\n                // Otherwise restore focus to the nodeToRestore (e.g menu within a popover -> tabbing to close the menu should move focus to menu trigger)\n                if (!$9bf71ea28793e738$var$isElementInAnyScope(nodeToRestore)) focusedElement.blur();\n                else $9bf71ea28793e738$var$focusElement(nodeToRestore, true);\n            }\n        };\n        if (!contain) ownerDocument.addEventListener('keydown', onKeyDown, true);\n        return ()=>{\n            if (!contain) ownerDocument.removeEventListener('keydown', onKeyDown, true);\n        };\n    }, [\n        scopeRef,\n        restoreFocus,\n        contain\n    ]);\n    // useLayoutEffect instead of useEffect so the active element is saved synchronously instead of asynchronously.\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        const ownerDocument = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.getOwnerDocument)(scopeRef.current ? scopeRef.current[0] : undefined);\n        if (!restoreFocus) return;\n        let treeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef);\n        if (!treeNode) return;\n        var _nodeToRestoreRef_current;\n        treeNode.nodeToRestore = (_nodeToRestoreRef_current = nodeToRestoreRef.current) !== null && _nodeToRestoreRef_current !== void 0 ? _nodeToRestoreRef_current : undefined;\n        return ()=>{\n            let treeNode = $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(scopeRef);\n            if (!treeNode) return;\n            let nodeToRestore = treeNode.nodeToRestore;\n            // if we already lost focus to the body and this was the active scope, then we should attempt to restore\n            let activeElement = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getActiveElement)(ownerDocument);\n            if (restoreFocus && nodeToRestore && (activeElement && $9bf71ea28793e738$var$isElementInChildScope(activeElement, scopeRef) || activeElement === ownerDocument.body && $9bf71ea28793e738$var$shouldRestoreFocus(scopeRef))) {\n                // freeze the focusScopeTree so it persists after the raf, otherwise during unmount nodes are removed from it\n                let clonedTree = $9bf71ea28793e738$export$d06fae2ee68b101e.clone();\n                requestAnimationFrame(()=>{\n                    // Only restore focus if we've lost focus to the body, the alternative is that focus has been purposefully moved elsewhere\n                    if (ownerDocument.activeElement === ownerDocument.body) {\n                        // look up the tree starting with our scope to find a nodeToRestore still in the DOM\n                        let treeNode = clonedTree.getTreeNode(scopeRef);\n                        while(treeNode){\n                            if (treeNode.nodeToRestore && treeNode.nodeToRestore.isConnected) {\n                                $9bf71ea28793e738$var$restoreFocusToElement(treeNode.nodeToRestore);\n                                return;\n                            }\n                            treeNode = treeNode.parent;\n                        }\n                        // If no nodeToRestore was found, focus the first element in the nearest\n                        // ancestor scope that is still in the tree.\n                        treeNode = clonedTree.getTreeNode(scopeRef);\n                        while(treeNode){\n                            if (treeNode.scopeRef && treeNode.scopeRef.current && $9bf71ea28793e738$export$d06fae2ee68b101e.getTreeNode(treeNode.scopeRef)) {\n                                let node = $9bf71ea28793e738$var$getFirstInScope(treeNode.scopeRef.current, true);\n                                $9bf71ea28793e738$var$restoreFocusToElement(node);\n                                return;\n                            }\n                            treeNode = treeNode.parent;\n                        }\n                    }\n                });\n            }\n        };\n    }, [\n        scopeRef,\n        restoreFocus\n    ]);\n}\nfunction $9bf71ea28793e738$var$restoreFocusToElement(node) {\n    // Dispatch a custom event that parent elements can intercept to customize focus restoration.\n    // For example, virtualized collection components reuse DOM elements, so the original element\n    // might still exist in the DOM but representing a different item.\n    if (node.dispatchEvent(new CustomEvent($9bf71ea28793e738$var$RESTORE_FOCUS_EVENT, {\n        bubbles: true,\n        cancelable: true\n    }))) $9bf71ea28793e738$var$focusElement(node);\n}\nfunction $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, opts, scope) {\n    let filter = (opts === null || opts === void 0 ? void 0 : opts.tabbable) ? (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_7__.isTabbable) : (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_7__.isFocusable);\n    // Ensure that root is an Element or fall back appropriately\n    let rootElement = (root === null || root === void 0 ? void 0 : root.nodeType) === Node.ELEMENT_NODE ? root : null;\n    // Determine the document to use\n    let doc = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.getOwnerDocument)(rootElement);\n    // Create a TreeWalker, ensuring the root is an Element or Document\n    let walker = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_8__.createShadowTreeWalker)(doc, root || doc, NodeFilter.SHOW_ELEMENT, {\n        acceptNode (node) {\n            var _opts_from;\n            // Skip nodes inside the starting node.\n            if (opts === null || opts === void 0 ? void 0 : (_opts_from = opts.from) === null || _opts_from === void 0 ? void 0 : _opts_from.contains(node)) return NodeFilter.FILTER_REJECT;\n            if (filter(node) && (0, _isElementVisible_mjs__WEBPACK_IMPORTED_MODULE_9__.isElementVisible)(node) && (!scope || $9bf71ea28793e738$var$isElementInScope(node, scope)) && (!(opts === null || opts === void 0 ? void 0 : opts.accept) || opts.accept(node))) return NodeFilter.FILTER_ACCEPT;\n            return NodeFilter.FILTER_SKIP;\n        }\n    });\n    if (opts === null || opts === void 0 ? void 0 : opts.from) walker.currentNode = opts.from;\n    return walker;\n}\nfunction $9bf71ea28793e738$export$c5251b9e124bf29(ref, defaultOptions = {}) {\n    return {\n        focusNext (opts = {}) {\n            let root = ref.current;\n            if (!root) return null;\n            let { from: from, tabbable: tabbable = defaultOptions.tabbable, wrap: wrap = defaultOptions.wrap, accept: accept = defaultOptions.accept } = opts;\n            let node = from || (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getActiveElement)((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.getOwnerDocument)(root));\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, {\n                tabbable: tabbable,\n                accept: accept\n            });\n            if (root.contains(node)) walker.currentNode = node;\n            let nextNode = walker.nextNode();\n            if (!nextNode && wrap) {\n                walker.currentNode = root;\n                nextNode = walker.nextNode();\n            }\n            if (nextNode) $9bf71ea28793e738$var$focusElement(nextNode, true);\n            return nextNode;\n        },\n        focusPrevious (opts = defaultOptions) {\n            let root = ref.current;\n            if (!root) return null;\n            let { from: from, tabbable: tabbable = defaultOptions.tabbable, wrap: wrap = defaultOptions.wrap, accept: accept = defaultOptions.accept } = opts;\n            let node = from || (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.getActiveElement)((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.getOwnerDocument)(root));\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, {\n                tabbable: tabbable,\n                accept: accept\n            });\n            if (root.contains(node)) walker.currentNode = node;\n            else {\n                let next = $9bf71ea28793e738$var$last(walker);\n                if (next) $9bf71ea28793e738$var$focusElement(next, true);\n                return next !== null && next !== void 0 ? next : null;\n            }\n            let previousNode = walker.previousNode();\n            if (!previousNode && wrap) {\n                walker.currentNode = root;\n                let lastNode = $9bf71ea28793e738$var$last(walker);\n                if (!lastNode) // couldn't wrap\n                return null;\n                previousNode = lastNode;\n            }\n            if (previousNode) $9bf71ea28793e738$var$focusElement(previousNode, true);\n            return previousNode !== null && previousNode !== void 0 ? previousNode : null;\n        },\n        focusFirst (opts = defaultOptions) {\n            let root = ref.current;\n            if (!root) return null;\n            let { tabbable: tabbable = defaultOptions.tabbable, accept: accept = defaultOptions.accept } = opts;\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, {\n                tabbable: tabbable,\n                accept: accept\n            });\n            let nextNode = walker.nextNode();\n            if (nextNode) $9bf71ea28793e738$var$focusElement(nextNode, true);\n            return nextNode;\n        },\n        focusLast (opts = defaultOptions) {\n            let root = ref.current;\n            if (!root) return null;\n            let { tabbable: tabbable = defaultOptions.tabbable, accept: accept = defaultOptions.accept } = opts;\n            let walker = $9bf71ea28793e738$export$2d6ec8fc375ceafa(root, {\n                tabbable: tabbable,\n                accept: accept\n            });\n            let next = $9bf71ea28793e738$var$last(walker);\n            if (next) $9bf71ea28793e738$var$focusElement(next, true);\n            return next !== null && next !== void 0 ? next : null;\n        }\n    };\n}\nfunction $9bf71ea28793e738$var$last(walker) {\n    let next = undefined;\n    let last;\n    do {\n        last = walker.lastChild();\n        if (last) next = last;\n    }while (last);\n    return next;\n}\nclass $9bf71ea28793e738$var$Tree {\n    get size() {\n        return this.fastMap.size;\n    }\n    getTreeNode(data) {\n        return this.fastMap.get(data);\n    }\n    addTreeNode(scopeRef, parent, nodeToRestore) {\n        let parentNode = this.fastMap.get(parent !== null && parent !== void 0 ? parent : null);\n        if (!parentNode) return;\n        let node = new $9bf71ea28793e738$var$TreeNode({\n            scopeRef: scopeRef\n        });\n        parentNode.addChild(node);\n        node.parent = parentNode;\n        this.fastMap.set(scopeRef, node);\n        if (nodeToRestore) node.nodeToRestore = nodeToRestore;\n    }\n    addNode(node) {\n        this.fastMap.set(node.scopeRef, node);\n    }\n    removeTreeNode(scopeRef) {\n        // never remove the root\n        if (scopeRef === null) return;\n        let node = this.fastMap.get(scopeRef);\n        if (!node) return;\n        let parentNode = node.parent;\n        // when we remove a scope, check if any sibling scopes are trying to restore focus to something inside the scope we're removing\n        // if we are, then replace the siblings restore with the restore from the scope we're removing\n        for (let current of this.traverse())if (current !== node && node.nodeToRestore && current.nodeToRestore && node.scopeRef && node.scopeRef.current && $9bf71ea28793e738$var$isElementInScope(current.nodeToRestore, node.scopeRef.current)) current.nodeToRestore = node.nodeToRestore;\n        let children = node.children;\n        if (parentNode) {\n            parentNode.removeChild(node);\n            if (children.size > 0) children.forEach((child)=>parentNode && parentNode.addChild(child));\n        }\n        this.fastMap.delete(node.scopeRef);\n    }\n    // Pre Order Depth First\n    *traverse(node = this.root) {\n        if (node.scopeRef != null) yield node;\n        if (node.children.size > 0) for (let child of node.children)yield* this.traverse(child);\n    }\n    clone() {\n        var _node_parent;\n        let newTree = new $9bf71ea28793e738$var$Tree();\n        var _node_parent_scopeRef;\n        for (let node of this.traverse())newTree.addTreeNode(node.scopeRef, (_node_parent_scopeRef = (_node_parent = node.parent) === null || _node_parent === void 0 ? void 0 : _node_parent.scopeRef) !== null && _node_parent_scopeRef !== void 0 ? _node_parent_scopeRef : null, node.nodeToRestore);\n        return newTree;\n    }\n    constructor(){\n        this.fastMap = new Map();\n        this.root = new $9bf71ea28793e738$var$TreeNode({\n            scopeRef: null\n        });\n        this.fastMap.set(null, this.root);\n    }\n}\nclass $9bf71ea28793e738$var$TreeNode {\n    addChild(node) {\n        this.children.add(node);\n        node.parent = this;\n    }\n    removeChild(node) {\n        this.children.delete(node);\n        node.parent = undefined;\n    }\n    constructor(props){\n        this.children = new Set();\n        this.contain = false;\n        this.scopeRef = props.scopeRef;\n    }\n}\nlet $9bf71ea28793e738$export$d06fae2ee68b101e = new $9bf71ea28793e738$var$Tree();\n\n\n\n//# sourceMappingURL=FocusScope.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LWFyaWErZm9jdXNAMy4yMC4wX3JlX2VkYWFhMjEyOTdhZmFmYzM3MzU1YjZmNTRiZTZlMTcxL25vZGVfbW9kdWxlcy9AcmVhY3QtYXJpYS9mb2N1cy9kaXN0L0ZvY3VzU2NvcGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBcUc7QUFDbVM7QUFDcFE7QUFDbUI7O0FBRXZKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7Ozs7QUFJQSw2REFBNkQsa0NBQVk7QUFDekU7QUFDQTtBQUNBO0FBQ0EsVUFBVSx5RkFBeUY7QUFDbkcsdUJBQXVCLHlDQUFhO0FBQ3BDLHFCQUFxQix5Q0FBYTtBQUNsQyx1QkFBdUIseUNBQWE7QUFDcEMsVUFBVSx5QkFBeUIsTUFBTSw2Q0FBaUI7QUFDMUQ7QUFDQSxtQkFBbUIsMENBQWM7QUFDakM7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBLFFBQVEsOERBQXNCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhEQUFzQjtBQUM5QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxRQUFRLDhEQUFzQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsNENBQWdCO0FBQ3hCLGtDQUFrQywrREFBdUIsTUFBTSwrREFBdUI7QUFDdEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhEQUFzQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSwyQkFBMkIsMENBQWM7QUFDekMsb0JBQW9CLDBDQUFjO0FBQ2xDO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLGtDQUFZO0FBQ3pDO0FBQ0EsS0FBSyxvQkFBb0Isa0NBQVk7QUFDckM7QUFDQTtBQUNBO0FBQ0EsS0FBSywrQkFBK0Isa0NBQVk7QUFDaEQ7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLDhCQUE4Qiw2Q0FBaUI7QUFDL0M7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCO0FBQzVCO0FBQ0Esa0JBQWtCLDZEQUE2RDtBQUMvRTtBQUNBLG1DQUFtQywrREFBdUIsTUFBTSwrREFBdUI7QUFDdkY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULGdDQUFnQztBQUNoQztBQUNBLGtCQUFrQiw2REFBNkQ7QUFDL0U7QUFDQSxtQ0FBbUMsK0RBQXVCLE1BQU0sK0RBQXVCO0FBQ3ZGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCw2QkFBNkI7QUFDN0I7QUFDQSxrQkFBa0IscUNBQXFDO0FBQ3ZEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULDRCQUE0QjtBQUM1QjtBQUNBLGtCQUFrQixxQ0FBcUM7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQkFBMEIseUNBQWE7QUFDdkMsa0JBQWtCLHlDQUFhO0FBQy9CLFFBQVEsOERBQXNCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQywrREFBdUI7QUFDekQ7QUFDQTtBQUNBO0FBQ0EscUNBQXFDLCtEQUF1QjtBQUM1RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUxBQXlMLDZEQUFxQjtBQUM5TTtBQUNBLDBDQUEwQyw2REFBcUI7QUFDL0QsY0FBYyxnSEFBZ0gsNkRBQXFCO0FBQ25KO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyx1RkFBdUYsNkRBQXFCO0FBQzFIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsNEVBQTZCO0FBQ2hFLGtHQUFrRyx3REFBZ0IsV0FBVyx1REFBZTtBQUM1STtBQUNBLHdDQUF3QywrREFBdUI7QUFDL0Q7QUFDQTtBQUNBLHFDQUFxQyw2REFBcUI7QUFDMUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0I7QUFDdEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOERBQXNCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsY0FBYztBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGlFQUFrQjtBQUM5QixNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsa0NBQVk7QUFDekMsUUFBUSw0Q0FBZ0I7QUFDeEI7QUFDQTtBQUNBLHNDQUFzQywrREFBdUI7QUFDN0QsNERBQTRELCtEQUF1QjtBQUNuRjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOERBQXNCO0FBQzlCO0FBQ0E7QUFDQSxrQ0FBa0MsK0RBQXVCO0FBQ3pEO0FBQ0EsNkJBQTZCLDZEQUFxQjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQ0FBaUMseUNBQWEsd0NBQXdDLCtEQUF1QixNQUFNLCtEQUF1QjtBQUMxSTtBQUNBO0FBQ0EsUUFBUSw4REFBc0I7QUFDOUI7QUFDQSxrQ0FBa0MsK0RBQXVCO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUxBQXlMLCtEQUF1QjtBQUNoTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLFFBQVEsOERBQXNCO0FBQzlCLGtDQUFrQywrREFBdUI7QUFDekQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLDhEQUFzQjtBQUM5QixrQ0FBa0MsK0RBQXVCO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLCtEQUF1QjtBQUMzRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxtRkFBbUYseURBQWlCLFFBQVEsMERBQWtCO0FBQzlIO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQiwrREFBdUI7QUFDekM7QUFDQSxxQkFBcUIscUVBQTZCO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLG1FQUF5QztBQUM3RTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLDBFQUEwRTtBQUMxRTtBQUNBLDRCQUE0QjtBQUM1QjtBQUNBO0FBQ0Esa0JBQWtCLHFJQUFxSTtBQUN2SixtQ0FBbUMsK0RBQXVCLE1BQU0sK0RBQXVCO0FBQ3ZGO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixxSUFBcUk7QUFDdkosbUNBQW1DLCtEQUF1QixNQUFNLCtEQUF1QjtBQUN2RjtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLHVGQUF1RjtBQUN6RztBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQix1RkFBdUY7QUFDekc7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7QUFHNlk7QUFDN1kiLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEByZWFjdC1hcmlhK2ZvY3VzQDMuMjAuMF9yZV9lZGFhYTIxMjk3YWZhZmMzNzM1NWI2ZjU0YmU2ZTE3MVxcbm9kZV9tb2R1bGVzXFxAcmVhY3QtYXJpYVxcZm9jdXNcXGRpc3RcXEZvY3VzU2NvcGUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7aXNFbGVtZW50VmlzaWJsZSBhcyAkNjQ1ZjJlNjdiODVhMjRjOSRleHBvcnQkZTk4OWMwZmZmYWE2YjI3YX0gZnJvbSBcIi4vaXNFbGVtZW50VmlzaWJsZS5tanNcIjtcbmltcG9ydCB7dXNlTGF5b3V0RWZmZWN0IGFzICRjZ2F3QyR1c2VMYXlvdXRFZmZlY3QsIGdldEFjdGl2ZUVsZW1lbnQgYXMgJGNnYXdDJGdldEFjdGl2ZUVsZW1lbnQsIGdldE93bmVyRG9jdW1lbnQgYXMgJGNnYXdDJGdldE93bmVyRG9jdW1lbnQsIGdldEV2ZW50VGFyZ2V0IGFzICRjZ2F3QyRnZXRFdmVudFRhcmdldCwgaXNBbmRyb2lkIGFzICRjZ2F3QyRpc0FuZHJvaWQsIGlzQ2hyb21lIGFzICRjZ2F3QyRpc0Nocm9tZSwgaXNUYWJiYWJsZSBhcyAkY2dhd0MkaXNUYWJiYWJsZSwgaXNGb2N1c2FibGUgYXMgJGNnYXdDJGlzRm9jdXNhYmxlLCBjcmVhdGVTaGFkb3dUcmVlV2Fsa2VyIGFzICRjZ2F3QyRjcmVhdGVTaGFkb3dUcmVlV2Fsa2VyfSBmcm9tIFwiQHJlYWN0LWFyaWEvdXRpbHNcIjtcbmltcG9ydCB7Z2V0SW50ZXJhY3Rpb25Nb2RhbGl0eSBhcyAkY2dhd0MkZ2V0SW50ZXJhY3Rpb25Nb2RhbGl0eSwgZm9jdXNTYWZlbHkgYXMgJGNnYXdDJGZvY3VzU2FmZWx5fSBmcm9tIFwiQHJlYWN0LWFyaWEvaW50ZXJhY3Rpb25zXCI7XG5pbXBvcnQgJGNnYXdDJHJlYWN0LCB7dXNlUmVmIGFzICRjZ2F3QyR1c2VSZWYsIHVzZUNvbnRleHQgYXMgJGNnYXdDJHVzZUNvbnRleHQsIHVzZU1lbW8gYXMgJGNnYXdDJHVzZU1lbW8sIHVzZUVmZmVjdCBhcyAkY2dhd0MkdXNlRWZmZWN0fSBmcm9tIFwicmVhY3RcIjtcblxuLypcbiAqIENvcHlyaWdodCAyMDIwIEFkb2JlLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuICogVGhpcyBmaWxlIGlzIGxpY2Vuc2VkIHRvIHlvdSB1bmRlciB0aGUgQXBhY2hlIExpY2Vuc2UsIFZlcnNpb24gMi4wICh0aGUgXCJMaWNlbnNlXCIpO1xuICogeW91IG1heSBub3QgdXNlIHRoaXMgZmlsZSBleGNlcHQgaW4gY29tcGxpYW5jZSB3aXRoIHRoZSBMaWNlbnNlLiBZb3UgbWF5IG9idGFpbiBhIGNvcHlcbiAqIG9mIHRoZSBMaWNlbnNlIGF0IGh0dHA6Ly93d3cuYXBhY2hlLm9yZy9saWNlbnNlcy9MSUNFTlNFLTIuMFxuICpcbiAqIFVubGVzcyByZXF1aXJlZCBieSBhcHBsaWNhYmxlIGxhdyBvciBhZ3JlZWQgdG8gaW4gd3JpdGluZywgc29mdHdhcmUgZGlzdHJpYnV0ZWQgdW5kZXJcbiAqIHRoZSBMaWNlbnNlIGlzIGRpc3RyaWJ1dGVkIG9uIGFuIFwiQVMgSVNcIiBCQVNJUywgV0lUSE9VVCBXQVJSQU5USUVTIE9SIFJFUFJFU0VOVEFUSU9OU1xuICogT0YgQU5ZIEtJTkQsIGVpdGhlciBleHByZXNzIG9yIGltcGxpZWQuIFNlZSB0aGUgTGljZW5zZSBmb3IgdGhlIHNwZWNpZmljIGxhbmd1YWdlXG4gKiBnb3Zlcm5pbmcgcGVybWlzc2lvbnMgYW5kIGxpbWl0YXRpb25zIHVuZGVyIHRoZSBMaWNlbnNlLlxuICovIFxuXG5cblxuY29uc3QgJDliZjcxZWEyODc5M2U3MzgkdmFyJEZvY3VzQ29udGV4dCA9IC8qI19fUFVSRV9fKi8gKDAsICRjZ2F3QyRyZWFjdCkuY3JlYXRlQ29udGV4dChudWxsKTtcbmNvbnN0ICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRSRVNUT1JFX0ZPQ1VTX0VWRU5UID0gJ3JlYWN0LWFyaWEtZm9jdXMtc2NvcGUtcmVzdG9yZSc7XG5sZXQgJDliZjcxZWEyODc5M2U3MzgkdmFyJGFjdGl2ZVNjb3BlID0gbnVsbDtcbmZ1bmN0aW9uICQ5YmY3MWVhMjg3OTNlNzM4JGV4cG9ydCQyMGU0MDI4OTY0MWZiYmI2KHByb3BzKSB7XG4gICAgbGV0IHsgY2hpbGRyZW46IGNoaWxkcmVuLCBjb250YWluOiBjb250YWluLCByZXN0b3JlRm9jdXM6IHJlc3RvcmVGb2N1cywgYXV0b0ZvY3VzOiBhdXRvRm9jdXMgfSA9IHByb3BzO1xuICAgIGxldCBzdGFydFJlZiA9ICgwLCAkY2dhd0MkdXNlUmVmKShudWxsKTtcbiAgICBsZXQgZW5kUmVmID0gKDAsICRjZ2F3QyR1c2VSZWYpKG51bGwpO1xuICAgIGxldCBzY29wZVJlZiA9ICgwLCAkY2dhd0MkdXNlUmVmKShbXSk7XG4gICAgbGV0IHsgcGFyZW50Tm9kZTogcGFyZW50Tm9kZSB9ID0gKDAsICRjZ2F3QyR1c2VDb250ZXh0KSgkOWJmNzFlYTI4NzkzZTczOCR2YXIkRm9jdXNDb250ZXh0KSB8fCB7fTtcbiAgICAvLyBDcmVhdGUgYSB0cmVlIG5vZGUgaGVyZSBzbyB3ZSBjYW4gYWRkIGNoaWxkcmVuIHRvIGl0IGV2ZW4gYmVmb3JlIGl0IGlzIGFkZGVkIHRvIHRoZSB0cmVlLlxuICAgIGxldCBub2RlID0gKDAsICRjZ2F3QyR1c2VNZW1vKSgoKT0+bmV3ICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRUcmVlTm9kZSh7XG4gICAgICAgICAgICBzY29wZVJlZjogc2NvcGVSZWZcbiAgICAgICAgfSksIFtcbiAgICAgICAgc2NvcGVSZWZcbiAgICBdKTtcbiAgICAoMCwgJGNnYXdDJHVzZUxheW91dEVmZmVjdCkoKCk9PntcbiAgICAgICAgLy8gSWYgYSBuZXcgc2NvcGUgbW91bnRzIG91dHNpZGUgdGhlIGFjdGl2ZSBzY29wZSwgKGUuZy4gRGlhbG9nQ29udGFpbmVyIGxhdW5jaGVkIGZyb20gYSBtZW51KSxcbiAgICAgICAgLy8gdXNlIHRoZSBhY3RpdmUgc2NvcGUgYXMgdGhlIHBhcmVudCBpbnN0ZWFkIG9mIHRoZSBwYXJlbnQgZnJvbSBjb250ZXh0LiBMYXlvdXQgZWZmZWN0cyBydW4gYm90dG9tXG4gICAgICAgIC8vIHVwLCBzbyBpZiB0aGUgcGFyZW50IGlzIG5vdCB5ZXQgYWRkZWQgdG8gdGhlIHRyZWUsIGRvbid0IGRvIHRoaXMuIE9ubHkgdGhlIG91dGVyLW1vc3QgRm9jdXNTY29wZVxuICAgICAgICAvLyB0aGF0IGlzIGJlaW5nIGFkZGVkIHNob3VsZCBnZXQgdGhlIGFjdGl2ZVNjb3BlIGFzIGl0cyBwYXJlbnQuXG4gICAgICAgIGxldCBwYXJlbnQgPSBwYXJlbnROb2RlIHx8ICQ5YmY3MWVhMjg3OTNlNzM4JGV4cG9ydCRkMDZmYWUyZWU2OGIxMDFlLnJvb3Q7XG4gICAgICAgIGlmICgkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkZDA2ZmFlMmVlNjhiMTAxZS5nZXRUcmVlTm9kZShwYXJlbnQuc2NvcGVSZWYpICYmICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRhY3RpdmVTY29wZSAmJiAhJDliZjcxZWEyODc5M2U3MzgkdmFyJGlzQW5jZXN0b3JTY29wZSgkOWJmNzFlYTI4NzkzZTczOCR2YXIkYWN0aXZlU2NvcGUsIHBhcmVudC5zY29wZVJlZikpIHtcbiAgICAgICAgICAgIGxldCBhY3RpdmVOb2RlID0gJDliZjcxZWEyODc5M2U3MzgkZXhwb3J0JGQwNmZhZTJlZTY4YjEwMWUuZ2V0VHJlZU5vZGUoJDliZjcxZWEyODc5M2U3MzgkdmFyJGFjdGl2ZVNjb3BlKTtcbiAgICAgICAgICAgIGlmIChhY3RpdmVOb2RlKSBwYXJlbnQgPSBhY3RpdmVOb2RlO1xuICAgICAgICB9XG4gICAgICAgIC8vIEFkZCB0aGUgbm9kZSB0byB0aGUgcGFyZW50LCBhbmQgdG8gdGhlIHRyZWUuXG4gICAgICAgIHBhcmVudC5hZGRDaGlsZChub2RlKTtcbiAgICAgICAgJDliZjcxZWEyODc5M2U3MzgkZXhwb3J0JGQwNmZhZTJlZTY4YjEwMWUuYWRkTm9kZShub2RlKTtcbiAgICB9LCBbXG4gICAgICAgIG5vZGUsXG4gICAgICAgIHBhcmVudE5vZGVcbiAgICBdKTtcbiAgICAoMCwgJGNnYXdDJHVzZUxheW91dEVmZmVjdCkoKCk9PntcbiAgICAgICAgbGV0IG5vZGUgPSAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkZDA2ZmFlMmVlNjhiMTAxZS5nZXRUcmVlTm9kZShzY29wZVJlZik7XG4gICAgICAgIGlmIChub2RlKSBub2RlLmNvbnRhaW4gPSAhIWNvbnRhaW47XG4gICAgfSwgW1xuICAgICAgICBjb250YWluXG4gICAgXSk7XG4gICAgKDAsICRjZ2F3QyR1c2VMYXlvdXRFZmZlY3QpKCgpPT57XG4gICAgICAgIHZhciBfc3RhcnRSZWZfY3VycmVudDtcbiAgICAgICAgLy8gRmluZCBhbGwgcmVuZGVyZWQgbm9kZXMgYmV0d2VlbiB0aGUgc2VudGluZWxzIGFuZCBhZGQgdGhlbSB0byB0aGUgc2NvcGUuXG4gICAgICAgIGxldCBub2RlID0gKF9zdGFydFJlZl9jdXJyZW50ID0gc3RhcnRSZWYuY3VycmVudCkgPT09IG51bGwgfHwgX3N0YXJ0UmVmX2N1cnJlbnQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9zdGFydFJlZl9jdXJyZW50Lm5leHRTaWJsaW5nO1xuICAgICAgICBsZXQgbm9kZXMgPSBbXTtcbiAgICAgICAgbGV0IHN0b3BQcm9wYWdhdGlvbiA9IChlKT0+ZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgd2hpbGUobm9kZSAmJiBub2RlICE9PSBlbmRSZWYuY3VycmVudCl7XG4gICAgICAgICAgICBub2Rlcy5wdXNoKG5vZGUpO1xuICAgICAgICAgICAgLy8gU3RvcCBjdXN0b20gcmVzdG9yZSBmb2N1cyBldmVudCBmcm9tIHByb3BhZ2F0aW5nIHRvIHBhcmVudCBmb2N1cyBzY29wZXMuXG4gICAgICAgICAgICBub2RlLmFkZEV2ZW50TGlzdGVuZXIoJDliZjcxZWEyODc5M2U3MzgkdmFyJFJFU1RPUkVfRk9DVVNfRVZFTlQsIHN0b3BQcm9wYWdhdGlvbik7XG4gICAgICAgICAgICBub2RlID0gbm9kZS5uZXh0U2libGluZztcbiAgICAgICAgfVxuICAgICAgICBzY29wZVJlZi5jdXJyZW50ID0gbm9kZXM7XG4gICAgICAgIHJldHVybiAoKT0+e1xuICAgICAgICAgICAgZm9yIChsZXQgbm9kZSBvZiBub2Rlcylub2RlLnJlbW92ZUV2ZW50TGlzdGVuZXIoJDliZjcxZWEyODc5M2U3MzgkdmFyJFJFU1RPUkVfRk9DVVNfRVZFTlQsIHN0b3BQcm9wYWdhdGlvbik7XG4gICAgICAgIH07XG4gICAgfSwgW1xuICAgICAgICBjaGlsZHJlblxuICAgIF0pO1xuICAgICQ5YmY3MWVhMjg3OTNlNzM4JHZhciR1c2VBY3RpdmVTY29wZVRyYWNrZXIoc2NvcGVSZWYsIHJlc3RvcmVGb2N1cywgY29udGFpbik7XG4gICAgJDliZjcxZWEyODc5M2U3MzgkdmFyJHVzZUZvY3VzQ29udGFpbm1lbnQoc2NvcGVSZWYsIGNvbnRhaW4pO1xuICAgICQ5YmY3MWVhMjg3OTNlNzM4JHZhciR1c2VSZXN0b3JlRm9jdXMoc2NvcGVSZWYsIHJlc3RvcmVGb2N1cywgY29udGFpbik7XG4gICAgJDliZjcxZWEyODc5M2U3MzgkdmFyJHVzZUF1dG9Gb2N1cyhzY29wZVJlZiwgYXV0b0ZvY3VzKTtcbiAgICAvLyBUaGlzIG5lZWRzIHRvIGJlIGFuIGVmZmVjdCBzbyB0aGF0IGFjdGl2ZVNjb3BlIGlzIHVwZGF0ZWQgYWZ0ZXIgdGhlIEZvY3VzU2NvcGUgdHJlZSBpcyBjb21wbGV0ZS5cbiAgICAvLyBJdCBjYW5ub3QgYmUgYSB1c2VMYXlvdXRFZmZlY3QgYmVjYXVzZSB0aGUgcGFyZW50IG9mIHRoaXMgbm9kZSBoYXNuJ3QgYmVlbiBhdHRhY2hlZCBpbiB0aGUgdHJlZSB5ZXQuXG4gICAgKDAsICRjZ2F3QyR1c2VFZmZlY3QpKCgpPT57XG4gICAgICAgIGNvbnN0IGFjdGl2ZUVsZW1lbnQgPSAoMCwgJGNnYXdDJGdldEFjdGl2ZUVsZW1lbnQpKCgwLCAkY2dhd0MkZ2V0T3duZXJEb2N1bWVudCkoc2NvcGVSZWYuY3VycmVudCA/IHNjb3BlUmVmLmN1cnJlbnRbMF0gOiB1bmRlZmluZWQpKTtcbiAgICAgICAgbGV0IHNjb3BlID0gbnVsbDtcbiAgICAgICAgaWYgKCQ5YmY3MWVhMjg3OTNlNzM4JHZhciRpc0VsZW1lbnRJblNjb3BlKGFjdGl2ZUVsZW1lbnQsIHNjb3BlUmVmLmN1cnJlbnQpKSB7XG4gICAgICAgICAgICAvLyBXZSBuZWVkIHRvIHRyYXZlcnNlIHRoZSBmb2N1c1Njb3BlIHRyZWUgYW5kIGZpbmQgdGhlIGJvdHRvbSBtb3N0IHNjb3BlIHRoYXRcbiAgICAgICAgICAgIC8vIGNvbnRhaW5zIHRoZSBhY3RpdmUgZWxlbWVudCBhbmQgc2V0IHRoYXQgYXMgdGhlIGFjdGl2ZVNjb3BlLlxuICAgICAgICAgICAgZm9yIChsZXQgbm9kZSBvZiAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkZDA2ZmFlMmVlNjhiMTAxZS50cmF2ZXJzZSgpKWlmIChub2RlLnNjb3BlUmVmICYmICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRpc0VsZW1lbnRJblNjb3BlKGFjdGl2ZUVsZW1lbnQsIG5vZGUuc2NvcGVSZWYuY3VycmVudCkpIHNjb3BlID0gbm9kZTtcbiAgICAgICAgICAgIGlmIChzY29wZSA9PT0gJDliZjcxZWEyODc5M2U3MzgkZXhwb3J0JGQwNmZhZTJlZTY4YjEwMWUuZ2V0VHJlZU5vZGUoc2NvcGVSZWYpKSAkOWJmNzFlYTI4NzkzZTczOCR2YXIkYWN0aXZlU2NvcGUgPSBzY29wZS5zY29wZVJlZjtcbiAgICAgICAgfVxuICAgIH0sIFtcbiAgICAgICAgc2NvcGVSZWZcbiAgICBdKTtcbiAgICAvLyBUaGlzIGxheW91dCBlZmZlY3QgY2xlYW51cCBpcyBzbyB0aGF0IHRoZSB0cmVlIG5vZGUgaXMgcmVtb3ZlZCBzeW5jaHJvbm91c2x5IHdpdGggcmVhY3QgYmVmb3JlIHRoZSBSQUZcbiAgICAvLyBpbiB1c2VSZXN0b3JlRm9jdXMgY2xlYW51cCBydW5zLlxuICAgICgwLCAkY2dhd0MkdXNlTGF5b3V0RWZmZWN0KSgoKT0+e1xuICAgICAgICByZXR1cm4gKCk9PntcbiAgICAgICAgICAgIHZhciBfZm9jdXNTY29wZVRyZWVfZ2V0VHJlZU5vZGVfcGFyZW50LCBfZm9jdXNTY29wZVRyZWVfZ2V0VHJlZU5vZGU7XG4gICAgICAgICAgICB2YXIgX2ZvY3VzU2NvcGVUcmVlX2dldFRyZWVOb2RlX3BhcmVudF9zY29wZVJlZjtcbiAgICAgICAgICAgIC8vIFNjb3BlIG1heSBoYXZlIGJlZW4gcmUtcGFyZW50ZWQuXG4gICAgICAgICAgICBsZXQgcGFyZW50U2NvcGUgPSAoX2ZvY3VzU2NvcGVUcmVlX2dldFRyZWVOb2RlX3BhcmVudF9zY29wZVJlZiA9IChfZm9jdXNTY29wZVRyZWVfZ2V0VHJlZU5vZGUgPSAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkZDA2ZmFlMmVlNjhiMTAxZS5nZXRUcmVlTm9kZShzY29wZVJlZikpID09PSBudWxsIHx8IF9mb2N1c1Njb3BlVHJlZV9nZXRUcmVlTm9kZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogKF9mb2N1c1Njb3BlVHJlZV9nZXRUcmVlTm9kZV9wYXJlbnQgPSBfZm9jdXNTY29wZVRyZWVfZ2V0VHJlZU5vZGUucGFyZW50KSA9PT0gbnVsbCB8fCBfZm9jdXNTY29wZVRyZWVfZ2V0VHJlZU5vZGVfcGFyZW50ID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfZm9jdXNTY29wZVRyZWVfZ2V0VHJlZU5vZGVfcGFyZW50LnNjb3BlUmVmKSAhPT0gbnVsbCAmJiBfZm9jdXNTY29wZVRyZWVfZ2V0VHJlZU5vZGVfcGFyZW50X3Njb3BlUmVmICE9PSB2b2lkIDAgPyBfZm9jdXNTY29wZVRyZWVfZ2V0VHJlZU5vZGVfcGFyZW50X3Njb3BlUmVmIDogbnVsbDtcbiAgICAgICAgICAgIGlmICgoc2NvcGVSZWYgPT09ICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRhY3RpdmVTY29wZSB8fCAkOWJmNzFlYTI4NzkzZTczOCR2YXIkaXNBbmNlc3RvclNjb3BlKHNjb3BlUmVmLCAkOWJmNzFlYTI4NzkzZTczOCR2YXIkYWN0aXZlU2NvcGUpKSAmJiAoIXBhcmVudFNjb3BlIHx8ICQ5YmY3MWVhMjg3OTNlNzM4JGV4cG9ydCRkMDZmYWUyZWU2OGIxMDFlLmdldFRyZWVOb2RlKHBhcmVudFNjb3BlKSkpICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRhY3RpdmVTY29wZSA9IHBhcmVudFNjb3BlO1xuICAgICAgICAgICAgJDliZjcxZWEyODc5M2U3MzgkZXhwb3J0JGQwNmZhZTJlZTY4YjEwMWUucmVtb3ZlVHJlZU5vZGUoc2NvcGVSZWYpO1xuICAgICAgICB9O1xuICAgIH0sIFtcbiAgICAgICAgc2NvcGVSZWZcbiAgICBdKTtcbiAgICBsZXQgZm9jdXNNYW5hZ2VyID0gKDAsICRjZ2F3QyR1c2VNZW1vKSgoKT0+JDliZjcxZWEyODc5M2U3MzgkdmFyJGNyZWF0ZUZvY3VzTWFuYWdlckZvclNjb3BlKHNjb3BlUmVmKSwgW10pO1xuICAgIGxldCB2YWx1ZSA9ICgwLCAkY2dhd0MkdXNlTWVtbykoKCk9Pih7XG4gICAgICAgICAgICBmb2N1c01hbmFnZXI6IGZvY3VzTWFuYWdlcixcbiAgICAgICAgICAgIHBhcmVudE5vZGU6IG5vZGVcbiAgICAgICAgfSksIFtcbiAgICAgICAgbm9kZSxcbiAgICAgICAgZm9jdXNNYW5hZ2VyXG4gICAgXSk7XG4gICAgcmV0dXJuIC8qI19fUFVSRV9fKi8gKDAsICRjZ2F3QyRyZWFjdCkuY3JlYXRlRWxlbWVudCgkOWJmNzFlYTI4NzkzZTczOCR2YXIkRm9jdXNDb250ZXh0LlByb3ZpZGVyLCB7XG4gICAgICAgIHZhbHVlOiB2YWx1ZVxuICAgIH0sIC8qI19fUFVSRV9fKi8gKDAsICRjZ2F3QyRyZWFjdCkuY3JlYXRlRWxlbWVudChcInNwYW5cIiwge1xuICAgICAgICBcImRhdGEtZm9jdXMtc2NvcGUtc3RhcnRcIjogdHJ1ZSxcbiAgICAgICAgaGlkZGVuOiB0cnVlLFxuICAgICAgICByZWY6IHN0YXJ0UmVmXG4gICAgfSksIGNoaWxkcmVuLCAvKiNfX1BVUkVfXyovICgwLCAkY2dhd0MkcmVhY3QpLmNyZWF0ZUVsZW1lbnQoXCJzcGFuXCIsIHtcbiAgICAgICAgXCJkYXRhLWZvY3VzLXNjb3BlLWVuZFwiOiB0cnVlLFxuICAgICAgICBoaWRkZW46IHRydWUsXG4gICAgICAgIHJlZjogZW5kUmVmXG4gICAgfSkpO1xufVxuZnVuY3Rpb24gJDliZjcxZWEyODc5M2U3MzgkZXhwb3J0JDEwYzUxNjk3NTVjZTdiZDcoKSB7XG4gICAgdmFyIF91c2VDb250ZXh0O1xuICAgIHJldHVybiAoX3VzZUNvbnRleHQgPSAoMCwgJGNnYXdDJHVzZUNvbnRleHQpKCQ5YmY3MWVhMjg3OTNlNzM4JHZhciRGb2N1c0NvbnRleHQpKSA9PT0gbnVsbCB8fCBfdXNlQ29udGV4dCA9PT0gdm9pZCAwID8gdm9pZCAwIDogX3VzZUNvbnRleHQuZm9jdXNNYW5hZ2VyO1xufVxuZnVuY3Rpb24gJDliZjcxZWEyODc5M2U3MzgkdmFyJGNyZWF0ZUZvY3VzTWFuYWdlckZvclNjb3BlKHNjb3BlUmVmKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgZm9jdXNOZXh0IChvcHRzID0ge30pIHtcbiAgICAgICAgICAgIGxldCBzY29wZSA9IHNjb3BlUmVmLmN1cnJlbnQ7XG4gICAgICAgICAgICBsZXQgeyBmcm9tOiBmcm9tLCB0YWJiYWJsZTogdGFiYmFibGUsIHdyYXA6IHdyYXAsIGFjY2VwdDogYWNjZXB0IH0gPSBvcHRzO1xuICAgICAgICAgICAgdmFyIF9zY29wZV87XG4gICAgICAgICAgICBsZXQgbm9kZSA9IGZyb20gfHwgKDAsICRjZ2F3QyRnZXRBY3RpdmVFbGVtZW50KSgoMCwgJGNnYXdDJGdldE93bmVyRG9jdW1lbnQpKChfc2NvcGVfID0gc2NvcGVbMF0pICE9PSBudWxsICYmIF9zY29wZV8gIT09IHZvaWQgMCA/IF9zY29wZV8gOiB1bmRlZmluZWQpKTtcbiAgICAgICAgICAgIGxldCBzZW50aW5lbCA9IHNjb3BlWzBdLnByZXZpb3VzRWxlbWVudFNpYmxpbmc7XG4gICAgICAgICAgICBsZXQgc2NvcGVSb290ID0gJDliZjcxZWEyODc5M2U3MzgkdmFyJGdldFNjb3BlUm9vdChzY29wZSk7XG4gICAgICAgICAgICBsZXQgd2Fsa2VyID0gJDliZjcxZWEyODc5M2U3MzgkZXhwb3J0JDJkNmVjOGZjMzc1Y2VhZmEoc2NvcGVSb290LCB7XG4gICAgICAgICAgICAgICAgdGFiYmFibGU6IHRhYmJhYmxlLFxuICAgICAgICAgICAgICAgIGFjY2VwdDogYWNjZXB0XG4gICAgICAgICAgICB9LCBzY29wZSk7XG4gICAgICAgICAgICB3YWxrZXIuY3VycmVudE5vZGUgPSAkOWJmNzFlYTI4NzkzZTczOCR2YXIkaXNFbGVtZW50SW5TY29wZShub2RlLCBzY29wZSkgPyBub2RlIDogc2VudGluZWw7XG4gICAgICAgICAgICBsZXQgbmV4dE5vZGUgPSB3YWxrZXIubmV4dE5vZGUoKTtcbiAgICAgICAgICAgIGlmICghbmV4dE5vZGUgJiYgd3JhcCkge1xuICAgICAgICAgICAgICAgIHdhbGtlci5jdXJyZW50Tm9kZSA9IHNlbnRpbmVsO1xuICAgICAgICAgICAgICAgIG5leHROb2RlID0gd2Fsa2VyLm5leHROb2RlKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAobmV4dE5vZGUpICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRmb2N1c0VsZW1lbnQobmV4dE5vZGUsIHRydWUpO1xuICAgICAgICAgICAgcmV0dXJuIG5leHROb2RlO1xuICAgICAgICB9LFxuICAgICAgICBmb2N1c1ByZXZpb3VzIChvcHRzID0ge30pIHtcbiAgICAgICAgICAgIGxldCBzY29wZSA9IHNjb3BlUmVmLmN1cnJlbnQ7XG4gICAgICAgICAgICBsZXQgeyBmcm9tOiBmcm9tLCB0YWJiYWJsZTogdGFiYmFibGUsIHdyYXA6IHdyYXAsIGFjY2VwdDogYWNjZXB0IH0gPSBvcHRzO1xuICAgICAgICAgICAgdmFyIF9zY29wZV87XG4gICAgICAgICAgICBsZXQgbm9kZSA9IGZyb20gfHwgKDAsICRjZ2F3QyRnZXRBY3RpdmVFbGVtZW50KSgoMCwgJGNnYXdDJGdldE93bmVyRG9jdW1lbnQpKChfc2NvcGVfID0gc2NvcGVbMF0pICE9PSBudWxsICYmIF9zY29wZV8gIT09IHZvaWQgMCA/IF9zY29wZV8gOiB1bmRlZmluZWQpKTtcbiAgICAgICAgICAgIGxldCBzZW50aW5lbCA9IHNjb3BlW3Njb3BlLmxlbmd0aCAtIDFdLm5leHRFbGVtZW50U2libGluZztcbiAgICAgICAgICAgIGxldCBzY29wZVJvb3QgPSAkOWJmNzFlYTI4NzkzZTczOCR2YXIkZ2V0U2NvcGVSb290KHNjb3BlKTtcbiAgICAgICAgICAgIGxldCB3YWxrZXIgPSAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkMmQ2ZWM4ZmMzNzVjZWFmYShzY29wZVJvb3QsIHtcbiAgICAgICAgICAgICAgICB0YWJiYWJsZTogdGFiYmFibGUsXG4gICAgICAgICAgICAgICAgYWNjZXB0OiBhY2NlcHRcbiAgICAgICAgICAgIH0sIHNjb3BlKTtcbiAgICAgICAgICAgIHdhbGtlci5jdXJyZW50Tm9kZSA9ICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRpc0VsZW1lbnRJblNjb3BlKG5vZGUsIHNjb3BlKSA/IG5vZGUgOiBzZW50aW5lbDtcbiAgICAgICAgICAgIGxldCBwcmV2aW91c05vZGUgPSB3YWxrZXIucHJldmlvdXNOb2RlKCk7XG4gICAgICAgICAgICBpZiAoIXByZXZpb3VzTm9kZSAmJiB3cmFwKSB7XG4gICAgICAgICAgICAgICAgd2Fsa2VyLmN1cnJlbnROb2RlID0gc2VudGluZWw7XG4gICAgICAgICAgICAgICAgcHJldmlvdXNOb2RlID0gd2Fsa2VyLnByZXZpb3VzTm9kZSgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHByZXZpb3VzTm9kZSkgJDliZjcxZWEyODc5M2U3MzgkdmFyJGZvY3VzRWxlbWVudChwcmV2aW91c05vZGUsIHRydWUpO1xuICAgICAgICAgICAgcmV0dXJuIHByZXZpb3VzTm9kZTtcbiAgICAgICAgfSxcbiAgICAgICAgZm9jdXNGaXJzdCAob3B0cyA9IHt9KSB7XG4gICAgICAgICAgICBsZXQgc2NvcGUgPSBzY29wZVJlZi5jdXJyZW50O1xuICAgICAgICAgICAgbGV0IHsgdGFiYmFibGU6IHRhYmJhYmxlLCBhY2NlcHQ6IGFjY2VwdCB9ID0gb3B0cztcbiAgICAgICAgICAgIGxldCBzY29wZVJvb3QgPSAkOWJmNzFlYTI4NzkzZTczOCR2YXIkZ2V0U2NvcGVSb290KHNjb3BlKTtcbiAgICAgICAgICAgIGxldCB3YWxrZXIgPSAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkMmQ2ZWM4ZmMzNzVjZWFmYShzY29wZVJvb3QsIHtcbiAgICAgICAgICAgICAgICB0YWJiYWJsZTogdGFiYmFibGUsXG4gICAgICAgICAgICAgICAgYWNjZXB0OiBhY2NlcHRcbiAgICAgICAgICAgIH0sIHNjb3BlKTtcbiAgICAgICAgICAgIHdhbGtlci5jdXJyZW50Tm9kZSA9IHNjb3BlWzBdLnByZXZpb3VzRWxlbWVudFNpYmxpbmc7XG4gICAgICAgICAgICBsZXQgbmV4dE5vZGUgPSB3YWxrZXIubmV4dE5vZGUoKTtcbiAgICAgICAgICAgIGlmIChuZXh0Tm9kZSkgJDliZjcxZWEyODc5M2U3MzgkdmFyJGZvY3VzRWxlbWVudChuZXh0Tm9kZSwgdHJ1ZSk7XG4gICAgICAgICAgICByZXR1cm4gbmV4dE5vZGU7XG4gICAgICAgIH0sXG4gICAgICAgIGZvY3VzTGFzdCAob3B0cyA9IHt9KSB7XG4gICAgICAgICAgICBsZXQgc2NvcGUgPSBzY29wZVJlZi5jdXJyZW50O1xuICAgICAgICAgICAgbGV0IHsgdGFiYmFibGU6IHRhYmJhYmxlLCBhY2NlcHQ6IGFjY2VwdCB9ID0gb3B0cztcbiAgICAgICAgICAgIGxldCBzY29wZVJvb3QgPSAkOWJmNzFlYTI4NzkzZTczOCR2YXIkZ2V0U2NvcGVSb290KHNjb3BlKTtcbiAgICAgICAgICAgIGxldCB3YWxrZXIgPSAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkMmQ2ZWM4ZmMzNzVjZWFmYShzY29wZVJvb3QsIHtcbiAgICAgICAgICAgICAgICB0YWJiYWJsZTogdGFiYmFibGUsXG4gICAgICAgICAgICAgICAgYWNjZXB0OiBhY2NlcHRcbiAgICAgICAgICAgIH0sIHNjb3BlKTtcbiAgICAgICAgICAgIHdhbGtlci5jdXJyZW50Tm9kZSA9IHNjb3BlW3Njb3BlLmxlbmd0aCAtIDFdLm5leHRFbGVtZW50U2libGluZztcbiAgICAgICAgICAgIGxldCBwcmV2aW91c05vZGUgPSB3YWxrZXIucHJldmlvdXNOb2RlKCk7XG4gICAgICAgICAgICBpZiAocHJldmlvdXNOb2RlKSAkOWJmNzFlYTI4NzkzZTczOCR2YXIkZm9jdXNFbGVtZW50KHByZXZpb3VzTm9kZSwgdHJ1ZSk7XG4gICAgICAgICAgICByZXR1cm4gcHJldmlvdXNOb2RlO1xuICAgICAgICB9XG4gICAgfTtcbn1cbmZ1bmN0aW9uICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRnZXRTY29wZVJvb3Qoc2NvcGUpIHtcbiAgICByZXR1cm4gc2NvcGVbMF0ucGFyZW50RWxlbWVudDtcbn1cbmZ1bmN0aW9uICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRzaG91bGRDb250YWluRm9jdXMoc2NvcGVSZWYpIHtcbiAgICBsZXQgc2NvcGUgPSAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkZDA2ZmFlMmVlNjhiMTAxZS5nZXRUcmVlTm9kZSgkOWJmNzFlYTI4NzkzZTczOCR2YXIkYWN0aXZlU2NvcGUpO1xuICAgIHdoaWxlKHNjb3BlICYmIHNjb3BlLnNjb3BlUmVmICE9PSBzY29wZVJlZil7XG4gICAgICAgIGlmIChzY29wZS5jb250YWluKSByZXR1cm4gZmFsc2U7XG4gICAgICAgIHNjb3BlID0gc2NvcGUucGFyZW50O1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbn1cbmZ1bmN0aW9uICQ5YmY3MWVhMjg3OTNlNzM4JHZhciR1c2VGb2N1c0NvbnRhaW5tZW50KHNjb3BlUmVmLCBjb250YWluKSB7XG4gICAgbGV0IGZvY3VzZWROb2RlID0gKDAsICRjZ2F3QyR1c2VSZWYpKHVuZGVmaW5lZCk7XG4gICAgbGV0IHJhZiA9ICgwLCAkY2dhd0MkdXNlUmVmKSh1bmRlZmluZWQpO1xuICAgICgwLCAkY2dhd0MkdXNlTGF5b3V0RWZmZWN0KSgoKT0+e1xuICAgICAgICBsZXQgc2NvcGUgPSBzY29wZVJlZi5jdXJyZW50O1xuICAgICAgICBpZiAoIWNvbnRhaW4pIHtcbiAgICAgICAgICAgIC8vIGlmIGNvbnRhaW4gd2FzIGNoYW5nZWQsIHRoZW4gd2Ugc2hvdWxkIGNhbmNlbCBhbnkgb25nb2luZyB3YWl0cyB0byBwdWxsIGZvY3VzIGJhY2sgaW50byBjb250YWlubWVudFxuICAgICAgICAgICAgaWYgKHJhZi5jdXJyZW50KSB7XG4gICAgICAgICAgICAgICAgY2FuY2VsQW5pbWF0aW9uRnJhbWUocmFmLmN1cnJlbnQpO1xuICAgICAgICAgICAgICAgIHJhZi5jdXJyZW50ID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IG93bmVyRG9jdW1lbnQgPSAoMCwgJGNnYXdDJGdldE93bmVyRG9jdW1lbnQpKHNjb3BlID8gc2NvcGVbMF0gOiB1bmRlZmluZWQpO1xuICAgICAgICAvLyBIYW5kbGUgdGhlIFRhYiBrZXkgdG8gY29udGFpbiBmb2N1cyB3aXRoaW4gdGhlIHNjb3BlXG4gICAgICAgIGxldCBvbktleURvd24gPSAoZSk9PntcbiAgICAgICAgICAgIGlmIChlLmtleSAhPT0gJ1RhYicgfHwgZS5hbHRLZXkgfHwgZS5jdHJsS2V5IHx8IGUubWV0YUtleSB8fCAhJDliZjcxZWEyODc5M2U3MzgkdmFyJHNob3VsZENvbnRhaW5Gb2N1cyhzY29wZVJlZikgfHwgZS5pc0NvbXBvc2luZykgcmV0dXJuO1xuICAgICAgICAgICAgbGV0IGZvY3VzZWRFbGVtZW50ID0gKDAsICRjZ2F3QyRnZXRBY3RpdmVFbGVtZW50KShvd25lckRvY3VtZW50KTtcbiAgICAgICAgICAgIGxldCBzY29wZSA9IHNjb3BlUmVmLmN1cnJlbnQ7XG4gICAgICAgICAgICBpZiAoIXNjb3BlIHx8ICEkOWJmNzFlYTI4NzkzZTczOCR2YXIkaXNFbGVtZW50SW5TY29wZShmb2N1c2VkRWxlbWVudCwgc2NvcGUpKSByZXR1cm47XG4gICAgICAgICAgICBsZXQgc2NvcGVSb290ID0gJDliZjcxZWEyODc5M2U3MzgkdmFyJGdldFNjb3BlUm9vdChzY29wZSk7XG4gICAgICAgICAgICBsZXQgd2Fsa2VyID0gJDliZjcxZWEyODc5M2U3MzgkZXhwb3J0JDJkNmVjOGZjMzc1Y2VhZmEoc2NvcGVSb290LCB7XG4gICAgICAgICAgICAgICAgdGFiYmFibGU6IHRydWVcbiAgICAgICAgICAgIH0sIHNjb3BlKTtcbiAgICAgICAgICAgIGlmICghZm9jdXNlZEVsZW1lbnQpIHJldHVybjtcbiAgICAgICAgICAgIHdhbGtlci5jdXJyZW50Tm9kZSA9IGZvY3VzZWRFbGVtZW50O1xuICAgICAgICAgICAgbGV0IG5leHRFbGVtZW50ID0gZS5zaGlmdEtleSA/IHdhbGtlci5wcmV2aW91c05vZGUoKSA6IHdhbGtlci5uZXh0Tm9kZSgpO1xuICAgICAgICAgICAgaWYgKCFuZXh0RWxlbWVudCkge1xuICAgICAgICAgICAgICAgIHdhbGtlci5jdXJyZW50Tm9kZSA9IGUuc2hpZnRLZXkgPyBzY29wZVtzY29wZS5sZW5ndGggLSAxXS5uZXh0RWxlbWVudFNpYmxpbmcgOiBzY29wZVswXS5wcmV2aW91c0VsZW1lbnRTaWJsaW5nO1xuICAgICAgICAgICAgICAgIG5leHRFbGVtZW50ID0gZS5zaGlmdEtleSA/IHdhbGtlci5wcmV2aW91c05vZGUoKSA6IHdhbGtlci5uZXh0Tm9kZSgpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgaWYgKG5leHRFbGVtZW50KSAkOWJmNzFlYTI4NzkzZTczOCR2YXIkZm9jdXNFbGVtZW50KG5leHRFbGVtZW50LCB0cnVlKTtcbiAgICAgICAgfTtcbiAgICAgICAgbGV0IG9uRm9jdXMgPSAoZSk9PntcbiAgICAgICAgICAgIC8vIElmIGZvY3VzaW5nIGFuIGVsZW1lbnQgaW4gYSBjaGlsZCBzY29wZSBvZiB0aGUgY3VycmVudGx5IGFjdGl2ZSBzY29wZSwgdGhlIGNoaWxkIGJlY29tZXMgYWN0aXZlLlxuICAgICAgICAgICAgLy8gTW92aW5nIG91dCBvZiB0aGUgYWN0aXZlIHNjb3BlIHRvIGFuIGFuY2VzdG9yIGlzIG5vdCBhbGxvd2VkLlxuICAgICAgICAgICAgaWYgKCghJDliZjcxZWEyODc5M2U3MzgkdmFyJGFjdGl2ZVNjb3BlIHx8ICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRpc0FuY2VzdG9yU2NvcGUoJDliZjcxZWEyODc5M2U3MzgkdmFyJGFjdGl2ZVNjb3BlLCBzY29wZVJlZikpICYmICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRpc0VsZW1lbnRJblNjb3BlKCgwLCAkY2dhd0MkZ2V0RXZlbnRUYXJnZXQpKGUpLCBzY29wZVJlZi5jdXJyZW50KSkge1xuICAgICAgICAgICAgICAgICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRhY3RpdmVTY29wZSA9IHNjb3BlUmVmO1xuICAgICAgICAgICAgICAgIGZvY3VzZWROb2RlLmN1cnJlbnQgPSAoMCwgJGNnYXdDJGdldEV2ZW50VGFyZ2V0KShlKTtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoJDliZjcxZWEyODc5M2U3MzgkdmFyJHNob3VsZENvbnRhaW5Gb2N1cyhzY29wZVJlZikgJiYgISQ5YmY3MWVhMjg3OTNlNzM4JHZhciRpc0VsZW1lbnRJbkNoaWxkU2NvcGUoKDAsICRjZ2F3QyRnZXRFdmVudFRhcmdldCkoZSksIHNjb3BlUmVmKSkge1xuICAgICAgICAgICAgICAgIC8vIElmIGEgZm9jdXMgZXZlbnQgb2NjdXJzIG91dHNpZGUgdGhlIGFjdGl2ZSBzY29wZSAoZS5nLiB1c2VyIHRhYnMgZnJvbSBicm93c2VyIGxvY2F0aW9uIGJhciksXG4gICAgICAgICAgICAgICAgLy8gcmVzdG9yZSBmb2N1cyB0byB0aGUgcHJldmlvdXNseSBmb2N1c2VkIG5vZGUgb3IgdGhlIGZpcnN0IHRhYmJhYmxlIGVsZW1lbnQgaW4gdGhlIGFjdGl2ZSBzY29wZS5cbiAgICAgICAgICAgICAgICBpZiAoZm9jdXNlZE5vZGUuY3VycmVudCkgZm9jdXNlZE5vZGUuY3VycmVudC5mb2N1cygpO1xuICAgICAgICAgICAgICAgIGVsc2UgaWYgKCQ5YmY3MWVhMjg3OTNlNzM4JHZhciRhY3RpdmVTY29wZSAmJiAkOWJmNzFlYTI4NzkzZTczOCR2YXIkYWN0aXZlU2NvcGUuY3VycmVudCkgJDliZjcxZWEyODc5M2U3MzgkdmFyJGZvY3VzRmlyc3RJblNjb3BlKCQ5YmY3MWVhMjg3OTNlNzM4JHZhciRhY3RpdmVTY29wZS5jdXJyZW50KTtcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoJDliZjcxZWEyODc5M2U3MzgkdmFyJHNob3VsZENvbnRhaW5Gb2N1cyhzY29wZVJlZikpIGZvY3VzZWROb2RlLmN1cnJlbnQgPSAoMCwgJGNnYXdDJGdldEV2ZW50VGFyZ2V0KShlKTtcbiAgICAgICAgfTtcbiAgICAgICAgbGV0IG9uQmx1ciA9IChlKT0+e1xuICAgICAgICAgICAgLy8gRmlyZWZveCBkb2Vzbid0IHNoaWZ0IGZvY3VzIGJhY2sgdG8gdGhlIERpYWxvZyBwcm9wZXJseSB3aXRob3V0IHRoaXNcbiAgICAgICAgICAgIGlmIChyYWYuY3VycmVudCkgY2FuY2VsQW5pbWF0aW9uRnJhbWUocmFmLmN1cnJlbnQpO1xuICAgICAgICAgICAgcmFmLmN1cnJlbnQgPSByZXF1ZXN0QW5pbWF0aW9uRnJhbWUoKCk9PntcbiAgICAgICAgICAgICAgICAvLyBQYXRjaGVzIGluZmluaXRlIGZvY3VzIGNvZXJzaW9uIGxvb3AgZm9yIEFuZHJvaWQgVGFsa2JhY2sgd2hlcmUgdGhlIHVzZXIgaXNuJ3QgYWJsZSB0byBtb3ZlIHRoZSB2aXJ0dWFsIGN1cnNvclxuICAgICAgICAgICAgICAgIC8vIGlmIHdpdGhpbiBhIGNvbnRhaW5pbmcgZm9jdXMgc2NvcGUuIEJ1ZyBmaWxlZCBhZ2FpbnN0IENocm9tZTogaHR0cHM6Ly9pc3N1ZXRyYWNrZXIuZ29vZ2xlLmNvbS9pc3N1ZXMvMzg0ODQ0MDE5LlxuICAgICAgICAgICAgICAgIC8vIE5vdGUgdGhhdCB0aGlzIG1lYW5zIGZvY3VzIGNhbiBsZWF2ZSBmb2N1cyBjb250YWluaW5nIG1vZGFscyBkdWUgdG8gdGhpcywgYnV0IGl0IGlzIGlzb2xhdGVkIHRvIENocm9tZSBUYWxrYmFjay5cbiAgICAgICAgICAgICAgICBsZXQgbW9kYWxpdHkgPSAoMCwgJGNnYXdDJGdldEludGVyYWN0aW9uTW9kYWxpdHkpKCk7XG4gICAgICAgICAgICAgICAgbGV0IHNob3VsZFNraXBGb2N1c1Jlc3RvcmUgPSAobW9kYWxpdHkgPT09ICd2aXJ0dWFsJyB8fCBtb2RhbGl0eSA9PT0gbnVsbCkgJiYgKDAsICRjZ2F3QyRpc0FuZHJvaWQpKCkgJiYgKDAsICRjZ2F3QyRpc0Nocm9tZSkoKTtcbiAgICAgICAgICAgICAgICAvLyBVc2UgZG9jdW1lbnQuYWN0aXZlRWxlbWVudCBpbnN0ZWFkIG9mIGUucmVsYXRlZFRhcmdldCBzbyB3ZSBjYW4gdGVsbCBpZiB1c2VyIGNsaWNrZWQgaW50byBpZnJhbWVcbiAgICAgICAgICAgICAgICBsZXQgYWN0aXZlRWxlbWVudCA9ICgwLCAkY2dhd0MkZ2V0QWN0aXZlRWxlbWVudCkob3duZXJEb2N1bWVudCk7XG4gICAgICAgICAgICAgICAgaWYgKCFzaG91bGRTa2lwRm9jdXNSZXN0b3JlICYmIGFjdGl2ZUVsZW1lbnQgJiYgJDliZjcxZWEyODc5M2U3MzgkdmFyJHNob3VsZENvbnRhaW5Gb2N1cyhzY29wZVJlZikgJiYgISQ5YmY3MWVhMjg3OTNlNzM4JHZhciRpc0VsZW1lbnRJbkNoaWxkU2NvcGUoYWN0aXZlRWxlbWVudCwgc2NvcGVSZWYpKSB7XG4gICAgICAgICAgICAgICAgICAgICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRhY3RpdmVTY29wZSA9IHNjb3BlUmVmO1xuICAgICAgICAgICAgICAgICAgICBsZXQgdGFyZ2V0ID0gKDAsICRjZ2F3QyRnZXRFdmVudFRhcmdldCkoZSk7XG4gICAgICAgICAgICAgICAgICAgIGlmICh0YXJnZXQgJiYgdGFyZ2V0LmlzQ29ubmVjdGVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YXIgX2ZvY3VzZWROb2RlX2N1cnJlbnQ7XG4gICAgICAgICAgICAgICAgICAgICAgICBmb2N1c2VkTm9kZS5jdXJyZW50ID0gdGFyZ2V0O1xuICAgICAgICAgICAgICAgICAgICAgICAgKF9mb2N1c2VkTm9kZV9jdXJyZW50ID0gZm9jdXNlZE5vZGUuY3VycmVudCkgPT09IG51bGwgfHwgX2ZvY3VzZWROb2RlX2N1cnJlbnQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9mb2N1c2VkTm9kZV9jdXJyZW50LmZvY3VzKCk7XG4gICAgICAgICAgICAgICAgICAgIH0gZWxzZSBpZiAoJDliZjcxZWEyODc5M2U3MzgkdmFyJGFjdGl2ZVNjb3BlLmN1cnJlbnQpICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRmb2N1c0ZpcnN0SW5TY29wZSgkOWJmNzFlYTI4NzkzZTczOCR2YXIkYWN0aXZlU2NvcGUuY3VycmVudCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH07XG4gICAgICAgIG93bmVyRG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIG9uS2V5RG93biwgZmFsc2UpO1xuICAgICAgICBvd25lckRvY3VtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2ZvY3VzaW4nLCBvbkZvY3VzLCBmYWxzZSk7XG4gICAgICAgIHNjb3BlID09PSBudWxsIHx8IHNjb3BlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBzY29wZS5mb3JFYWNoKChlbGVtZW50KT0+ZWxlbWVudC5hZGRFdmVudExpc3RlbmVyKCdmb2N1c2luJywgb25Gb2N1cywgZmFsc2UpKTtcbiAgICAgICAgc2NvcGUgPT09IG51bGwgfHwgc2NvcGUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHNjb3BlLmZvckVhY2goKGVsZW1lbnQpPT5lbGVtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2ZvY3Vzb3V0Jywgb25CbHVyLCBmYWxzZSkpO1xuICAgICAgICByZXR1cm4gKCk9PntcbiAgICAgICAgICAgIG93bmVyRG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIG9uS2V5RG93biwgZmFsc2UpO1xuICAgICAgICAgICAgb3duZXJEb2N1bWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdmb2N1c2luJywgb25Gb2N1cywgZmFsc2UpO1xuICAgICAgICAgICAgc2NvcGUgPT09IG51bGwgfHwgc2NvcGUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHNjb3BlLmZvckVhY2goKGVsZW1lbnQpPT5lbGVtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2ZvY3VzaW4nLCBvbkZvY3VzLCBmYWxzZSkpO1xuICAgICAgICAgICAgc2NvcGUgPT09IG51bGwgfHwgc2NvcGUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHNjb3BlLmZvckVhY2goKGVsZW1lbnQpPT5lbGVtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2ZvY3Vzb3V0Jywgb25CbHVyLCBmYWxzZSkpO1xuICAgICAgICB9O1xuICAgIH0sIFtcbiAgICAgICAgc2NvcGVSZWYsXG4gICAgICAgIGNvbnRhaW5cbiAgICBdKTtcbiAgICAvLyBUaGlzIGlzIGEgdXNlTGF5b3V0RWZmZWN0IHNvIGl0IGlzIGd1YXJhbnRlZWQgdG8gcnVuIGJlZm9yZSBvdXIgYXN5bmMgc3ludGhldGljIGJsdXJcbiAgICAoMCwgJGNnYXdDJHVzZUxheW91dEVmZmVjdCkoKCk9PntcbiAgICAgICAgcmV0dXJuICgpPT57XG4gICAgICAgICAgICBpZiAocmFmLmN1cnJlbnQpIGNhbmNlbEFuaW1hdGlvbkZyYW1lKHJhZi5jdXJyZW50KTtcbiAgICAgICAgfTtcbiAgICB9LCBbXG4gICAgICAgIHJhZlxuICAgIF0pO1xufVxuZnVuY3Rpb24gJDliZjcxZWEyODc5M2U3MzgkdmFyJGlzRWxlbWVudEluQW55U2NvcGUoZWxlbWVudCkge1xuICAgIHJldHVybiAkOWJmNzFlYTI4NzkzZTczOCR2YXIkaXNFbGVtZW50SW5DaGlsZFNjb3BlKGVsZW1lbnQpO1xufVxuZnVuY3Rpb24gJDliZjcxZWEyODc5M2U3MzgkdmFyJGlzRWxlbWVudEluU2NvcGUoZWxlbWVudCwgc2NvcGUpIHtcbiAgICBpZiAoIWVsZW1lbnQpIHJldHVybiBmYWxzZTtcbiAgICBpZiAoIXNjb3BlKSByZXR1cm4gZmFsc2U7XG4gICAgcmV0dXJuIHNjb3BlLnNvbWUoKG5vZGUpPT5ub2RlLmNvbnRhaW5zKGVsZW1lbnQpKTtcbn1cbmZ1bmN0aW9uICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRpc0VsZW1lbnRJbkNoaWxkU2NvcGUoZWxlbWVudCwgc2NvcGUgPSBudWxsKSB7XG4gICAgLy8gSWYgdGhlIGVsZW1lbnQgaXMgd2l0aGluIGEgdG9wIGxheWVyIGVsZW1lbnQgKGUuZy4gdG9hc3RzKSwgYWx3YXlzIGFsbG93IG1vdmluZyBmb2N1cyB0aGVyZS5cbiAgICBpZiAoZWxlbWVudCBpbnN0YW5jZW9mIEVsZW1lbnQgJiYgZWxlbWVudC5jbG9zZXN0KCdbZGF0YS1yZWFjdC1hcmlhLXRvcC1sYXllcl0nKSkgcmV0dXJuIHRydWU7XG4gICAgLy8gbm9kZS5jb250YWlucyBpbiBpc0VsZW1lbnRJblNjb3BlIGNvdmVycyBjaGlsZCBzY29wZXMgdGhhdCBhcmUgYWxzbyBET00gY2hpbGRyZW4sXG4gICAgLy8gYnV0IGRvZXMgbm90IGNvdmVyIGNoaWxkIHNjb3BlcyBpbiBwb3J0YWxzLlxuICAgIGZvciAobGV0IHsgc2NvcGVSZWY6IHMgfSBvZiAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkZDA2ZmFlMmVlNjhiMTAxZS50cmF2ZXJzZSgkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkZDA2ZmFlMmVlNjhiMTAxZS5nZXRUcmVlTm9kZShzY29wZSkpKXtcbiAgICAgICAgaWYgKHMgJiYgJDliZjcxZWEyODc5M2U3MzgkdmFyJGlzRWxlbWVudEluU2NvcGUoZWxlbWVudCwgcy5jdXJyZW50KSkgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbn1cbmZ1bmN0aW9uICQ5YmY3MWVhMjg3OTNlNzM4JGV4cG9ydCQxMjU4Mzk1Zjk5YmY5Y2JmKGVsZW1lbnQpIHtcbiAgICByZXR1cm4gJDliZjcxZWEyODc5M2U3MzgkdmFyJGlzRWxlbWVudEluQ2hpbGRTY29wZShlbGVtZW50LCAkOWJmNzFlYTI4NzkzZTczOCR2YXIkYWN0aXZlU2NvcGUpO1xufVxuZnVuY3Rpb24gJDliZjcxZWEyODc5M2U3MzgkdmFyJGlzQW5jZXN0b3JTY29wZShhbmNlc3Rvciwgc2NvcGUpIHtcbiAgICB2YXIgX2ZvY3VzU2NvcGVUcmVlX2dldFRyZWVOb2RlO1xuICAgIGxldCBwYXJlbnQgPSAoX2ZvY3VzU2NvcGVUcmVlX2dldFRyZWVOb2RlID0gJDliZjcxZWEyODc5M2U3MzgkZXhwb3J0JGQwNmZhZTJlZTY4YjEwMWUuZ2V0VHJlZU5vZGUoc2NvcGUpKSA9PT0gbnVsbCB8fCBfZm9jdXNTY29wZVRyZWVfZ2V0VHJlZU5vZGUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9mb2N1c1Njb3BlVHJlZV9nZXRUcmVlTm9kZS5wYXJlbnQ7XG4gICAgd2hpbGUocGFyZW50KXtcbiAgICAgICAgaWYgKHBhcmVudC5zY29wZVJlZiA9PT0gYW5jZXN0b3IpIHJldHVybiB0cnVlO1xuICAgICAgICBwYXJlbnQgPSBwYXJlbnQucGFyZW50O1xuICAgIH1cbiAgICByZXR1cm4gZmFsc2U7XG59XG5mdW5jdGlvbiAkOWJmNzFlYTI4NzkzZTczOCR2YXIkZm9jdXNFbGVtZW50KGVsZW1lbnQsIHNjcm9sbCA9IGZhbHNlKSB7XG4gICAgaWYgKGVsZW1lbnQgIT0gbnVsbCAmJiAhc2Nyb2xsKSB0cnkge1xuICAgICAgICAoMCwgJGNnYXdDJGZvY3VzU2FmZWx5KShlbGVtZW50KTtcbiAgICB9IGNhdGNoICB7XG4gICAgLy8gaWdub3JlXG4gICAgfVxuICAgIGVsc2UgaWYgKGVsZW1lbnQgIT0gbnVsbCkgdHJ5IHtcbiAgICAgICAgZWxlbWVudC5mb2N1cygpO1xuICAgIH0gY2F0Y2ggIHtcbiAgICAvLyBpZ25vcmVcbiAgICB9XG59XG5mdW5jdGlvbiAkOWJmNzFlYTI4NzkzZTczOCR2YXIkZ2V0Rmlyc3RJblNjb3BlKHNjb3BlLCB0YWJiYWJsZSA9IHRydWUpIHtcbiAgICBsZXQgc2VudGluZWwgPSBzY29wZVswXS5wcmV2aW91c0VsZW1lbnRTaWJsaW5nO1xuICAgIGxldCBzY29wZVJvb3QgPSAkOWJmNzFlYTI4NzkzZTczOCR2YXIkZ2V0U2NvcGVSb290KHNjb3BlKTtcbiAgICBsZXQgd2Fsa2VyID0gJDliZjcxZWEyODc5M2U3MzgkZXhwb3J0JDJkNmVjOGZjMzc1Y2VhZmEoc2NvcGVSb290LCB7XG4gICAgICAgIHRhYmJhYmxlOiB0YWJiYWJsZVxuICAgIH0sIHNjb3BlKTtcbiAgICB3YWxrZXIuY3VycmVudE5vZGUgPSBzZW50aW5lbDtcbiAgICBsZXQgbmV4dE5vZGUgPSB3YWxrZXIubmV4dE5vZGUoKTtcbiAgICAvLyBJZiB0aGUgc2NvcGUgZG9lcyBub3QgY29udGFpbiBhIHRhYmJhYmxlIGVsZW1lbnQsIHVzZSB0aGUgZmlyc3QgZm9jdXNhYmxlIGVsZW1lbnQuXG4gICAgaWYgKHRhYmJhYmxlICYmICFuZXh0Tm9kZSkge1xuICAgICAgICBzY29wZVJvb3QgPSAkOWJmNzFlYTI4NzkzZTczOCR2YXIkZ2V0U2NvcGVSb290KHNjb3BlKTtcbiAgICAgICAgd2Fsa2VyID0gJDliZjcxZWEyODc5M2U3MzgkZXhwb3J0JDJkNmVjOGZjMzc1Y2VhZmEoc2NvcGVSb290LCB7XG4gICAgICAgICAgICB0YWJiYWJsZTogZmFsc2VcbiAgICAgICAgfSwgc2NvcGUpO1xuICAgICAgICB3YWxrZXIuY3VycmVudE5vZGUgPSBzZW50aW5lbDtcbiAgICAgICAgbmV4dE5vZGUgPSB3YWxrZXIubmV4dE5vZGUoKTtcbiAgICB9XG4gICAgcmV0dXJuIG5leHROb2RlO1xufVxuZnVuY3Rpb24gJDliZjcxZWEyODc5M2U3MzgkdmFyJGZvY3VzRmlyc3RJblNjb3BlKHNjb3BlLCB0YWJiYWJsZSA9IHRydWUpIHtcbiAgICAkOWJmNzFlYTI4NzkzZTczOCR2YXIkZm9jdXNFbGVtZW50KCQ5YmY3MWVhMjg3OTNlNzM4JHZhciRnZXRGaXJzdEluU2NvcGUoc2NvcGUsIHRhYmJhYmxlKSk7XG59XG5mdW5jdGlvbiAkOWJmNzFlYTI4NzkzZTczOCR2YXIkdXNlQXV0b0ZvY3VzKHNjb3BlUmVmLCBhdXRvRm9jdXMpIHtcbiAgICBjb25zdCBhdXRvRm9jdXNSZWYgPSAoMCwgJGNnYXdDJHJlYWN0KS51c2VSZWYoYXV0b0ZvY3VzKTtcbiAgICAoMCwgJGNnYXdDJHVzZUVmZmVjdCkoKCk9PntcbiAgICAgICAgaWYgKGF1dG9Gb2N1c1JlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICAkOWJmNzFlYTI4NzkzZTczOCR2YXIkYWN0aXZlU2NvcGUgPSBzY29wZVJlZjtcbiAgICAgICAgICAgIGNvbnN0IG93bmVyRG9jdW1lbnQgPSAoMCwgJGNnYXdDJGdldE93bmVyRG9jdW1lbnQpKHNjb3BlUmVmLmN1cnJlbnQgPyBzY29wZVJlZi5jdXJyZW50WzBdIDogdW5kZWZpbmVkKTtcbiAgICAgICAgICAgIGlmICghJDliZjcxZWEyODc5M2U3MzgkdmFyJGlzRWxlbWVudEluU2NvcGUoKDAsICRjZ2F3QyRnZXRBY3RpdmVFbGVtZW50KShvd25lckRvY3VtZW50KSwgJDliZjcxZWEyODc5M2U3MzgkdmFyJGFjdGl2ZVNjb3BlLmN1cnJlbnQpICYmIHNjb3BlUmVmLmN1cnJlbnQpICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRmb2N1c0ZpcnN0SW5TY29wZShzY29wZVJlZi5jdXJyZW50KTtcbiAgICAgICAgfVxuICAgICAgICBhdXRvRm9jdXNSZWYuY3VycmVudCA9IGZhbHNlO1xuICAgIH0sIFtcbiAgICAgICAgc2NvcGVSZWZcbiAgICBdKTtcbn1cbmZ1bmN0aW9uICQ5YmY3MWVhMjg3OTNlNzM4JHZhciR1c2VBY3RpdmVTY29wZVRyYWNrZXIoc2NvcGVSZWYsIHJlc3RvcmUsIGNvbnRhaW4pIHtcbiAgICAvLyB0cmFja3MgdGhlIGFjdGl2ZSBzY29wZSwgaW4gY2FzZSByZXN0b3JlIGFuZCBjb250YWluIGFyZSBib3RoIGZhbHNlLlxuICAgIC8vIGlmIGVpdGhlciBhcmUgdHJ1ZSwgdGhpcyBpcyB0cmFja2VkIGluIHVzZVJlc3RvcmVGb2N1cyBvciB1c2VGb2N1c0NvbnRhaW5tZW50LlxuICAgICgwLCAkY2dhd0MkdXNlTGF5b3V0RWZmZWN0KSgoKT0+e1xuICAgICAgICBpZiAocmVzdG9yZSB8fCBjb250YWluKSByZXR1cm47XG4gICAgICAgIGxldCBzY29wZSA9IHNjb3BlUmVmLmN1cnJlbnQ7XG4gICAgICAgIGNvbnN0IG93bmVyRG9jdW1lbnQgPSAoMCwgJGNnYXdDJGdldE93bmVyRG9jdW1lbnQpKHNjb3BlID8gc2NvcGVbMF0gOiB1bmRlZmluZWQpO1xuICAgICAgICBsZXQgb25Gb2N1cyA9IChlKT0+e1xuICAgICAgICAgICAgbGV0IHRhcmdldCA9ICgwLCAkY2dhd0MkZ2V0RXZlbnRUYXJnZXQpKGUpO1xuICAgICAgICAgICAgaWYgKCQ5YmY3MWVhMjg3OTNlNzM4JHZhciRpc0VsZW1lbnRJblNjb3BlKHRhcmdldCwgc2NvcGVSZWYuY3VycmVudCkpICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRhY3RpdmVTY29wZSA9IHNjb3BlUmVmO1xuICAgICAgICAgICAgZWxzZSBpZiAoISQ5YmY3MWVhMjg3OTNlNzM4JHZhciRpc0VsZW1lbnRJbkFueVNjb3BlKHRhcmdldCkpICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRhY3RpdmVTY29wZSA9IG51bGw7XG4gICAgICAgIH07XG4gICAgICAgIG93bmVyRG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignZm9jdXNpbicsIG9uRm9jdXMsIGZhbHNlKTtcbiAgICAgICAgc2NvcGUgPT09IG51bGwgfHwgc2NvcGUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHNjb3BlLmZvckVhY2goKGVsZW1lbnQpPT5lbGVtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2ZvY3VzaW4nLCBvbkZvY3VzLCBmYWxzZSkpO1xuICAgICAgICByZXR1cm4gKCk9PntcbiAgICAgICAgICAgIG93bmVyRG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignZm9jdXNpbicsIG9uRm9jdXMsIGZhbHNlKTtcbiAgICAgICAgICAgIHNjb3BlID09PSBudWxsIHx8IHNjb3BlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBzY29wZS5mb3JFYWNoKChlbGVtZW50KT0+ZWxlbWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdmb2N1c2luJywgb25Gb2N1cywgZmFsc2UpKTtcbiAgICAgICAgfTtcbiAgICB9LCBbXG4gICAgICAgIHNjb3BlUmVmLFxuICAgICAgICByZXN0b3JlLFxuICAgICAgICBjb250YWluXG4gICAgXSk7XG59XG5mdW5jdGlvbiAkOWJmNzFlYTI4NzkzZTczOCR2YXIkc2hvdWxkUmVzdG9yZUZvY3VzKHNjb3BlUmVmKSB7XG4gICAgbGV0IHNjb3BlID0gJDliZjcxZWEyODc5M2U3MzgkZXhwb3J0JGQwNmZhZTJlZTY4YjEwMWUuZ2V0VHJlZU5vZGUoJDliZjcxZWEyODc5M2U3MzgkdmFyJGFjdGl2ZVNjb3BlKTtcbiAgICB3aGlsZShzY29wZSAmJiBzY29wZS5zY29wZVJlZiAhPT0gc2NvcGVSZWYpe1xuICAgICAgICBpZiAoc2NvcGUubm9kZVRvUmVzdG9yZSkgcmV0dXJuIGZhbHNlO1xuICAgICAgICBzY29wZSA9IHNjb3BlLnBhcmVudDtcbiAgICB9XG4gICAgcmV0dXJuIChzY29wZSA9PT0gbnVsbCB8fCBzY29wZSA9PT0gdm9pZCAwID8gdm9pZCAwIDogc2NvcGUuc2NvcGVSZWYpID09PSBzY29wZVJlZjtcbn1cbmZ1bmN0aW9uICQ5YmY3MWVhMjg3OTNlNzM4JHZhciR1c2VSZXN0b3JlRm9jdXMoc2NvcGVSZWYsIHJlc3RvcmVGb2N1cywgY29udGFpbikge1xuICAgIC8vIGNyZWF0ZSBhIHJlZiBkdXJpbmcgcmVuZGVyIGluc3RlYWQgb2YgdXNlTGF5b3V0RWZmZWN0IHNvIHRoZSBhY3RpdmUgZWxlbWVudCBpcyBzYXZlZCBiZWZvcmUgYSBjaGlsZCB3aXRoIGF1dG9Gb2N1cz10cnVlIG1vdW50cy5cbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tcmVzdHJpY3RlZC1nbG9iYWxzXG4gICAgY29uc3Qgbm9kZVRvUmVzdG9yZVJlZiA9ICgwLCAkY2dhd0MkdXNlUmVmKSh0eXBlb2YgZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnID8gKDAsICRjZ2F3QyRnZXRBY3RpdmVFbGVtZW50KSgoMCwgJGNnYXdDJGdldE93bmVyRG9jdW1lbnQpKHNjb3BlUmVmLmN1cnJlbnQgPyBzY29wZVJlZi5jdXJyZW50WzBdIDogdW5kZWZpbmVkKSkgOiBudWxsKTtcbiAgICAvLyByZXN0b3Jpbmcgc2NvcGVzIHNob3VsZCBhbGwgdHJhY2sgaWYgdGhleSBhcmUgYWN0aXZlIHJlZ2FyZGxlc3Mgb2YgY29udGFpbiwgYnV0IGNvbnRhaW4gYWxyZWFkeSB0cmFja3MgaXQgcGx1cyBsb2dpYyB0byBjb250YWluIHRoZSBmb2N1c1xuICAgIC8vIHJlc3RvcmluZy1ub24tY29udGFpbmluZyBzY29wZXMgc2hvdWxkIG9ubHkgY2FyZSBpZiB0aGV5IGJlY29tZSBhY3RpdmUgc28gdGhleSBjYW4gcGVyZm9ybSB0aGUgcmVzdG9yZVxuICAgICgwLCAkY2dhd0MkdXNlTGF5b3V0RWZmZWN0KSgoKT0+e1xuICAgICAgICBsZXQgc2NvcGUgPSBzY29wZVJlZi5jdXJyZW50O1xuICAgICAgICBjb25zdCBvd25lckRvY3VtZW50ID0gKDAsICRjZ2F3QyRnZXRPd25lckRvY3VtZW50KShzY29wZSA/IHNjb3BlWzBdIDogdW5kZWZpbmVkKTtcbiAgICAgICAgaWYgKCFyZXN0b3JlRm9jdXMgfHwgY29udGFpbikgcmV0dXJuO1xuICAgICAgICBsZXQgb25Gb2N1cyA9ICgpPT57XG4gICAgICAgICAgICAvLyBJZiBmb2N1c2luZyBhbiBlbGVtZW50IGluIGEgY2hpbGQgc2NvcGUgb2YgdGhlIGN1cnJlbnRseSBhY3RpdmUgc2NvcGUsIHRoZSBjaGlsZCBiZWNvbWVzIGFjdGl2ZS5cbiAgICAgICAgICAgIC8vIE1vdmluZyBvdXQgb2YgdGhlIGFjdGl2ZSBzY29wZSB0byBhbiBhbmNlc3RvciBpcyBub3QgYWxsb3dlZC5cbiAgICAgICAgICAgIGlmICgoISQ5YmY3MWVhMjg3OTNlNzM4JHZhciRhY3RpdmVTY29wZSB8fCAkOWJmNzFlYTI4NzkzZTczOCR2YXIkaXNBbmNlc3RvclNjb3BlKCQ5YmY3MWVhMjg3OTNlNzM4JHZhciRhY3RpdmVTY29wZSwgc2NvcGVSZWYpKSAmJiAkOWJmNzFlYTI4NzkzZTczOCR2YXIkaXNFbGVtZW50SW5TY29wZSgoMCwgJGNnYXdDJGdldEFjdGl2ZUVsZW1lbnQpKG93bmVyRG9jdW1lbnQpLCBzY29wZVJlZi5jdXJyZW50KSkgJDliZjcxZWEyODc5M2U3MzgkdmFyJGFjdGl2ZVNjb3BlID0gc2NvcGVSZWY7XG4gICAgICAgIH07XG4gICAgICAgIG93bmVyRG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcignZm9jdXNpbicsIG9uRm9jdXMsIGZhbHNlKTtcbiAgICAgICAgc2NvcGUgPT09IG51bGwgfHwgc2NvcGUgPT09IHZvaWQgMCA/IHZvaWQgMCA6IHNjb3BlLmZvckVhY2goKGVsZW1lbnQpPT5lbGVtZW50LmFkZEV2ZW50TGlzdGVuZXIoJ2ZvY3VzaW4nLCBvbkZvY3VzLCBmYWxzZSkpO1xuICAgICAgICByZXR1cm4gKCk9PntcbiAgICAgICAgICAgIG93bmVyRG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignZm9jdXNpbicsIG9uRm9jdXMsIGZhbHNlKTtcbiAgICAgICAgICAgIHNjb3BlID09PSBudWxsIHx8IHNjb3BlID09PSB2b2lkIDAgPyB2b2lkIDAgOiBzY29wZS5mb3JFYWNoKChlbGVtZW50KT0+ZWxlbWVudC5yZW1vdmVFdmVudExpc3RlbmVyKCdmb2N1c2luJywgb25Gb2N1cywgZmFsc2UpKTtcbiAgICAgICAgfTtcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXG4gICAgfSwgW1xuICAgICAgICBzY29wZVJlZixcbiAgICAgICAgY29udGFpblxuICAgIF0pO1xuICAgICgwLCAkY2dhd0MkdXNlTGF5b3V0RWZmZWN0KSgoKT0+e1xuICAgICAgICBjb25zdCBvd25lckRvY3VtZW50ID0gKDAsICRjZ2F3QyRnZXRPd25lckRvY3VtZW50KShzY29wZVJlZi5jdXJyZW50ID8gc2NvcGVSZWYuY3VycmVudFswXSA6IHVuZGVmaW5lZCk7XG4gICAgICAgIGlmICghcmVzdG9yZUZvY3VzKSByZXR1cm47XG4gICAgICAgIC8vIEhhbmRsZSB0aGUgVGFiIGtleSBzbyB0aGF0IHRhYmJpbmcgb3V0IG9mIHRoZSBzY29wZSBnb2VzIHRvIHRoZSBuZXh0IGVsZW1lbnRcbiAgICAgICAgLy8gYWZ0ZXIgdGhlIG5vZGUgdGhhdCBoYWQgZm9jdXMgd2hlbiB0aGUgc2NvcGUgbW91bnRlZC4gVGhpcyBpcyBpbXBvcnRhbnQgd2hlblxuICAgICAgICAvLyB1c2luZyBwb3J0YWxzIGZvciBvdmVybGF5cywgc28gdGhhdCBmb2N1cyBnb2VzIHRvIHRoZSBleHBlY3RlZCBlbGVtZW50IHdoZW5cbiAgICAgICAgLy8gdGFiYmluZyBvdXQgb2YgdGhlIG92ZXJsYXkuXG4gICAgICAgIGxldCBvbktleURvd24gPSAoZSk9PntcbiAgICAgICAgICAgIGlmIChlLmtleSAhPT0gJ1RhYicgfHwgZS5hbHRLZXkgfHwgZS5jdHJsS2V5IHx8IGUubWV0YUtleSB8fCAhJDliZjcxZWEyODc5M2U3MzgkdmFyJHNob3VsZENvbnRhaW5Gb2N1cyhzY29wZVJlZikgfHwgZS5pc0NvbXBvc2luZykgcmV0dXJuO1xuICAgICAgICAgICAgbGV0IGZvY3VzZWRFbGVtZW50ID0gb3duZXJEb2N1bWVudC5hY3RpdmVFbGVtZW50O1xuICAgICAgICAgICAgaWYgKCEkOWJmNzFlYTI4NzkzZTczOCR2YXIkaXNFbGVtZW50SW5DaGlsZFNjb3BlKGZvY3VzZWRFbGVtZW50LCBzY29wZVJlZikgfHwgISQ5YmY3MWVhMjg3OTNlNzM4JHZhciRzaG91bGRSZXN0b3JlRm9jdXMoc2NvcGVSZWYpKSByZXR1cm47XG4gICAgICAgICAgICBsZXQgdHJlZU5vZGUgPSAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkZDA2ZmFlMmVlNjhiMTAxZS5nZXRUcmVlTm9kZShzY29wZVJlZik7XG4gICAgICAgICAgICBpZiAoIXRyZWVOb2RlKSByZXR1cm47XG4gICAgICAgICAgICBsZXQgbm9kZVRvUmVzdG9yZSA9IHRyZWVOb2RlLm5vZGVUb1Jlc3RvcmU7XG4gICAgICAgICAgICAvLyBDcmVhdGUgYSBET00gdHJlZSB3YWxrZXIgdGhhdCBtYXRjaGVzIGFsbCB0YWJiYWJsZSBlbGVtZW50c1xuICAgICAgICAgICAgbGV0IHdhbGtlciA9ICQ5YmY3MWVhMjg3OTNlNzM4JGV4cG9ydCQyZDZlYzhmYzM3NWNlYWZhKG93bmVyRG9jdW1lbnQuYm9keSwge1xuICAgICAgICAgICAgICAgIHRhYmJhYmxlOiB0cnVlXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIC8vIEZpbmQgdGhlIG5leHQgdGFiYmFibGUgZWxlbWVudCBhZnRlciB0aGUgY3VycmVudGx5IGZvY3VzZWQgZWxlbWVudFxuICAgICAgICAgICAgd2Fsa2VyLmN1cnJlbnROb2RlID0gZm9jdXNlZEVsZW1lbnQ7XG4gICAgICAgICAgICBsZXQgbmV4dEVsZW1lbnQgPSBlLnNoaWZ0S2V5ID8gd2Fsa2VyLnByZXZpb3VzTm9kZSgpIDogd2Fsa2VyLm5leHROb2RlKCk7XG4gICAgICAgICAgICBpZiAoIW5vZGVUb1Jlc3RvcmUgfHwgIW5vZGVUb1Jlc3RvcmUuaXNDb25uZWN0ZWQgfHwgbm9kZVRvUmVzdG9yZSA9PT0gb3duZXJEb2N1bWVudC5ib2R5KSB7XG4gICAgICAgICAgICAgICAgbm9kZVRvUmVzdG9yZSA9IHVuZGVmaW5lZDtcbiAgICAgICAgICAgICAgICB0cmVlTm9kZS5ub2RlVG9SZXN0b3JlID0gdW5kZWZpbmVkO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgLy8gSWYgdGhlcmUgaXMgbm8gbmV4dCBlbGVtZW50LCBvciBpdCBpcyBvdXRzaWRlIHRoZSBjdXJyZW50IHNjb3BlLCBtb3ZlIGZvY3VzIHRvIHRoZVxuICAgICAgICAgICAgLy8gbmV4dCBlbGVtZW50IGFmdGVyIHRoZSBub2RlIHRvIHJlc3RvcmUgdG8gaW5zdGVhZC5cbiAgICAgICAgICAgIGlmICgoIW5leHRFbGVtZW50IHx8ICEkOWJmNzFlYTI4NzkzZTczOCR2YXIkaXNFbGVtZW50SW5DaGlsZFNjb3BlKG5leHRFbGVtZW50LCBzY29wZVJlZikpICYmIG5vZGVUb1Jlc3RvcmUpIHtcbiAgICAgICAgICAgICAgICB3YWxrZXIuY3VycmVudE5vZGUgPSBub2RlVG9SZXN0b3JlO1xuICAgICAgICAgICAgICAgIC8vIFNraXAgb3ZlciBlbGVtZW50cyB3aXRoaW4gdGhlIHNjb3BlLCBpbiBjYXNlIHRoZSBzY29wZSBpbW1lZGlhdGVseSBmb2xsb3dzIHRoZSBub2RlIHRvIHJlc3RvcmUuXG4gICAgICAgICAgICAgICAgZG8gbmV4dEVsZW1lbnQgPSBlLnNoaWZ0S2V5ID8gd2Fsa2VyLnByZXZpb3VzTm9kZSgpIDogd2Fsa2VyLm5leHROb2RlKCk7XG4gICAgICAgICAgICAgICAgd2hpbGUgKCQ5YmY3MWVhMjg3OTNlNzM4JHZhciRpc0VsZW1lbnRJbkNoaWxkU2NvcGUobmV4dEVsZW1lbnQsIHNjb3BlUmVmKSk7XG4gICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgICAgICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgICAgaWYgKG5leHRFbGVtZW50KSAkOWJmNzFlYTI4NzkzZTczOCR2YXIkZm9jdXNFbGVtZW50KG5leHRFbGVtZW50LCB0cnVlKTtcbiAgICAgICAgICAgICAgICBlbHNlIC8vIElmIHRoZXJlIGlzIG5vIG5leHQgZWxlbWVudCBhbmQgdGhlIG5vZGVUb1Jlc3RvcmUgaXNuJ3Qgd2l0aGluIGEgRm9jdXNTY29wZSAoaS5lLiB3ZSBhcmUgbGVhdmluZyB0aGUgdG9wIGxldmVsIGZvY3VzIHNjb3BlKVxuICAgICAgICAgICAgICAgIC8vIHRoZW4gbW92ZSBmb2N1cyB0byB0aGUgYm9keS5cbiAgICAgICAgICAgICAgICAvLyBPdGhlcndpc2UgcmVzdG9yZSBmb2N1cyB0byB0aGUgbm9kZVRvUmVzdG9yZSAoZS5nIG1lbnUgd2l0aGluIGEgcG9wb3ZlciAtPiB0YWJiaW5nIHRvIGNsb3NlIHRoZSBtZW51IHNob3VsZCBtb3ZlIGZvY3VzIHRvIG1lbnUgdHJpZ2dlcilcbiAgICAgICAgICAgICAgICBpZiAoISQ5YmY3MWVhMjg3OTNlNzM4JHZhciRpc0VsZW1lbnRJbkFueVNjb3BlKG5vZGVUb1Jlc3RvcmUpKSBmb2N1c2VkRWxlbWVudC5ibHVyKCk7XG4gICAgICAgICAgICAgICAgZWxzZSAkOWJmNzFlYTI4NzkzZTczOCR2YXIkZm9jdXNFbGVtZW50KG5vZGVUb1Jlc3RvcmUsIHRydWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9O1xuICAgICAgICBpZiAoIWNvbnRhaW4pIG93bmVyRG9jdW1lbnQuYWRkRXZlbnRMaXN0ZW5lcigna2V5ZG93bicsIG9uS2V5RG93biwgdHJ1ZSk7XG4gICAgICAgIHJldHVybiAoKT0+e1xuICAgICAgICAgICAgaWYgKCFjb250YWluKSBvd25lckRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2tleWRvd24nLCBvbktleURvd24sIHRydWUpO1xuICAgICAgICB9O1xuICAgIH0sIFtcbiAgICAgICAgc2NvcGVSZWYsXG4gICAgICAgIHJlc3RvcmVGb2N1cyxcbiAgICAgICAgY29udGFpblxuICAgIF0pO1xuICAgIC8vIHVzZUxheW91dEVmZmVjdCBpbnN0ZWFkIG9mIHVzZUVmZmVjdCBzbyB0aGUgYWN0aXZlIGVsZW1lbnQgaXMgc2F2ZWQgc3luY2hyb25vdXNseSBpbnN0ZWFkIG9mIGFzeW5jaHJvbm91c2x5LlxuICAgICgwLCAkY2dhd0MkdXNlTGF5b3V0RWZmZWN0KSgoKT0+e1xuICAgICAgICBjb25zdCBvd25lckRvY3VtZW50ID0gKDAsICRjZ2F3QyRnZXRPd25lckRvY3VtZW50KShzY29wZVJlZi5jdXJyZW50ID8gc2NvcGVSZWYuY3VycmVudFswXSA6IHVuZGVmaW5lZCk7XG4gICAgICAgIGlmICghcmVzdG9yZUZvY3VzKSByZXR1cm47XG4gICAgICAgIGxldCB0cmVlTm9kZSA9ICQ5YmY3MWVhMjg3OTNlNzM4JGV4cG9ydCRkMDZmYWUyZWU2OGIxMDFlLmdldFRyZWVOb2RlKHNjb3BlUmVmKTtcbiAgICAgICAgaWYgKCF0cmVlTm9kZSkgcmV0dXJuO1xuICAgICAgICB2YXIgX25vZGVUb1Jlc3RvcmVSZWZfY3VycmVudDtcbiAgICAgICAgdHJlZU5vZGUubm9kZVRvUmVzdG9yZSA9IChfbm9kZVRvUmVzdG9yZVJlZl9jdXJyZW50ID0gbm9kZVRvUmVzdG9yZVJlZi5jdXJyZW50KSAhPT0gbnVsbCAmJiBfbm9kZVRvUmVzdG9yZVJlZl9jdXJyZW50ICE9PSB2b2lkIDAgPyBfbm9kZVRvUmVzdG9yZVJlZl9jdXJyZW50IDogdW5kZWZpbmVkO1xuICAgICAgICByZXR1cm4gKCk9PntcbiAgICAgICAgICAgIGxldCB0cmVlTm9kZSA9ICQ5YmY3MWVhMjg3OTNlNzM4JGV4cG9ydCRkMDZmYWUyZWU2OGIxMDFlLmdldFRyZWVOb2RlKHNjb3BlUmVmKTtcbiAgICAgICAgICAgIGlmICghdHJlZU5vZGUpIHJldHVybjtcbiAgICAgICAgICAgIGxldCBub2RlVG9SZXN0b3JlID0gdHJlZU5vZGUubm9kZVRvUmVzdG9yZTtcbiAgICAgICAgICAgIC8vIGlmIHdlIGFscmVhZHkgbG9zdCBmb2N1cyB0byB0aGUgYm9keSBhbmQgdGhpcyB3YXMgdGhlIGFjdGl2ZSBzY29wZSwgdGhlbiB3ZSBzaG91bGQgYXR0ZW1wdCB0byByZXN0b3JlXG4gICAgICAgICAgICBsZXQgYWN0aXZlRWxlbWVudCA9ICgwLCAkY2dhd0MkZ2V0QWN0aXZlRWxlbWVudCkob3duZXJEb2N1bWVudCk7XG4gICAgICAgICAgICBpZiAocmVzdG9yZUZvY3VzICYmIG5vZGVUb1Jlc3RvcmUgJiYgKGFjdGl2ZUVsZW1lbnQgJiYgJDliZjcxZWEyODc5M2U3MzgkdmFyJGlzRWxlbWVudEluQ2hpbGRTY29wZShhY3RpdmVFbGVtZW50LCBzY29wZVJlZikgfHwgYWN0aXZlRWxlbWVudCA9PT0gb3duZXJEb2N1bWVudC5ib2R5ICYmICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRzaG91bGRSZXN0b3JlRm9jdXMoc2NvcGVSZWYpKSkge1xuICAgICAgICAgICAgICAgIC8vIGZyZWV6ZSB0aGUgZm9jdXNTY29wZVRyZWUgc28gaXQgcGVyc2lzdHMgYWZ0ZXIgdGhlIHJhZiwgb3RoZXJ3aXNlIGR1cmluZyB1bm1vdW50IG5vZGVzIGFyZSByZW1vdmVkIGZyb20gaXRcbiAgICAgICAgICAgICAgICBsZXQgY2xvbmVkVHJlZSA9ICQ5YmY3MWVhMjg3OTNlNzM4JGV4cG9ydCRkMDZmYWUyZWU2OGIxMDFlLmNsb25lKCk7XG4gICAgICAgICAgICAgICAgcmVxdWVzdEFuaW1hdGlvbkZyYW1lKCgpPT57XG4gICAgICAgICAgICAgICAgICAgIC8vIE9ubHkgcmVzdG9yZSBmb2N1cyBpZiB3ZSd2ZSBsb3N0IGZvY3VzIHRvIHRoZSBib2R5LCB0aGUgYWx0ZXJuYXRpdmUgaXMgdGhhdCBmb2N1cyBoYXMgYmVlbiBwdXJwb3NlZnVsbHkgbW92ZWQgZWxzZXdoZXJlXG4gICAgICAgICAgICAgICAgICAgIGlmIChvd25lckRvY3VtZW50LmFjdGl2ZUVsZW1lbnQgPT09IG93bmVyRG9jdW1lbnQuYm9keSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gbG9vayB1cCB0aGUgdHJlZSBzdGFydGluZyB3aXRoIG91ciBzY29wZSB0byBmaW5kIGEgbm9kZVRvUmVzdG9yZSBzdGlsbCBpbiB0aGUgRE9NXG4gICAgICAgICAgICAgICAgICAgICAgICBsZXQgdHJlZU5vZGUgPSBjbG9uZWRUcmVlLmdldFRyZWVOb2RlKHNjb3BlUmVmKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIHdoaWxlKHRyZWVOb2RlKXtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAodHJlZU5vZGUubm9kZVRvUmVzdG9yZSAmJiB0cmVlTm9kZS5ub2RlVG9SZXN0b3JlLmlzQ29ubmVjdGVkKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRyZXN0b3JlRm9jdXNUb0VsZW1lbnQodHJlZU5vZGUubm9kZVRvUmVzdG9yZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJlZU5vZGUgPSB0cmVlTm9kZS5wYXJlbnQ7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBJZiBubyBub2RlVG9SZXN0b3JlIHdhcyBmb3VuZCwgZm9jdXMgdGhlIGZpcnN0IGVsZW1lbnQgaW4gdGhlIG5lYXJlc3RcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIGFuY2VzdG9yIHNjb3BlIHRoYXQgaXMgc3RpbGwgaW4gdGhlIHRyZWUuXG4gICAgICAgICAgICAgICAgICAgICAgICB0cmVlTm9kZSA9IGNsb25lZFRyZWUuZ2V0VHJlZU5vZGUoc2NvcGVSZWYpO1xuICAgICAgICAgICAgICAgICAgICAgICAgd2hpbGUodHJlZU5vZGUpe1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICh0cmVlTm9kZS5zY29wZVJlZiAmJiB0cmVlTm9kZS5zY29wZVJlZi5jdXJyZW50ICYmICQ5YmY3MWVhMjg3OTNlNzM4JGV4cG9ydCRkMDZmYWUyZWU2OGIxMDFlLmdldFRyZWVOb2RlKHRyZWVOb2RlLnNjb3BlUmVmKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsZXQgbm9kZSA9ICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRnZXRGaXJzdEluU2NvcGUodHJlZU5vZGUuc2NvcGVSZWYuY3VycmVudCwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRyZXN0b3JlRm9jdXNUb0VsZW1lbnQobm9kZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdHJlZU5vZGUgPSB0cmVlTm9kZS5wYXJlbnQ7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfTtcbiAgICB9LCBbXG4gICAgICAgIHNjb3BlUmVmLFxuICAgICAgICByZXN0b3JlRm9jdXNcbiAgICBdKTtcbn1cbmZ1bmN0aW9uICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRyZXN0b3JlRm9jdXNUb0VsZW1lbnQobm9kZSkge1xuICAgIC8vIERpc3BhdGNoIGEgY3VzdG9tIGV2ZW50IHRoYXQgcGFyZW50IGVsZW1lbnRzIGNhbiBpbnRlcmNlcHQgdG8gY3VzdG9taXplIGZvY3VzIHJlc3RvcmF0aW9uLlxuICAgIC8vIEZvciBleGFtcGxlLCB2aXJ0dWFsaXplZCBjb2xsZWN0aW9uIGNvbXBvbmVudHMgcmV1c2UgRE9NIGVsZW1lbnRzLCBzbyB0aGUgb3JpZ2luYWwgZWxlbWVudFxuICAgIC8vIG1pZ2h0IHN0aWxsIGV4aXN0IGluIHRoZSBET00gYnV0IHJlcHJlc2VudGluZyBhIGRpZmZlcmVudCBpdGVtLlxuICAgIGlmIChub2RlLmRpc3BhdGNoRXZlbnQobmV3IEN1c3RvbUV2ZW50KCQ5YmY3MWVhMjg3OTNlNzM4JHZhciRSRVNUT1JFX0ZPQ1VTX0VWRU5ULCB7XG4gICAgICAgIGJ1YmJsZXM6IHRydWUsXG4gICAgICAgIGNhbmNlbGFibGU6IHRydWVcbiAgICB9KSkpICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRmb2N1c0VsZW1lbnQobm9kZSk7XG59XG5mdW5jdGlvbiAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkMmQ2ZWM4ZmMzNzVjZWFmYShyb290LCBvcHRzLCBzY29wZSkge1xuICAgIGxldCBmaWx0ZXIgPSAob3B0cyA9PT0gbnVsbCB8fCBvcHRzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvcHRzLnRhYmJhYmxlKSA/ICgwLCAkY2dhd0MkaXNUYWJiYWJsZSkgOiAoMCwgJGNnYXdDJGlzRm9jdXNhYmxlKTtcbiAgICAvLyBFbnN1cmUgdGhhdCByb290IGlzIGFuIEVsZW1lbnQgb3IgZmFsbCBiYWNrIGFwcHJvcHJpYXRlbHlcbiAgICBsZXQgcm9vdEVsZW1lbnQgPSAocm9vdCA9PT0gbnVsbCB8fCByb290ID09PSB2b2lkIDAgPyB2b2lkIDAgOiByb290Lm5vZGVUeXBlKSA9PT0gTm9kZS5FTEVNRU5UX05PREUgPyByb290IDogbnVsbDtcbiAgICAvLyBEZXRlcm1pbmUgdGhlIGRvY3VtZW50IHRvIHVzZVxuICAgIGxldCBkb2MgPSAoMCwgJGNnYXdDJGdldE93bmVyRG9jdW1lbnQpKHJvb3RFbGVtZW50KTtcbiAgICAvLyBDcmVhdGUgYSBUcmVlV2Fsa2VyLCBlbnN1cmluZyB0aGUgcm9vdCBpcyBhbiBFbGVtZW50IG9yIERvY3VtZW50XG4gICAgbGV0IHdhbGtlciA9ICgwLCAkY2dhd0MkY3JlYXRlU2hhZG93VHJlZVdhbGtlcikoZG9jLCByb290IHx8IGRvYywgTm9kZUZpbHRlci5TSE9XX0VMRU1FTlQsIHtcbiAgICAgICAgYWNjZXB0Tm9kZSAobm9kZSkge1xuICAgICAgICAgICAgdmFyIF9vcHRzX2Zyb207XG4gICAgICAgICAgICAvLyBTa2lwIG5vZGVzIGluc2lkZSB0aGUgc3RhcnRpbmcgbm9kZS5cbiAgICAgICAgICAgIGlmIChvcHRzID09PSBudWxsIHx8IG9wdHMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IChfb3B0c19mcm9tID0gb3B0cy5mcm9tKSA9PT0gbnVsbCB8fCBfb3B0c19mcm9tID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfb3B0c19mcm9tLmNvbnRhaW5zKG5vZGUpKSByZXR1cm4gTm9kZUZpbHRlci5GSUxURVJfUkVKRUNUO1xuICAgICAgICAgICAgaWYgKGZpbHRlcihub2RlKSAmJiAoMCwgJDY0NWYyZTY3Yjg1YTI0YzkkZXhwb3J0JGU5ODljMGZmZmFhNmIyN2EpKG5vZGUpICYmICghc2NvcGUgfHwgJDliZjcxZWEyODc5M2U3MzgkdmFyJGlzRWxlbWVudEluU2NvcGUobm9kZSwgc2NvcGUpKSAmJiAoIShvcHRzID09PSBudWxsIHx8IG9wdHMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9wdHMuYWNjZXB0KSB8fCBvcHRzLmFjY2VwdChub2RlKSkpIHJldHVybiBOb2RlRmlsdGVyLkZJTFRFUl9BQ0NFUFQ7XG4gICAgICAgICAgICByZXR1cm4gTm9kZUZpbHRlci5GSUxURVJfU0tJUDtcbiAgICAgICAgfVxuICAgIH0pO1xuICAgIGlmIChvcHRzID09PSBudWxsIHx8IG9wdHMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9wdHMuZnJvbSkgd2Fsa2VyLmN1cnJlbnROb2RlID0gb3B0cy5mcm9tO1xuICAgIHJldHVybiB3YWxrZXI7XG59XG5mdW5jdGlvbiAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkYzUyNTFiOWUxMjRiZjI5KHJlZiwgZGVmYXVsdE9wdGlvbnMgPSB7fSkge1xuICAgIHJldHVybiB7XG4gICAgICAgIGZvY3VzTmV4dCAob3B0cyA9IHt9KSB7XG4gICAgICAgICAgICBsZXQgcm9vdCA9IHJlZi5jdXJyZW50O1xuICAgICAgICAgICAgaWYgKCFyb290KSByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgIGxldCB7IGZyb206IGZyb20sIHRhYmJhYmxlOiB0YWJiYWJsZSA9IGRlZmF1bHRPcHRpb25zLnRhYmJhYmxlLCB3cmFwOiB3cmFwID0gZGVmYXVsdE9wdGlvbnMud3JhcCwgYWNjZXB0OiBhY2NlcHQgPSBkZWZhdWx0T3B0aW9ucy5hY2NlcHQgfSA9IG9wdHM7XG4gICAgICAgICAgICBsZXQgbm9kZSA9IGZyb20gfHwgKDAsICRjZ2F3QyRnZXRBY3RpdmVFbGVtZW50KSgoMCwgJGNnYXdDJGdldE93bmVyRG9jdW1lbnQpKHJvb3QpKTtcbiAgICAgICAgICAgIGxldCB3YWxrZXIgPSAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkMmQ2ZWM4ZmMzNzVjZWFmYShyb290LCB7XG4gICAgICAgICAgICAgICAgdGFiYmFibGU6IHRhYmJhYmxlLFxuICAgICAgICAgICAgICAgIGFjY2VwdDogYWNjZXB0XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGlmIChyb290LmNvbnRhaW5zKG5vZGUpKSB3YWxrZXIuY3VycmVudE5vZGUgPSBub2RlO1xuICAgICAgICAgICAgbGV0IG5leHROb2RlID0gd2Fsa2VyLm5leHROb2RlKCk7XG4gICAgICAgICAgICBpZiAoIW5leHROb2RlICYmIHdyYXApIHtcbiAgICAgICAgICAgICAgICB3YWxrZXIuY3VycmVudE5vZGUgPSByb290O1xuICAgICAgICAgICAgICAgIG5leHROb2RlID0gd2Fsa2VyLm5leHROb2RlKCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAobmV4dE5vZGUpICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRmb2N1c0VsZW1lbnQobmV4dE5vZGUsIHRydWUpO1xuICAgICAgICAgICAgcmV0dXJuIG5leHROb2RlO1xuICAgICAgICB9LFxuICAgICAgICBmb2N1c1ByZXZpb3VzIChvcHRzID0gZGVmYXVsdE9wdGlvbnMpIHtcbiAgICAgICAgICAgIGxldCByb290ID0gcmVmLmN1cnJlbnQ7XG4gICAgICAgICAgICBpZiAoIXJvb3QpIHJldHVybiBudWxsO1xuICAgICAgICAgICAgbGV0IHsgZnJvbTogZnJvbSwgdGFiYmFibGU6IHRhYmJhYmxlID0gZGVmYXVsdE9wdGlvbnMudGFiYmFibGUsIHdyYXA6IHdyYXAgPSBkZWZhdWx0T3B0aW9ucy53cmFwLCBhY2NlcHQ6IGFjY2VwdCA9IGRlZmF1bHRPcHRpb25zLmFjY2VwdCB9ID0gb3B0cztcbiAgICAgICAgICAgIGxldCBub2RlID0gZnJvbSB8fCAoMCwgJGNnYXdDJGdldEFjdGl2ZUVsZW1lbnQpKCgwLCAkY2dhd0MkZ2V0T3duZXJEb2N1bWVudCkocm9vdCkpO1xuICAgICAgICAgICAgbGV0IHdhbGtlciA9ICQ5YmY3MWVhMjg3OTNlNzM4JGV4cG9ydCQyZDZlYzhmYzM3NWNlYWZhKHJvb3QsIHtcbiAgICAgICAgICAgICAgICB0YWJiYWJsZTogdGFiYmFibGUsXG4gICAgICAgICAgICAgICAgYWNjZXB0OiBhY2NlcHRcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaWYgKHJvb3QuY29udGFpbnMobm9kZSkpIHdhbGtlci5jdXJyZW50Tm9kZSA9IG5vZGU7XG4gICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICBsZXQgbmV4dCA9ICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRsYXN0KHdhbGtlcik7XG4gICAgICAgICAgICAgICAgaWYgKG5leHQpICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRmb2N1c0VsZW1lbnQobmV4dCwgdHJ1ZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG5leHQgIT09IG51bGwgJiYgbmV4dCAhPT0gdm9pZCAwID8gbmV4dCA6IG51bGw7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBsZXQgcHJldmlvdXNOb2RlID0gd2Fsa2VyLnByZXZpb3VzTm9kZSgpO1xuICAgICAgICAgICAgaWYgKCFwcmV2aW91c05vZGUgJiYgd3JhcCkge1xuICAgICAgICAgICAgICAgIHdhbGtlci5jdXJyZW50Tm9kZSA9IHJvb3Q7XG4gICAgICAgICAgICAgICAgbGV0IGxhc3ROb2RlID0gJDliZjcxZWEyODc5M2U3MzgkdmFyJGxhc3Qod2Fsa2VyKTtcbiAgICAgICAgICAgICAgICBpZiAoIWxhc3ROb2RlKSAvLyBjb3VsZG4ndCB3cmFwXG4gICAgICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgICAgICAgcHJldmlvdXNOb2RlID0gbGFzdE5vZGU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAocHJldmlvdXNOb2RlKSAkOWJmNzFlYTI4NzkzZTczOCR2YXIkZm9jdXNFbGVtZW50KHByZXZpb3VzTm9kZSwgdHJ1ZSk7XG4gICAgICAgICAgICByZXR1cm4gcHJldmlvdXNOb2RlICE9PSBudWxsICYmIHByZXZpb3VzTm9kZSAhPT0gdm9pZCAwID8gcHJldmlvdXNOb2RlIDogbnVsbDtcbiAgICAgICAgfSxcbiAgICAgICAgZm9jdXNGaXJzdCAob3B0cyA9IGRlZmF1bHRPcHRpb25zKSB7XG4gICAgICAgICAgICBsZXQgcm9vdCA9IHJlZi5jdXJyZW50O1xuICAgICAgICAgICAgaWYgKCFyb290KSByZXR1cm4gbnVsbDtcbiAgICAgICAgICAgIGxldCB7IHRhYmJhYmxlOiB0YWJiYWJsZSA9IGRlZmF1bHRPcHRpb25zLnRhYmJhYmxlLCBhY2NlcHQ6IGFjY2VwdCA9IGRlZmF1bHRPcHRpb25zLmFjY2VwdCB9ID0gb3B0cztcbiAgICAgICAgICAgIGxldCB3YWxrZXIgPSAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkMmQ2ZWM4ZmMzNzVjZWFmYShyb290LCB7XG4gICAgICAgICAgICAgICAgdGFiYmFibGU6IHRhYmJhYmxlLFxuICAgICAgICAgICAgICAgIGFjY2VwdDogYWNjZXB0XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGxldCBuZXh0Tm9kZSA9IHdhbGtlci5uZXh0Tm9kZSgpO1xuICAgICAgICAgICAgaWYgKG5leHROb2RlKSAkOWJmNzFlYTI4NzkzZTczOCR2YXIkZm9jdXNFbGVtZW50KG5leHROb2RlLCB0cnVlKTtcbiAgICAgICAgICAgIHJldHVybiBuZXh0Tm9kZTtcbiAgICAgICAgfSxcbiAgICAgICAgZm9jdXNMYXN0IChvcHRzID0gZGVmYXVsdE9wdGlvbnMpIHtcbiAgICAgICAgICAgIGxldCByb290ID0gcmVmLmN1cnJlbnQ7XG4gICAgICAgICAgICBpZiAoIXJvb3QpIHJldHVybiBudWxsO1xuICAgICAgICAgICAgbGV0IHsgdGFiYmFibGU6IHRhYmJhYmxlID0gZGVmYXVsdE9wdGlvbnMudGFiYmFibGUsIGFjY2VwdDogYWNjZXB0ID0gZGVmYXVsdE9wdGlvbnMuYWNjZXB0IH0gPSBvcHRzO1xuICAgICAgICAgICAgbGV0IHdhbGtlciA9ICQ5YmY3MWVhMjg3OTNlNzM4JGV4cG9ydCQyZDZlYzhmYzM3NWNlYWZhKHJvb3QsIHtcbiAgICAgICAgICAgICAgICB0YWJiYWJsZTogdGFiYmFibGUsXG4gICAgICAgICAgICAgICAgYWNjZXB0OiBhY2NlcHRcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgbGV0IG5leHQgPSAkOWJmNzFlYTI4NzkzZTczOCR2YXIkbGFzdCh3YWxrZXIpO1xuICAgICAgICAgICAgaWYgKG5leHQpICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRmb2N1c0VsZW1lbnQobmV4dCwgdHJ1ZSk7XG4gICAgICAgICAgICByZXR1cm4gbmV4dCAhPT0gbnVsbCAmJiBuZXh0ICE9PSB2b2lkIDAgPyBuZXh0IDogbnVsbDtcbiAgICAgICAgfVxuICAgIH07XG59XG5mdW5jdGlvbiAkOWJmNzFlYTI4NzkzZTczOCR2YXIkbGFzdCh3YWxrZXIpIHtcbiAgICBsZXQgbmV4dCA9IHVuZGVmaW5lZDtcbiAgICBsZXQgbGFzdDtcbiAgICBkbyB7XG4gICAgICAgIGxhc3QgPSB3YWxrZXIubGFzdENoaWxkKCk7XG4gICAgICAgIGlmIChsYXN0KSBuZXh0ID0gbGFzdDtcbiAgICB9d2hpbGUgKGxhc3QpO1xuICAgIHJldHVybiBuZXh0O1xufVxuY2xhc3MgJDliZjcxZWEyODc5M2U3MzgkdmFyJFRyZWUge1xuICAgIGdldCBzaXplKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5mYXN0TWFwLnNpemU7XG4gICAgfVxuICAgIGdldFRyZWVOb2RlKGRhdGEpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuZmFzdE1hcC5nZXQoZGF0YSk7XG4gICAgfVxuICAgIGFkZFRyZWVOb2RlKHNjb3BlUmVmLCBwYXJlbnQsIG5vZGVUb1Jlc3RvcmUpIHtcbiAgICAgICAgbGV0IHBhcmVudE5vZGUgPSB0aGlzLmZhc3RNYXAuZ2V0KHBhcmVudCAhPT0gbnVsbCAmJiBwYXJlbnQgIT09IHZvaWQgMCA/IHBhcmVudCA6IG51bGwpO1xuICAgICAgICBpZiAoIXBhcmVudE5vZGUpIHJldHVybjtcbiAgICAgICAgbGV0IG5vZGUgPSBuZXcgJDliZjcxZWEyODc5M2U3MzgkdmFyJFRyZWVOb2RlKHtcbiAgICAgICAgICAgIHNjb3BlUmVmOiBzY29wZVJlZlxuICAgICAgICB9KTtcbiAgICAgICAgcGFyZW50Tm9kZS5hZGRDaGlsZChub2RlKTtcbiAgICAgICAgbm9kZS5wYXJlbnQgPSBwYXJlbnROb2RlO1xuICAgICAgICB0aGlzLmZhc3RNYXAuc2V0KHNjb3BlUmVmLCBub2RlKTtcbiAgICAgICAgaWYgKG5vZGVUb1Jlc3RvcmUpIG5vZGUubm9kZVRvUmVzdG9yZSA9IG5vZGVUb1Jlc3RvcmU7XG4gICAgfVxuICAgIGFkZE5vZGUobm9kZSkge1xuICAgICAgICB0aGlzLmZhc3RNYXAuc2V0KG5vZGUuc2NvcGVSZWYsIG5vZGUpO1xuICAgIH1cbiAgICByZW1vdmVUcmVlTm9kZShzY29wZVJlZikge1xuICAgICAgICAvLyBuZXZlciByZW1vdmUgdGhlIHJvb3RcbiAgICAgICAgaWYgKHNjb3BlUmVmID09PSBudWxsKSByZXR1cm47XG4gICAgICAgIGxldCBub2RlID0gdGhpcy5mYXN0TWFwLmdldChzY29wZVJlZik7XG4gICAgICAgIGlmICghbm9kZSkgcmV0dXJuO1xuICAgICAgICBsZXQgcGFyZW50Tm9kZSA9IG5vZGUucGFyZW50O1xuICAgICAgICAvLyB3aGVuIHdlIHJlbW92ZSBhIHNjb3BlLCBjaGVjayBpZiBhbnkgc2libGluZyBzY29wZXMgYXJlIHRyeWluZyB0byByZXN0b3JlIGZvY3VzIHRvIHNvbWV0aGluZyBpbnNpZGUgdGhlIHNjb3BlIHdlJ3JlIHJlbW92aW5nXG4gICAgICAgIC8vIGlmIHdlIGFyZSwgdGhlbiByZXBsYWNlIHRoZSBzaWJsaW5ncyByZXN0b3JlIHdpdGggdGhlIHJlc3RvcmUgZnJvbSB0aGUgc2NvcGUgd2UncmUgcmVtb3ZpbmdcbiAgICAgICAgZm9yIChsZXQgY3VycmVudCBvZiB0aGlzLnRyYXZlcnNlKCkpaWYgKGN1cnJlbnQgIT09IG5vZGUgJiYgbm9kZS5ub2RlVG9SZXN0b3JlICYmIGN1cnJlbnQubm9kZVRvUmVzdG9yZSAmJiBub2RlLnNjb3BlUmVmICYmIG5vZGUuc2NvcGVSZWYuY3VycmVudCAmJiAkOWJmNzFlYTI4NzkzZTczOCR2YXIkaXNFbGVtZW50SW5TY29wZShjdXJyZW50Lm5vZGVUb1Jlc3RvcmUsIG5vZGUuc2NvcGVSZWYuY3VycmVudCkpIGN1cnJlbnQubm9kZVRvUmVzdG9yZSA9IG5vZGUubm9kZVRvUmVzdG9yZTtcbiAgICAgICAgbGV0IGNoaWxkcmVuID0gbm9kZS5jaGlsZHJlbjtcbiAgICAgICAgaWYgKHBhcmVudE5vZGUpIHtcbiAgICAgICAgICAgIHBhcmVudE5vZGUucmVtb3ZlQ2hpbGQobm9kZSk7XG4gICAgICAgICAgICBpZiAoY2hpbGRyZW4uc2l6ZSA+IDApIGNoaWxkcmVuLmZvckVhY2goKGNoaWxkKT0+cGFyZW50Tm9kZSAmJiBwYXJlbnROb2RlLmFkZENoaWxkKGNoaWxkKSk7XG4gICAgICAgIH1cbiAgICAgICAgdGhpcy5mYXN0TWFwLmRlbGV0ZShub2RlLnNjb3BlUmVmKTtcbiAgICB9XG4gICAgLy8gUHJlIE9yZGVyIERlcHRoIEZpcnN0XG4gICAgKnRyYXZlcnNlKG5vZGUgPSB0aGlzLnJvb3QpIHtcbiAgICAgICAgaWYgKG5vZGUuc2NvcGVSZWYgIT0gbnVsbCkgeWllbGQgbm9kZTtcbiAgICAgICAgaWYgKG5vZGUuY2hpbGRyZW4uc2l6ZSA+IDApIGZvciAobGV0IGNoaWxkIG9mIG5vZGUuY2hpbGRyZW4peWllbGQqIHRoaXMudHJhdmVyc2UoY2hpbGQpO1xuICAgIH1cbiAgICBjbG9uZSgpIHtcbiAgICAgICAgdmFyIF9ub2RlX3BhcmVudDtcbiAgICAgICAgbGV0IG5ld1RyZWUgPSBuZXcgJDliZjcxZWEyODc5M2U3MzgkdmFyJFRyZWUoKTtcbiAgICAgICAgdmFyIF9ub2RlX3BhcmVudF9zY29wZVJlZjtcbiAgICAgICAgZm9yIChsZXQgbm9kZSBvZiB0aGlzLnRyYXZlcnNlKCkpbmV3VHJlZS5hZGRUcmVlTm9kZShub2RlLnNjb3BlUmVmLCAoX25vZGVfcGFyZW50X3Njb3BlUmVmID0gKF9ub2RlX3BhcmVudCA9IG5vZGUucGFyZW50KSA9PT0gbnVsbCB8fCBfbm9kZV9wYXJlbnQgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9ub2RlX3BhcmVudC5zY29wZVJlZikgIT09IG51bGwgJiYgX25vZGVfcGFyZW50X3Njb3BlUmVmICE9PSB2b2lkIDAgPyBfbm9kZV9wYXJlbnRfc2NvcGVSZWYgOiBudWxsLCBub2RlLm5vZGVUb1Jlc3RvcmUpO1xuICAgICAgICByZXR1cm4gbmV3VHJlZTtcbiAgICB9XG4gICAgY29uc3RydWN0b3IoKXtcbiAgICAgICAgdGhpcy5mYXN0TWFwID0gbmV3IE1hcCgpO1xuICAgICAgICB0aGlzLnJvb3QgPSBuZXcgJDliZjcxZWEyODc5M2U3MzgkdmFyJFRyZWVOb2RlKHtcbiAgICAgICAgICAgIHNjb3BlUmVmOiBudWxsXG4gICAgICAgIH0pO1xuICAgICAgICB0aGlzLmZhc3RNYXAuc2V0KG51bGwsIHRoaXMucm9vdCk7XG4gICAgfVxufVxuY2xhc3MgJDliZjcxZWEyODc5M2U3MzgkdmFyJFRyZWVOb2RlIHtcbiAgICBhZGRDaGlsZChub2RlKSB7XG4gICAgICAgIHRoaXMuY2hpbGRyZW4uYWRkKG5vZGUpO1xuICAgICAgICBub2RlLnBhcmVudCA9IHRoaXM7XG4gICAgfVxuICAgIHJlbW92ZUNoaWxkKG5vZGUpIHtcbiAgICAgICAgdGhpcy5jaGlsZHJlbi5kZWxldGUobm9kZSk7XG4gICAgICAgIG5vZGUucGFyZW50ID0gdW5kZWZpbmVkO1xuICAgIH1cbiAgICBjb25zdHJ1Y3Rvcihwcm9wcyl7XG4gICAgICAgIHRoaXMuY2hpbGRyZW4gPSBuZXcgU2V0KCk7XG4gICAgICAgIHRoaXMuY29udGFpbiA9IGZhbHNlO1xuICAgICAgICB0aGlzLnNjb3BlUmVmID0gcHJvcHMuc2NvcGVSZWY7XG4gICAgfVxufVxubGV0ICQ5YmY3MWVhMjg3OTNlNzM4JGV4cG9ydCRkMDZmYWUyZWU2OGIxMDFlID0gbmV3ICQ5YmY3MWVhMjg3OTNlNzM4JHZhciRUcmVlKCk7XG5cblxuZXhwb3J0IHskOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkMjBlNDAyODk2NDFmYmJiNiBhcyBGb2N1c1Njb3BlLCAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkZDA2ZmFlMmVlNjhiMTAxZSBhcyBmb2N1c1Njb3BlVHJlZSwgJDliZjcxZWEyODc5M2U3MzgkZXhwb3J0JDEwYzUxNjk3NTVjZTdiZDcgYXMgdXNlRm9jdXNNYW5hZ2VyLCAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkMmQ2ZWM4ZmMzNzVjZWFmYSBhcyBnZXRGb2N1c2FibGVUcmVlV2Fsa2VyLCAkOWJmNzFlYTI4NzkzZTczOCRleHBvcnQkMTI1ODM5NWY5OWJmOWNiZiBhcyBpc0VsZW1lbnRJbkNoaWxkT2ZBY3RpdmVTY29wZSwgJDliZjcxZWEyODc5M2U3MzgkZXhwb3J0JGM1MjUxYjllMTI0YmYyOSBhcyBjcmVhdGVGb2N1c01hbmFnZXJ9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9Rm9jdXNTY29wZS5tb2R1bGUuanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/FocusScope.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/isElementVisible.mjs":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/isElementVisible.mjs ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isElementVisible: () => (/* binding */ $645f2e67b85a24c9$export$e989c0fffaa6b27a)\n/* harmony export */ });\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/domHelpers.mjs\");\n\n\n/*\n * Copyright 2021 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \nfunction $645f2e67b85a24c9$var$isStyleVisible(element) {\n    const windowObject = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.getOwnerWindow)(element);\n    if (!(element instanceof windowObject.HTMLElement) && !(element instanceof windowObject.SVGElement)) return false;\n    let { display: display, visibility: visibility } = element.style;\n    let isVisible = display !== 'none' && visibility !== 'hidden' && visibility !== 'collapse';\n    if (isVisible) {\n        const { getComputedStyle: getComputedStyle } = element.ownerDocument.defaultView;\n        let { display: computedDisplay, visibility: computedVisibility } = getComputedStyle(element);\n        isVisible = computedDisplay !== 'none' && computedVisibility !== 'hidden' && computedVisibility !== 'collapse';\n    }\n    return isVisible;\n}\nfunction $645f2e67b85a24c9$var$isAttributeVisible(element, childElement) {\n    return !element.hasAttribute('hidden') && // Ignore HiddenSelect when tree walking.\n    !element.hasAttribute('data-react-aria-prevent-focus') && (element.nodeName === 'DETAILS' && childElement && childElement.nodeName !== 'SUMMARY' ? element.hasAttribute('open') : true);\n}\nfunction $645f2e67b85a24c9$export$e989c0fffaa6b27a(element, childElement) {\n    return element.nodeName !== '#comment' && $645f2e67b85a24c9$var$isStyleVisible(element) && $645f2e67b85a24c9$var$isAttributeVisible(element, childElement) && (!element.parentElement || $645f2e67b85a24c9$export$e989c0fffaa6b27a(element.parentElement, element));\n}\n\n\n\n//# sourceMappingURL=isElementVisible.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/isElementVisible.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/useFocusRing.mjs":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/useFocusRing.mjs ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useFocusRing: () => (/* binding */ $f7dceffc5ad7768b$export$4e328f61c538687f)\n/* harmony export */ });\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f4c4e47fdd38dbbc2317f9013c56c4ca/node_modules/@react-aria/interactions/dist/useFocusVisible.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f4c4e47fdd38dbbc2317f9013c56c4ca/node_modules/@react-aria/interactions/dist/useFocus.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f4c4e47fdd38dbbc2317f9013c56c4ca/node_modules/@react-aria/interactions/dist/useFocusWithin.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\nfunction $f7dceffc5ad7768b$export$4e328f61c538687f(props = {}) {\n    let { autoFocus: autoFocus = false, isTextInput: isTextInput, within: within } = props;\n    let state = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        isFocused: false,\n        isFocusVisible: autoFocus || (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__.isFocusVisible)()\n    });\n    let [isFocused, setFocused] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    let [isFocusVisibleState, setFocusVisible] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>state.current.isFocused && state.current.isFocusVisible);\n    let updateState = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>setFocusVisible(state.current.isFocused && state.current.isFocusVisible), []);\n    let onFocusChange = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((isFocused)=>{\n        state.current.isFocused = isFocused;\n        setFocused(isFocused);\n        updateState();\n    }, [\n        updateState\n    ]);\n    (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__.useFocusVisibleListener)((isFocusVisible)=>{\n        state.current.isFocusVisible = isFocusVisible;\n        updateState();\n    }, [], {\n        isTextInput: isTextInput\n    });\n    let { focusProps: focusProps } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_2__.useFocus)({\n        isDisabled: within,\n        onFocusChange: onFocusChange\n    });\n    let { focusWithinProps: focusWithinProps } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_3__.useFocusWithin)({\n        isDisabled: !within,\n        onFocusWithinChange: onFocusChange\n    });\n    return {\n        isFocused: isFocused,\n        isFocusVisible: isFocusVisibleState,\n        focusProps: within ? focusWithinProps : focusProps\n    };\n}\n\n\n\n//# sourceMappingURL=useFocusRing.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/useFocusRing.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/useHasTabbableChild.mjs":
/*!*****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/useHasTabbableChild.mjs ***!
  \*****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useHasTabbableChild: () => (/* binding */ $83013635b024ae3d$export$eac1895992b9f3d6)\n/* harmony export */ });\n/* harmony import */ var _FocusScope_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./FocusScope.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/FocusScope.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/useLayoutEffect.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\nfunction $83013635b024ae3d$export$eac1895992b9f3d6(ref, options) {\n    let isDisabled = options === null || options === void 0 ? void 0 : options.isDisabled;\n    let [hasTabbableChild, setHasTabbableChild] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useLayoutEffect)(()=>{\n        if ((ref === null || ref === void 0 ? void 0 : ref.current) && !isDisabled) {\n            let update = ()=>{\n                if (ref.current) {\n                    let walker = (0, _FocusScope_mjs__WEBPACK_IMPORTED_MODULE_2__.getFocusableTreeWalker)(ref.current, {\n                        tabbable: true\n                    });\n                    setHasTabbableChild(!!walker.nextNode());\n                }\n            };\n            update();\n            // Update when new elements are inserted, or the tabIndex/disabled attribute updates.\n            let observer = new MutationObserver(update);\n            observer.observe(ref.current, {\n                subtree: true,\n                childList: true,\n                attributes: true,\n                attributeFilter: [\n                    'tabIndex',\n                    'disabled'\n                ]\n            });\n            return ()=>{\n                // Disconnect mutation observer when a React update occurs on the top-level component\n                // so we update synchronously after re-rendering. Otherwise React will emit act warnings\n                // in tests since mutation observers fire asynchronously. The mutation observer is necessary\n                // so we also update if a child component re-renders and adds/removes something tabbable.\n                observer.disconnect();\n            };\n        }\n    });\n    return isDisabled ? false : hasTabbableChild;\n}\n\n\n\n//# sourceMappingURL=useHasTabbableChild.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/useHasTabbableChild.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/virtualFocus.mjs":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/virtualFocus.mjs ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dispatchVirtualBlur: () => (/* binding */ $55f9b1ae81f22853$export$6c5dc7e81d2cc29a),\n/* harmony export */   dispatchVirtualFocus: () => (/* binding */ $55f9b1ae81f22853$export$2b35b76d2e30e129),\n/* harmony export */   getVirtuallyFocusedElement: () => (/* binding */ $55f9b1ae81f22853$export$759df0d867455a91),\n/* harmony export */   moveVirtualFocus: () => (/* binding */ $55f9b1ae81f22853$export$76e4e37e5339496d)\n/* harmony export */ });\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/domHelpers.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/DOMFunctions.mjs\");\n\n\n\nfunction $55f9b1ae81f22853$export$76e4e37e5339496d(to) {\n    let from = $55f9b1ae81f22853$export$759df0d867455a91((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.getOwnerDocument)(to));\n    if (from !== to) {\n        if (from) $55f9b1ae81f22853$export$6c5dc7e81d2cc29a(from, to);\n        if (to) $55f9b1ae81f22853$export$2b35b76d2e30e129(to, from);\n    }\n}\nfunction $55f9b1ae81f22853$export$6c5dc7e81d2cc29a(from, to) {\n    from.dispatchEvent(new FocusEvent('blur', {\n        relatedTarget: to\n    }));\n    from.dispatchEvent(new FocusEvent('focusout', {\n        bubbles: true,\n        relatedTarget: to\n    }));\n}\nfunction $55f9b1ae81f22853$export$2b35b76d2e30e129(to, from) {\n    to.dispatchEvent(new FocusEvent('focus', {\n        relatedTarget: from\n    }));\n    to.dispatchEvent(new FocusEvent('focusin', {\n        bubbles: true,\n        relatedTarget: from\n    }));\n}\nfunction $55f9b1ae81f22853$export$759df0d867455a91(document) {\n    let activeElement = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.getActiveElement)(document);\n    let activeDescendant = activeElement === null || activeElement === void 0 ? void 0 : activeElement.getAttribute('aria-activedescendant');\n    if (activeDescendant) return document.getElementById(activeDescendant) || activeElement;\n    return activeElement;\n}\n\n\n\n//# sourceMappingURL=virtualFocus.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.0_re_edaaa21297afafc37355b6f54be6e171/node_modules/@react-aria/focus/dist/virtualFocus.mjs\n");

/***/ })

};
;