"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_node_modules_pnpm_framer-motion_11_11_17_reac_abbc4774c585884cba6a3ef630bc-e7057e",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/framer-motion@11.11.17_reac_abbc4774c585884cba6a3ef630bc223c/node_modules/framer-motion/dist/es/components/MotionConfig/index.mjs":
/*!**************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/framer-motion@11.11.17_reac_abbc4774c585884cba6a3ef630bc223c/node_modules/framer-motion/dist/es/components/MotionConfig/index.mjs ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MotionConfig: () => (/* binding */ MotionConfig)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var _context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../context/MotionConfigContext.mjs */ \"(app-pages-browser)/./node_modules/.pnpm/framer-motion@11.11.17_reac_abbc4774c585884cba6a3ef630bc223c/node_modules/framer-motion/dist/es/context/MotionConfigContext.mjs\");\n/* harmony import */ var _render_dom_utils_filter_props_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../render/dom/utils/filter-props.mjs */ \"(app-pages-browser)/./node_modules/.pnpm/framer-motion@11.11.17_reac_abbc4774c585884cba6a3ef630bc223c/node_modules/framer-motion/dist/es/render/dom/utils/filter-props.mjs\");\n/* harmony import */ var _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/use-constant.mjs */ \"(app-pages-browser)/./node_modules/.pnpm/framer-motion@11.11.17_reac_abbc4774c585884cba6a3ef630bc223c/node_modules/framer-motion/dist/es/utils/use-constant.mjs\");\n/* __next_internal_client_entry_do_not_use__ MotionConfig auto */ var _s = $RefreshSig$();\n\n\n\n\n\n/**\n * `MotionConfig` is used to set configuration options for all children `motion` components.\n *\n * ```jsx\n * import { motion, MotionConfig } from \"framer-motion\"\n *\n * export function App() {\n *   return (\n *     <MotionConfig transition={{ type: \"spring\" }}>\n *       <motion.div animate={{ x: 100 }} />\n *     </MotionConfig>\n *   )\n * }\n * ```\n *\n * @public\n */ function MotionConfig(param) {\n    let { children, isValidProp, ...config } = param;\n    _s();\n    isValidProp && (0,_render_dom_utils_filter_props_mjs__WEBPACK_IMPORTED_MODULE_2__.loadExternalIsValidProp)(isValidProp);\n    /**\n     * Inherit props from any parent MotionConfig components\n     */ config = {\n        ...(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext),\n        ...config\n    };\n    /**\n     * Don't allow isStatic to change between renders as it affects how many hooks\n     * motion components fire.\n     */ config.isStatic = (0,_utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__.useConstant)({\n        \"MotionConfig.useConstant\": ()=>config.isStatic\n    }[\"MotionConfig.useConstant\"]);\n    /**\n     * Creating a new config context object will re-render every `motion` component\n     * every time it renders. So we only want to create a new one sparingly.\n     */ const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"MotionConfig.useMemo[context]\": ()=>config\n    }[\"MotionConfig.useMemo[context]\"], [\n        JSON.stringify(config.transition),\n        config.transformPagePoint,\n        config.reducedMotion\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_context_MotionConfigContext_mjs__WEBPACK_IMPORTED_MODULE_3__.MotionConfigContext.Provider, {\n        value: context,\n        children: children\n    });\n}\n_s(MotionConfig, \"tO5bvUVqLxw2p027BDiStQY978M=\", false, function() {\n    return [\n        _utils_use_constant_mjs__WEBPACK_IMPORTED_MODULE_4__.useConstant\n    ];\n});\n_c = MotionConfig;\n\nvar _c;\n$RefreshReg$(_c, \"MotionConfig\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/framer-motion@11.11.17_reac_abbc4774c585884cba6a3ef630bc223c/node_modules/framer-motion/dist/es/components/MotionConfig/index.mjs\n"));

/***/ })

});