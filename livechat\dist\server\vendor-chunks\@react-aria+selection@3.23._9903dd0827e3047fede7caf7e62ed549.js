"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549";
exports.ids = ["vendor-chunks/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/DOMLayoutDelegate.mjs":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/DOMLayoutDelegate.mjs ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DOMLayoutDelegate: () => (/* binding */ $657e4dc4a6e88df0$export$8f5ed9ff9f511381)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/utils.mjs\");\n\n\n/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \nclass $657e4dc4a6e88df0$export$8f5ed9ff9f511381 {\n    getItemRect(key) {\n        let container = this.ref.current;\n        if (!container) return null;\n        let item = key != null ? (0, _utils_mjs__WEBPACK_IMPORTED_MODULE_0__.getItemElement)(this.ref, key) : null;\n        if (!item) return null;\n        let containerRect = container.getBoundingClientRect();\n        let itemRect = item.getBoundingClientRect();\n        return {\n            x: itemRect.left - containerRect.left + container.scrollLeft,\n            y: itemRect.top - containerRect.top + container.scrollTop,\n            width: itemRect.width,\n            height: itemRect.height\n        };\n    }\n    getContentSize() {\n        let container = this.ref.current;\n        var _container_scrollWidth, _container_scrollHeight;\n        return {\n            width: (_container_scrollWidth = container === null || container === void 0 ? void 0 : container.scrollWidth) !== null && _container_scrollWidth !== void 0 ? _container_scrollWidth : 0,\n            height: (_container_scrollHeight = container === null || container === void 0 ? void 0 : container.scrollHeight) !== null && _container_scrollHeight !== void 0 ? _container_scrollHeight : 0\n        };\n    }\n    getVisibleRect() {\n        let container = this.ref.current;\n        var _container_scrollLeft, _container_scrollTop, _container_offsetWidth, _container_offsetHeight;\n        return {\n            x: (_container_scrollLeft = container === null || container === void 0 ? void 0 : container.scrollLeft) !== null && _container_scrollLeft !== void 0 ? _container_scrollLeft : 0,\n            y: (_container_scrollTop = container === null || container === void 0 ? void 0 : container.scrollTop) !== null && _container_scrollTop !== void 0 ? _container_scrollTop : 0,\n            width: (_container_offsetWidth = container === null || container === void 0 ? void 0 : container.offsetWidth) !== null && _container_offsetWidth !== void 0 ? _container_offsetWidth : 0,\n            height: (_container_offsetHeight = container === null || container === void 0 ? void 0 : container.offsetHeight) !== null && _container_offsetHeight !== void 0 ? _container_offsetHeight : 0\n        };\n    }\n    constructor(ref){\n        this.ref = ref;\n    }\n}\n\n\n\n//# sourceMappingURL=DOMLayoutDelegate.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/DOMLayoutDelegate.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/ListKeyboardDelegate.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/ListKeyboardDelegate.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListKeyboardDelegate: () => (/* binding */ $2a25aae57d74318e$export$a05409b8bb224a5a)\n/* harmony export */ });\n/* harmony import */ var _DOMLayoutDelegate_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./DOMLayoutDelegate.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/DOMLayoutDelegate.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/isScrollable.mjs\");\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\nclass $2a25aae57d74318e$export$a05409b8bb224a5a {\n    isDisabled(item) {\n        var _item_props;\n        return this.disabledBehavior === 'all' && (((_item_props = item.props) === null || _item_props === void 0 ? void 0 : _item_props.isDisabled) || this.disabledKeys.has(item.key));\n    }\n    findNextNonDisabled(key, getNext) {\n        let nextKey = key;\n        while(nextKey != null){\n            let item = this.collection.getItem(nextKey);\n            if ((item === null || item === void 0 ? void 0 : item.type) === 'item' && !this.isDisabled(item)) return nextKey;\n            nextKey = getNext(nextKey);\n        }\n        return null;\n    }\n    getNextKey(key) {\n        let nextKey = key;\n        nextKey = this.collection.getKeyAfter(nextKey);\n        return this.findNextNonDisabled(nextKey, (key)=>this.collection.getKeyAfter(key));\n    }\n    getPreviousKey(key) {\n        let nextKey = key;\n        nextKey = this.collection.getKeyBefore(nextKey);\n        return this.findNextNonDisabled(nextKey, (key)=>this.collection.getKeyBefore(key));\n    }\n    findKey(key, nextKey, shouldSkip) {\n        let tempKey = key;\n        let itemRect = this.layoutDelegate.getItemRect(tempKey);\n        if (!itemRect || tempKey == null) return null;\n        // Find the item above or below in the same column.\n        let prevRect = itemRect;\n        do {\n            tempKey = nextKey(tempKey);\n            if (tempKey == null) break;\n            itemRect = this.layoutDelegate.getItemRect(tempKey);\n        }while (itemRect && shouldSkip(prevRect, itemRect) && tempKey != null);\n        return tempKey;\n    }\n    isSameRow(prevRect, itemRect) {\n        return prevRect.y === itemRect.y || prevRect.x !== itemRect.x;\n    }\n    isSameColumn(prevRect, itemRect) {\n        return prevRect.x === itemRect.x || prevRect.y !== itemRect.y;\n    }\n    getKeyBelow(key) {\n        if (this.layout === 'grid' && this.orientation === 'vertical') return this.findKey(key, (key)=>this.getNextKey(key), this.isSameRow);\n        else return this.getNextKey(key);\n    }\n    getKeyAbove(key) {\n        if (this.layout === 'grid' && this.orientation === 'vertical') return this.findKey(key, (key)=>this.getPreviousKey(key), this.isSameRow);\n        else return this.getPreviousKey(key);\n    }\n    getNextColumn(key, right) {\n        return right ? this.getPreviousKey(key) : this.getNextKey(key);\n    }\n    getKeyRightOf(key) {\n        // This is a temporary solution for CardView until we refactor useSelectableCollection.\n        // https://github.com/orgs/adobe/projects/19/views/32?pane=issue&itemId=77825042\n        let layoutDelegateMethod = this.direction === 'ltr' ? 'getKeyRightOf' : 'getKeyLeftOf';\n        if (this.layoutDelegate[layoutDelegateMethod]) {\n            key = this.layoutDelegate[layoutDelegateMethod](key);\n            return this.findNextNonDisabled(key, (key)=>this.layoutDelegate[layoutDelegateMethod](key));\n        }\n        if (this.layout === 'grid') {\n            if (this.orientation === 'vertical') return this.getNextColumn(key, this.direction === 'rtl');\n            else return this.findKey(key, (key)=>this.getNextColumn(key, this.direction === 'rtl'), this.isSameColumn);\n        } else if (this.orientation === 'horizontal') return this.getNextColumn(key, this.direction === 'rtl');\n        return null;\n    }\n    getKeyLeftOf(key) {\n        let layoutDelegateMethod = this.direction === 'ltr' ? 'getKeyLeftOf' : 'getKeyRightOf';\n        if (this.layoutDelegate[layoutDelegateMethod]) {\n            key = this.layoutDelegate[layoutDelegateMethod](key);\n            return this.findNextNonDisabled(key, (key)=>this.layoutDelegate[layoutDelegateMethod](key));\n        }\n        if (this.layout === 'grid') {\n            if (this.orientation === 'vertical') return this.getNextColumn(key, this.direction === 'ltr');\n            else return this.findKey(key, (key)=>this.getNextColumn(key, this.direction === 'ltr'), this.isSameColumn);\n        } else if (this.orientation === 'horizontal') return this.getNextColumn(key, this.direction === 'ltr');\n        return null;\n    }\n    getFirstKey() {\n        let key = this.collection.getFirstKey();\n        return this.findNextNonDisabled(key, (key)=>this.collection.getKeyAfter(key));\n    }\n    getLastKey() {\n        let key = this.collection.getLastKey();\n        return this.findNextNonDisabled(key, (key)=>this.collection.getKeyBefore(key));\n    }\n    getKeyPageAbove(key) {\n        let menu = this.ref.current;\n        let itemRect = this.layoutDelegate.getItemRect(key);\n        if (!itemRect) return null;\n        if (menu && !(0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.isScrollable)(menu)) return this.getFirstKey();\n        let nextKey = key;\n        if (this.orientation === 'horizontal') {\n            let pageX = Math.max(0, itemRect.x + itemRect.width - this.layoutDelegate.getVisibleRect().width);\n            while(itemRect && itemRect.x > pageX && nextKey != null){\n                nextKey = this.getKeyAbove(nextKey);\n                itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n            }\n        } else {\n            let pageY = Math.max(0, itemRect.y + itemRect.height - this.layoutDelegate.getVisibleRect().height);\n            while(itemRect && itemRect.y > pageY && nextKey != null){\n                nextKey = this.getKeyAbove(nextKey);\n                itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n            }\n        }\n        return nextKey !== null && nextKey !== void 0 ? nextKey : this.getFirstKey();\n    }\n    getKeyPageBelow(key) {\n        let menu = this.ref.current;\n        let itemRect = this.layoutDelegate.getItemRect(key);\n        if (!itemRect) return null;\n        if (menu && !(0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.isScrollable)(menu)) return this.getLastKey();\n        let nextKey = key;\n        if (this.orientation === 'horizontal') {\n            let pageX = Math.min(this.layoutDelegate.getContentSize().width, itemRect.y - itemRect.width + this.layoutDelegate.getVisibleRect().width);\n            while(itemRect && itemRect.x < pageX && nextKey != null){\n                nextKey = this.getKeyBelow(nextKey);\n                itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n            }\n        } else {\n            let pageY = Math.min(this.layoutDelegate.getContentSize().height, itemRect.y - itemRect.height + this.layoutDelegate.getVisibleRect().height);\n            while(itemRect && itemRect.y < pageY && nextKey != null){\n                nextKey = this.getKeyBelow(nextKey);\n                itemRect = nextKey == null ? null : this.layoutDelegate.getItemRect(nextKey);\n            }\n        }\n        return nextKey !== null && nextKey !== void 0 ? nextKey : this.getLastKey();\n    }\n    getKeyForSearch(search, fromKey) {\n        if (!this.collator) return null;\n        let collection = this.collection;\n        let key = fromKey || this.getFirstKey();\n        while(key != null){\n            let item = collection.getItem(key);\n            if (!item) return null;\n            let substring = item.textValue.slice(0, search.length);\n            if (item.textValue && this.collator.compare(substring, search) === 0) return key;\n            key = this.getNextKey(key);\n        }\n        return null;\n    }\n    constructor(...args){\n        if (args.length === 1) {\n            let opts = args[0];\n            this.collection = opts.collection;\n            this.ref = opts.ref;\n            this.collator = opts.collator;\n            this.disabledKeys = opts.disabledKeys || new Set();\n            this.disabledBehavior = opts.disabledBehavior || 'all';\n            this.orientation = opts.orientation || 'vertical';\n            this.direction = opts.direction;\n            this.layout = opts.layout || 'stack';\n            this.layoutDelegate = opts.layoutDelegate || new (0, _DOMLayoutDelegate_mjs__WEBPACK_IMPORTED_MODULE_1__.DOMLayoutDelegate)(opts.ref);\n        } else {\n            this.collection = args[0];\n            this.disabledKeys = args[1];\n            this.ref = args[2];\n            this.collator = args[3];\n            this.layout = 'stack';\n            this.orientation = 'vertical';\n            this.disabledBehavior = 'all';\n            this.layoutDelegate = new (0, _DOMLayoutDelegate_mjs__WEBPACK_IMPORTED_MODULE_1__.DOMLayoutDelegate)(this.ref);\n        }\n        // If this is a vertical stack, remove the left/right methods completely\n        // so they aren't called by useDroppableCollection.\n        if (this.layout === 'stack' && this.orientation === 'vertical') {\n            this.getKeyLeftOf = undefined;\n            this.getKeyRightOf = undefined;\n        }\n    }\n}\n\n\n\n//# sourceMappingURL=ListKeyboardDelegate.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/ListKeyboardDelegate.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/useSelectableCollection.mjs":
/*!*************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/useSelectableCollection.mjs ***!
  \*************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSelectableCollection: () => (/* binding */ $ae20dd8cbca75726$export$d6daf82dcd84e87c)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/utils.mjs\");\n/* harmony import */ var _useTypeSelect_mjs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./useTypeSelect.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/useTypeSelect.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/openLink.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/keyboard.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/focusWithoutScrolling.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/useEvent.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/scrollIntoView.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/constants.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/useEffectEvent.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/useUpdateLayoutEffect.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/mergeProps.mjs\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f2d9528c866cda2b9c509f40adab71db/node_modules/@react-aria/interactions/dist/useFocusVisible.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f2d9528c866cda2b9c509f40adab71db/node_modules/@react-aria/interactions/dist/focusSafely.mjs\");\n/* harmony import */ var _react_aria_focus__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @react-aria/focus */ \"(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.1_re_cd532c8d0ba197188e265f8903747858/node_modules/@react-aria/focus/dist/FocusScope.mjs\");\n/* harmony import */ var _react_aria_focus__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @react-aria/focus */ \"(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.1_re_cd532c8d0ba197188e265f8903747858/node_modules/@react-aria/focus/dist/virtualFocus.mjs\");\n/* harmony import */ var _react_aria_i18n__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/i18n */ \"(ssr)/./node_modules/.pnpm/@react-aria+i18n@3.12.7_rea_e4b9ec774c1595ed697c15570238dc19/node_modules/@react-aria/i18n/dist/context.mjs\");\n\n\n\n\n\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n\n\n\n\n\nfunction $ae20dd8cbca75726$export$d6daf82dcd84e87c(options) {\n    let { selectionManager: manager, keyboardDelegate: delegate, ref: ref, autoFocus: autoFocus = false, shouldFocusWrap: shouldFocusWrap = false, disallowEmptySelection: disallowEmptySelection = false, disallowSelectAll: disallowSelectAll = false, selectOnFocus: selectOnFocus = manager.selectionBehavior === 'replace', disallowTypeAhead: disallowTypeAhead = false, shouldUseVirtualFocus: shouldUseVirtualFocus, allowsTabNavigation: allowsTabNavigation = false, isVirtualized: isVirtualized, scrollRef: // If no scrollRef is provided, assume the collection ref is the scrollable region\n    scrollRef = ref, linkBehavior: linkBehavior = 'action' } = options;\n    let { direction: direction } = (0, _react_aria_i18n__WEBPACK_IMPORTED_MODULE_2__.useLocale)();\n    let router = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    let onKeyDown = (e)=>{\n        var _ref_current;\n        // Prevent option + tab from doing anything since it doesn't move focus to the cells, only buttons/checkboxes\n        if (e.altKey && e.key === 'Tab') e.preventDefault();\n        // Keyboard events bubble through portals. Don't handle keyboard events\n        // for elements outside the collection (e.g. menus).\n        if (!((_ref_current = ref.current) === null || _ref_current === void 0 ? void 0 : _ref_current.contains(e.target))) return;\n        const navigateToKey = (key, childFocus)=>{\n            if (key != null) {\n                if (manager.isLink(key) && linkBehavior === 'selection' && selectOnFocus && !(0, _utils_mjs__WEBPACK_IMPORTED_MODULE_4__.isNonContiguousSelectionModifier)(e)) {\n                    // Set focused key and re-render synchronously to bring item into view if needed.\n                    (0, react_dom__WEBPACK_IMPORTED_MODULE_0__.flushSync)(()=>{\n                        manager.setFocusedKey(key, childFocus);\n                    });\n                    let item = (0, _utils_mjs__WEBPACK_IMPORTED_MODULE_4__.getItemElement)(ref, key);\n                    let itemProps = manager.getItemProps(key);\n                    if (item) router.open(item, e, itemProps.href, itemProps.routerOptions);\n                    return;\n                }\n                manager.setFocusedKey(key, childFocus);\n                if (manager.isLink(key) && linkBehavior === 'override') return;\n                if (e.shiftKey && manager.selectionMode === 'multiple') manager.extendSelection(key);\n                else if (selectOnFocus && !(0, _utils_mjs__WEBPACK_IMPORTED_MODULE_4__.isNonContiguousSelectionModifier)(e)) manager.replaceSelection(key);\n            }\n        };\n        switch(e.key){\n            case 'ArrowDown':\n                if (delegate.getKeyBelow) {\n                    var _delegate_getKeyBelow, _delegate_getFirstKey, _delegate_getFirstKey1;\n                    let nextKey = manager.focusedKey != null ? (_delegate_getKeyBelow = delegate.getKeyBelow) === null || _delegate_getKeyBelow === void 0 ? void 0 : _delegate_getKeyBelow.call(delegate, manager.focusedKey) : (_delegate_getFirstKey = delegate.getFirstKey) === null || _delegate_getFirstKey === void 0 ? void 0 : _delegate_getFirstKey.call(delegate);\n                    if (nextKey == null && shouldFocusWrap) nextKey = (_delegate_getFirstKey1 = delegate.getFirstKey) === null || _delegate_getFirstKey1 === void 0 ? void 0 : _delegate_getFirstKey1.call(delegate, manager.focusedKey);\n                    if (nextKey != null) {\n                        e.preventDefault();\n                        navigateToKey(nextKey);\n                    }\n                }\n                break;\n            case 'ArrowUp':\n                if (delegate.getKeyAbove) {\n                    var _delegate_getKeyAbove, _delegate_getLastKey, _delegate_getLastKey1;\n                    let nextKey = manager.focusedKey != null ? (_delegate_getKeyAbove = delegate.getKeyAbove) === null || _delegate_getKeyAbove === void 0 ? void 0 : _delegate_getKeyAbove.call(delegate, manager.focusedKey) : (_delegate_getLastKey = delegate.getLastKey) === null || _delegate_getLastKey === void 0 ? void 0 : _delegate_getLastKey.call(delegate);\n                    if (nextKey == null && shouldFocusWrap) nextKey = (_delegate_getLastKey1 = delegate.getLastKey) === null || _delegate_getLastKey1 === void 0 ? void 0 : _delegate_getLastKey1.call(delegate, manager.focusedKey);\n                    if (nextKey != null) {\n                        e.preventDefault();\n                        navigateToKey(nextKey);\n                    }\n                }\n                break;\n            case 'ArrowLeft':\n                if (delegate.getKeyLeftOf) {\n                    var _delegate_getKeyLeftOf, _delegate_getFirstKey2, _delegate_getLastKey2;\n                    let nextKey = manager.focusedKey != null ? (_delegate_getKeyLeftOf = delegate.getKeyLeftOf) === null || _delegate_getKeyLeftOf === void 0 ? void 0 : _delegate_getKeyLeftOf.call(delegate, manager.focusedKey) : null;\n                    if (nextKey == null && shouldFocusWrap) nextKey = direction === 'rtl' ? (_delegate_getFirstKey2 = delegate.getFirstKey) === null || _delegate_getFirstKey2 === void 0 ? void 0 : _delegate_getFirstKey2.call(delegate, manager.focusedKey) : (_delegate_getLastKey2 = delegate.getLastKey) === null || _delegate_getLastKey2 === void 0 ? void 0 : _delegate_getLastKey2.call(delegate, manager.focusedKey);\n                    if (nextKey != null) {\n                        e.preventDefault();\n                        navigateToKey(nextKey, direction === 'rtl' ? 'first' : 'last');\n                    }\n                }\n                break;\n            case 'ArrowRight':\n                if (delegate.getKeyRightOf) {\n                    var _delegate_getKeyRightOf, _delegate_getLastKey3, _delegate_getFirstKey3;\n                    let nextKey = manager.focusedKey != null ? (_delegate_getKeyRightOf = delegate.getKeyRightOf) === null || _delegate_getKeyRightOf === void 0 ? void 0 : _delegate_getKeyRightOf.call(delegate, manager.focusedKey) : null;\n                    if (nextKey == null && shouldFocusWrap) nextKey = direction === 'rtl' ? (_delegate_getLastKey3 = delegate.getLastKey) === null || _delegate_getLastKey3 === void 0 ? void 0 : _delegate_getLastKey3.call(delegate, manager.focusedKey) : (_delegate_getFirstKey3 = delegate.getFirstKey) === null || _delegate_getFirstKey3 === void 0 ? void 0 : _delegate_getFirstKey3.call(delegate, manager.focusedKey);\n                    if (nextKey != null) {\n                        e.preventDefault();\n                        navigateToKey(nextKey, direction === 'rtl' ? 'last' : 'first');\n                    }\n                }\n                break;\n            case 'Home':\n                if (delegate.getFirstKey) {\n                    if (manager.focusedKey === null && e.shiftKey) return;\n                    e.preventDefault();\n                    let firstKey = delegate.getFirstKey(manager.focusedKey, (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.isCtrlKeyPressed)(e));\n                    manager.setFocusedKey(firstKey);\n                    if (firstKey != null) {\n                        if ((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.isCtrlKeyPressed)(e) && e.shiftKey && manager.selectionMode === 'multiple') manager.extendSelection(firstKey);\n                        else if (selectOnFocus) manager.replaceSelection(firstKey);\n                    }\n                }\n                break;\n            case 'End':\n                if (delegate.getLastKey) {\n                    if (manager.focusedKey === null && e.shiftKey) return;\n                    e.preventDefault();\n                    let lastKey = delegate.getLastKey(manager.focusedKey, (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.isCtrlKeyPressed)(e));\n                    manager.setFocusedKey(lastKey);\n                    if (lastKey != null) {\n                        if ((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.isCtrlKeyPressed)(e) && e.shiftKey && manager.selectionMode === 'multiple') manager.extendSelection(lastKey);\n                        else if (selectOnFocus) manager.replaceSelection(lastKey);\n                    }\n                }\n                break;\n            case 'PageDown':\n                if (delegate.getKeyPageBelow && manager.focusedKey != null) {\n                    let nextKey = delegate.getKeyPageBelow(manager.focusedKey);\n                    if (nextKey != null) {\n                        e.preventDefault();\n                        navigateToKey(nextKey);\n                    }\n                }\n                break;\n            case 'PageUp':\n                if (delegate.getKeyPageAbove && manager.focusedKey != null) {\n                    let nextKey = delegate.getKeyPageAbove(manager.focusedKey);\n                    if (nextKey != null) {\n                        e.preventDefault();\n                        navigateToKey(nextKey);\n                    }\n                }\n                break;\n            case 'a':\n                if ((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.isCtrlKeyPressed)(e) && manager.selectionMode === 'multiple' && disallowSelectAll !== true) {\n                    e.preventDefault();\n                    manager.selectAll();\n                }\n                break;\n            case 'Escape':\n                if (!disallowEmptySelection && manager.selectedKeys.size !== 0) {\n                    e.stopPropagation();\n                    e.preventDefault();\n                    manager.clearSelection();\n                }\n                break;\n            case 'Tab':\n                if (!allowsTabNavigation) {\n                    // There may be elements that are \"tabbable\" inside a collection (e.g. in a grid cell).\n                    // However, collections should be treated as a single tab stop, with arrow key navigation internally.\n                    // We don't control the rendering of these, so we can't override the tabIndex to prevent tabbing.\n                    // Instead, we handle the Tab key, and move focus manually to the first/last tabbable element\n                    // in the collection, so that the browser default behavior will apply starting from that element\n                    // rather than the currently focused one.\n                    if (e.shiftKey) ref.current.focus();\n                    else {\n                        let walker = (0, _react_aria_focus__WEBPACK_IMPORTED_MODULE_6__.getFocusableTreeWalker)(ref.current, {\n                            tabbable: true\n                        });\n                        let next = undefined;\n                        let last;\n                        do {\n                            last = walker.lastChild();\n                            if (last) next = last;\n                        }while (last);\n                        if (next && !next.contains(document.activeElement)) (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_7__.focusWithoutScrolling)(next);\n                    }\n                    break;\n                }\n        }\n    };\n    // Store the scroll position so we can restore it later.\n    /// TODO: should this happen all the time??\n    let scrollPos = (0, react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        top: 0,\n        left: 0\n    });\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_8__.useEvent)(scrollRef, 'scroll', isVirtualized ? undefined : ()=>{\n        var _scrollRef_current, _scrollRef_current1;\n        var _scrollRef_current_scrollTop, _scrollRef_current_scrollLeft;\n        scrollPos.current = {\n            top: (_scrollRef_current_scrollTop = (_scrollRef_current = scrollRef.current) === null || _scrollRef_current === void 0 ? void 0 : _scrollRef_current.scrollTop) !== null && _scrollRef_current_scrollTop !== void 0 ? _scrollRef_current_scrollTop : 0,\n            left: (_scrollRef_current_scrollLeft = (_scrollRef_current1 = scrollRef.current) === null || _scrollRef_current1 === void 0 ? void 0 : _scrollRef_current1.scrollLeft) !== null && _scrollRef_current_scrollLeft !== void 0 ? _scrollRef_current_scrollLeft : 0\n        };\n    });\n    let onFocus = (e)=>{\n        if (manager.isFocused) {\n            // If a focus event bubbled through a portal, reset focus state.\n            if (!e.currentTarget.contains(e.target)) manager.setFocused(false);\n            return;\n        }\n        // Focus events can bubble through portals. Ignore these events.\n        if (!e.currentTarget.contains(e.target)) return;\n        manager.setFocused(true);\n        if (manager.focusedKey == null) {\n            var _delegate_getLastKey, _delegate_getFirstKey;\n            let navigateToKey = (key)=>{\n                if (key != null) {\n                    manager.setFocusedKey(key);\n                    if (selectOnFocus && !manager.isSelected(key)) manager.replaceSelection(key);\n                }\n            };\n            // If the user hasn't yet interacted with the collection, there will be no focusedKey set.\n            // Attempt to detect whether the user is tabbing forward or backward into the collection\n            // and either focus the first or last item accordingly.\n            let relatedTarget = e.relatedTarget;\n            var _manager_lastSelectedKey, _manager_firstSelectedKey;\n            if (relatedTarget && e.currentTarget.compareDocumentPosition(relatedTarget) & Node.DOCUMENT_POSITION_FOLLOWING) navigateToKey((_manager_lastSelectedKey = manager.lastSelectedKey) !== null && _manager_lastSelectedKey !== void 0 ? _manager_lastSelectedKey : (_delegate_getLastKey = delegate.getLastKey) === null || _delegate_getLastKey === void 0 ? void 0 : _delegate_getLastKey.call(delegate));\n            else navigateToKey((_manager_firstSelectedKey = manager.firstSelectedKey) !== null && _manager_firstSelectedKey !== void 0 ? _manager_firstSelectedKey : (_delegate_getFirstKey = delegate.getFirstKey) === null || _delegate_getFirstKey === void 0 ? void 0 : _delegate_getFirstKey.call(delegate));\n        } else if (!isVirtualized && scrollRef.current) {\n            // Restore the scroll position to what it was before.\n            scrollRef.current.scrollTop = scrollPos.current.top;\n            scrollRef.current.scrollLeft = scrollPos.current.left;\n        }\n        if (manager.focusedKey != null && scrollRef.current) {\n            // Refocus and scroll the focused item into view if it exists within the scrollable region.\n            let element = (0, _utils_mjs__WEBPACK_IMPORTED_MODULE_4__.getItemElement)(ref, manager.focusedKey);\n            if (element instanceof HTMLElement) {\n                // This prevents a flash of focus on the first/last element in the collection, or the collection itself.\n                if (!element.contains(document.activeElement) && !shouldUseVirtualFocus) (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_7__.focusWithoutScrolling)(element);\n                let modality = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_9__.getInteractionModality)();\n                if (modality === 'keyboard') (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_10__.scrollIntoViewport)(element, {\n                    containingElement: ref.current\n                });\n            }\n        }\n    };\n    let onBlur = (e)=>{\n        // Don't set blurred and then focused again if moving focus within the collection.\n        if (!e.currentTarget.contains(e.relatedTarget)) manager.setFocused(false);\n    };\n    // Ref to track whether the first item in the collection should be automatically focused. Specifically used for autocomplete when user types\n    // to focus the first key AFTER the collection updates.\n    // TODO: potentially expand the usage of this\n    let shouldVirtualFocusFirst = (0, react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    // Add event listeners for custom virtual events. These handle updating the focused key in response to various keyboard events\n    // at the autocomplete level\n    // TODO: fix type later\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_8__.useEvent)(ref, (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_11__.FOCUS_EVENT), !shouldUseVirtualFocus ? undefined : (e)=>{\n        let { detail: detail } = e;\n        e.stopPropagation();\n        manager.setFocused(true);\n        // If the user is typing forwards, autofocus the first option in the list.\n        if ((detail === null || detail === void 0 ? void 0 : detail.focusStrategy) === 'first') shouldVirtualFocusFirst.current = true;\n    });\n    let updateActiveDescendant = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_12__.useEffectEvent)(()=>{\n        var _delegate_getFirstKey;\n        var _delegate_getFirstKey1;\n        let keyToFocus = (_delegate_getFirstKey1 = (_delegate_getFirstKey = delegate.getFirstKey) === null || _delegate_getFirstKey === void 0 ? void 0 : _delegate_getFirstKey.call(delegate)) !== null && _delegate_getFirstKey1 !== void 0 ? _delegate_getFirstKey1 : null;\n        // If no focusable items exist in the list, make sure to clear any activedescendant that may still exist\n        if (keyToFocus == null) {\n            (0, _react_aria_focus__WEBPACK_IMPORTED_MODULE_13__.moveVirtualFocus)(ref.current);\n            // If there wasn't a focusable key but the collection had items, then that means we aren't in an intermediate load state and all keys are disabled.\n            // Reset shouldVirtualFocusFirst so that we don't erronously autofocus an item when the collection is filtered again.\n            if (manager.collection.size > 0) shouldVirtualFocusFirst.current = false;\n        } else {\n            manager.setFocusedKey(keyToFocus);\n            // Only set shouldVirtualFocusFirst to false if we've successfully set the first key as the focused key\n            // If there wasn't a key to focus, we might be in a temporary loading state so we'll want to still focus the first key\n            // after the collection updates after load\n            shouldVirtualFocusFirst.current = false;\n        }\n    });\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_14__.useUpdateLayoutEffect)(()=>{\n        if (shouldVirtualFocusFirst.current) updateActiveDescendant();\n    }, [\n        manager.collection,\n        updateActiveDescendant\n    ]);\n    let resetFocusFirstFlag = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_12__.useEffectEvent)(()=>{\n        // If user causes the focused key to change in any other way, clear shouldVirtualFocusFirst so we don't\n        // accidentally move focus from under them. Skip this if the collection was empty because we might be in a load\n        // state and will still want to focus the first item after load\n        if (manager.collection.size > 0) shouldVirtualFocusFirst.current = false;\n    });\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_14__.useUpdateLayoutEffect)(()=>{\n        resetFocusFirstFlag();\n    }, [\n        manager.focusedKey,\n        resetFocusFirstFlag\n    ]);\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_8__.useEvent)(ref, (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_11__.CLEAR_FOCUS_EVENT), !shouldUseVirtualFocus ? undefined : (e)=>{\n        var _e_detail;\n        e.stopPropagation();\n        manager.setFocused(false);\n        if ((_e_detail = e.detail) === null || _e_detail === void 0 ? void 0 : _e_detail.clearFocusKey) manager.setFocusedKey(null);\n    });\n    const autoFocusRef = (0, react__WEBPACK_IMPORTED_MODULE_1__.useRef)(autoFocus);\n    const didAutoFocusRef = (0, react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    (0, react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (autoFocusRef.current) {\n            var _delegate_getFirstKey, _delegate_getLastKey;\n            let focusedKey = null;\n            var _delegate_getFirstKey1;\n            // Check focus strategy to determine which item to focus\n            if (autoFocus === 'first') focusedKey = (_delegate_getFirstKey1 = (_delegate_getFirstKey = delegate.getFirstKey) === null || _delegate_getFirstKey === void 0 ? void 0 : _delegate_getFirstKey.call(delegate)) !== null && _delegate_getFirstKey1 !== void 0 ? _delegate_getFirstKey1 : null;\n            var _delegate_getLastKey1;\n            if (autoFocus === 'last') focusedKey = (_delegate_getLastKey1 = (_delegate_getLastKey = delegate.getLastKey) === null || _delegate_getLastKey === void 0 ? void 0 : _delegate_getLastKey.call(delegate)) !== null && _delegate_getLastKey1 !== void 0 ? _delegate_getLastKey1 : null;\n            // If there are any selected keys, make the first one the new focus target\n            let selectedKeys = manager.selectedKeys;\n            if (selectedKeys.size) {\n                for (let key of selectedKeys)if (manager.canSelectItem(key)) {\n                    focusedKey = key;\n                    break;\n                }\n            }\n            manager.setFocused(true);\n            manager.setFocusedKey(focusedKey);\n            // If no default focus key is selected, focus the collection itself.\n            if (focusedKey == null && !shouldUseVirtualFocus && ref.current) (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_15__.focusSafely)(ref.current);\n            // Wait until the collection has items to autofocus.\n            if (manager.collection.size > 0) {\n                autoFocusRef.current = false;\n                didAutoFocusRef.current = true;\n            }\n        }\n    });\n    // Scroll the focused element into view when the focusedKey changes.\n    let lastFocusedKey = (0, react__WEBPACK_IMPORTED_MODULE_1__.useRef)(manager.focusedKey);\n    (0, react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (manager.isFocused && manager.focusedKey != null && (manager.focusedKey !== lastFocusedKey.current || didAutoFocusRef.current) && scrollRef.current && ref.current) {\n            let modality = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_9__.getInteractionModality)();\n            let element = (0, _utils_mjs__WEBPACK_IMPORTED_MODULE_4__.getItemElement)(ref, manager.focusedKey);\n            if (!(element instanceof HTMLElement)) // If item element wasn't found, return early (don't update autoFocusRef and lastFocusedKey).\n            // The collection may initially be empty (e.g. virtualizer), so wait until the element exists.\n            return;\n            if (modality === 'keyboard' || didAutoFocusRef.current) {\n                (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_10__.scrollIntoView)(scrollRef.current, element);\n                // Avoid scroll in iOS VO, since it may cause overlay to close (i.e. RAC submenu)\n                if (modality !== 'virtual') (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_10__.scrollIntoViewport)(element, {\n                    containingElement: ref.current\n                });\n            }\n        }\n        // If the focused key becomes null (e.g. the last item is deleted), focus the whole collection.\n        if (!shouldUseVirtualFocus && manager.isFocused && manager.focusedKey == null && lastFocusedKey.current != null && ref.current) (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_15__.focusSafely)(ref.current);\n        lastFocusedKey.current = manager.focusedKey;\n        didAutoFocusRef.current = false;\n    });\n    // Intercept FocusScope restoration since virtualized collections can reuse DOM nodes.\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_8__.useEvent)(ref, 'react-aria-focus-scope-restore', (e)=>{\n        e.preventDefault();\n        manager.setFocused(true);\n    });\n    let handlers = {\n        onKeyDown: onKeyDown,\n        onFocus: onFocus,\n        onBlur: onBlur,\n        onMouseDown (e) {\n            // Ignore events that bubbled through portals.\n            if (scrollRef.current === e.target) // Prevent focus going to the collection when clicking on the scrollbar.\n            e.preventDefault();\n        }\n    };\n    let { typeSelectProps: typeSelectProps } = (0, _useTypeSelect_mjs__WEBPACK_IMPORTED_MODULE_16__.useTypeSelect)({\n        keyboardDelegate: delegate,\n        selectionManager: manager\n    });\n    if (!disallowTypeAhead) handlers = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_17__.mergeProps)(typeSelectProps, handlers);\n    // If nothing is focused within the collection, make the collection itself tabbable.\n    // This will be marshalled to either the first or last item depending on where focus came from.\n    let tabIndex = undefined;\n    if (!shouldUseVirtualFocus) tabIndex = manager.focusedKey == null ? 0 : -1;\n    let collectionId = (0, _utils_mjs__WEBPACK_IMPORTED_MODULE_4__.useCollectionId)(manager.collection);\n    return {\n        collectionProps: (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_17__.mergeProps)(handlers, {\n            tabIndex: tabIndex,\n            'data-collection': collectionId\n        })\n    };\n}\n\n\n\n//# sourceMappingURL=useSelectableCollection.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/useSelectableCollection.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/useSelectableItem.mjs":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/useSelectableItem.mjs ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSelectableItem: () => (/* binding */ $880e95eb8b93ba9a$export$ecf600387e221c37)\n/* harmony export */ });\n/* harmony import */ var _utils_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/utils.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f2d9528c866cda2b9c509f40adab71db/node_modules/@react-aria/interactions/dist/focusSafely.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f2d9528c866cda2b9c509f40adab71db/node_modules/@react-aria/interactions/dist/usePress.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f2d9528c866cda2b9c509f40adab71db/node_modules/@react-aria/interactions/dist/useLongPress.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/openLink.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/useId.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/keyboard.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/mergeProps.mjs\");\n/* harmony import */ var _react_aria_focus__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @react-aria/focus */ \"(ssr)/./node_modules/.pnpm/@react-aria+focus@3.20.1_re_cd532c8d0ba197188e265f8903747858/node_modules/@react-aria/focus/dist/virtualFocus.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n\n\nfunction $880e95eb8b93ba9a$export$ecf600387e221c37(options) {\n    let { id: id, selectionManager: manager, key: key, ref: ref, shouldSelectOnPressUp: shouldSelectOnPressUp, shouldUseVirtualFocus: shouldUseVirtualFocus, focus: focus, isDisabled: isDisabled, onAction: onAction, allowsDifferentPressOrigin: allowsDifferentPressOrigin, linkBehavior: linkBehavior = 'action' } = options;\n    let router = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    id = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.useId)(id);\n    let onSelect = (e)=>{\n        if (e.pointerType === 'keyboard' && (0, _utils_mjs__WEBPACK_IMPORTED_MODULE_3__.isNonContiguousSelectionModifier)(e)) manager.toggleSelection(key);\n        else {\n            if (manager.selectionMode === 'none') return;\n            if (manager.isLink(key)) {\n                if (linkBehavior === 'selection' && ref.current) {\n                    let itemProps = manager.getItemProps(key);\n                    router.open(ref.current, e, itemProps.href, itemProps.routerOptions);\n                    // Always set selected keys back to what they were so that select and combobox close.\n                    manager.setSelectedKeys(manager.selectedKeys);\n                    return;\n                } else if (linkBehavior === 'override' || linkBehavior === 'none') return;\n            }\n            if (manager.selectionMode === 'single') {\n                if (manager.isSelected(key) && !manager.disallowEmptySelection) manager.toggleSelection(key);\n                else manager.replaceSelection(key);\n            } else if (e && e.shiftKey) manager.extendSelection(key);\n            else if (manager.selectionBehavior === 'toggle' || e && ((0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__.isCtrlKeyPressed)(e) || e.pointerType === 'touch' || e.pointerType === 'virtual')) // if touch or virtual (VO) then we just want to toggle, otherwise it's impossible to multi select because they don't have modifier keys\n            manager.toggleSelection(key);\n            else manager.replaceSelection(key);\n        }\n    };\n    // Focus the associated DOM node when this item becomes the focusedKey\n    // TODO: can't make this useLayoutEffect bacause it breaks menus inside dialogs\n    // However, if this is a useEffect, it runs twice and dispatches two blur events and immediately sets\n    // aria-activeDescendant in useAutocomplete... I've worked around this for now\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        let isFocused = key === manager.focusedKey;\n        if (isFocused && manager.isFocused) {\n            if (!shouldUseVirtualFocus) {\n                if (focus) focus();\n                else if (document.activeElement !== ref.current && ref.current) (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_5__.focusSafely)(ref.current);\n            } else (0, _react_aria_focus__WEBPACK_IMPORTED_MODULE_6__.moveVirtualFocus)(ref.current);\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        ref,\n        key,\n        manager.focusedKey,\n        manager.childFocusStrategy,\n        manager.isFocused,\n        shouldUseVirtualFocus\n    ]);\n    isDisabled = isDisabled || manager.isDisabled(key);\n    // Set tabIndex to 0 if the element is focused, or -1 otherwise so that only the last focused\n    // item is tabbable.  If using virtual focus, don't set a tabIndex at all so that VoiceOver\n    // on iOS 14 doesn't try to move real DOM focus to the item anyway.\n    let itemProps = {};\n    if (!shouldUseVirtualFocus && !isDisabled) itemProps = {\n        tabIndex: key === manager.focusedKey ? 0 : -1,\n        onFocus (e) {\n            if (e.target === ref.current) manager.setFocusedKey(key);\n        }\n    };\n    else if (isDisabled) itemProps.onMouseDown = (e)=>{\n        // Prevent focus going to the body when clicking on a disabled item.\n        e.preventDefault();\n    };\n    // With checkbox selection, onAction (i.e. navigation) becomes primary, and occurs on a single click of the row.\n    // Clicking the checkbox enters selection mode, after which clicking anywhere on any row toggles selection for that row.\n    // With highlight selection, onAction is secondary, and occurs on double click. Single click selects the row.\n    // With touch, onAction occurs on single tap, and long press enters selection mode.\n    let isLinkOverride = manager.isLink(key) && linkBehavior === 'override';\n    let hasLinkAction = manager.isLink(key) && linkBehavior !== 'selection' && linkBehavior !== 'none';\n    let allowsSelection = !isDisabled && manager.canSelectItem(key) && !isLinkOverride;\n    let allowsActions = (onAction || hasLinkAction) && !isDisabled;\n    let hasPrimaryAction = allowsActions && (manager.selectionBehavior === 'replace' ? !allowsSelection : !allowsSelection || manager.isEmpty);\n    let hasSecondaryAction = allowsActions && allowsSelection && manager.selectionBehavior === 'replace';\n    let hasAction = hasPrimaryAction || hasSecondaryAction;\n    let modality = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    let longPressEnabled = hasAction && allowsSelection;\n    let longPressEnabledOnPressStart = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    let hadPrimaryActionOnPressStart = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    let performAction = (e)=>{\n        if (onAction) onAction();\n        if (hasLinkAction && ref.current) {\n            let itemProps = manager.getItemProps(key);\n            router.open(ref.current, e, itemProps.href, itemProps.routerOptions);\n        }\n    };\n    // By default, selection occurs on pointer down. This can be strange if selecting an\n    // item causes the UI to disappear immediately (e.g. menus).\n    // If shouldSelectOnPressUp is true, we use onPressUp instead of onPressStart.\n    // onPress requires a pointer down event on the same element as pointer up. For menus,\n    // we want to be able to have the pointer down on the trigger that opens the menu and\n    // the pointer up on the menu item rather than requiring a separate press.\n    // For keyboard events, selection still occurs on key down.\n    let itemPressProps = {};\n    if (shouldSelectOnPressUp) {\n        itemPressProps.onPressStart = (e)=>{\n            modality.current = e.pointerType;\n            longPressEnabledOnPressStart.current = longPressEnabled;\n            if (e.pointerType === 'keyboard' && (!hasAction || $880e95eb8b93ba9a$var$isSelectionKey())) onSelect(e);\n        };\n        // If allowsDifferentPressOrigin and interacting with mouse, make selection happen on pressUp (e.g. open menu on press down, selection on menu item happens on press up.)\n        // Otherwise, have selection happen onPress (prevents listview row selection when clicking on interactable elements in the row)\n        if (!allowsDifferentPressOrigin) itemPressProps.onPress = (e)=>{\n            if (hasPrimaryAction || hasSecondaryAction && e.pointerType !== 'mouse') {\n                if (e.pointerType === 'keyboard' && !$880e95eb8b93ba9a$var$isActionKey()) return;\n                performAction(e);\n            } else if (e.pointerType !== 'keyboard' && allowsSelection) onSelect(e);\n        };\n        else {\n            itemPressProps.onPressUp = hasPrimaryAction ? undefined : (e)=>{\n                if (e.pointerType === 'mouse' && allowsSelection) onSelect(e);\n            };\n            itemPressProps.onPress = hasPrimaryAction ? performAction : (e)=>{\n                if (e.pointerType !== 'keyboard' && e.pointerType !== 'mouse' && allowsSelection) onSelect(e);\n            };\n        }\n    } else {\n        itemPressProps.onPressStart = (e)=>{\n            modality.current = e.pointerType;\n            longPressEnabledOnPressStart.current = longPressEnabled;\n            hadPrimaryActionOnPressStart.current = hasPrimaryAction;\n            // Select on mouse down unless there is a primary action which will occur on mouse up.\n            // For keyboard, select on key down. If there is an action, the Space key selects on key down,\n            // and the Enter key performs onAction on key up.\n            if (allowsSelection && (e.pointerType === 'mouse' && !hasPrimaryAction || e.pointerType === 'keyboard' && (!allowsActions || $880e95eb8b93ba9a$var$isSelectionKey()))) onSelect(e);\n        };\n        itemPressProps.onPress = (e)=>{\n            // Selection occurs on touch up. Primary actions always occur on pointer up.\n            // Both primary and secondary actions occur on Enter key up. The only exception\n            // is secondary actions, which occur on double click with a mouse.\n            if (e.pointerType === 'touch' || e.pointerType === 'pen' || e.pointerType === 'virtual' || e.pointerType === 'keyboard' && hasAction && $880e95eb8b93ba9a$var$isActionKey() || e.pointerType === 'mouse' && hadPrimaryActionOnPressStart.current) {\n                if (hasAction) performAction(e);\n                else if (allowsSelection) onSelect(e);\n            }\n        };\n    }\n    itemProps['data-collection'] = (0, _utils_mjs__WEBPACK_IMPORTED_MODULE_3__.getCollectionId)(manager.collection);\n    itemProps['data-key'] = key;\n    itemPressProps.preventFocusOnPress = shouldUseVirtualFocus;\n    // When using virtual focus, make sure the focused key gets updated on press.\n    if (shouldUseVirtualFocus) itemPressProps = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_7__.mergeProps)(itemPressProps, {\n        onPressStart (e) {\n            if (e.pointerType !== 'touch') {\n                manager.setFocused(true);\n                manager.setFocusedKey(key);\n            }\n        },\n        onPress (e) {\n            if (e.pointerType === 'touch') {\n                manager.setFocused(true);\n                manager.setFocusedKey(key);\n            }\n        }\n    });\n    let { pressProps: pressProps, isPressed: isPressed } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_8__.usePress)(itemPressProps);\n    // Double clicking with a mouse with selectionBehavior = 'replace' performs an action.\n    let onDoubleClick = hasSecondaryAction ? (e)=>{\n        if (modality.current === 'mouse') {\n            e.stopPropagation();\n            e.preventDefault();\n            performAction(e);\n        }\n    } : undefined;\n    // Long pressing an item with touch when selectionBehavior = 'replace' switches the selection behavior\n    // to 'toggle'. This changes the single tap behavior from performing an action (i.e. navigating) to\n    // selecting, and may toggle the appearance of a UI affordance like checkboxes on each item.\n    let { longPressProps: longPressProps } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_9__.useLongPress)({\n        isDisabled: !longPressEnabled,\n        onLongPress (e) {\n            if (e.pointerType === 'touch') {\n                onSelect(e);\n                manager.setSelectionBehavior('toggle');\n            }\n        }\n    });\n    // Prevent native drag and drop on long press if we also select on long press.\n    // Once the user is in selection mode, they can long press again to drag.\n    // Use a capturing listener to ensure this runs before useDrag, regardless of\n    // the order the props get merged.\n    let onDragStartCapture = (e)=>{\n        if (modality.current === 'touch' && longPressEnabledOnPressStart.current) e.preventDefault();\n    };\n    // Prevent default on link clicks so that we control exactly\n    // when they open (to match selection behavior).\n    let onClick = manager.isLink(key) ? (e)=>{\n        if (!(0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.openLink).isOpening) e.preventDefault();\n    } : undefined;\n    return {\n        itemProps: (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_7__.mergeProps)(itemProps, allowsSelection || hasPrimaryAction || shouldUseVirtualFocus ? pressProps : {}, longPressEnabled ? longPressProps : {}, {\n            onDoubleClick: onDoubleClick,\n            onDragStartCapture: onDragStartCapture,\n            onClick: onClick,\n            id: id\n        }, // Prevent DOM focus from moving on mouse down when using virtual focus\n        shouldUseVirtualFocus ? {\n            onMouseDown: (e)=>e.preventDefault()\n        } : undefined),\n        isPressed: isPressed,\n        isSelected: manager.isSelected(key),\n        isFocused: manager.isFocused && manager.focusedKey === key,\n        isDisabled: isDisabled,\n        allowsSelection: allowsSelection,\n        hasAction: hasAction\n    };\n}\nfunction $880e95eb8b93ba9a$var$isActionKey() {\n    let event = window.event;\n    return (event === null || event === void 0 ? void 0 : event.key) === 'Enter';\n}\nfunction $880e95eb8b93ba9a$var$isSelectionKey() {\n    let event = window.event;\n    return (event === null || event === void 0 ? void 0 : event.key) === ' ' || (event === null || event === void 0 ? void 0 : event.code) === 'Space';\n}\n\n\n\n//# sourceMappingURL=useSelectableItem.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/useSelectableItem.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/useSelectableList.mjs":
/*!*******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/useSelectableList.mjs ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSelectableList: () => (/* binding */ $982254629710d113$export$b95089534ab7c1fd)\n/* harmony export */ });\n/* harmony import */ var _useSelectableCollection_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./useSelectableCollection.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/useSelectableCollection.mjs\");\n/* harmony import */ var _ListKeyboardDelegate_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ListKeyboardDelegate.mjs */ \"(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/ListKeyboardDelegate.mjs\");\n/* harmony import */ var _react_aria_i18n__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/i18n */ \"(ssr)/./node_modules/.pnpm/@react-aria+i18n@3.12.7_rea_e4b9ec774c1595ed697c15570238dc19/node_modules/@react-aria/i18n/dist/useCollator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n\nfunction $982254629710d113$export$b95089534ab7c1fd(props) {\n    let { selectionManager: selectionManager, collection: collection, disabledKeys: disabledKeys, ref: ref, keyboardDelegate: keyboardDelegate, layoutDelegate: layoutDelegate } = props;\n    // By default, a KeyboardDelegate is provided which uses the DOM to query layout information (e.g. for page up/page down).\n    // When virtualized, the layout object will be passed in as a prop and override this.\n    let collator = (0, _react_aria_i18n__WEBPACK_IMPORTED_MODULE_1__.useCollator)({\n        usage: 'search',\n        sensitivity: 'base'\n    });\n    let disabledBehavior = selectionManager.disabledBehavior;\n    let delegate = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>keyboardDelegate || new (0, _ListKeyboardDelegate_mjs__WEBPACK_IMPORTED_MODULE_2__.ListKeyboardDelegate)({\n            collection: collection,\n            disabledKeys: disabledKeys,\n            disabledBehavior: disabledBehavior,\n            ref: ref,\n            collator: collator,\n            layoutDelegate: layoutDelegate\n        }), [\n        keyboardDelegate,\n        layoutDelegate,\n        collection,\n        disabledKeys,\n        ref,\n        collator,\n        disabledBehavior\n    ]);\n    let { collectionProps: collectionProps } = (0, _useSelectableCollection_mjs__WEBPACK_IMPORTED_MODULE_3__.useSelectableCollection)({\n        ...props,\n        ref: ref,\n        selectionManager: selectionManager,\n        keyboardDelegate: delegate\n    });\n    return {\n        listProps: collectionProps\n    };\n}\n\n\n\n//# sourceMappingURL=useSelectableList.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/useSelectableList.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/useTypeSelect.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/useTypeSelect.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useTypeSelect: () => (/* binding */ $fb3050f43d946246$export$e32c88dfddc6e1d8)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n/**\n * Controls how long to wait before clearing the typeahead buffer.\n */ const $fb3050f43d946246$var$TYPEAHEAD_DEBOUNCE_WAIT_MS = 1000; // 1 second\nfunction $fb3050f43d946246$export$e32c88dfddc6e1d8(options) {\n    let { keyboardDelegate: keyboardDelegate, selectionManager: selectionManager, onTypeSelect: onTypeSelect } = options;\n    let state = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        search: '',\n        timeout: undefined\n    }).current;\n    let onKeyDown = (e)=>{\n        let character = $fb3050f43d946246$var$getStringForKey(e.key);\n        if (!character || e.ctrlKey || e.metaKey || !e.currentTarget.contains(e.target)) return;\n        // Do not propagate the Spacebar event if it's meant to be part of the search.\n        // When we time out, the search term becomes empty, hence the check on length.\n        // Trimming is to account for the case of pressing the Spacebar more than once,\n        // which should cycle through the selection/deselection of the focused item.\n        if (character === ' ' && state.search.trim().length > 0) {\n            e.preventDefault();\n            if (!('continuePropagation' in e)) e.stopPropagation();\n        }\n        state.search += character;\n        if (keyboardDelegate.getKeyForSearch != null) {\n            // Use the delegate to find a key to focus.\n            // Prioritize items after the currently focused item, falling back to searching the whole list.\n            let key = keyboardDelegate.getKeyForSearch(state.search, selectionManager.focusedKey);\n            // If no key found, search from the top.\n            if (key == null) key = keyboardDelegate.getKeyForSearch(state.search);\n            if (key != null) {\n                selectionManager.setFocusedKey(key);\n                if (onTypeSelect) onTypeSelect(key);\n            }\n        }\n        clearTimeout(state.timeout);\n        state.timeout = setTimeout(()=>{\n            state.search = '';\n        }, $fb3050f43d946246$var$TYPEAHEAD_DEBOUNCE_WAIT_MS);\n    };\n    return {\n        typeSelectProps: {\n            // Using a capturing listener to catch the keydown event before\n            // other hooks in order to handle the Spacebar event.\n            onKeyDownCapture: keyboardDelegate.getKeyForSearch ? onKeyDown : undefined\n        }\n    };\n}\nfunction $fb3050f43d946246$var$getStringForKey(key) {\n    // If the key is of length 1, it is an ASCII value.\n    // Otherwise, if there are no ASCII characters in the key name,\n    // it is a Unicode character.\n    // See https://www.w3.org/TR/uievents-key/\n    if (key.length === 1 || !/^[A-Z]/i.test(key)) return key;\n    return '';\n}\n\n\n\n//# sourceMappingURL=useTypeSelect.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/useTypeSelect.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/utils.mjs":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/utils.mjs ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCollectionId: () => (/* binding */ $feb5ffebff200149$export$6aeb1680a0ae8741),\n/* harmony export */   getItemElement: () => (/* binding */ $feb5ffebff200149$export$c3d8340acf92597f),\n/* harmony export */   isNonContiguousSelectionModifier: () => (/* binding */ $feb5ffebff200149$export$d3e3bd3e26688c04),\n/* harmony export */   useCollectionId: () => (/* binding */ $feb5ffebff200149$export$881eb0d9f3605d9d)\n/* harmony export */ });\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/platform.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/useId.mjs\");\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \nfunction $feb5ffebff200149$export$d3e3bd3e26688c04(e) {\n    // Ctrl + Arrow Up/Arrow Down has a system wide meaning on macOS, so use Alt instead.\n    // On Windows and Ubuntu, Alt + Space has a system wide meaning.\n    return (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.isAppleDevice)() ? e.altKey : e.ctrlKey;\n}\nfunction $feb5ffebff200149$export$c3d8340acf92597f(collectionRef, key) {\n    var _collectionRef_current, _collectionRef_current1;\n    let selector = `[data-key=\"${CSS.escape(String(key))}\"]`;\n    let collection = (_collectionRef_current = collectionRef.current) === null || _collectionRef_current === void 0 ? void 0 : _collectionRef_current.dataset.collection;\n    if (collection) selector = `[data-collection=\"${CSS.escape(collection)}\"]${selector}`;\n    return (_collectionRef_current1 = collectionRef.current) === null || _collectionRef_current1 === void 0 ? void 0 : _collectionRef_current1.querySelector(selector);\n}\nconst $feb5ffebff200149$var$collectionMap = new WeakMap();\nfunction $feb5ffebff200149$export$881eb0d9f3605d9d(collection) {\n    let id = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_1__.useId)();\n    $feb5ffebff200149$var$collectionMap.set(collection, id);\n    return id;\n}\nfunction $feb5ffebff200149$export$6aeb1680a0ae8741(collection) {\n    return $feb5ffebff200149$var$collectionMap.get(collection);\n}\n\n\n\n//# sourceMappingURL=utils.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+selection@3.23._9903dd0827e3047fede7caf7e62ed549/node_modules/@react-aria/selection/dist/utils.mjs\n");

/***/ })

};
;