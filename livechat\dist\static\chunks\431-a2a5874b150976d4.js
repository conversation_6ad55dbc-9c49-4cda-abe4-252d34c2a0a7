"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[431],{33415:(e,t,r)=>{r.d(t,{$:()=>l});var n=new Set(["id","type","style","title","role","tabIndex","htmlFor","width","height","abbr","accept","acceptCharset","accessKey","action","allowFullScreen","allowTransparency","alt","async","autoComplete","autoFocus","autoPlay","cellPadding","cellSpacing","challenge","charset","checked","cite","class","className","cols","colSpan","command","content","contentEditable","contextMenu","controls","coords","crossOrigin","data","dateTime","default","defer","dir","disabled","download","draggable","dropzone","encType","enterKeyHint","for","form","formAction","formEncType","formMethod","formNoValidate","formTarget","frameBorder","headers","hidden","high","href","hrefLang","httpEquiv","icon","inputMode","isMap","itemId","itemProp","itemRef","itemScope","itemType","kind","label","lang","list","loop","manifest","max","maxLength","media","mediaGroup","method","min","minLength","multiple","muted","name","noValidate","open","optimum","pattern","ping","placeholder","poster","preload","radioGroup","referrerPolicy","readOnly","rel","required","rows","rowSpan","sandbox","scope","scoped","scrolling","seamless","selected","shape","size","sizes","slot","sortable","span","spellCheck","src","srcDoc","srcSet","start","step","target","translate","typeMustMatch","useMap","value","wmode","wrap"]),o=new Set(["onCopy","onCut","onPaste","onLoad","onError","onWheel","onScroll","onCompositionEnd","onCompositionStart","onCompositionUpdate","onKeyDown","onKeyPress","onKeyUp","onFocus","onBlur","onChange","onInput","onSubmit","onClick","onContextMenu","onDoubleClick","onDrag","onDragEnd","onDragEnter","onDragExit","onDragLeave","onDragOver","onDragStart","onDrop","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseOut","onMouseOver","onMouseUp","onPointerDown","onPointerEnter","onPointerLeave","onPointerUp","onSelect","onTouchCancel","onTouchEnd","onTouchMove","onTouchStart","onAnimationStart","onAnimationEnd","onAnimationIteration","onTransitionEnd"]),a=/^(data-.*)$/,i=/^(aria-.*)$/,s=/^(on[A-Z].*)$/;function l(e,t={}){let{labelable:r=!0,enabled:d=!0,propNames:u,omitPropNames:c,omitEventNames:p,omitDataProps:f,omitEventProps:g}=t,v={};if(!d)return e;for(let t in e)!((null==c?void 0:c.has(t))||(null==p?void 0:p.has(t))&&s.test(t)||s.test(t)&&!o.has(t)||f&&a.test(t)||g&&s.test(t))&&(Object.prototype.hasOwnProperty.call(e,t)&&(n.has(t)||r&&i.test(t)||(null==u?void 0:u.has(t))||a.test(t))||s.test(t))&&(v[t]=e[t]);return v}},72855:(e,t,r)=>{r.d(t,{q:()=>o});var n=r(41473);function o(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{strict:t=!0,errorMessage:r="useContext: `context` is undefined. Seems you forgot to wrap component within the Provider",name:o}=e,a=n.createContext(void 0);return a.displayName=o,[a.Provider,function e(){var o;let i=n.useContext(a);if(!i&&t){let t=Error(r);throw t.name="ContextError",null==(o=Error.captureStackTrace)||o.call(Error,t,e),t}return i},a]}},74029:(e,t,r)=>{r.d(t,{mK:()=>o,zD:()=>a});var n=r(41473);function o(e){return{UNSAFE_getDOMNode:()=>e.current}}function a(e){let t=(0,n.useRef)(null);return(0,n.useImperativeHandle)(e,()=>t.current),t}"undefined"!=typeof window&&window.document&&window.document.createElement},38579:(e,t,r)=>{r.d(t,{$z:()=>d,GU:()=>g,Hk:()=>u,Im:()=>i,Lz:()=>f,QA:()=>n,R8:()=>h,Tn:()=>s,ZH:()=>p,j1:()=>c,qE:()=>m,sE:()=>l,t6:()=>v});var n=e=>e?"":void 0;function o(e){return Array.isArray(e)}function a(e){let t=typeof e;return null!=e&&("object"===t||"function"===t)&&!o(e)}function i(e){return o(e)?o(e)&&0===e.length:a(e)?a(e)&&0===Object.keys(e).length:null==e||""===e}function s(e){return"function"==typeof e}var l=e=>e?"true":void 0;function d(...e){for(var t,r,n=0,o="";n<e.length;)(t=e[n++])&&(r=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t))for(r=0;r<t.length;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n);else for(r in t)t[r]&&(o&&(o+=" "),o+=r)}return o}(t))&&(o&&(o+=" "),o+=r);return o}var u=e=>(null==e?void 0:e.length)<=4?e:null==e?void 0:e.slice(0,3),c=(...e)=>{let t=" ";for(let r of e)if("string"==typeof r&&r.length>0){t=r;break}return t},p=e=>e?e.charAt(0).toUpperCase()+e.slice(1).toLowerCase():"";function f(e){return`${e}-${Math.floor(1e6*Math.random())}`}function g(e){for(let t in e)t.startsWith("on")&&delete e[t];return e}function v(e){if(!e||"object"!=typeof e)return"";try{return JSON.stringify(e)}catch(e){return""}}function m(e,t,r){return Math.min(Math.max(e,t),r)}var b={};function h(e,t,...r){let n=t?` [${t}]`:" ",o=`[Hero UI]${n}: ${e}`;"undefined"!=typeof console&&(b[o]||(b[o]=!0))}},34583:(e,t,r)=>{r.d(t,{o:()=>u});var n=r(78790),o=(0,r(76365).tv)({slots:{base:"relative inline-flex flex-col gap-2 items-center justify-center",wrapper:"relative flex",label:"text-foreground dark:text-foreground-dark font-regular",circle1:"absolute w-full h-full rounded-full",circle2:"absolute w-full h-full rounded-full",dots:"relative rounded-full mx-auto",spinnerBars:["absolute","animate-fade-out","rounded-full","w-[25%]","h-[8%]","left-[calc(37.5%)]","top-[calc(46%)]","spinner-bar-animation"]},variants:{size:{sm:{wrapper:"w-5 h-5",circle1:"border-2",circle2:"border-2",dots:"size-1",label:"text-small"},md:{wrapper:"w-8 h-8",circle1:"border-3",circle2:"border-3",dots:"size-1.5",label:"text-medium"},lg:{wrapper:"w-10 h-10",circle1:"border-3",circle2:"border-3",dots:"size-2",label:"text-large"}},color:{current:{circle1:"border-b-current",circle2:"border-b-current",dots:"bg-current",spinnerBars:"bg-current"},white:{circle1:"border-b-white",circle2:"border-b-white",dots:"bg-white",spinnerBars:"bg-white"},default:{circle1:"border-b-default",circle2:"border-b-default",dots:"bg-default",spinnerBars:"bg-default"},primary:{circle1:"border-b-primary",circle2:"border-b-primary",dots:"bg-primary",spinnerBars:"bg-primary"},secondary:{circle1:"border-b-secondary",circle2:"border-b-secondary",dots:"bg-secondary",spinnerBars:"bg-secondary"},success:{circle1:"border-b-success",circle2:"border-b-success",dots:"bg-success",spinnerBars:"bg-success"},warning:{circle1:"border-b-warning",circle2:"border-b-warning",dots:"bg-warning",spinnerBars:"bg-warning"},danger:{circle1:"border-b-danger",circle2:"border-b-danger",dots:"bg-danger",spinnerBars:"bg-danger"}},labelColor:{foreground:{label:"text-foreground"},primary:{label:"text-primary"},secondary:{label:"text-secondary"},success:{label:"text-success"},warning:{label:"text-warning"},danger:{label:"text-danger"}},variant:{default:{circle1:["animate-spinner-ease-spin","border-solid","border-t-transparent","border-l-transparent","border-r-transparent"],circle2:["opacity-75","animate-spinner-linear-spin","border-dotted","border-t-transparent","border-l-transparent","border-r-transparent"]},gradient:{circle1:["border-0","bg-gradient-to-b","from-transparent","via-transparent","to-primary","animate-spinner-linear-spin","[animation-duration:1s]","[-webkit-mask:radial-gradient(closest-side,rgba(0,0,0,0.0)calc(100%-3px),rgba(0,0,0,1)calc(100%-3px))]"],circle2:["hidden"]},wave:{wrapper:"translate-y-3/4",dots:["animate-sway","spinner-dot-animation"]},dots:{wrapper:"translate-y-2/4",dots:["animate-blink","spinner-dot-blink-animation"]},spinner:{},simple:{wrapper:"text-foreground h-5 w-5 animate-spin",circle1:"opacity-25",circle2:"opacity-75"}}},defaultVariants:{size:"md",color:"primary",labelColor:"foreground",variant:"default"},compoundVariants:[{variant:"gradient",color:"current",class:{circle1:"to-current"}},{variant:"gradient",color:"white",class:{circle1:"to-white"}},{variant:"gradient",color:"default",class:{circle1:"to-default"}},{variant:"gradient",color:"primary",class:{circle1:"to-primary"}},{variant:"gradient",color:"secondary",class:{circle1:"to-secondary"}},{variant:"gradient",color:"success",class:{circle1:"to-success"}},{variant:"gradient",color:"warning",class:{circle1:"to-warning"}},{variant:"gradient",color:"danger",class:{circle1:"to-danger"}},{variant:"wave",size:"sm",class:{wrapper:"w-5 h-5"}},{variant:"wave",size:"md",class:{wrapper:"w-8 h-8"}},{variant:"wave",size:"lg",class:{wrapper:"w-12 h-12"}},{variant:"dots",size:"sm",class:{wrapper:"w-5 h-5"}},{variant:"dots",size:"md",class:{wrapper:"w-8 h-8"}},{variant:"dots",size:"lg",class:{wrapper:"w-12 h-12"}},{variant:"simple",size:"sm",class:{wrapper:"w-5 h-5"}},{variant:"simple",size:"md",class:{wrapper:"w-8 h-8"}},{variant:"simple",size:"lg",class:{wrapper:"w-12 h-12"}},{variant:"simple",color:"current",class:{wrapper:"text-current"}},{variant:"simple",color:"white",class:{wrapper:"text-white"}},{variant:"simple",color:"default",class:{wrapper:"text-default"}},{variant:"simple",color:"primary",class:{wrapper:"text-primary"}},{variant:"simple",color:"secondary",class:{wrapper:"text-secondary"}},{variant:"simple",color:"success",class:{wrapper:"text-success"}},{variant:"simple",color:"warning",class:{wrapper:"text-warning"}},{variant:"simple",color:"danger",class:{wrapper:"text-danger"}}]}),a=r(38579),i=r(41473),s=r(58148),l=r(12389),d=(0,n.Rf)((e,t)=>{let{slots:r,classNames:d,label:u,variant:c,getSpinnerProps:p}=function(e){var t,r;let[l,d]=(0,n.rE)(e,o.variantKeys),u=(0,s.o)(),c=null!=(r=null!=(t=null==e?void 0:e.variant)?t:null==u?void 0:u.spinnerVariant)?r:"default",{children:p,className:f,classNames:g,label:v,...m}=l,b=(0,i.useMemo)(()=>o({...d}),[(0,a.t6)(d)]),h=(0,a.$z)(null==g?void 0:g.base,f),y=v||p,w=(0,i.useMemo)(()=>y&&"string"==typeof y?y:m["aria-label"]?"":"Loading",[p,y,m["aria-label"]]),x=(0,i.useCallback)(()=>({"aria-label":w,className:b.base({class:h}),...m}),[w,b,h,m]);return{label:y,slots:b,classNames:g,variant:c,getSpinnerProps:x}}({...e});return"wave"===c||"dots"===c?(0,l.jsxs)("div",{ref:t,...p(),children:[(0,l.jsx)("div",{className:r.wrapper({class:null==d?void 0:d.wrapper}),children:[void 0,void 0,void 0].map((e,t)=>(0,l.jsx)("i",{className:r.dots({class:null==d?void 0:d.dots}),style:{"--dot-index":t}},"dot-".concat(t)))}),u&&(0,l.jsx)("span",{className:r.label({class:null==d?void 0:d.label}),children:u})]}):"simple"===c?(0,l.jsxs)("div",{ref:t,...p(),children:[(0,l.jsxs)("svg",{className:r.wrapper({class:null==d?void 0:d.wrapper}),fill:"none",viewBox:"0 0 24 24",children:[(0,l.jsx)("circle",{className:r.circle1({class:null==d?void 0:d.circle1}),cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,l.jsx)("path",{className:r.circle2({class:null==d?void 0:d.circle2}),d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z",fill:"currentColor"})]}),u&&(0,l.jsx)("span",{className:r.label({class:null==d?void 0:d.label}),children:u})]}):"spinner"===c?(0,l.jsxs)("div",{ref:t,...p(),children:[(0,l.jsx)("div",{className:r.wrapper({class:null==d?void 0:d.wrapper}),children:[...Array(12)].map((e,t)=>(0,l.jsx)("i",{className:r.spinnerBars({class:null==d?void 0:d.spinnerBars}),style:{"--bar-index":t}},"star-".concat(t)))}),u&&(0,l.jsx)("span",{className:r.label({class:null==d?void 0:d.label}),children:u})]}):(0,l.jsxs)("div",{ref:t,...p(),children:[(0,l.jsxs)("div",{className:r.wrapper({class:null==d?void 0:d.wrapper}),children:[(0,l.jsx)("i",{className:r.circle1({class:null==d?void 0:d.circle1})}),(0,l.jsx)("i",{className:r.circle2({class:null==d?void 0:d.circle2})})]}),u&&(0,l.jsx)("span",{className:r.label({class:null==d?void 0:d.label}),children:u})]})});d.displayName="HeroUI.Spinner";var u=d},78790:(e,t,r)=>{r.d(t,{Rf:()=>o,rE:()=>a});var n=r(41473);function o(e){return(0,n.forwardRef)(e)}var a=(e,t,r=!0)=>{if(!t)return[e,{}];let n=t.reduce((t,r)=>r in e?{...t,[r]:e[r]}:t,{});return r?[Object.keys(e).filter(e=>!t.includes(e)).reduce((t,r)=>({...t,[r]:e[r]}),{}),n]:[e,n]}},58148:(e,t,r)=>{r.d(t,{n:()=>n,o:()=>o});var[n,o]=(0,r(72855).q)({name:"ProviderContext",strict:!1})},52294:(e,t,r)=>{r.d(t,{KU:()=>a,n3:()=>s,oT:()=>i,wA:()=>o,zb:()=>n});var n=["outline-none","data-[focus-visible=true]:z-10","data-[focus-visible=true]:outline-2","data-[focus-visible=true]:outline-focus","data-[focus-visible=true]:outline-offset-2"],o=["outline-none","group-data-[focus-visible=true]:z-10","group-data-[focus-visible=true]:ring-2","group-data-[focus-visible=true]:ring-focus","group-data-[focus-visible=true]:ring-offset-2","group-data-[focus-visible=true]:ring-offset-background"],a=["absolute","top-1/2","left-1/2","-translate-x-1/2","-translate-y-1/2"],i={default:["[&+.border-medium.border-default]:ms-[calc(theme(borderWidth.medium)*-1)]"],primary:["[&+.border-medium.border-primary]:ms-[calc(theme(borderWidth.medium)*-1)]"],secondary:["[&+.border-medium.border-secondary]:ms-[calc(theme(borderWidth.medium)*-1)]"],success:["[&+.border-medium.border-success]:ms-[calc(theme(borderWidth.medium)*-1)]"],warning:["[&+.border-medium.border-warning]:ms-[calc(theme(borderWidth.medium)*-1)]"],danger:["[&+.border-medium.border-danger]:ms-[calc(theme(borderWidth.medium)*-1)]"]},s=["font-inherit","text-[100%]","leading-[1.15]","m-0","p-0","overflow-visible","box-border","absolute","top-0","w-full","h-full","opacity-[0.0001]","z-[1]","cursor-pointer","disabled:cursor-default"]},97356:(e,t,r)=>{r.d(t,{k:()=>n});var n={solid:{default:"bg-default text-default-foreground",primary:"bg-primary text-primary-foreground",secondary:"bg-secondary text-secondary-foreground",success:"bg-success text-success-foreground",warning:"bg-warning text-warning-foreground",danger:"bg-danger text-danger-foreground",foreground:"bg-foreground text-background"},shadow:{default:"shadow-lg shadow-default/50 bg-default text-default-foreground",primary:"shadow-lg shadow-primary/40 bg-primary text-primary-foreground",secondary:"shadow-lg shadow-secondary/40 bg-secondary text-secondary-foreground",success:"shadow-lg shadow-success/40 bg-success text-success-foreground",warning:"shadow-lg shadow-warning/40 bg-warning text-warning-foreground",danger:"shadow-lg shadow-danger/40 bg-danger text-danger-foreground",foreground:"shadow-lg shadow-foreground/40 bg-foreground text-background"},bordered:{default:"bg-transparent border-default text-foreground",primary:"bg-transparent border-primary text-primary",secondary:"bg-transparent border-secondary text-secondary",success:"bg-transparent border-success text-success",warning:"bg-transparent border-warning text-warning",danger:"bg-transparent border-danger text-danger",foreground:"bg-transparent border-foreground text-foreground"},flat:{default:"bg-default/40 text-default-700",primary:"bg-primary/20 text-primary-600",secondary:"bg-secondary/20 text-secondary-600",success:"bg-success/20 text-success-700 dark:text-success",warning:"bg-warning/20 text-warning-700 dark:text-warning",danger:"bg-danger/20 text-danger-600 dark:text-danger-500",foreground:"bg-foreground/10 text-foreground"},faded:{default:"border-default bg-default-100 text-default-foreground",primary:"border-default bg-default-100 text-primary",secondary:"border-default bg-default-100 text-secondary",success:"border-default bg-default-100 text-success",warning:"border-default bg-default-100 text-warning",danger:"border-default bg-default-100 text-danger",foreground:"border-default bg-default-100 text-foreground"},light:{default:"bg-transparent text-default-foreground",primary:"bg-transparent text-primary",secondary:"bg-transparent text-secondary",success:"bg-transparent text-success",warning:"bg-transparent text-warning",danger:"bg-transparent text-danger",foreground:"bg-transparent text-foreground"},ghost:{default:"border-default text-default-foreground",primary:"border-primary text-primary",secondary:"border-secondary text-secondary",success:"border-success text-success",warning:"border-warning text-warning",danger:"border-danger text-danger",foreground:"border-foreground text-foreground hover:!bg-foreground"}}},44290:(e,t,r)=>{r.d(t,{x:()=>i});var n=r(97356),o=r(76365),a=r(52294),i=(0,o.tv)({base:["z-0","group","relative","inline-flex","items-center","justify-center","box-border","appearance-none","outline-none","select-none","whitespace-nowrap","min-w-max","font-normal","subpixel-antialiased","overflow-hidden","tap-highlight-transparent","data-[pressed=true]:scale-[0.97]",...a.zb],variants:{variant:{solid:"",bordered:"border-medium bg-transparent",light:"bg-transparent",flat:"",faded:"border-medium",shadow:"",ghost:"border-medium bg-transparent"},size:{sm:"px-3 min-w-16 h-8 text-tiny gap-2 rounded-small",md:"px-4 min-w-20 h-10 text-small gap-2 rounded-medium",lg:"px-6 min-w-24 h-12 text-medium gap-3 rounded-large"},color:{default:"",primary:"",secondary:"",success:"",warning:"",danger:""},radius:{none:"rounded-none",sm:"rounded-small",md:"rounded-medium",lg:"rounded-large",full:"rounded-full"},fullWidth:{true:"w-full"},isDisabled:{true:"opacity-disabled pointer-events-none"},isInGroup:{true:"[&:not(:first-child):not(:last-child)]:rounded-none"},isIconOnly:{true:"px-0 !gap-0",false:"[&>svg]:max-w-[theme(spacing.8)]"},disableAnimation:{true:"!transition-none data-[pressed=true]:scale-100",false:"transition-transform-colors-opacity motion-reduce:transition-none"}},defaultVariants:{size:"md",variant:"solid",color:"default",fullWidth:!1,isDisabled:!1,isInGroup:!1},compoundVariants:[{variant:"solid",color:"default",class:n.k.solid.default},{variant:"solid",color:"primary",class:n.k.solid.primary},{variant:"solid",color:"secondary",class:n.k.solid.secondary},{variant:"solid",color:"success",class:n.k.solid.success},{variant:"solid",color:"warning",class:n.k.solid.warning},{variant:"solid",color:"danger",class:n.k.solid.danger},{variant:"shadow",color:"default",class:n.k.shadow.default},{variant:"shadow",color:"primary",class:n.k.shadow.primary},{variant:"shadow",color:"secondary",class:n.k.shadow.secondary},{variant:"shadow",color:"success",class:n.k.shadow.success},{variant:"shadow",color:"warning",class:n.k.shadow.warning},{variant:"shadow",color:"danger",class:n.k.shadow.danger},{variant:"bordered",color:"default",class:n.k.bordered.default},{variant:"bordered",color:"primary",class:n.k.bordered.primary},{variant:"bordered",color:"secondary",class:n.k.bordered.secondary},{variant:"bordered",color:"success",class:n.k.bordered.success},{variant:"bordered",color:"warning",class:n.k.bordered.warning},{variant:"bordered",color:"danger",class:n.k.bordered.danger},{variant:"flat",color:"default",class:n.k.flat.default},{variant:"flat",color:"primary",class:n.k.flat.primary},{variant:"flat",color:"secondary",class:n.k.flat.secondary},{variant:"flat",color:"success",class:n.k.flat.success},{variant:"flat",color:"warning",class:n.k.flat.warning},{variant:"flat",color:"danger",class:n.k.flat.danger},{variant:"faded",color:"default",class:n.k.faded.default},{variant:"faded",color:"primary",class:n.k.faded.primary},{variant:"faded",color:"secondary",class:n.k.faded.secondary},{variant:"faded",color:"success",class:n.k.faded.success},{variant:"faded",color:"warning",class:n.k.faded.warning},{variant:"faded",color:"danger",class:n.k.faded.danger},{variant:"light",color:"default",class:[n.k.light.default,"data-[hover=true]:bg-default/40"]},{variant:"light",color:"primary",class:[n.k.light.primary,"data-[hover=true]:bg-primary/20"]},{variant:"light",color:"secondary",class:[n.k.light.secondary,"data-[hover=true]:bg-secondary/20"]},{variant:"light",color:"success",class:[n.k.light.success,"data-[hover=true]:bg-success/20"]},{variant:"light",color:"warning",class:[n.k.light.warning,"data-[hover=true]:bg-warning/20"]},{variant:"light",color:"danger",class:[n.k.light.danger,"data-[hover=true]:bg-danger/20"]},{variant:"ghost",color:"default",class:[n.k.ghost.default,"data-[hover=true]:!bg-default"]},{variant:"ghost",color:"primary",class:[n.k.ghost.primary,"data-[hover=true]:!bg-primary data-[hover=true]:!text-primary-foreground"]},{variant:"ghost",color:"secondary",class:[n.k.ghost.secondary,"data-[hover=true]:!bg-secondary data-[hover=true]:!text-secondary-foreground"]},{variant:"ghost",color:"success",class:[n.k.ghost.success,"data-[hover=true]:!bg-success data-[hover=true]:!text-success-foreground"]},{variant:"ghost",color:"warning",class:[n.k.ghost.warning,"data-[hover=true]:!bg-warning data-[hover=true]:!text-warning-foreground"]},{variant:"ghost",color:"danger",class:[n.k.ghost.danger,"data-[hover=true]:!bg-danger data-[hover=true]:!text-danger-foreground"]},{isInGroup:!0,class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,size:"sm",class:"rounded-none first:rounded-s-small last:rounded-e-small"},{isInGroup:!0,size:"md",class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,size:"lg",class:"rounded-none first:rounded-s-large last:rounded-e-large"},{isInGroup:!0,isRounded:!0,class:"rounded-none first:rounded-s-full last:rounded-e-full"},{isInGroup:!0,radius:"none",class:"rounded-none first:rounded-s-none last:rounded-e-none"},{isInGroup:!0,radius:"sm",class:"rounded-none first:rounded-s-small last:rounded-e-small"},{isInGroup:!0,radius:"md",class:"rounded-none first:rounded-s-medium last:rounded-e-medium"},{isInGroup:!0,radius:"lg",class:"rounded-none first:rounded-s-large last:rounded-e-large"},{isInGroup:!0,radius:"full",class:"rounded-none first:rounded-s-full last:rounded-e-full"},{isInGroup:!0,variant:["ghost","bordered"],color:"default",className:a.oT.default},{isInGroup:!0,variant:["ghost","bordered"],color:"primary",className:a.oT.primary},{isInGroup:!0,variant:["ghost","bordered"],color:"secondary",className:a.oT.secondary},{isInGroup:!0,variant:["ghost","bordered"],color:"success",className:a.oT.success},{isInGroup:!0,variant:["ghost","bordered"],color:"warning",className:a.oT.warning},{isInGroup:!0,variant:["ghost","bordered"],color:"danger",className:a.oT.danger},{isIconOnly:!0,size:"sm",class:"min-w-8 w-8 h-8"},{isIconOnly:!0,size:"md",class:"min-w-10 w-10 h-10"},{isIconOnly:!0,size:"lg",class:"min-w-12 w-12 h-12"},{variant:["solid","faded","flat","bordered","shadow"],class:"data-[hover=true]:opacity-hover"}]});(0,o.tv)({base:"inline-flex items-center justify-center h-auto",variants:{fullWidth:{true:"w-full"}},defaultVariants:{fullWidth:!1}})},76365:(e,t,r)=>{r.d(t,{tv:()=>ex});var n=["small","medium","large"],o={theme:{opacity:["disabled"],spacing:["divider"],borderWidth:n,borderRadius:n},classGroups:{shadow:[{shadow:n}],"font-size":[{text:["tiny",...n]}],"bg-image":["bg-stripe-gradient-default","bg-stripe-gradient-primary","bg-stripe-gradient-secondary","bg-stripe-gradient-success","bg-stripe-gradient-warning","bg-stripe-gradient-danger"]}},a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=e=>!e||"object"!=typeof e||0===Object.keys(e).length,s=(e,t)=>JSON.stringify(e)===JSON.stringify(t);function l(e){let t=[];return function e(t,r){t.forEach(function(t){Array.isArray(t)?e(t,r):r.push(t)})}(e,t),t}var d=(...e)=>l(e).filter(Boolean),u=(e,t)=>{let r={},n=Object.keys(e),o=Object.keys(t);for(let a of n)if(o.includes(a)){let n=e[a],o=t[a];Array.isArray(n)||Array.isArray(o)?r[a]=d(o,n):"object"==typeof n&&"object"==typeof o?r[a]=u(n,o):r[a]=o+" "+n}else r[a]=e[a];for(let e of o)n.includes(e)||(r[e]=t[e]);return r},c=e=>e&&"string"==typeof e?e.replace(/\s+/g," ").trim():e;let p=e=>{let t=m(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),f(r,t)||v(e)},getConflictingClassGroupIds:(e,t)=>{let o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},f=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),o=n?f(e.slice(1),n):void 0;if(o)return o;if(0===t.validators.length)return;let a=e.join("-");return t.validators.find(({validator:e})=>e(a))?.classGroupId},g=/^\[(.+)\]$/,v=e=>{if(g.test(e)){let t=g.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},m=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return w(Object.entries(e.classGroups),r).forEach(([e,r])=>{b(r,n,e,t)}),n},b=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:h(t,e)).classGroupId=r;return}if("function"==typeof e){if(y(e)){b(e(n),t,r,n);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,o])=>{b(o,h(t,e),r,n)})})},h=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},y=e=>e.isThemeGetter,w=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,x=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,a)=>{r.set(o,a),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},E=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,o=t[0],a=t.length,i=e=>{let r;let i=[],s=0,l=0;for(let d=0;d<e.length;d++){let u=e[d];if(0===s){if(u===o&&(n||e.slice(d,d+a)===t)){i.push(e.slice(l,d)),l=d+a;continue}if("/"===u){r=d;continue}}"["===u?s++:"]"===u&&s--}let d=0===i.length?e:e.substring(l),u=d.startsWith("!"),c=u?d.substring(1):d;return{modifiers:i,hasImportantModifier:u,baseClassName:c,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};return r?e=>r({className:e,parseClassName:i}):i},T=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},k=e=>({cache:x(e.cacheSize),parseClassName:E(e),...p(e)}),P=/\s+/,S=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o}=t,a=[],i=e.trim().split(P),s="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{modifiers:l,hasImportantModifier:d,baseClassName:u,maybePostfixModifierPosition:c}=r(t),p=!!c,f=n(p?u.substring(0,c):u);if(!f){if(!p||!(f=n(u))){s=t+(s.length>0?" "+s:s);continue}p=!1}let g=T(l).join(":"),v=d?g+"!":g,m=v+f;if(a.includes(m))continue;a.push(m);let b=o(f,p);for(let e=0;e<b.length;++e){let t=b[e];a.push(v+t)}s=t+(s.length>0?" "+s:s)}return s};function M(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=C(e))&&(n&&(n+=" "),n+=t);return n}let C=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=C(e[n]))&&(r&&(r+=" "),r+=t);return r};function L(e,...t){let r,n,o;let a=function(s){return n=(r=k(t.reduce((e,t)=>t(e),e()))).cache.get,o=r.cache.set,a=i,i(s)};function i(e){let t=n(e);if(t)return t;let a=S(e,r);return o(e,a),a}return function(){return a(M.apply(null,arguments))}}let A=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},j=/^\[(?:([a-z-]+):)?(.+)\]$/i,z=/^\d+\/\d+$/,D=new Set(["px","full","screen"]),O=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,I=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,N=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,W=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,K=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,R=e=>F(e)||D.has(e)||z.test(e),H=e=>ee(e,"length",et),F=e=>!!e&&!Number.isNaN(Number(e)),B=e=>ee(e,"number",F),G=e=>!!e&&Number.isInteger(Number(e)),$=e=>e.endsWith("%")&&F(e.slice(0,-1)),V=e=>j.test(e),U=e=>O.test(e),_=new Set(["length","size","percentage"]),Y=e=>ee(e,_,er),X=e=>ee(e,"position",er),q=new Set(["image","url"]),J=e=>ee(e,q,eo),Z=e=>ee(e,"",en),Q=()=>!0,ee=(e,t,r)=>{let n=j.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},et=e=>I.test(e)&&!N.test(e),er=()=>!1,en=e=>W.test(e),eo=e=>K.test(e);Symbol.toStringTag;let ea=()=>{let e=A("colors"),t=A("spacing"),r=A("blur"),n=A("brightness"),o=A("borderColor"),a=A("borderRadius"),i=A("borderSpacing"),s=A("borderWidth"),l=A("contrast"),d=A("grayscale"),u=A("hueRotate"),c=A("invert"),p=A("gap"),f=A("gradientColorStops"),g=A("gradientColorStopPositions"),v=A("inset"),m=A("margin"),b=A("opacity"),h=A("padding"),y=A("saturate"),w=A("scale"),x=A("sepia"),E=A("skew"),T=A("space"),k=A("translate"),P=()=>["auto","contain","none"],S=()=>["auto","hidden","clip","visible","scroll"],M=()=>["auto",V,t],C=()=>[V,t],L=()=>["",R,H],j=()=>["auto",F,V],z=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],D=()=>["solid","dashed","dotted","double","none"],O=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],I=()=>["start","end","center","between","around","evenly","stretch"],N=()=>["","0",V],W=()=>["auto","avoid","all","avoid-page","page","left","right","column"],K=()=>[F,V];return{cacheSize:500,separator:":",theme:{colors:[Q],spacing:[R,H],blur:["none","",U,V],brightness:K(),borderColor:[e],borderRadius:["none","","full",U,V],borderSpacing:C(),borderWidth:L(),contrast:K(),grayscale:N(),hueRotate:K(),invert:N(),gap:C(),gradientColorStops:[e],gradientColorStopPositions:[$,H],inset:M(),margin:M(),opacity:K(),padding:C(),saturate:K(),scale:K(),sepia:N(),skew:K(),space:C(),translate:C()},classGroups:{aspect:[{aspect:["auto","square","video",V]}],container:["container"],columns:[{columns:[U]}],"break-after":[{"break-after":W()}],"break-before":[{"break-before":W()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...z(),V]}],overflow:[{overflow:S()}],"overflow-x":[{"overflow-x":S()}],"overflow-y":[{"overflow-y":S()}],overscroll:[{overscroll:P()}],"overscroll-x":[{"overscroll-x":P()}],"overscroll-y":[{"overscroll-y":P()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[v]}],"inset-x":[{"inset-x":[v]}],"inset-y":[{"inset-y":[v]}],start:[{start:[v]}],end:[{end:[v]}],top:[{top:[v]}],right:[{right:[v]}],bottom:[{bottom:[v]}],left:[{left:[v]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",G,V]}],basis:[{basis:M()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",V]}],grow:[{grow:N()}],shrink:[{shrink:N()}],order:[{order:["first","last","none",G,V]}],"grid-cols":[{"grid-cols":[Q]}],"col-start-end":[{col:["auto",{span:["full",G,V]},V]}],"col-start":[{"col-start":j()}],"col-end":[{"col-end":j()}],"grid-rows":[{"grid-rows":[Q]}],"row-start-end":[{row:["auto",{span:[G,V]},V]}],"row-start":[{"row-start":j()}],"row-end":[{"row-end":j()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",V]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",V]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal",...I()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...I(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...I(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[h]}],px:[{px:[h]}],py:[{py:[h]}],ps:[{ps:[h]}],pe:[{pe:[h]}],pt:[{pt:[h]}],pr:[{pr:[h]}],pb:[{pb:[h]}],pl:[{pl:[h]}],m:[{m:[m]}],mx:[{mx:[m]}],my:[{my:[m]}],ms:[{ms:[m]}],me:[{me:[m]}],mt:[{mt:[m]}],mr:[{mr:[m]}],mb:[{mb:[m]}],ml:[{ml:[m]}],"space-x":[{"space-x":[T]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[T]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",V,t]}],"min-w":[{"min-w":[V,t,"min","max","fit"]}],"max-w":[{"max-w":[V,t,"none","full","min","max","fit","prose",{screen:[U]},U]}],h:[{h:[V,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[V,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[V,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[V,t,"auto","min","max","fit"]}],"font-size":[{text:["base",U,H]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",B]}],"font-family":[{font:[Q]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",V]}],"line-clamp":[{"line-clamp":["none",F,B]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",R,V]}],"list-image":[{"list-image":["none",V]}],"list-style-type":[{list:["none","disc","decimal",V]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[b]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[b]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...D(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",R,H]}],"underline-offset":[{"underline-offset":["auto",R,V]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:C()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",V]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",V]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[b]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...z(),X]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",Y]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},J]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[g]}],"gradient-via-pos":[{via:[g]}],"gradient-to-pos":[{to:[g]}],"gradient-from":[{from:[f]}],"gradient-via":[{via:[f]}],"gradient-to":[{to:[f]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[b]}],"border-style":[{border:[...D(),"hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[b]}],"divide-style":[{divide:D()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...D()]}],"outline-offset":[{"outline-offset":[R,V]}],"outline-w":[{outline:[R,H]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:L()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[b]}],"ring-offset-w":[{"ring-offset":[R,H]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",U,Z]}],"shadow-color":[{shadow:[Q]}],opacity:[{opacity:[b]}],"mix-blend":[{"mix-blend":[...O(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":O()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",U,V]}],grayscale:[{grayscale:[d]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[c]}],saturate:[{saturate:[y]}],sepia:[{sepia:[x]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[d]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[c]}],"backdrop-opacity":[{"backdrop-opacity":[b]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[x]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",V]}],duration:[{duration:K()}],ease:[{ease:["linear","in","out","in-out",V]}],delay:[{delay:K()}],animate:[{animate:["none","spin","ping","pulse","bounce",V]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[G,V]}],"translate-x":[{"translate-x":[k]}],"translate-y":[{"translate-y":[k]}],"skew-x":[{"skew-x":[E]}],"skew-y":[{"skew-y":[E]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",V]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",V]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":C()}],"scroll-mx":[{"scroll-mx":C()}],"scroll-my":[{"scroll-my":C()}],"scroll-ms":[{"scroll-ms":C()}],"scroll-me":[{"scroll-me":C()}],"scroll-mt":[{"scroll-mt":C()}],"scroll-mr":[{"scroll-mr":C()}],"scroll-mb":[{"scroll-mb":C()}],"scroll-ml":[{"scroll-ml":C()}],"scroll-p":[{"scroll-p":C()}],"scroll-px":[{"scroll-px":C()}],"scroll-py":[{"scroll-py":C()}],"scroll-ps":[{"scroll-ps":C()}],"scroll-pe":[{"scroll-pe":C()}],"scroll-pt":[{"scroll-pt":C()}],"scroll-pr":[{"scroll-pr":C()}],"scroll-pb":[{"scroll-pb":C()}],"scroll-pl":[{"scroll-pl":C()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",V]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[R,H,B]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},ei=(e,{cacheSize:t,prefix:r,separator:n,experimentalParseClassName:o,extend:a={},override:i={}})=>{for(let a in es(e,"cacheSize",t),es(e,"prefix",r),es(e,"separator",n),es(e,"experimentalParseClassName",o),i)el(e[a],i[a]);for(let t in a)ed(e[t],a[t]);return e},es=(e,t,r)=>{void 0!==r&&(e[t]=r)},el=(e,t)=>{if(t)for(let r in t)es(e,r,t[r])},ed=(e,t)=>{if(t)for(let r in t){let n=t[r];void 0!==n&&(e[r]=(e[r]||[]).concat(n))}},eu=(e,...t)=>"function"==typeof e?L(ea,e,...t):L(()=>ei(ea(),e),...t),ec=L(ea);var ep={twMerge:!0,twMergeConfig:{},responsiveVariants:!1},ef=e=>e||void 0,eg=(...e)=>ef(l(e).filter(Boolean).join(" ")),ev=null,em={},eb=!1,eh=(...e)=>t=>t.twMerge?((!ev||eb)&&(eb=!1,ev=i(em)?ec:eu({...em,extend:{theme:em.theme,classGroups:em.classGroups,conflictingClassGroupModifiers:em.conflictingClassGroupModifiers,conflictingClassGroups:em.conflictingClassGroups,...em.extend}})),ef(ev(eg(e)))):eg(e),ey=(e,t)=>{for(let r in t)e.hasOwnProperty(r)?e[r]=eg(e[r],t[r]):e[r]=t[r];return e},ew=(e,t)=>{let{extend:r=null,slots:n={},variants:o={},compoundVariants:l=[],compoundSlots:p=[],defaultVariants:f={}}=e,g={...ep,...t},v=null!=r&&r.base?eg(r.base,null==e?void 0:e.base):null==e?void 0:e.base,m=null!=r&&r.variants&&!i(r.variants)?u(o,r.variants):o,b=null!=r&&r.defaultVariants&&!i(r.defaultVariants)?{...r.defaultVariants,...f}:f;i(g.twMergeConfig)||s(g.twMergeConfig,em)||(eb=!0,em=g.twMergeConfig);let h=i(null==r?void 0:r.slots),y=i(n)?{}:{base:eg(null==e?void 0:e.base,h&&(null==r?void 0:r.base)),...n},w=h?y:ey({...null==r?void 0:r.slots},i(y)?{base:null==e?void 0:e.base}:y),x=i(null==r?void 0:r.compoundVariants)?l:d(null==r?void 0:r.compoundVariants,l),E=e=>{if(i(m)&&i(n)&&h)return eh(v,null==e?void 0:e.class,null==e?void 0:e.className)(g);if(x&&!Array.isArray(x))throw TypeError(`The "compoundVariants" prop must be an array. Received: ${typeof x}`);if(p&&!Array.isArray(p))throw TypeError(`The "compoundSlots" prop must be an array. Received: ${typeof p}`);let t=(e,t,r=[],n)=>{let o=r;if("string"==typeof t)o=o.concat(c(t).split(" ").map(t=>`${e}:${t}`));else if(Array.isArray(t))o=o.concat(t.reduce((t,r)=>t.concat(`${e}:${r}`),[]));else if("object"==typeof t&&"string"==typeof n){for(let r in t)if(t.hasOwnProperty(r)&&r===n){let a=t[r];if(a&&"string"==typeof a){let t=c(a);o[n]?o[n]=o[n].concat(t.split(" ").map(t=>`${e}:${t}`)):o[n]=t.split(" ").map(t=>`${e}:${t}`)}else Array.isArray(a)&&a.length>0&&(o[n]=a.reduce((t,r)=>t.concat(`${e}:${r}`),[]))}}return o},r=(r,n=m,o=null,s=null)=>{var l;let d=n[r];if(!d||i(d))return null;let u=null!=(l=null==s?void 0:s[r])?l:null==e?void 0:e[r];if(null===u)return null;let c=a(u),p=Array.isArray(g.responsiveVariants)&&g.responsiveVariants.length>0||!0===g.responsiveVariants,f=null==b?void 0:b[r],v=[];if("object"==typeof c&&p)for(let[e,r]of Object.entries(c)){let n=d[r];if("initial"===e){f=r;continue}Array.isArray(g.responsiveVariants)&&!g.responsiveVariants.includes(e)||(v=t(e,n,v,o))}let h=d[(null!=c&&"object"!=typeof c?c:a(f))||"false"];return"object"==typeof v&&"string"==typeof o&&v[o]?ey(v,h):v.length>0?(v.push(h),"base"===o?v.join(" "):v):h},o=(e,t)=>{if(!m||"object"!=typeof m)return null;let n=[];for(let o in m){let a=r(o,m,e,t),i="base"===e&&"string"==typeof a?a:a&&a[e];i&&(n[n.length]=i)}return n},s={};for(let t in e)void 0!==e[t]&&(s[t]=e[t]);let l=(t,r)=>{var n;let o="object"==typeof(null==e?void 0:e[t])?{[t]:null==(n=e[t])?void 0:n.initial}:{};return{...b,...s,...o,...r}},d=(e=[],t)=>{let r=[];for(let{class:n,className:o,...a}of e){let e=!0;for(let[r,n]of Object.entries(a)){let o=l(r,t)[r];if(Array.isArray(n)){if(!n.includes(o)){e=!1;break}}else{let t=e=>null==e||!1===e;if(t(n)&&t(o))continue;if(o!==n){e=!1;break}}}e&&(n&&r.push(n),o&&r.push(o))}return r},u=e=>{let t=d(x,e);if(!Array.isArray(t))return t;let r={};for(let e of t)if("string"==typeof e&&(r.base=eh(r.base,e)(g)),"object"==typeof e)for(let[t,n]of Object.entries(e))r[t]=eh(r[t],n)(g);return r},f=e=>{if(p.length<1)return null;let t={};for(let{slots:r=[],class:n,className:o,...a}of p){if(!i(a)){let t=!0;for(let r of Object.keys(a)){let n=l(r,e)[r];if(void 0===n||(Array.isArray(a[r])?!a[r].includes(n):a[r]!==n)){t=!1;break}}if(!t)continue}for(let e of r)t[e]=t[e]||[],t[e].push([n,o])}return t};if(!i(n)||!h){let e={};if("object"==typeof w&&!i(w))for(let t of Object.keys(w))e[t]=e=>{var r,n;return eh(w[t],o(t,e),(null!=(r=u(e))?r:[])[t],(null!=(n=f(e))?n:[])[t],null==e?void 0:e.class,null==e?void 0:e.className)(g)};return e}return eh(v,m?Object.keys(m).map(e=>r(e,m)):null,d(x),null==e?void 0:e.class,null==e?void 0:e.className)(g)};return E.variantKeys=(()=>{if(!(!m||"object"!=typeof m))return Object.keys(m)})(),E.extend=r,E.base=v,E.slots=w,E.variants=m,E.defaultVariants=b,E.compoundSlots=p,E.compoundVariants=x,E},ex=(e,t)=>{var r,n,a;return ew(e,{...t,twMerge:null==(r=null==t?void 0:t.twMerge)||r,twMergeConfig:{...null==t?void 0:t.twMergeConfig,theme:{...null==(n=null==t?void 0:t.twMergeConfig)?void 0:n.theme,...o.theme},classGroups:{...null==(a=null==t?void 0:t.twMergeConfig)?void 0:a.classGroups,...o.classGroups}}})}},79119:(e,t,r)=>{r.d(t,{l:()=>l});var n=r(49134),o=r(27241),a=r(31901),i=r(83460),s=r(46353);function l(e){let t=(0,o.TW)(e),r=(0,a.bq)(t);"virtual"===(0,n.ME)()?(0,i.v)(()=>{(0,a.bq)(t)===r&&e.isConnected&&(0,s.e)(e)}):(0,s.e)(e)}},36992:(e,t,r)=>{r.d(t,{E:()=>u,M:()=>d});var n=r(13239),o=r(27241),a=r(83460);let i="default",s="",l=new WeakMap;function d(e){if((0,n.un)()){if("default"===i){let t=(0,o.TW)(e);s=t.documentElement.style.webkitUserSelect,t.documentElement.style.webkitUserSelect="none"}i="disabled"}else if(e instanceof HTMLElement||e instanceof SVGElement){let t="userSelect"in e.style?"userSelect":"webkitUserSelect";l.set(e,e.style[t]),e.style[t]="none"}}function u(e){if((0,n.un)())"disabled"===i&&(i="restoring",setTimeout(()=>{(0,a.v)(()=>{if("restoring"===i){let t=(0,o.TW)(e);"none"===t.documentElement.style.webkitUserSelect&&(t.documentElement.style.webkitUserSelect=s||""),s="",i="default"}})},300));else if((e instanceof HTMLElement||e instanceof SVGElement)&&e&&l.has(e)){let t=l.get(e),r="userSelect"in e.style?"userSelect":"webkitUserSelect";"none"===e.style[r]&&(e.style[r]=t),""===e.getAttribute("style")&&e.removeAttribute("style"),l.delete(e)}}},88450:(e,t,r)=>{r.d(t,{i:()=>s});var n=r(22454),o=r(41473),a=r(27241),i=r(31901);function s(e){let{isDisabled:t,onFocus:r,onBlur:s,onFocusChange:l}=e,d=(0,o.useCallback)(e=>{if(e.target===e.currentTarget)return s&&s(e),l&&l(!1),!0},[s,l]),u=(0,n.yB)(d),c=(0,o.useCallback)(e=>{let t=(0,a.TW)(e.target),n=t?(0,i.bq)(t):(0,i.bq)();e.target===e.currentTarget&&n===(0,i.wt)(e.nativeEvent)&&(r&&r(e),l&&l(!0),u(e))},[l,r,u]);return{focusProps:{onFocus:!t&&(r||l||s)?c:void 0,onBlur:!t&&(s||l)?d:void 0}}}},49134:(e,t,r)=>{r.d(t,{Cl:()=>k,K7:()=>S,ME:()=>T,pP:()=>E});var n=r(22454),o=r(13239),a=r(96569),i=r(27241),s=r(41473);let l=null,d=new Set,u=new Map,c=!1,p=!1,f={Tab:!0,Escape:!0};function g(e,t){for(let r of d)r(e,t)}function v(e){c=!0,e.metaKey||!(0,o.cX)()&&e.altKey||e.ctrlKey||"Control"===e.key||"Shift"===e.key||"Meta"===e.key||(l="keyboard",g("keyboard",e))}function m(e){l="pointer",("mousedown"===e.type||"pointerdown"===e.type)&&(c=!0,g("pointer",e))}function b(e){(0,a.Y)(e)&&(c=!0,l="virtual")}function h(e){e.target!==window&&e.target!==document&&!n.lR&&e.isTrusted&&(c||p||(l="virtual",g("virtual",e)),c=!1,p=!1)}function y(){n.lR||(c=!1,p=!0)}function w(e){if("undefined"==typeof window||u.get((0,i.mD)(e)))return;let t=(0,i.mD)(e),r=(0,i.TW)(e),n=t.HTMLElement.prototype.focus;t.HTMLElement.prototype.focus=function(){c=!0,n.apply(this,arguments)},r.addEventListener("keydown",v,!0),r.addEventListener("keyup",v,!0),r.addEventListener("click",b,!0),t.addEventListener("focus",h,!0),t.addEventListener("blur",y,!1),"undefined"!=typeof PointerEvent?(r.addEventListener("pointerdown",m,!0),r.addEventListener("pointermove",m,!0),r.addEventListener("pointerup",m,!0)):(r.addEventListener("mousedown",m,!0),r.addEventListener("mousemove",m,!0),r.addEventListener("mouseup",m,!0)),t.addEventListener("beforeunload",()=>{x(e)},{once:!0}),u.set(t,{focus:n})}let x=(e,t)=>{let r=(0,i.mD)(e),n=(0,i.TW)(e);t&&n.removeEventListener("DOMContentLoaded",t),u.has(r)&&(r.HTMLElement.prototype.focus=u.get(r).focus,n.removeEventListener("keydown",v,!0),n.removeEventListener("keyup",v,!0),n.removeEventListener("click",b,!0),r.removeEventListener("focus",h,!0),r.removeEventListener("blur",y,!1),"undefined"!=typeof PointerEvent?(n.removeEventListener("pointerdown",m,!0),n.removeEventListener("pointermove",m,!0),n.removeEventListener("pointerup",m,!0)):(n.removeEventListener("mousedown",m,!0),n.removeEventListener("mousemove",m,!0),n.removeEventListener("mouseup",m,!0)),u.delete(r))};function E(){return"pointer"!==l}function T(){return l}function k(e){l=e,g(e,null)}"undefined"!=typeof document&&function(e){let t;let r=(0,i.TW)(void 0);"loading"!==r.readyState?w(void 0):(t=()=>{w(void 0)},r.addEventListener("DOMContentLoaded",t)),()=>x(e,t)}();let P=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function S(e,t,r){w(),(0,s.useEffect)(()=>{let t=(t,n)=>{(function(e,t,r){let n=(0,i.TW)(null==r?void 0:r.target),o="undefined"!=typeof window?(0,i.mD)(null==r?void 0:r.target).HTMLInputElement:HTMLInputElement,a="undefined"!=typeof window?(0,i.mD)(null==r?void 0:r.target).HTMLTextAreaElement:HTMLTextAreaElement,s="undefined"!=typeof window?(0,i.mD)(null==r?void 0:r.target).HTMLElement:HTMLElement,l="undefined"!=typeof window?(0,i.mD)(null==r?void 0:r.target).KeyboardEvent:KeyboardEvent;return!((e=e||n.activeElement instanceof o&&!P.has(n.activeElement.type)||n.activeElement instanceof a||n.activeElement instanceof s&&n.activeElement.isContentEditable)&&"keyboard"===t&&r instanceof l&&!f[r.key])})(!!(null==r?void 0:r.isTextInput),t,n)&&e(E())};return d.add(t),()=>{d.delete(t)}},t)}},73235:(e,t,r)=>{r.d(t,{R:()=>l});var n=r(22454),o=r(41473),a=r(43353),i=r(27241),s=r(31901);function l(e){let{isDisabled:t,onBlurWithin:r,onFocusWithin:l,onFocusWithinChange:d}=e,u=(0,o.useRef)({isFocusWithin:!1}),{addGlobalListener:c,removeAllGlobalListeners:p}=(0,a.A)(),f=(0,o.useCallback)(e=>{e.currentTarget.contains(e.target)&&u.current.isFocusWithin&&!e.currentTarget.contains(e.relatedTarget)&&(u.current.isFocusWithin=!1,p(),r&&r(e),d&&d(!1))},[r,d,u,p]),g=(0,n.yB)(f),v=(0,o.useCallback)(e=>{if(!e.currentTarget.contains(e.target))return;let t=(0,i.TW)(e.target),r=(0,s.bq)(t);if(!u.current.isFocusWithin&&r===(0,s.wt)(e.nativeEvent)){l&&l(e),d&&d(!0),u.current.isFocusWithin=!0,g(e);let r=e.currentTarget;c(t,"focus",e=>{if(u.current.isFocusWithin&&!(0,s.sD)(r,e.target)){let o=new n.KU("blur",new t.defaultView.FocusEvent("blur",{relatedTarget:e.target}));o.target=r,o.currentTarget=r,f(o)}},{capture:!0})}},[l,d,g,c,f]);return t?{focusWithinProps:{onFocus:void 0,onBlur:void 0}}:{focusWithinProps:{onFocus:v,onBlur:f}}}},47734:(e,t,r)=>{r.d(t,{Wc:()=>u});var n=r(79119),o=r(88450),a=r(63591),i=r(80809),s=r(44700),l=r(41473);let d=l.createContext(null);function u(e,t){let{focusProps:r}=(0,o.i)(e),{keyboardProps:u}=(0,a.d)(e),c=(0,s.v)(r,u),p=function(e){let t=(0,l.useContext)(d)||{};(0,i.w)(t,e);let{ref:r,...n}=t;return n}(t),f=e.isDisabled?{}:p,g=(0,l.useRef)(e.autoFocus);(0,l.useEffect)(()=>{g.current&&t.current&&(0,n.l)(t.current),g.current=!1},[t]);let v=e.excludeFromTabOrder?-1:0;return e.isDisabled&&(v=void 0),{focusableProps:(0,s.v)({...c,tabIndex:v},f)}}},84084:(e,t,r)=>{r.d(t,{M:()=>p});var n=r(43353),o=r(27241),a=r(31901),i=r(41473);let s=!1,l=0;function d(){s=!0,setTimeout(()=>{s=!1},50)}function u(e){"touch"===e.pointerType&&d()}function c(){if("undefined"!=typeof document)return"undefined"!=typeof PointerEvent?document.addEventListener("pointerup",u):document.addEventListener("touchend",d),l++,()=>{--l>0||("undefined"!=typeof PointerEvent?document.removeEventListener("pointerup",u):document.removeEventListener("touchend",d))}}function p(e){let{onHoverStart:t,onHoverChange:r,onHoverEnd:l,isDisabled:d}=e,[u,p]=(0,i.useState)(!1),f=(0,i.useRef)({isHovered:!1,ignoreEmulatedMouseEvents:!1,pointerType:"",target:null}).current;(0,i.useEffect)(c,[]);let{addGlobalListener:g,removeAllGlobalListeners:v}=(0,n.A)(),{hoverProps:m,triggerHoverEnd:b}=(0,i.useMemo)(()=>{let e=(e,i)=>{if(f.pointerType=i,d||"touch"===i||f.isHovered||!e.currentTarget.contains(e.target))return;f.isHovered=!0;let s=e.currentTarget;f.target=s,g((0,o.TW)(e.target),"pointerover",e=>{f.isHovered&&f.target&&!(0,a.sD)(f.target,e.target)&&n(e,e.pointerType)},{capture:!0}),t&&t({type:"hoverstart",target:s,pointerType:i}),r&&r(!0),p(!0)},n=(e,t)=>{let n=f.target;f.pointerType="",f.target=null,"touch"!==t&&f.isHovered&&n&&(f.isHovered=!1,v(),l&&l({type:"hoverend",target:n,pointerType:t}),r&&r(!1),p(!1))},i={};return"undefined"!=typeof PointerEvent?(i.onPointerEnter=t=>{s&&"mouse"===t.pointerType||e(t,t.pointerType)},i.onPointerLeave=e=>{!d&&e.currentTarget.contains(e.target)&&n(e,e.pointerType)}):(i.onTouchStart=()=>{f.ignoreEmulatedMouseEvents=!0},i.onMouseEnter=t=>{f.ignoreEmulatedMouseEvents||s||e(t,"mouse"),f.ignoreEmulatedMouseEvents=!1},i.onMouseLeave=e=>{!d&&e.currentTarget.contains(e.target)&&n(e,"mouse")}),{hoverProps:i,triggerHoverEnd:n}},[t,r,l,d,f,g,v]);return(0,i.useEffect)(()=>{d&&b({currentTarget:f.target},f.pointerType)},[d]),{hoverProps:m,isHovered:u}}},63591:(e,t,r)=>{function n(e){if(!e)return;let t=!0;return r=>{e({...r,preventDefault(){r.preventDefault()},isDefaultPrevented:()=>r.isDefaultPrevented(),stopPropagation(){t?console.error("stopPropagation is now the default behavior for events in React Spectrum. You can use continuePropagation() to revert this behavior."):t=!0},continuePropagation(){t=!1},isPropagationStopped:()=>t}),t&&r.stopPropagation()}}function o(e){return{keyboardProps:e.isDisabled?{}:{onKeyDown:n(e.onKeyDown),onKeyUp:n(e.onKeyUp)}}}r.d(t,{d:()=>o})},30631:(e,t,r)=>{r.d(t,{d:()=>P});var n=r(36992),o=r(41473);let a=o.createContext({register:()=>{}});a.displayName="PressResponderContext";var i=r(22454),s=r(33992),l=r(49148),d=r(31178),u=r(44700),c=r(80809),p=r(43353),f=r(82476),g=r(31901),v=r(27241),m=r(84843),b=r(13239),h=r(73290),y=r(96569),w=r(46353),x=r(37988),E=new WeakMap;class T{continuePropagation(){(0,d._)(this,E,!1)}get shouldStopPropagation(){return(0,s._)(this,E)}constructor(e,t,r,n){var o;(0,l._)(this,E,{writable:!0,value:void 0}),(0,d._)(this,E,!0);let a=null!==(o=null==n?void 0:n.target)&&void 0!==o?o:r.currentTarget,i=null==a?void 0:a.getBoundingClientRect(),s,u=0,c,p=null;null!=r.clientX&&null!=r.clientY&&(c=r.clientX,p=r.clientY),i&&(null!=c&&null!=p?(s=c-i.left,u=p-i.top):(s=i.width/2,u=i.height/2)),this.type=e,this.pointerType=t,this.target=r.currentTarget,this.shiftKey=r.shiftKey,this.metaKey=r.metaKey,this.ctrlKey=r.ctrlKey,this.altKey=r.altKey,this.x=s,this.y=u}}let k=Symbol("linkClicked");function P(e){let{onPress:t,onPressChange:r,onPressStart:s,onPressEnd:l,onPressUp:d,isDisabled:E,isPressed:P,preventFocusOnPress:D,shouldCancelOnPointerExit:O,allowTextSelectionOnPress:I,ref:N,...W}=function(e){let t=(0,o.useContext)(a);if(t){let{register:r,...n}=t;e=(0,u.v)(n,e),r()}return(0,c.w)(t,e.ref),e}(e),[K,R]=(0,o.useState)(!1),H=(0,o.useRef)({isPressed:!1,ignoreEmulatedMouseEvents:!1,didFirePressStart:!1,isTriggeringEvent:!1,activePointerId:null,target:null,isOverTarget:!1,pointerType:null,disposables:[]}),{addGlobalListener:F,removeAllGlobalListeners:B}=(0,p.A)(),G=(0,f.J)((e,t)=>{let n=H.current;if(E||n.didFirePressStart)return!1;let o=!0;if(n.isTriggeringEvent=!0,s){let r=new T("pressstart",t,e);s(r),o=r.shouldStopPropagation}return r&&r(!0),n.isTriggeringEvent=!1,n.didFirePressStart=!0,R(!0),o}),$=(0,f.J)((e,n,o=!0)=>{let a=H.current;if(!a.didFirePressStart)return!1;a.didFirePressStart=!1,a.isTriggeringEvent=!0;let i=!0;if(l){let t=new T("pressend",n,e);l(t),i=t.shouldStopPropagation}if(r&&r(!1),R(!1),t&&o&&!E){let r=new T("press",n,e);t(r),i&&(i=r.shouldStopPropagation)}return a.isTriggeringEvent=!1,i}),V=(0,f.J)((e,t)=>{let r=H.current;if(E)return!1;if(d){r.isTriggeringEvent=!0;let n=new T("pressup",t,e);return d(n),r.isTriggeringEvent=!1,n.shouldStopPropagation}return!0}),U=(0,f.J)(e=>{let t=H.current;if(t.isPressed&&t.target){for(let r of(t.didFirePressStart&&null!=t.pointerType&&$(A(t.target,e),t.pointerType,!1),t.isPressed=!1,t.isOverTarget=!1,t.activePointerId=null,t.pointerType=null,B(),I||(0,n.E)(t.target),t.disposables))r();t.disposables=[]}}),_=(0,f.J)(e=>{O&&U(e)}),Y=(0,o.useMemo)(()=>{let e=H.current,t={onKeyDown(t){if(M(t.nativeEvent,t.currentTarget)&&(0,g.sD)(t.currentTarget,(0,g.wt)(t.nativeEvent))){var n;z((0,g.wt)(t.nativeEvent),t.key)&&t.preventDefault();let o=!0;if(!e.isPressed&&!t.repeat){e.target=t.currentTarget,e.isPressed=!0,e.pointerType="keyboard",o=G(t,"keyboard");let n=t.currentTarget;F((0,v.TW)(t.currentTarget),"keyup",(0,m.c)(t=>{M(t,n)&&!t.repeat&&(0,g.sD)(n,(0,g.wt)(t))&&e.target&&V(A(e.target,t),"keyboard")},r),!0)}o&&t.stopPropagation(),t.metaKey&&(0,b.cX)()&&(null===(n=e.metaKeyEvents)||void 0===n||n.set(t.key,t.nativeEvent))}else"Meta"===t.key&&(e.metaKeyEvents=new Map)},onClick(t){if((!t||(0,g.sD)(t.currentTarget,(0,g.wt)(t.nativeEvent)))&&t&&0===t.button&&!e.isTriggeringEvent&&!h.Fe.isOpening){let r=!0;if(E&&t.preventDefault(),!e.ignoreEmulatedMouseEvents&&!e.isPressed&&("virtual"===e.pointerType||(0,y.Y)(t.nativeEvent))){let e=G(t,"virtual"),n=V(t,"virtual"),o=$(t,"virtual");r=e&&n&&o}else if(e.isPressed&&"keyboard"!==e.pointerType){let n=e.pointerType||t.nativeEvent.pointerType||"virtual";r=$(A(t.currentTarget,t),n,!0),e.isOverTarget=!1,U(t)}e.ignoreEmulatedMouseEvents=!1,r&&t.stopPropagation()}}},r=t=>{var r,n,o;if(e.isPressed&&e.target&&M(t,e.target)){z((0,g.wt)(t),t.key)&&t.preventDefault();let r=(0,g.wt)(t);$(A(e.target,t),"keyboard",(0,g.sD)(e.target,(0,g.wt)(t))),B(),"Enter"!==t.key&&S(e.target)&&(0,g.sD)(e.target,r)&&!t[k]&&(t[k]=!0,(0,h.Fe)(e.target,t,!1)),e.isPressed=!1,null===(n=e.metaKeyEvents)||void 0===n||n.delete(t.key)}else if("Meta"===t.key&&(null===(r=e.metaKeyEvents)||void 0===r?void 0:r.size)){let t=e.metaKeyEvents;for(let r of(e.metaKeyEvents=void 0,t.values()))null===(o=e.target)||void 0===o||o.dispatchEvent(new KeyboardEvent("keyup",r))}};if("undefined"!=typeof PointerEvent){t.onPointerDown=t=>{if(0!==t.button||!(0,g.sD)(t.currentTarget,(0,g.wt)(t.nativeEvent)))return;if((0,y.P)(t.nativeEvent)){e.pointerType="virtual";return}e.pointerType=t.pointerType;let a=!0;if(!e.isPressed){e.isPressed=!0,e.isOverTarget=!0,e.activePointerId=t.pointerId,e.target=t.currentTarget,I||(0,n.M)(e.target),a=G(t,e.pointerType);let i=(0,g.wt)(t.nativeEvent);"releasePointerCapture"in i&&i.releasePointerCapture(t.pointerId),F((0,v.TW)(t.currentTarget),"pointerup",r,!1),F((0,v.TW)(t.currentTarget),"pointercancel",o,!1)}a&&t.stopPropagation()},t.onMouseDown=t=>{if((0,g.sD)(t.currentTarget,(0,g.wt)(t.nativeEvent))&&0===t.button){if(D){let r=(0,i.LE)(t.target);r&&e.disposables.push(r)}t.stopPropagation()}},t.onPointerUp=t=>{(0,g.sD)(t.currentTarget,(0,g.wt)(t.nativeEvent))&&"virtual"!==e.pointerType&&0===t.button&&V(t,e.pointerType||t.pointerType)},t.onPointerEnter=t=>{t.pointerId===e.activePointerId&&e.target&&!e.isOverTarget&&null!=e.pointerType&&(e.isOverTarget=!0,G(A(e.target,t),e.pointerType))},t.onPointerLeave=t=>{t.pointerId===e.activePointerId&&e.target&&e.isOverTarget&&null!=e.pointerType&&(e.isOverTarget=!1,$(A(e.target,t),e.pointerType,!1),_(t))};let r=t=>{if(t.pointerId===e.activePointerId&&e.isPressed&&0===t.button&&e.target){if((0,g.sD)(e.target,(0,g.wt)(t))&&null!=e.pointerType){let r=!1,n=setTimeout(()=>{e.isPressed&&e.target instanceof HTMLElement&&(r?U(t):((0,w.e)(e.target),e.target.click()))},80);F(t.currentTarget,"click",()=>r=!0,!0),e.disposables.push(()=>clearTimeout(n))}else U(t);e.isOverTarget=!1}},o=e=>{U(e)};t.onDragStart=e=>{(0,g.sD)(e.currentTarget,(0,g.wt)(e.nativeEvent))&&U(e)}}else{t.onMouseDown=t=>{if(0===t.button&&(0,g.sD)(t.currentTarget,(0,g.wt)(t.nativeEvent))){if(e.ignoreEmulatedMouseEvents){t.stopPropagation();return}if(e.isPressed=!0,e.isOverTarget=!0,e.target=t.currentTarget,e.pointerType=(0,y.Y)(t.nativeEvent)?"virtual":"mouse",(0,x.flushSync)(()=>G(t,e.pointerType))&&t.stopPropagation(),D){let r=(0,i.LE)(t.target);r&&e.disposables.push(r)}F((0,v.TW)(t.currentTarget),"mouseup",r,!1)}},t.onMouseEnter=t=>{if(!(0,g.sD)(t.currentTarget,(0,g.wt)(t.nativeEvent)))return;let r=!0;e.isPressed&&!e.ignoreEmulatedMouseEvents&&null!=e.pointerType&&(e.isOverTarget=!0,r=G(t,e.pointerType)),r&&t.stopPropagation()},t.onMouseLeave=t=>{if(!(0,g.sD)(t.currentTarget,(0,g.wt)(t.nativeEvent)))return;let r=!0;e.isPressed&&!e.ignoreEmulatedMouseEvents&&null!=e.pointerType&&(e.isOverTarget=!1,r=$(t,e.pointerType,!1),_(t)),r&&t.stopPropagation()},t.onMouseUp=t=>{(0,g.sD)(t.currentTarget,(0,g.wt)(t.nativeEvent))&&!e.ignoreEmulatedMouseEvents&&0===t.button&&V(t,e.pointerType||"mouse")};let r=t=>{if(0===t.button){if(e.ignoreEmulatedMouseEvents){e.ignoreEmulatedMouseEvents=!1;return}e.target&&e.target.contains(t.target)&&null!=e.pointerType||U(t),e.isOverTarget=!1}};t.onTouchStart=t=>{if(!(0,g.sD)(t.currentTarget,(0,g.wt)(t.nativeEvent)))return;let r=function(e){let{targetTouches:t}=e;return t.length>0?t[0]:null}(t.nativeEvent);r&&(e.activePointerId=r.identifier,e.ignoreEmulatedMouseEvents=!0,e.isOverTarget=!0,e.isPressed=!0,e.target=t.currentTarget,e.pointerType="touch",I||(0,n.M)(e.target),G(L(e.target,t),e.pointerType)&&t.stopPropagation(),F((0,v.mD)(t.currentTarget),"scroll",o,!0))},t.onTouchMove=t=>{if(!(0,g.sD)(t.currentTarget,(0,g.wt)(t.nativeEvent)))return;if(!e.isPressed){t.stopPropagation();return}let r=C(t.nativeEvent,e.activePointerId),n=!0;r&&j(r,t.currentTarget)?e.isOverTarget||null==e.pointerType||(e.isOverTarget=!0,n=G(L(e.target,t),e.pointerType)):e.isOverTarget&&null!=e.pointerType&&(e.isOverTarget=!1,n=$(L(e.target,t),e.pointerType,!1),_(L(e.target,t))),n&&t.stopPropagation()},t.onTouchEnd=t=>{if(!(0,g.sD)(t.currentTarget,(0,g.wt)(t.nativeEvent)))return;if(!e.isPressed){t.stopPropagation();return}let r=C(t.nativeEvent,e.activePointerId),o=!0;r&&j(r,t.currentTarget)&&null!=e.pointerType?(V(L(e.target,t),e.pointerType),o=$(L(e.target,t),e.pointerType)):e.isOverTarget&&null!=e.pointerType&&(o=$(L(e.target,t),e.pointerType,!1)),o&&t.stopPropagation(),e.isPressed=!1,e.activePointerId=null,e.isOverTarget=!1,e.ignoreEmulatedMouseEvents=!0,e.target&&!I&&(0,n.E)(e.target),B()},t.onTouchCancel=t=>{(0,g.sD)(t.currentTarget,(0,g.wt)(t.nativeEvent))&&(t.stopPropagation(),e.isPressed&&U(L(e.target,t)))};let o=t=>{e.isPressed&&(0,g.sD)((0,g.wt)(t),e.target)&&U({currentTarget:e.target,shiftKey:!1,ctrlKey:!1,metaKey:!1,altKey:!1})};t.onDragStart=e=>{(0,g.sD)(e.currentTarget,(0,g.wt)(e.nativeEvent))&&U(e)}}return t},[F,E,D,B,I,U,_,$,G,V]);return(0,o.useEffect)(()=>{let e=H.current;return()=>{var t;for(let r of(I||(0,n.E)(null!==(t=e.target)&&void 0!==t?t:void 0),e.disposables))r();e.disposables=[]}},[I]),{isPressed:P||K,pressProps:(0,u.v)(W,Y)}}function S(e){return"A"===e.tagName&&e.hasAttribute("href")}function M(e,t){let{key:r,code:n}=e,o=t.getAttribute("role");return("Enter"===r||" "===r||"Spacebar"===r||"Space"===n)&&!(t instanceof(0,v.mD)(t).HTMLInputElement&&!O(t,r)||t instanceof(0,v.mD)(t).HTMLTextAreaElement||t.isContentEditable)&&!(("link"===o||!o&&S(t))&&"Enter"!==r)}function C(e,t){let r=e.changedTouches;for(let e=0;e<r.length;e++){let n=r[e];if(n.identifier===t)return n}return null}function L(e,t){let r=0,n=0;return t.targetTouches&&1===t.targetTouches.length&&(r=t.targetTouches[0].clientX,n=t.targetTouches[0].clientY),{currentTarget:e,shiftKey:t.shiftKey,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,clientX:r,clientY:n}}function A(e,t){let r=t.clientX,n=t.clientY;return{currentTarget:e,shiftKey:t.shiftKey,ctrlKey:t.ctrlKey,metaKey:t.metaKey,altKey:t.altKey,clientX:r,clientY:n}}function j(e,t){let r,n,o=t.getBoundingClientRect(),a=(r=0,n=0,void 0!==e.width?r=e.width/2:void 0!==e.radiusX&&(r=e.radiusX),void 0!==e.height?n=e.height/2:void 0!==e.radiusY&&(n=e.radiusY),{top:e.clientY-n,right:e.clientX+r,bottom:e.clientY+n,left:e.clientX-r});return!(o.left>a.right)&&!(a.left>o.right)&&!(o.top>a.bottom)&&!(a.top>o.bottom)}function z(e,t){return e instanceof HTMLInputElement?!O(e,t):!(e instanceof HTMLInputElement)&&(e instanceof HTMLButtonElement?"submit"!==e.type&&"reset"!==e.type:!S(e))}let D=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]);function O(e,t){return"checkbox"===e.type||"radio"===e.type?" "===t:D.has(e.type)}},22454:(e,t,r)=>{r.d(t,{KU:()=>d,LE:()=>p,lR:()=>c,yB:()=>u});var n=r(9828),o=r(82476),a=r(50800),i=r(27241),s=r(46353),l=r(41473);class d{isDefaultPrevented(){return this.nativeEvent.defaultPrevented}preventDefault(){this.defaultPrevented=!0,this.nativeEvent.preventDefault()}stopPropagation(){this.nativeEvent.stopPropagation(),this.isPropagationStopped=()=>!0}isPropagationStopped(){return!1}persist(){}constructor(e,t){this.nativeEvent=t,this.target=t.target,this.currentTarget=t.currentTarget,this.relatedTarget=t.relatedTarget,this.bubbles=t.bubbles,this.cancelable=t.cancelable,this.defaultPrevented=t.defaultPrevented,this.eventPhase=t.eventPhase,this.isTrusted=t.isTrusted,this.timeStamp=t.timeStamp,this.type=e}}function u(e){let t=(0,l.useRef)({isFocused:!1,observer:null});(0,n.N)(()=>{let e=t.current;return()=>{e.observer&&(e.observer.disconnect(),e.observer=null)}},[]);let r=(0,o.J)(t=>{null==e||e(t)});return(0,l.useCallback)(e=>{if(e.target instanceof HTMLButtonElement||e.target instanceof HTMLInputElement||e.target instanceof HTMLTextAreaElement||e.target instanceof HTMLSelectElement){t.current.isFocused=!0;let n=e.target;n.addEventListener("focusout",e=>{t.current.isFocused=!1,n.disabled&&r(new d("blur",e)),t.current.observer&&(t.current.observer.disconnect(),t.current.observer=null)},{once:!0}),t.current.observer=new MutationObserver(()=>{if(t.current.isFocused&&n.disabled){var e;null===(e=t.current.observer)||void 0===e||e.disconnect();let r=n===document.activeElement?null:document.activeElement;n.dispatchEvent(new FocusEvent("blur",{relatedTarget:r})),n.dispatchEvent(new FocusEvent("focusout",{bubbles:!0,relatedTarget:r}))}}),t.current.observer.observe(n,{attributes:!0,attributeFilter:["disabled"]})}},[r])}let c=!1;function p(e){for(;e&&!(0,a.t)(e);)e=e.parentElement;let t=(0,i.mD)(e),r=t.document.activeElement;if(!r||r===e)return;c=!0;let n=!1,o=e=>{(e.target===r||n)&&e.stopImmediatePropagation()},l=t=>{t.target!==r&&!n||(t.stopImmediatePropagation(),e||n||(n=!0,(0,s.e)(r),p()))},d=t=>{(t.target===e||n)&&t.stopImmediatePropagation()},u=t=>{(t.target===e||n)&&(t.stopImmediatePropagation(),n||(n=!0,(0,s.e)(r),p()))};t.addEventListener("blur",o,!0),t.addEventListener("focusout",l,!0),t.addEventListener("focusin",u,!0),t.addEventListener("focus",d,!0);let p=()=>{cancelAnimationFrame(f),t.removeEventListener("blur",o,!0),t.removeEventListener("focusout",l,!0),t.removeEventListener("focusin",u,!0),t.removeEventListener("focus",d,!0),c=!1,n=!1},f=requestAnimationFrame(p);return p}},51034:(e,t,r)=>{r.d(t,{Cc:()=>d,wR:()=>f});var n=r(41473);let o={prefix:String(Math.round(1e10*Math.random())),current:0},a=n.createContext(o),i=n.createContext(!1),s=!!("undefined"!=typeof window&&window.document&&window.document.createElement),l=new WeakMap,d="function"==typeof n.useId?function(e){let t=n.useId(),[r]=(0,n.useState)(f()),a=r?"react-aria":`react-aria${o.prefix}`;return e||`${a}-${t}`}:function(e){let t=(0,n.useContext)(a);t!==o||s||console.warn("When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.");let r=function(e=!1){let t=(0,n.useContext)(a),r=(0,n.useRef)(null);if(null===r.current&&!e){var o,i;let e=null===(i=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED)||void 0===i?void 0:null===(o=i.ReactCurrentOwner)||void 0===o?void 0:o.current;if(e){let r=l.get(e);null==r?l.set(e,{id:t.current,state:e.memoizedState}):e.memoizedState!==r.state&&(t.current=r.id,l.delete(e))}r.current=++t.current}return r.current}(!!e),i=`react-aria${t.prefix}`;return e||`${i}-${r}`};function u(){return!1}function c(){return!0}function p(e){return()=>{}}function f(){return"function"==typeof n.useSyncExternalStore?n.useSyncExternalStore(p,u,c):(0,n.useContext)(i)}},31901:(e,t,r)=>{r.d(t,{bq:()=>i,sD:()=>a,wt:()=>s});var n=r(27241),o=r(88970);function a(e,t){if(!(0,o.Nf)())return!!t&&!!e&&e.contains(t);if(!e||!t)return!1;let r=t;for(;null!==r;){if(r===e)return!0;r="SLOT"===r.tagName&&r.assignedSlot?r.assignedSlot.parentNode:(0,n.Ng)(r)?r.host:r.parentNode}return!1}let i=(e=document)=>{var t;if(!(0,o.Nf)())return e.activeElement;let r=e.activeElement;for(;r&&"shadowRoot"in r&&(null===(t=r.shadowRoot)||void 0===t?void 0:t.activeElement);)r=r.shadowRoot.activeElement;return r};function s(e){return(0,o.Nf)()&&e.target.shadowRoot&&e.composedPath?e.composedPath()[0]:e.target}},84843:(e,t,r)=>{r.d(t,{c:()=>n});function n(...e){return(...t)=>{for(let r of e)"function"==typeof r&&r(...t)}}},27241:(e,t,r)=>{r.d(t,{Ng:()=>a,TW:()=>n,mD:()=>o});let n=e=>{var t;return null!==(t=null==e?void 0:e.ownerDocument)&&void 0!==t?t:document},o=e=>e&&"window"in e&&e.window===e?e:n(e).defaultView||window;function a(e){return null!==e&&"object"==typeof e&&"nodeType"in e&&"number"==typeof e.nodeType&&e.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&"host"in e}},24102:(e,t,r)=>{r.d(t,{$:()=>s});let n=new Set(["id"]),o=new Set(["aria-label","aria-labelledby","aria-describedby","aria-details"]),a=new Set(["href","hrefLang","target","rel","download","ping","referrerPolicy"]),i=/^(data-.*)$/;function s(e,t={}){let{labelable:r,isLink:l,propNames:d}=t,u={};for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n.has(t)||r&&o.has(t)||l&&a.has(t)||(null==d?void 0:d.has(t))||i.test(t))&&(u[t]=e[t]);return u}},46353:(e,t,r)=>{function n(e){if(function(){if(null==o){o=!1;try{document.createElement("div").focus({get preventScroll(){return o=!0,!0}})}catch{}}return o}())e.focus({preventScroll:!0});else{let t=function(e){let t=e.parentNode,r=[],n=document.scrollingElement||document.documentElement;for(;t instanceof HTMLElement&&t!==n;)(t.offsetHeight<t.scrollHeight||t.offsetWidth<t.scrollWidth)&&r.push({element:t,scrollTop:t.scrollTop,scrollLeft:t.scrollLeft}),t=t.parentNode;return n instanceof HTMLElement&&r.push({element:n,scrollTop:n.scrollTop,scrollLeft:n.scrollLeft}),r}(e);e.focus(),function(e){for(let{element:t,scrollTop:r,scrollLeft:n}of e)t.scrollTop=r,t.scrollLeft=n}(t)}}r.d(t,{e:()=>n});let o=null},50800:(e,t,r)=>{r.d(t,{A:()=>s,t:()=>i});let n=["input:not([disabled]):not([type=hidden])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","a[href]","area[href]","summary","iframe","object","embed","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable^="false"])'],o=n.join(":not([hidden]),")+",[tabindex]:not([disabled]):not([hidden])";n.push('[tabindex]:not([tabindex="-1"]):not([disabled])');let a=n.join(':not([hidden]):not([tabindex="-1"]),');function i(e){return e.matches(o)}function s(e){return e.matches(a)}},96569:(e,t,r)=>{r.d(t,{P:()=>a,Y:()=>o});var n=r(13239);function o(e){return 0===e.mozInputSource&&!!e.isTrusted||((0,n.m0)()&&e.pointerType?"click"===e.type&&1===e.buttons:0===e.detail&&!e.pointerType)}function a(e){return!(0,n.m0)()&&0===e.width&&0===e.height||1===e.width&&1===e.height&&0===e.pressure&&0===e.detail&&"mouse"===e.pointerType}},44700:(e,t,r)=>{r.d(t,{v:()=>i});var n=r(84843),o=r(71424),a=r(84269);function i(...e){let t={...e[0]};for(let r=1;r<e.length;r++){let i=e[r];for(let e in i){let r=t[e],s=i[e];"function"==typeof r&&"function"==typeof s&&"o"===e[0]&&"n"===e[1]&&e.charCodeAt(2)>=65&&90>=e.charCodeAt(2)?t[e]=(0,n.c)(r,s):("className"===e||"UNSAFE_className"===e)&&"string"==typeof r&&"string"==typeof s?t[e]=(0,a.A)(r,s):"id"===e&&r&&s?t.id=(0,o.Tw)(r,s):t[e]=void 0!==s?s:r}}return t}},73290:(e,t,r)=>{r.d(t,{Fe:()=>u,_h:()=>p,pg:()=>s,rd:()=>l,sU:()=>d});var n=r(46353),o=r(13239),a=r(41473);let i=(0,a.createContext)({isNative:!0,open:function(e,t){c(e,e=>u(e,t))},useHref:e=>e});function s(e){let{children:t,navigate:r,useHref:n}=e,o=(0,a.useMemo)(()=>({isNative:!1,open:(e,t,n,o)=>{c(e,e=>{d(e,t)?r(n,o):u(e,t)})},useHref:n||(e=>e)}),[r,n]);return a.createElement(i.Provider,{value:o},t)}function l(){return(0,a.useContext)(i)}function d(e,t){let r=e.getAttribute("target");return(!r||"_self"===r)&&e.origin===location.origin&&!e.hasAttribute("download")&&!t.metaKey&&!t.ctrlKey&&!t.altKey&&!t.shiftKey}function u(e,t,r=!0){var a,i;let{metaKey:s,ctrlKey:l,altKey:d,shiftKey:c}=t;(0,o.gm)()&&(null===(i=window.event)||void 0===i?void 0:null===(a=i.type)||void 0===a?void 0:a.startsWith("key"))&&"_blank"===e.target&&((0,o.cX)()?s=!0:l=!0);let p=(0,o.Tc)()&&(0,o.cX)()&&!(0,o.bh)()?new KeyboardEvent("keydown",{keyIdentifier:"Enter",metaKey:s,ctrlKey:l,altKey:d,shiftKey:c}):new MouseEvent("click",{metaKey:s,ctrlKey:l,altKey:d,shiftKey:c,bubbles:!0,cancelable:!0});u.isOpening=r,(0,n.e)(e),e.dispatchEvent(p),u.isOpening=!1}function c(e,t){if(e instanceof HTMLAnchorElement)t(e);else if(e.hasAttribute("data-href")){let r=document.createElement("a");r.href=e.getAttribute("data-href"),e.hasAttribute("data-target")&&(r.target=e.getAttribute("data-target")),e.hasAttribute("data-rel")&&(r.rel=e.getAttribute("data-rel")),e.hasAttribute("data-download")&&(r.download=e.getAttribute("data-download")),e.hasAttribute("data-ping")&&(r.ping=e.getAttribute("data-ping")),e.hasAttribute("data-referrer-policy")&&(r.referrerPolicy=e.getAttribute("data-referrer-policy")),e.appendChild(r),t(r),e.removeChild(r)}}function p(e){var t;let r=l().useHref(null!==(t=null==e?void 0:e.href)&&void 0!==t?t:"");return{href:(null==e?void 0:e.href)?r:void 0,target:null==e?void 0:e.target,rel:null==e?void 0:e.rel,download:null==e?void 0:e.download,ping:null==e?void 0:e.ping,referrerPolicy:null==e?void 0:e.referrerPolicy}}u.isOpening=!1},13239:(e,t,r)=>{function n(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&((null===(t=window.navigator.userAgentData)||void 0===t?void 0:t.brands.some(t=>e.test(t.brand)))||e.test(window.navigator.userAgent))}function o(e){var t;return"undefined"!=typeof window&&null!=window.navigator&&e.test((null===(t=window.navigator.userAgentData)||void 0===t?void 0:t.platform)||window.navigator.platform)}function a(e){let t=null;return()=>(null==t&&(t=e()),t)}r.d(t,{H8:()=>p,Tc:()=>c,bh:()=>l,cX:()=>i,gm:()=>g,lg:()=>u,m0:()=>f,un:()=>d});let i=a(function(){return o(/^Mac/i)}),s=a(function(){return o(/^iPhone/i)}),l=a(function(){return o(/^iPad/i)||i()&&navigator.maxTouchPoints>1}),d=a(function(){return s()||l()}),u=a(function(){return i()||d()}),c=a(function(){return n(/AppleWebKit/i)&&!p()}),p=a(function(){return n(/Chrome/i)}),f=a(function(){return n(/Android/i)}),g=a(function(){return n(/Firefox/i)})},83460:(e,t,r)=>{r.d(t,{v:()=>i});let n=new Map,o=new Set;function a(){if("undefined"==typeof window)return;function e(e){return"propertyName"in e}let t=r=>{if(!e(r)||!r.target)return;let a=n.get(r.target);if(a&&(a.delete(r.propertyName),0===a.size&&(r.target.removeEventListener("transitioncancel",t),n.delete(r.target)),0===n.size)){for(let e of o)e();o.clear()}};document.body.addEventListener("transitionrun",r=>{if(!e(r)||!r.target)return;let o=n.get(r.target);o||(o=new Set,n.set(r.target,o),r.target.addEventListener("transitioncancel",t,{once:!0})),o.add(r.propertyName)}),document.body.addEventListener("transitionend",t)}function i(e){requestAnimationFrame(()=>{0===n.size?e():o.add(e)})}"undefined"!=typeof document&&("loading"!==document.readyState?a():document.addEventListener("DOMContentLoaded",a))},82476:(e,t,r)=>{r.d(t,{J:()=>a});var n=r(9828),o=r(41473);function a(e){let t=(0,o.useRef)(null);return(0,n.N)(()=>{t.current=e},[e]),(0,o.useCallback)((...e)=>{let r=t.current;return null==r?void 0:r(...e)},[])}},43353:(e,t,r)=>{r.d(t,{A:()=>o});var n=r(41473);function o(){let e=(0,n.useRef)(new Map),t=(0,n.useCallback)((t,r,n,o)=>{let a=(null==o?void 0:o.once)?(...t)=>{e.current.delete(n),n(...t)}:n;e.current.set(n,{type:r,eventTarget:t,fn:a,options:o}),t.addEventListener(r,a,o)},[]),r=(0,n.useCallback)((t,r,n,o)=>{var a;let i=(null===(a=e.current.get(n))||void 0===a?void 0:a.fn)||n;t.removeEventListener(r,i,o),e.current.delete(n)},[]),o=(0,n.useCallback)(()=>{e.current.forEach((e,t)=>{r(e.eventTarget,e.type,t,e.options)})},[r]);return(0,n.useEffect)(()=>o,[o]),{addGlobalListener:t,removeGlobalListener:r,removeAllGlobalListeners:o}}},71424:(e,t,r)=>{r.d(t,{Tw:()=>c,Bi:()=>u,X1:()=>p});var n=r(9828),o=r(82476),a=r(41473),i=r(51034);let s=!!("undefined"!=typeof window&&window.document&&window.document.createElement),l=new Map,d=new FinalizationRegistry(e=>{l.delete(e)});function u(e){let[t,r]=(0,a.useState)(e),o=(0,a.useRef)(null),u=(0,i.Cc)(t),c=(0,a.useRef)(null);if(d.register(c,u),s){let e=l.get(u);e&&!e.includes(o)?e.push(o):l.set(u,[o])}return(0,n.N)(()=>()=>{d.unregister(c),l.delete(u)},[u]),(0,a.useEffect)(()=>{let e=o.current;return e&&r(e),()=>{e&&(o.current=null)}}),u}function c(e,t){if(e===t)return e;let r=l.get(e);if(r)return r.forEach(e=>e.current=t),t;let n=l.get(t);return n?(n.forEach(t=>t.current=e),e):t}function p(e=[]){let t=u(),[r,i]=function(e){let[t,r]=(0,a.useState)(e),i=(0,a.useRef)(null),s=(0,o.J)(()=>{if(!i.current)return;let e=i.current.next();if(e.done){i.current=null;return}t===e.value?s():r(e.value)});(0,n.N)(()=>{i.current&&s()});let l=(0,o.J)(e=>{i.current=e(t),s()});return[t,l]}(t),s=(0,a.useCallback)(()=>{i(function*(){yield t,yield document.getElementById(t)?t:void 0})},[t,i]);return(0,n.N)(s,[t,s,...e]),r}},9828:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(41473);let o="undefined"!=typeof document?n.useLayoutEffect:()=>{}},80809:(e,t,r)=>{r.d(t,{w:()=>o});var n=r(9828);function o(e,t){(0,n.N)(()=>{if(e&&e.ref&&t)return e.ref.current=t.current,()=>{e.ref&&(e.ref.current=null)}})}},88970:(e,t,r)=>{r.d(t,{Nf:()=>n});function n(){return!1}},13222:(e,t,r)=>{r.d(t,{_:()=>n});function n(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}},33992:(e,t,r)=>{r.d(t,{_:()=>o});var n=r(13222);function o(e,t){var r=(0,n._)(e,t,"get");return r.get?r.get.call(e):r.value}},49148:(e,t,r)=>{r.d(t,{_:()=>n});function n(e,t,r){!function(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}(e,t),t.set(e,r)}},31178:(e,t,r)=>{r.d(t,{_:()=>o});var n=r(13222);function o(e,t,r){var o=(0,n._)(e,t,"set");return!function(e,t,r){if(t.set)t.set.call(e,r);else{if(!t.writable)throw TypeError("attempted to set read only private field");t.value=r}}(e,o,r),r}},84269:(e,t,r)=>{r.d(t,{A:()=>n});let n=function(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t){if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n)}return o}(e))&&(n&&(n+=" "),n+=t);return n}},11208:(e,t,r)=>{r.d(t,{c:()=>o,n:()=>a});var n=r(36198);let o="framerAppearId",a="data-"+(0,n.I)(o)},76296:(e,t,r)=>{r.d(t,{N:()=>n});function n(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}},59162:(e,t,r)=>{r.d(t,{p:()=>n});let n=e=>Array.isArray(e)},35277:(e,t,r)=>{r.d(t,{N:()=>b});var n=r(12389),o=r(41473),a=r(92729),i=r(58209),s=r(51432);class l extends o.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function d(e){let{children:t,isPresent:r}=e,a=(0,o.useId)(),i=(0,o.useRef)(null),d=(0,o.useRef)({width:0,height:0,top:0,left:0}),{nonce:u}=(0,o.useContext)(s.Q);return(0,o.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:o}=d.current;if(r||!i.current||!e||!t)return;i.current.dataset.motionPopId=a;let s=document.createElement("style");return u&&(s.nonce=u),document.head.appendChild(s),s.sheet&&s.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            top: ").concat(n,"px !important;\n            left: ").concat(o,"px !important;\n          }\n        ")),()=>{document.head.removeChild(s)}},[r]),(0,n.jsx)(l,{isPresent:r,childRef:i,sizeRef:d,children:o.cloneElement(t,{ref:i})})}let u=e=>{let{children:t,initial:r,isPresent:s,onExitComplete:l,custom:u,presenceAffectsLayout:p,mode:f}=e,g=(0,i.M)(c),v=(0,o.useId)(),m=(0,o.useCallback)(e=>{for(let t of(g.set(e,!0),g.values()))if(!t)return;l&&l()},[g,l]),b=(0,o.useMemo)(()=>({id:v,initial:r,isPresent:s,custom:u,onExitComplete:m,register:e=>(g.set(e,!1),()=>g.delete(e))}),p?[Math.random(),m]:[s,m]);return(0,o.useMemo)(()=>{g.forEach((e,t)=>g.set(t,!1))},[s]),o.useEffect(()=>{s||g.size||!l||l()},[s]),"popLayout"===f&&(t=(0,n.jsx)(d,{isPresent:s,children:t})),(0,n.jsx)(a.t.Provider,{value:b,children:t})};function c(){return new Map}var p=r(76177),f=r(35042);let g=e=>e.key||"";function v(e){let t=[];return o.Children.forEach(e,e=>{(0,o.isValidElement)(e)&&t.push(e)}),t}var m=r(24250);let b=e=>{let{children:t,exitBeforeEnter:r,custom:a,initial:s=!0,onExitComplete:l,presenceAffectsLayout:d=!0,mode:c="sync"}=e;(0,f.V)(!r,"Replace exitBeforeEnter with mode='wait'");let b=(0,o.useMemo)(()=>v(t),[t]),h=b.map(g),y=(0,o.useRef)(!0),w=(0,o.useRef)(b),x=(0,i.M)(()=>new Map),[E,T]=(0,o.useState)(b),[k,P]=(0,o.useState)(b);(0,m.E)(()=>{y.current=!1,w.current=b;for(let e=0;e<k.length;e++){let t=g(k[e]);h.includes(t)?x.delete(t):!0!==x.get(t)&&x.set(t,!1)}},[k,h.length,h.join("-")]);let S=[];if(b!==E){let e=[...b];for(let t=0;t<k.length;t++){let r=k[t],n=g(r);h.includes(n)||(e.splice(t,0,r),S.push(r))}"wait"===c&&S.length&&(e=S),P(v(e)),T(b);return}let{forceRender:M}=(0,o.useContext)(p.L);return(0,n.jsx)(n.Fragment,{children:k.map(e=>{let t=g(e),r=b===k||h.includes(t);return(0,n.jsx)(u,{isPresent:r,initial:(!y.current||!!s)&&void 0,custom:r?void 0:a,presenceAffectsLayout:d,mode:c,onExitComplete:r?void 0:()=>{if(!x.has(t))return;x.set(t,!0);let e=!0;x.forEach(t=>{t||(e=!1)}),e&&(null==M||M(),P(w.current),l&&l())},children:e},t)})})}},97783:(e,t,r)=>{r.d(t,{F:()=>s});var n=r(12389),o=r(41473),a=r(40482),i=r(35700);function s(e){let{children:t,features:r,strict:s=!1}=e,[,d]=(0,o.useState)(!l(r)),u=(0,o.useRef)(void 0);if(!l(r)){let{renderer:e,...t}=r;u.current=e,(0,i.Y)(t)}return(0,o.useEffect)(()=>{l(r)&&r().then(e=>{let{renderer:t,...r}=e;(0,i.Y)(r),u.current=t,d(!0)})},[]),(0,n.jsx)(a.Y.Provider,{value:{renderer:u.current,strict:s},children:t})}function l(e){return"function"==typeof e}},76177:(e,t,r)=>{r.d(t,{L:()=>n});let n=(0,r(41473).createContext)({})},40482:(e,t,r)=>{r.d(t,{Y:()=>n});let n=(0,r(41473).createContext)({strict:!1})},51432:(e,t,r)=>{r.d(t,{Q:()=>n});let n=(0,r(41473).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},99979:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(41473).createContext)({})},92729:(e,t,r)=>{r.d(t,{t:()=>n});let n=(0,r(41473).createContext)(null)},24297:(e,t,r)=>{r.d(t,{N:()=>n});let n=(0,r(41473).createContext)({})},69756:(e,t,r)=>{r.d(t,{I:()=>a,q:()=>o});var n=r(79864);let o=["read","resolveKeyframes","update","preRender","render","postRender"];function a(e,t){let r=!1,a=!0,i={delta:0,timestamp:0,isProcessing:!1},s=()=>r=!0,l=o.reduce((e,t)=>(e[t]=function(e){let t=new Set,r=new Set,n=!1,o=!1,a=new WeakSet,i={delta:0,timestamp:0,isProcessing:!1};function s(t){a.has(t)&&(l.schedule(t),e()),t(i)}let l={schedule:(e,o=!1,i=!1)=>{let s=i&&n?t:r;return o&&a.add(e),s.has(e)||s.add(e),e},cancel:e=>{r.delete(e),a.delete(e)},process:e=>{if(i=e,n){o=!0;return}n=!0,[t,r]=[r,t],r.clear(),t.forEach(s),n=!1,o&&(o=!1,l.process(e))}};return l}(s),e),{}),{read:d,resolveKeyframes:u,update:c,preRender:p,render:f,postRender:g}=l,v=()=>{let o=n.W.useManualTiming?i.timestamp:performance.now();r=!1,i.delta=a?1e3/60:Math.max(Math.min(o-i.timestamp,40),1),i.timestamp=o,i.isProcessing=!0,d.process(i),u.process(i),c.process(i),p.process(i),f.process(i),g.process(i),i.isProcessing=!1,r&&t&&(a=!1,e(v))},m=()=>{r=!0,a=!0,i.isProcessing||e(v)};return{schedule:o.reduce((e,t)=>{let n=l[t];return e[t]=(e,t=!1,o=!1)=>(r||m(),n.schedule(e,t,o)),e},{}),cancel:e=>{for(let t=0;t<o.length;t++)l[o[t]].cancel(e)},state:i,steps:l}}},20638:(e,t,r)=>{r.d(t,{Gt:()=>o,PP:()=>s,WG:()=>a,uv:()=>i});var n=r(63679);let{schedule:o,cancel:a,state:i,steps:s}=(0,r(69756).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.l,!0)},45990:(e,t,r)=>{r.d(t,{k:()=>n});let{schedule:n,cancel:o}=(0,r(69756).I)(queueMicrotask,!1)},80750:(e,t,r)=>{r.d(t,{B:()=>o});let n={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},o={};for(let e in n)o[e]={isEnabled:t=>n[e].some(e=>!!t[e])}},35700:(e,t,r)=>{r.d(t,{Y:()=>o});var n=r(80750);function o(e){for(let t in e)n.B[t]={...n.B[t],...e[t]}}},27484:(e,t,r)=>{r.d(t,{Z:()=>E});var n=r(12389),o=r(41473),a=r(51432),i=r(99979),s=r(92729),l=r(24250),d=r(40482),u=r(11208),c=r(45990),p=r(1827),f=r(24297),g=r(51029),v=r(97969);function m(e){return Array.isArray(e)?e.join(" "):e}var b=r(35700),h=r(27112),y=r(76177),w=r(2350),x=r(80750);function E(e){let{preloadedFeatures:t,createVisualElement:r,useRender:E,useVisualState:T,Component:k}=e;t&&(0,b.Y)(t);let P=(0,o.forwardRef)(function(e,t){var b;let w;let P={...(0,o.useContext)(a.Q),...e,layoutId:function(e){let{layoutId:t}=e,r=(0,o.useContext)(y.L).id;return r&&void 0!==t?r+"-"+t:t}(e)},{isStatic:S}=P,M=function(e){let{initial:t,animate:r}=function(e,t){if((0,v.e)(e)){let{initial:t,animate:r}=e;return{initial:!1===t||(0,g.w)(t)?t:void 0,animate:(0,g.w)(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,o.useContext)(i.A));return(0,o.useMemo)(()=>({initial:t,animate:r}),[m(t),m(r)])}(e),C=T(e,S);if(!S&&h.B){(0,o.useContext)(d.Y).strict;let e=function(e){let{drag:t,layout:r}=x.B;if(!t&&!r)return{};let n={...t,...r};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==r?void 0:r.isEnabled(e))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(P);w=e.MeasureLayout,M.visualElement=function(e,t,r,n,g){var v,m;let{visualElement:b}=(0,o.useContext)(i.A),h=(0,o.useContext)(d.Y),y=(0,o.useContext)(s.t),w=(0,o.useContext)(a.Q).reducedMotion,x=(0,o.useRef)();n=n||h.renderer,!x.current&&n&&(x.current=n(e,{visualState:t,parent:b,props:r,presenceContext:y,blockInitialAnimation:!!y&&!1===y.initial,reducedMotionConfig:w}));let E=x.current,T=(0,o.useContext)(f.N);E&&!E.projection&&g&&("html"===E.type||"svg"===E.type)&&function(e,t,r,n){let{layoutId:o,layout:a,drag:i,dragConstraints:s,layoutScroll:l,layoutRoot:d}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:o,layout:a,alwaysMeasureLayout:!!i||s&&(0,p.X)(s),visualElement:e,animationType:"string"==typeof a?a:"both",initialPromotionConfig:n,layoutScroll:l,layoutRoot:d})}(x.current,r,g,T);let k=(0,o.useRef)(!1);(0,o.useInsertionEffect)(()=>{E&&k.current&&E.update(r,y)});let P=r[u.n],S=(0,o.useRef)(!!P&&!(null===(v=window.MotionHandoffIsComplete)||void 0===v?void 0:v.call(window,P))&&(null===(m=window.MotionHasOptimisedAnimation)||void 0===m?void 0:m.call(window,P)));return(0,l.E)(()=>{E&&(k.current=!0,window.MotionIsMounted=!0,E.updateFeatures(),c.k.render(E.render),S.current&&E.animationState&&E.animationState.animateChanges())}),(0,o.useEffect)(()=>{E&&(!S.current&&E.animationState&&E.animationState.animateChanges(),S.current&&(queueMicrotask(()=>{var e;null===(e=window.MotionHandoffMarkAsComplete)||void 0===e||e.call(window,P)}),S.current=!1))}),E}(k,C,P,r,e.ProjectionNode)}return(0,n.jsxs)(i.A.Provider,{value:M,children:[w&&M.visualElement?(0,n.jsx)(w,{visualElement:M.visualElement,...P}):null,E(k,e,(b=M.visualElement,(0,o.useCallback)(e=>{e&&C.mount&&C.mount(e),b&&(e?b.mount(e):b.unmount()),t&&("function"==typeof t?t(e):(0,p.X)(t)&&(t.current=e))},[b])),C,S,M.visualElement)]})});return P[w.o]=k,P}},44867:(e,t,r)=>{r.d(t,{z:()=>a});var n=r(7769),o=r(93218);function a(e,{layout:t,layoutId:r}){return o.f.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!n.H[e]||"opacity"===e)}},2350:(e,t,r)=>{r.d(t,{o:()=>n});let n=Symbol.for("motionComponentSymbol")},25116:(e,t,r)=>{r.d(t,{T:()=>c});var n=r(41473),o=r(76296),a=r(92729),i=r(60435),s=r(58209),l=r(43218),d=r(99979),u=r(97969);let c=e=>(t,r)=>{let c=(0,n.useContext)(d.A),p=(0,n.useContext)(a.t),f=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:r},n,a,s){let d={latestValues:function(e,t,r,n){let a={},s=n(e,{});for(let e in s)a[e]=(0,l.u)(s[e]);let{initial:d,animate:c}=e,p=(0,u.e)(e),f=(0,u.O)(e);t&&f&&!p&&!1!==e.inherit&&(void 0===d&&(d=t.initial),void 0===c&&(c=t.animate));let g=!!r&&!1===r.initial,v=(g=g||!1===d)?c:d;if(v&&"boolean"!=typeof v&&!(0,o.N)(v)){let t=Array.isArray(v)?v:[v];for(let r=0;r<t.length;r++){let n=(0,i.a)(e,t[r]);if(n){let{transitionEnd:e,transition:t,...r}=n;for(let e in r){let t=r[e];if(Array.isArray(t)){let e=g?t.length-1:0;t=t[e]}null!==t&&(a[e]=t)}for(let t in e)a[t]=e[t]}}}return a}(n,a,s,e),renderState:t()};return r&&(d.mount=e=>r(n,e,d)),d})(e,t,c,p);return r?f():(0,s.M)(f)}},49674:(e,t,r)=>{r.d(t,{S:()=>o});let n=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function o(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||n.has(e)}},7769:(e,t,r)=>{r.d(t,{$:()=>o,H:()=>n});let n={};function o(e){Object.assign(n,e)}},16676:(e,t,r)=>{r.d(t,{C:()=>E});var n=r(27484),o=r(23867),a=r(85057),i=r(10419),s=r(25116);let l=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),d=()=>({...l(),attrs:{}});var u=r(5367),c=r(82635),p=r(20638);let f={useVisualState:(0,s.T)({scrapeMotionValuesFromProps:i.x,createRenderState:d,onMount:(e,t,{renderState:r,latestValues:n})=>{p.Gt.read(()=>{try{r.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(e){r.dimensions={x:0,y:0,width:0,height:0}}}),p.Gt.render(()=>{(0,u.B)(r,n,(0,c.n)(t.tagName),e.transformTemplate),(0,a.d)(t,r)})}})};var g=r(64978);let v={useVisualState:(0,s.T)({scrapeMotionValuesFromProps:g.x,createRenderState:l})};var m=r(41473),b=r(44867),h=r(99630),y=r(94815);function w(e,t,r){for(let n in t)(0,h.S)(t[n])||(0,b.z)(n,r)||(e[n]=t[n])}var x=r(43228);function E(e,t){return function(r,{forwardMotionProps:a}={forwardMotionProps:!1}){let i={...(0,o.Q)(r)?f:v,preloadedFeatures:e,useRender:function(e=!1){return(t,r,n,{latestValues:a},i)=>{let s=((0,o.Q)(t)?function(e,t,r,n){let o=(0,m.useMemo)(()=>{let r=d();return(0,u.B)(r,t,(0,c.n)(n),e.transformTemplate),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};w(t,e.style,e),o.style={...t,...o.style}}return o}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return w(n,r,e),Object.assign(n,function({transformTemplate:e},t){return(0,m.useMemo)(()=>{let r=l();return(0,y.O)(r,t,e),Object.assign({},r.vars,r.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,a,i,t),p=(0,x.J)(r,"string"==typeof t,e),f=t!==m.Fragment?{...p,...s,ref:n}:{},{children:g}=r,v=(0,m.useMemo)(()=>(0,h.S)(g)?g.get():g,[g]);return(0,m.createElement)(t,{...f,children:v})}}(a),createVisualElement:t,Component:r};return(0,n.Z)(i)}}},52408:(e,t,r)=>{r.d(t,{I:()=>n});function n(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(r,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}},46211:(e,t,r)=>{r.d(t,{m:()=>a});var n=r(52408);let o=(0,r(16676).C)(),a=(0,n.I)(o)},36198:(e,t,r)=>{r.d(t,{I:()=>n});let n=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase()},43228:(e,t,r)=>{r.d(t,{D:()=>a,J:()=>i});var n=r(49674);let o=e=>!(0,n.S)(e);function a(e){e&&(o=t=>t.startsWith("on")?!(0,n.S)(t):e(t))}try{a(require("@emotion/is-prop-valid").default)}catch(e){}function i(e,t,r){let a={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(o(i)||!0===r&&(0,n.S)(i)||!t&&!(0,n.S)(i)||e.draggable&&i.startsWith("onDrag"))&&(a[i]=e[i]);return a}},35128:(e,t,r)=>{r.d(t,{j:()=>o,p:()=>i});let n=e=>t=>"string"==typeof t&&t.startsWith(e),o=n("--"),a=n("var(--"),i=e=>!!a(e)&&s.test(e.split("/*")[0].trim()),s=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},23867:(e,t,r)=>{r.d(t,{Q:()=>o});let n=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function o(e){if("string"!=typeof e||e.includes("-"));else if(n.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}},94521:(e,t,r)=>{r.d(t,{e:()=>n});let n=(e,t)=>t&&"number"==typeof e?t.transform(e):e},56399:(e,t,r)=>{r.d(t,{o:()=>o});var n=r(97299);let o={borderWidth:n.px,borderTopWidth:n.px,borderRightWidth:n.px,borderBottomWidth:n.px,borderLeftWidth:n.px,borderRadius:n.px,radius:n.px,borderTopLeftRadius:n.px,borderTopRightRadius:n.px,borderBottomRightRadius:n.px,borderBottomLeftRadius:n.px,width:n.px,maxWidth:n.px,height:n.px,maxHeight:n.px,top:n.px,right:n.px,bottom:n.px,left:n.px,padding:n.px,paddingTop:n.px,paddingRight:n.px,paddingBottom:n.px,paddingLeft:n.px,margin:n.px,marginTop:n.px,marginRight:n.px,marginBottom:n.px,marginLeft:n.px,backgroundPositionX:n.px,backgroundPositionY:n.px}},47518:(e,t,r)=>{r.d(t,{W:()=>l});var n=r(16942),o=r(97299),a=r(56399);let i={rotate:o.uj,rotateX:o.uj,rotateY:o.uj,rotateZ:o.uj,scale:n.hs,scaleX:n.hs,scaleY:n.hs,scaleZ:n.hs,skew:o.uj,skewX:o.uj,skewY:o.uj,distance:o.px,translateX:o.px,translateY:o.px,translateZ:o.px,x:o.px,y:o.px,z:o.px,perspective:o.px,transformPerspective:o.px,opacity:n.X4,originX:o.gQ,originY:o.gQ,originZ:o.px},s={...n.ai,transform:Math.round},l={...a.o,...i,zIndex:s,size:o.px,fillOpacity:n.X4,strokeOpacity:n.X4,numOctaves:s}},94815:(e,t,r)=>{r.d(t,{O:()=>l});var n=r(15381),o=r(35128),a=r(93218),i=r(94521),s=r(47518);function l(e,t,r){let{style:l,vars:d,transformOrigin:u}=e,c=!1,p=!1;for(let e in t){let r=t[e];if(a.f.has(e)){c=!0;continue}if((0,o.j)(e)){d[e]=r;continue}{let t=(0,i.e)(r,s.W[e]);e.startsWith("origin")?(p=!0,u[e]=t):l[e]=t}}if(!t.transform&&(c||r?l.transform=(0,n.d)(t,e.transform,r):l.transform&&(l.transform="none")),p){let{originX:e="50%",originY:t="50%",originZ:r=0}=u;l.transformOrigin=`${e} ${t} ${r}`}}},15381:(e,t,r)=>{r.d(t,{d:()=>l});var n=r(93218),o=r(94521),a=r(47518);let i={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},s=n.U.length;function l(e,t,r){let l="",d=!0;for(let u=0;u<s;u++){let s=n.U[u],c=e[s];if(void 0===c)continue;let p=!0;if(!(p="number"==typeof c?c===(s.startsWith("scale")?1:0):0===parseFloat(c))||r){let e=(0,o.e)(c,a.W[s]);if(!p){d=!1;let t=i[s]||s;l+=`${t}(${e}) `}r&&(t[s]=e)}}return l=l.trim(),r?l=r(t,d?"":l):d&&(l="none"),l}},89244:(e,t,r)=>{r.d(t,{e:()=>n});function n(e,{style:t,vars:r},n,o){for(let a in Object.assign(e.style,t,o&&o.getProjectionStyles(n)),r)e.style.setProperty(a,r[a])}},64978:(e,t,r)=>{r.d(t,{x:()=>a});var n=r(44867),o=r(99630);function a(e,t,r){var a;let{style:i}=e,s={};for(let l in i)((0,o.S)(i[l])||t.style&&(0,o.S)(t.style[l])||(0,n.z)(l,e)||(null===(a=null==r?void 0:r.getValue(l))||void 0===a?void 0:a.liveStyle)!==void 0)&&(s[l]=i[l]);return s}},93218:(e,t,r)=>{r.d(t,{U:()=>n,f:()=>o});let n=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],o=new Set(n)},5367:(e,t,r)=>{r.d(t,{B:()=>l});var n=r(94815),o=r(97299);function a(e,t,r){return"string"==typeof e?e:o.px.transform(t+r*e)}let i={offset:"stroke-dashoffset",array:"stroke-dasharray"},s={offset:"strokeDashoffset",array:"strokeDasharray"};function l(e,{attrX:t,attrY:r,attrScale:l,originX:d,originY:u,pathLength:c,pathSpacing:p=1,pathOffset:f=0,...g},v,m){if((0,n.O)(e,g,m),v){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:b,style:h,dimensions:y}=e;b.transform&&(y&&(h.transform=b.transform),delete b.transform),y&&(void 0!==d||void 0!==u||h.transform)&&(h.transformOrigin=function(e,t,r){let n=a(t,e.x,e.width),o=a(r,e.y,e.height);return`${n} ${o}`}(y,void 0!==d?d:.5,void 0!==u?u:.5)),void 0!==t&&(b.x=t),void 0!==r&&(b.y=r),void 0!==l&&(b.scale=l),void 0!==c&&function(e,t,r=1,n=0,a=!0){e.pathLength=1;let l=a?i:s;e[l.offset]=o.px.transform(-n);let d=o.px.transform(t),u=o.px.transform(r);e[l.array]=`${d} ${u}`}(b,c,p,f,!1)}},77473:(e,t,r)=>{r.d(t,{e:()=>n});let n=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"])},82635:(e,t,r)=>{r.d(t,{n:()=>n});let n=e=>"string"==typeof e&&"svg"===e.toLowerCase()},85057:(e,t,r)=>{r.d(t,{d:()=>i});var n=r(36198),o=r(89244),a=r(77473);function i(e,t,r,i){for(let r in(0,o.e)(e,t,void 0,i),t.attrs)e.setAttribute(a.e.has(r)?r:(0,n.I)(r),t.attrs[r])}},10419:(e,t,r)=>{r.d(t,{x:()=>i});var n=r(99630),o=r(64978),a=r(93218);function i(e,t,r){let i=(0,o.x)(e,t,r);for(let r in e)((0,n.S)(e[r])||(0,n.S)(t[r]))&&(i[-1!==a.U.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return i}},97969:(e,t,r)=>{r.d(t,{O:()=>s,e:()=>i});var n=r(76296),o=r(51029),a=r(79700);function i(e){return(0,n.N)(e.animate)||a._.some(t=>(0,o.w)(e[t]))}function s(e){return!!(i(e)||e.variants)}},51029:(e,t,r)=>{r.d(t,{w:()=>n});function n(e){return"string"==typeof e||Array.isArray(e)}},60435:(e,t,r)=>{function n(e,t,r,n){if("function"==typeof t||("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t)){let[o,a]=function(e){let t=[{},{}];return null==e||e.values.forEach((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()}),t}(n);t=t(void 0!==r?r:e.custom,o,a)}return t}r.d(t,{a:()=>n})},79700:(e,t,r)=>{r.d(t,{U:()=>n,_:()=>o});let n=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],o=["initial",...n]},79864:(e,t,r)=>{r.d(t,{W:()=>n});let n={skipAnimations:!1,useManualTiming:!1}},57162:(e,t,r)=>{r.d(t,{q:()=>n});let n=(e,t,r)=>r>t?t:r<e?e:r},35042:(e,t,r)=>{r.d(t,{$:()=>o,V:()=>a});var n=r(63679);let o=n.l,a=n.l},27112:(e,t,r)=>{r.d(t,{B:()=>n});let n="undefined"!=typeof window},1827:(e,t,r)=>{r.d(t,{X:()=>n});function n(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}},63679:(e,t,r)=>{r.d(t,{l:()=>n});let n=e=>e},82431:(e,t,r)=>{r.d(t,{B:()=>o,K:()=>a});var n=r(59162);let o=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),a=e=>(0,n.p)(e)?e[e.length-1]||0:e},58209:(e,t,r)=>{r.d(t,{M:()=>o});var n=r(41473);function o(e){let t=(0,n.useRef)(null);return null===t.current&&(t.current=e()),t.current}},24250:(e,t,r)=>{r.d(t,{E:()=>o});var n=r(41473);let o=r(27112).B?n.useLayoutEffect:n.useEffect},16942:(e,t,r)=>{r.d(t,{X4:()=>a,ai:()=>o,hs:()=>i});var n=r(57162);let o={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},a={...o,transform:e=>(0,n.q)(0,1,e)},i={...o,default:1}},97299:(e,t,r)=>{r.d(t,{KN:()=>a,gQ:()=>d,px:()=>i,uj:()=>o,vh:()=>s,vw:()=>l});let n=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),o=n("deg"),a=n("%"),i=n("px"),s=n("vh"),l=n("vw"),d={...a,parse:e=>a.parse(e)/100,transform:e=>a.transform(100*e)}},99630:(e,t,r)=>{r.d(t,{S:()=>n});let n=e=>!!(e&&e.getVelocity)},43218:(e,t,r)=>{r.d(t,{u:()=>a});var n=r(82431),o=r(99630);function a(e){let t=(0,o.S)(e)?e.get():e;return(0,n.B)(t)?t.toValue():t}}}]);