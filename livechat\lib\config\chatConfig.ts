import { ChatConfig } from '../types';

/**
 * Default configuration values
 */
export const defaultChatConfig: ChatConfig = {
  server: {
    apiUrl: "http://localhost:7272",
    useHttps: false,
    apiVersion: "v3",
    timeout: 30000,
  },
  app: {
    appName: "LiveChat",
    appDescription: "Live chat application with R2R integration",
    version: "1.0.0",
    defaultMode: "rag_agent",
    conversationHistoryLimit: 10,
  },
  vectorSearch: {
    enabled: true,
    searchLimit: 10,
    searchFilters: "{}",
    indexMeasure: "cosine_distance",
    includeMetadatas: false,
    probes: undefined,
    efSearch: undefined,
  },
  hybridSearch: {
    enabled: false,
    fullTextWeight: undefined,
    semanticWeight: undefined,
    fullTextLimit: undefined,
    rrfK: undefined,
  },
  graphSearch: {
    enabled: true,
    kgSearchLevel: null,
    maxCommunityDescriptionLength: 100,
    localSearchLimits: {},
    maxLlmQueries: undefined,
  },
  ragGeneration: {
    temperature: 0.1,
    topP: 1.0,
    topK: 100,
    maxTokensToSample: 1024,
    kgTemperature: 0.1,
    kgTopP: 1.0,
    kgTopK: 100,
    kgMaxTokensToSample: 1024,
  },
};

/**
 * Load configuration from public/config.json with fallback to defaults
 */
export const loadChatConfig = async (): Promise<ChatConfig> => {
  try {
    const response = await fetch('/config.json');
    if (!response.ok) {
      console.warn('Failed to load config.json, using default configuration');
      return defaultChatConfig;
    }
    
    const config = await response.json();
    
    // Merge with defaults to ensure all required fields are present
    return {
      server: { ...defaultChatConfig.server, ...config.server },
      app: { ...defaultChatConfig.app, ...config.app },
      vectorSearch: { ...defaultChatConfig.vectorSearch, ...config.vectorSearch },
      hybridSearch: { ...defaultChatConfig.hybridSearch, ...config.hybridSearch },
      graphSearch: { ...defaultChatConfig.graphSearch, ...config.graphSearch },
      ragGeneration: { ...defaultChatConfig.ragGeneration, ...config.ragGeneration },
    };
  } catch (error) {
    console.error('Error loading configuration:', error);
    return defaultChatConfig;
  }
};

/**
 * Get the deployment URL from configuration
 */
export const getDeploymentUrl = (config?: ChatConfig): string => {
  const cfg = config || defaultChatConfig;

  // If apiUrl already includes protocol, use it as-is
  if (cfg.server.apiUrl.startsWith('http://') || cfg.server.apiUrl.startsWith('https://')) {
    return cfg.server.apiUrl;
  }

  // Otherwise, construct URL with protocol
  const protocol = cfg.server.useHttps ? 'https' : 'http';
  return `${protocol}://${cfg.server.apiUrl}`;
};