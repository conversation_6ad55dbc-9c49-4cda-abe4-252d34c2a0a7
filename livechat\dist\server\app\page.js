(()=>{var e={};e.id=974,e.ids=[974],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},21820:e=>{"use strict";e.exports=require("os")},33873:e=>{"use strict";e.exports=require("path")},27910:e=>{"use strict";e.exports=require("stream")},83997:e=>{"use strict";e.exports=require("tty")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},62830:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>l,routeModule:()=>d,tree:()=>u});var s=r(13950),i=r(14953),a=r(13905),n=r.n(a),o=r(98238),p={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(p[e]=()=>o[e]);r.d(t,p);let u=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,48868)),"D:\\LLM\\Learning\\ai_coding\\anythingchat\\livechat\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,66965))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,42221)),"D:\\LLM\\Learning\\ai_coding\\anythingchat\\livechat\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,35453)),"D:\\LLM\\Learning\\ai_coding\\anythingchat\\livechat\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,66965))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["D:\\LLM\\Learning\\ai_coding\\anythingchat\\livechat\\app\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},d=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},52754:(e,t,r)=>{Promise.resolve().then(r.bind(r,48868))},99610:(e,t,r)=>{Promise.resolve().then(r.bind(r,9900))},9900:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a}),r(423);var s=r(78320),i=r(59579);function a(){(0,s.useRouter)();let{isAuthenticated:e}=(0,i.J)();return null}},48868:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(29622).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\LLM\\Learning\\ai_coding\\anythingchat\\livechat\\app\\page.tsx","default")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[233,497,875],()=>r(62830));module.exports=s})();