/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./i18n/locales lazy recursive ^\\.\\/.*\\.json$":
/*!************************************************************!*\
  !*** ./i18n/locales/ lazy ^\.\/.*\.json$ namespace object ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

var map = {
	"./en.json": [
		"(rsc)/./i18n/locales/en.json",
		"_rsc_i18n_locales_en_json"
	],
	"./zh.json": [
		"(rsc)/./i18n/locales/zh.json",
		"_rsc_i18n_locales_zh_json"
	]
};
function webpackAsyncContext(req) {
	if(!__webpack_require__.o(map, req)) {
		return Promise.resolve().then(() => {
			var e = new Error("Cannot find module '" + req + "'");
			e.code = 'MODULE_NOT_FOUND';
			throw e;
		});
	}

	var ids = map[req], id = ids[0];
	return __webpack_require__.e(ids[1]).then(() => {
		return __webpack_require__.t(id, 3 | 16);
	});
}
webpackAsyncContext.keys = () => (Object.keys(map));
webpackAsyncContext.id = "(rsc)/./i18n/locales lazy recursive ^\\.\\/.*\\.json$";
module.exports = webpackAsyncContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CLLM%5CLearning%5Cai_coding%5Canythingchat%5Clivechat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLLM%5CLearning%5Cai_coding%5Canythingchat%5Clivechat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CLLM%5CLearning%5Cai_coding%5Canythingchat%5Clivechat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLLM%5CLearning%5Cai_coding%5Canythingchat%5Clivechat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/module.compiled.js?3f5a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/not-found.tsx */ \"(rsc)/./app/not-found.tsx\"));\nconst page2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page2, \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [module0, \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\not-found.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CLLM%5CLearning%5Cai_coding%5Canythingchat%5Clivechat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLLM%5CLearning%5Cai_coding%5Canythingchat%5Clivechat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Clib%5C%5Clocale.ts%22%2C%5B%5B%2200710cb852dc46949f6337cc8e295416e52998adcf%22%2C%22getUserLocale%22%5D%2C%5B%22400e6bc43a9f5734884e1c8701d037dde4cf563a27%22%2C%22setUserLocale%22%5D%5D%5D%5D&__client_imported__=!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Clib%5C%5Clocale.ts%22%2C%5B%5B%2200710cb852dc46949f6337cc8e295416e52998adcf%22%2C%22getUserLocale%22%5D%2C%5B%22400e6bc43a9f5734884e1c8701d037dde4cf563a27%22%2C%22setUserLocale%22%5D%5D%5D%5D&__client_imported__=! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"00710cb852dc46949f6337cc8e295416e52998adcf\": () => (/* reexport safe */ D_LLM_Learning_ai_coding_anythingchat_livechat_lib_locale_ts__WEBPACK_IMPORTED_MODULE_0__.getUserLocale),\n/* harmony export */   \"400e6bc43a9f5734884e1c8701d037dde4cf563a27\": () => (/* reexport safe */ D_LLM_Learning_ai_coding_anythingchat_livechat_lib_locale_ts__WEBPACK_IMPORTED_MODULE_0__.setUserLocale)\n/* harmony export */ });\n/* harmony import */ var D_LLM_Learning_ai_coding_anythingchat_livechat_lib_locale_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/locale.ts */ \"(rsc)/./lib/locale.ts\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4wLjNfQGJhYmVsK2NvcmVANy4yX2JlYTRiOGY4NjcwZDgxNDg3M2VmYjY5YWUxYmJlM2U1L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWFjdGlvbi1lbnRyeS1sb2FkZXIuanM/YWN0aW9ucz0lNUIlNUIlMjJEJTNBJTVDJTVDTExNJTVDJTVDTGVhcm5pbmclNUMlNUNhaV9jb2RpbmclNUMlNUNhbnl0aGluZ2NoYXQlNUMlNUNsaXZlY2hhdCU1QyU1Q2xpYiU1QyU1Q2xvY2FsZS50cyUyMiUyQyU1QiU1QiUyMjAwNzEwY2I4NTJkYzQ2OTQ5ZjYzMzdjYzhlMjk1NDE2ZTUyOTk4YWRjZiUyMiUyQyUyMmdldFVzZXJMb2NhbGUlMjIlNUQlMkMlNUIlMjI0MDBlNmJjNDNhOWY1NzM0ODg0ZTFjODcwMWQwMzdkZGU0Y2Y1NjNhMjclMjIlMkMlMjJzZXRVc2VyTG9jYWxlJTIyJTVEJTVEJTVEJTVEJl9fY2xpZW50X2ltcG9ydGVkX189ISIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQ29KO0FBQ0EiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGdldFVzZXJMb2NhbGUgYXMgXCIwMDcxMGNiODUyZGM0Njk0OWY2MzM3Y2M4ZTI5NTQxNmU1Mjk5OGFkY2ZcIiB9IGZyb20gXCJEOlxcXFxMTE1cXFxcTGVhcm5pbmdcXFxcYWlfY29kaW5nXFxcXGFueXRoaW5nY2hhdFxcXFxsaXZlY2hhdFxcXFxsaWJcXFxcbG9jYWxlLnRzXCJcbmV4cG9ydCB7IHNldFVzZXJMb2NhbGUgYXMgXCI0MDBlNmJjNDNhOWY1NzM0ODg0ZTFjODcwMWQwMzdkZGU0Y2Y1NjNhMjdcIiB9IGZyb20gXCJEOlxcXFxMTE1cXFxcTGVhcm5pbmdcXFxcYWlfY29kaW5nXFxcXGFueXRoaW5nY2hhdFxcXFxsaXZlY2hhdFxcXFxsaWJcXFxcbG9jYWxlLnRzXCJcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Clib%5C%5Clocale.ts%22%2C%5B%5B%2200710cb852dc46949f6337cc8e295416e52998adcf%22%2C%22getUserLocale%22%5D%2C%5B%22400e6bc43a9f5734884e1c8701d037dde4cf563a27%22%2C%22setUserLocale%22%5D%5D%5D%5D&__client_imported__=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4wLjNfQGJhYmVsK2NvcmVANy4yX2JlYTRiOGY4NjcwZDgxNDg3M2VmYjY5YWUxYmJlM2U1L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0xMTSU1QyU1Q0xlYXJuaW5nJTVDJTVDYWlfY29kaW5nJTVDJTVDYW55dGhpbmdjaGF0JTVDJTVDbGl2ZWNoYXQlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQXdHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxMTE1cXFxcTGVhcm5pbmdcXFxcYWlfY29kaW5nXFxcXGFueXRoaW5nY2hhdFxcXFxsaXZlY2hhdFxcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4wLjNfQGJhYmVsK2NvcmVANy4yX2JlYTRiOGY4NjcwZDgxNDg3M2VmYjY5YWUxYmJlM2U1L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0xMTSU1QyU1Q0xlYXJuaW5nJTVDJTVDYWlfY29kaW5nJTVDJTVDYW55dGhpbmdjaGF0JTVDJTVDbGl2ZWNoYXQlNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQXdHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxMTE1cXFxcTGVhcm5pbmdcXFxcYWlfY29kaW5nXFxcXGFueXRoaW5nY2hhdFxcXFxsaXZlY2hhdFxcXFxhcHBcXFxccGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext-intl%403.23.5_next%4015.0._fbda60b06d7929e72c7e0b0ddfa53d11%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext-intl%403.23.5_next%4015.0._fbda60b06d7929e72c7e0b0ddfa53d11%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(rsc)/./app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/.pnpm/next-intl@3.23.5_next@15.0._fbda60b06d7929e72c7e0b0ddfa53d11/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(rsc)/./node_modules/.pnpm/next-intl@3.23.5_next@15.0._fbda60b06d7929e72c7e0b0ddfa53d11/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext-intl%403.23.5_next%4015.0._fbda60b06d7929e72c7e0b0ddfa53d11%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext-intl%403.23.5_next%4015.0._fbda60b06d7929e72c7e0b0ddfa53d11%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext-intl%403.23.5_next%4015.0._fbda60b06d7929e72c7e0b0ddfa53d11%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/.pnpm/next-intl@3.23.5_next@15.0._fbda60b06d7929e72c7e0b0ddfa53d11/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js */ \"(ssr)/./node_modules/.pnpm/next-intl@3.23.5_next@15.0._fbda60b06d7929e72c7e0b0ddfa53d11/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4wLjNfQGJhYmVsK2NvcmVANy4yX2JlYTRiOGY4NjcwZDgxNDg3M2VmYjY5YWUxYmJlM2U1L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0xMTSU1QyU1Q0xlYXJuaW5nJTVDJTVDYWlfY29kaW5nJTVDJTVDYW55dGhpbmdjaGF0JTVDJTVDbGl2ZWNoYXQlNUMlNUNhcHAlNUMlNUNwcm92aWRlcnMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUHJvdmlkZXJzJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMTE0lNUMlNUNMZWFybmluZyU1QyU1Q2FpX2NvZGluZyU1QyU1Q2FueXRoaW5nY2hhdCU1QyU1Q2xpdmVjaGF0JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0LWludGwlNDAzLjIzLjVfbmV4dCU0MDE1LjAuX2ZiZGE2MGIwNmQ3OTI5ZTcyYzdlMGIwZGRmYTUzZDExJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dC1pbnRsJTVDJTVDZGlzdCU1QyU1Q2VzbSU1QyU1Q3NoYXJlZCU1QyU1Q05leHRJbnRsQ2xpZW50UHJvdmlkZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNMTE0lNUMlNUNMZWFybmluZyU1QyU1Q2FpX2NvZGluZyU1QyU1Q2FueXRoaW5nY2hhdCU1QyU1Q2xpdmVjaGF0JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTUuMC4zXyU0MGJhYmVsJTJCY29yZSU0MDcuMl9iZWE0YjhmODY3MGQ4MTQ4NzNlZmI2OWFlMWJiZTNlNSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDTExNJTVDJTVDTGVhcm5pbmclNUMlNUNhaV9jb2RpbmclNUMlNUNhbnl0aGluZ2NoYXQlNUMlNUNsaXZlY2hhdCU1QyU1Q3N0eWxlcyU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrSkFBNEk7QUFDNUk7QUFDQSxnWkFBZ1IiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlByb3ZpZGVyc1wiXSAqLyBcIkQ6XFxcXExMTVxcXFxMZWFybmluZ1xcXFxhaV9jb2RpbmdcXFxcYW55dGhpbmdjaGF0XFxcXGxpdmVjaGF0XFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiRDpcXFxcTExNXFxcXExlYXJuaW5nXFxcXGFpX2NvZGluZ1xcXFxhbnl0aGluZ2NoYXRcXFxcbGl2ZWNoYXRcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXG5leHQtaW50bEAzLjIzLjVfbmV4dEAxNS4wLl9mYmRhNjBiMDZkNzkyOWU3MmM3ZTBiMGRkZmE1M2QxMVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dC1pbnRsXFxcXGRpc3RcXFxcZXNtXFxcXHNoYXJlZFxcXFxOZXh0SW50bENsaWVudFByb3ZpZGVyLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext-intl%403.23.5_next%4015.0._fbda60b06d7929e72c7e0b0ddfa53d11%5C%5Cnode_modules%5C%5Cnext-intl%5C%5Cdist%5C%5Cesm%5C%5Cshared%5C%5CNextIntlClientProvider.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cstyles%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/not-found-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/lib/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/lib/metadata/metadata-boundary.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/link.js */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4wLjNfQGJhYmVsK2NvcmVANy4yX2JlYTRiOGY4NjcwZDgxNDg3M2VmYjY5YWUxYmJlM2U1L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0xMTSU1QyU1Q0xlYXJuaW5nJTVDJTVDYWlfY29kaW5nJTVDJTVDYW55dGhpbmdjaGF0JTVDJTVDbGl2ZWNoYXQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNS4wLjNfJTQwYmFiZWwlMkJjb3JlJTQwNy4yX2JlYTRiOGY4NjcwZDgxNDg3M2VmYjY5YWUxYmJlM2U1JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyKiUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ1dBQXVOIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxMTE1cXFxcTGVhcm5pbmdcXFxcYWlfY29kaW5nXFxcXGFueXRoaW5nY2hhdFxcXFxsaXZlY2hhdFxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNS4wLjNfQGJhYmVsK2NvcmVANy4yX2JlYTRiOGY4NjcwZDgxNDg3M2VmYjY5YWUxYmJlM2U1XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4wLjNfQGJhYmVsK2NvcmVANy4yX2JlYTRiOGY4NjcwZDgxNDg3M2VmYjY5YWUxYmJlM2U1L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q0xMTSU1QyU1Q0xlYXJuaW5nJTVDJTVDYWlfY29kaW5nJTVDJTVDYW55dGhpbmdjaGF0JTVDJTVDbGl2ZWNoYXQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNS4wLjNfJTQwYmFiZWwlMkJjb3JlJTQwNy4yX2JlYTRiOGY4NjcwZDgxNDg3M2VmYjY5YWUxYmJlM2U1JTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNsaW5rLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyKiUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ1dBQXVOIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJEOlxcXFxMTE1cXFxcTGVhcm5pbmdcXFxcYWlfY29kaW5nXFxcXGFueXRoaW5nY2hhdFxcXFxsaXZlY2hhdFxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNS4wLjNfQGJhYmVsK2NvcmVANy4yX2JlYTRiOGY4NjcwZDgxNDg3M2VmYjY5YWUxYmJlM2U1XFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CLLM%5C%5CLearning%5C%5Cai_coding%5C%5Canythingchat%5C%5Clivechat%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4015.0.3_%40babel%2Bcore%407.2_bea4b8f8670d814873efb69ae1bbe3e5%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_context_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/context/UserContext */ \"(ssr)/./lib/context/UserContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n// 根据认证状态跳转页面\nfunction Page() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { isAuthenticated } = (0,_lib_context_UserContext__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"Page.useEffect\": ()=>{\n            // Check authentication and redirect accordingly\n            if (isAuthenticated) {\n                router.push('/sentio');\n            } else {\n                router.push('/auth/login');\n            }\n        }\n    }[\"Page.useEffect\"], [\n        isAuthenticated,\n        router\n    ]);\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7NkRBRWlDO0FBQ1U7QUFDUTtBQUVuRCxhQUFhO0FBQ0UsU0FBU0c7SUFDdEIsTUFBTUMsU0FBU0gsMERBQVNBO0lBQ3hCLE1BQU0sRUFBRUksZUFBZSxFQUFFLEdBQUdILGlFQUFPQTtJQUVuQ0YsZ0RBQVNBOzBCQUFDO1lBQ1IsZ0RBQWdEO1lBQ2hELElBQUlLLGlCQUFpQjtnQkFDbkJELE9BQU9FLElBQUksQ0FBQztZQUNkLE9BQU87Z0JBQ0xGLE9BQU9FLElBQUksQ0FBQztZQUNkO1FBQ0Y7eUJBQUc7UUFBQ0Q7UUFBaUJEO0tBQU87SUFFNUIsT0FBTztBQUNUIiwic291cmNlcyI6WyJEOlxcTExNXFxMZWFybmluZ1xcYWlfY29kaW5nXFxhbnl0aGluZ2NoYXRcXGxpdmVjaGF0XFxhcHBcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbidcbmltcG9ydCB7IHVzZVVzZXIgfSBmcm9tICdAL2xpYi9jb250ZXh0L1VzZXJDb250ZXh0J1xuXG4vLyDmoLnmja7orqTor4HnirbmgIHot7PovazpobXpnaJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFBhZ2UoKTogSlNYLkVsZW1lbnQgfCBudWxsIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gIGNvbnN0IHsgaXNBdXRoZW50aWNhdGVkIH0gPSB1c2VVc2VyKCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAvLyBDaGVjayBhdXRoZW50aWNhdGlvbiBhbmQgcmVkaXJlY3QgYWNjb3JkaW5nbHlcbiAgICBpZiAoaXNBdXRoZW50aWNhdGVkKSB7XG4gICAgICByb3V0ZXIucHVzaCgnL3NlbnRpbycpO1xuICAgIH0gZWxzZSB7XG4gICAgICByb3V0ZXIucHVzaCgnL2F1dGgvbG9naW4nKTtcbiAgICB9XG4gIH0sIFtpc0F1dGhlbnRpY2F0ZWQsIHJvdXRlcl0pO1xuXG4gIHJldHVybiBudWxsO1xufSJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJ1c2VVc2VyIiwiUGFnZSIsInJvdXRlciIsImlzQXV0aGVudGljYXRlZCIsInB1c2giXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _heroui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @heroui/react */ \"(ssr)/./node_modules/.pnpm/@heroui+system@2.4.12_@hero_6d545e84cf25acf49df02fdc8d5c89ed/node_modules/@heroui/system/dist/chunk-OKNU54ZL.mjs\");\n/* harmony import */ var _heroui_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @heroui/toast */ \"(ssr)/./node_modules/.pnpm/@heroui+toast@2.0.6_@heroui_99af8d3b985385384198473e3104e5b8/node_modules/@heroui/toast/dist/chunk-CRSRLBAU.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_context_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/context/UserContext */ \"(ssr)/./lib/context/UserContext.tsx\");\n// app/providers.tsx\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\n// import {ThemeProvider as NextThemesProvider} from \"next-themes\";\nfunction Providers({ children }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroui_react__WEBPACK_IMPORTED_MODULE_3__.HeroUIProvider, {\n        navigate: router.push,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroui_toast__WEBPACK_IMPORTED_MODULE_4__.ToastProvider, {\n                placement: \"top-right\",\n                toastProps: {\n                    timeout: 3000\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\providers.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_context_UserContext__WEBPACK_IMPORTED_MODULE_2__.UserProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\providers.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\providers.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUEsb0JBQW9COztBQUcyQjtBQUNEO0FBQ0Y7QUFDYTtBQUN6RCxtRUFBbUU7QUFFNUQsU0FBU0ksVUFBVSxFQUFFQyxRQUFRLEVBQWlDO0lBQ25FLE1BQU1DLFNBQVNKLDBEQUFTQTtJQUV4QixxQkFDRSw4REFBQ0YseURBQWNBO1FBQUNPLFVBQVVELE9BQU9FLElBQUk7OzBCQUVuQyw4REFBQ1Asd0RBQWFBO2dCQUFDUSxXQUFVO2dCQUFZQyxZQUFZO29CQUFFQyxTQUFTO2dCQUFLOzs7Ozs7MEJBQ2pFLDhEQUFDUixrRUFBWUE7MEJBQ1ZFOzs7Ozs7Ozs7Ozs7QUFLVCIsInNvdXJjZXMiOlsiRDpcXExMTVxcTGVhcm5pbmdcXGFpX2NvZGluZ1xcYW55dGhpbmdjaGF0XFxsaXZlY2hhdFxcYXBwXFxwcm92aWRlcnMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIGFwcC9wcm92aWRlcnMudHN4XG5cInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IHsgSGVyb1VJUHJvdmlkZXIgfSBmcm9tIFwiQGhlcm91aS9yZWFjdFwiO1xuaW1wb3J0IHsgVG9hc3RQcm92aWRlciB9IGZyb20gXCJAaGVyb3VpL3RvYXN0XCI7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCI7XG5pbXBvcnQgeyBVc2VyUHJvdmlkZXIgfSBmcm9tIFwiQC9saWIvY29udGV4dC9Vc2VyQ29udGV4dFwiO1xuLy8gaW1wb3J0IHtUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlcn0gZnJvbSBcIm5leHQtdGhlbWVzXCI7XG5cbmV4cG9ydCBmdW5jdGlvbiBQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcblxuICByZXR1cm4gKFxuICAgIDxIZXJvVUlQcm92aWRlciBuYXZpZ2F0ZT17cm91dGVyLnB1c2h9PlxuICAgICAgey8qIDxOZXh0VGhlbWVzUHJvdmlkZXIgYXR0cmlidXRlPVwiY2xhc3NcIiBkZWZhdWx0VGhlbWU9XCJkYXJrXCI+ICovfVxuICAgICAgPFRvYXN0UHJvdmlkZXIgcGxhY2VtZW50PVwidG9wLXJpZ2h0XCIgdG9hc3RQcm9wcz17eyB0aW1lb3V0OiAzMDAwIH19IC8+XG4gICAgICA8VXNlclByb3ZpZGVyPlxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L1VzZXJQcm92aWRlcj5cbiAgICAgIHsvKiA8L05leHRUaGVtZXNQcm92aWRlcj4gKi99XG4gICAgPC9IZXJvVUlQcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJIZXJvVUlQcm92aWRlciIsIlRvYXN0UHJvdmlkZXIiLCJ1c2VSb3V0ZXIiLCJVc2VyUHJvdmlkZXIiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiIsInJvdXRlciIsIm5hdmlnYXRlIiwicHVzaCIsInBsYWNlbWVudCIsInRvYXN0UHJvcHMiLCJ0aW1lb3V0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/context/UserContext.tsx":
/*!*************************************!*\
  !*** ./lib/context/UserContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! r2r-js */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/index.js\");\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(r2r_js__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useUser,UserProvider auto */ \n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    pipeline: null,\n    setPipeline: ()=>{},\n    selectedModel: \"null\",\n    setSelectedModel: ()=>{},\n    isAuthenticated: false,\n    login: async ()=>({\n            success: false,\n            userRole: \"user\"\n        }),\n    loginWithToken: async ()=>({\n            success: false,\n            userRole: \"user\"\n        }),\n    logout: async ()=>{},\n    unsetCredentials: async ()=>{},\n    register: async ()=>{},\n    authState: {\n        isAuthenticated: false,\n        email: null,\n        userRole: null,\n        userId: null\n    },\n    getClient: ()=>null,\n    client: null,\n    viewMode: \"user\",\n    setViewMode: ()=>{},\n    isSuperUser: ()=>false,\n    createUser: async ()=>{\n        throw new Error(\"createUser is not implemented in the default context\");\n    }\n});\nconst useUser = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (!context) {\n        throw new Error('useUser must be used within a UserProvider');\n    }\n    return context;\n};\nconst UserProvider = ({ children })=>{\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pipeline, setPipeline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"null\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('user');\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isAuthenticated: false,\n        email: null,\n        userRole: null,\n        userId: null\n    });\n    const isSuperUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[isSuperUser]\": ()=>{\n            return authState.userRole === 'admin' && viewMode === 'admin';\n        }\n    }[\"UserProvider.useCallback[isSuperUser]\"], [\n        authState.userRole,\n        viewMode\n    ]);\n    const [lastLoginTime, setLastLoginTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const login = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[login]\": async (email, password, instanceUrl)=>{\n            const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(instanceUrl);\n            try {\n                const tokens = await newClient.users.login({\n                    email: email,\n                    password: password\n                });\n                // Handle both camelCase and snake_case response formats\n                const accessToken = tokens.results.accessToken?.token || tokens.results.access_token?.token;\n                const refreshToken = tokens.results.refreshToken?.token || tokens.results.refresh_token?.token;\n                if (!accessToken) {\n                    throw new Error('No access token received from server');\n                }\n                localStorage.setItem(\"livechatAccessToken\", accessToken);\n                if (refreshToken) {\n                    localStorage.setItem(\"livechatRefreshToken\", refreshToken);\n                }\n                newClient.setTokens(accessToken, refreshToken || \"\");\n                setClient(newClient);\n                // Get user info\n                const userInfo = await newClient.users.me();\n                if (!userInfo.results) {\n                    throw new Error('Failed to get user information');\n                }\n                let userRole = \"user\";\n                try {\n                    await newClient.system.settings();\n                    userRole = \"admin\";\n                } catch (error) {\n                    if (error instanceof Error && \"status\" in error && error.status === 403) {\n                    // User doesn't have admin access\n                    } else {\n                        console.error(\"Unexpected error when checking user role:\", error);\n                    }\n                }\n                setAuthState({\n                    isAuthenticated: true,\n                    email: userInfo.results.email,\n                    userRole: userRole,\n                    userId: userInfo.results.id\n                });\n                setLastLoginTime(Date.now());\n                return {\n                    success: true,\n                    userRole\n                };\n            } catch (error) {\n                console.error('Login error:', error);\n                throw error;\n            }\n        }\n    }[\"UserProvider.useCallback[login]\"], []);\n    const loginWithToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[loginWithToken]\": async (token, instanceUrl)=>{\n            const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(instanceUrl);\n            try {\n                // First, set the token on the client to authenticate the request\n                newClient.setTokens(token, \"\");\n                // Try to get user info to validate the token\n                const userInfo = await newClient.users.me();\n                if (!userInfo.results) {\n                    throw new Error('Failed to get user information');\n                }\n                // If we get here, the token is valid, so keep it\n                localStorage.setItem(\"livechatAccessToken\", token);\n                setClient(newClient);\n                let userRole = \"user\";\n                try {\n                    await newClient.system.settings();\n                    userRole = \"admin\";\n                } catch (error) {\n                    if (error instanceof Error && \"status\" in error && error.status === 403) {\n                    // User doesn't have admin access\n                    } else {\n                        console.error(\"Unexpected error when checking user role:\", error);\n                    }\n                }\n                setAuthState({\n                    isAuthenticated: true,\n                    email: userInfo.results.email,\n                    userRole: userRole,\n                    userId: userInfo.results.id\n                });\n                return {\n                    success: true,\n                    userRole\n                };\n            } catch (error) {\n                console.error('Token login error:', error);\n                // Clear invalid token\n                localStorage.removeItem(\"livechatAccessToken\");\n                localStorage.removeItem(\"livechatRefreshToken\");\n                throw error;\n            }\n        }\n    }[\"UserProvider.useCallback[loginWithToken]\"], []);\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[logout]\": async ()=>{\n            try {\n                if (client) {\n                    await client.users.logout();\n                }\n            } catch (error) {\n                console.error('Logout error:', error);\n            } finally{\n                localStorage.removeItem(\"livechatAccessToken\");\n                localStorage.removeItem(\"livechatRefreshToken\");\n                setClient(null);\n                setAuthState({\n                    isAuthenticated: false,\n                    email: null,\n                    userRole: null,\n                    userId: null\n                });\n                setLastLoginTime(null);\n            }\n        }\n    }[\"UserProvider.useCallback[logout]\"], [\n        client\n    ]);\n    const unsetCredentials = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[unsetCredentials]\": async ()=>{\n            localStorage.removeItem(\"livechatAccessToken\");\n            localStorage.removeItem(\"livechatRefreshToken\");\n            setClient(null);\n            setAuthState({\n                isAuthenticated: false,\n                email: null,\n                userRole: null,\n                userId: null\n            });\n            setLastLoginTime(null);\n        }\n    }[\"UserProvider.useCallback[unsetCredentials]\"], []);\n    const register = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[register]\": async (email, password, instanceUrl)=>{\n            const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(instanceUrl);\n            try {\n                await newClient.users.create({\n                    email: email,\n                    password: password\n                });\n                // After successful registration, log in\n                await login(email, password, instanceUrl);\n            } catch (error) {\n                console.error('Registration error:', error);\n                throw error;\n            }\n        }\n    }[\"UserProvider.useCallback[register]\"], [\n        login\n    ]);\n    const getClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[getClient]\": ()=>{\n            return client;\n        }\n    }[\"UserProvider.useCallback[getClient]\"], [\n        client\n    ]);\n    const createUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[createUser]\": async (email, password, isAdmin = false)=>{\n            if (!client) {\n                throw new Error('No authenticated client available');\n            }\n            try {\n                await client.users.create({\n                    email: email,\n                    password: password\n                });\n            } catch (error) {\n                console.error('Create user error:', error);\n                throw error;\n            }\n        }\n    }[\"UserProvider.useCallback[createUser]\"], [\n        client\n    ]);\n    // Initialize authentication on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"UserProvider.useEffect.initializeAuth\": async ()=>{\n                    const accessToken = localStorage.getItem('livechatAccessToken');\n                    if (accessToken) {\n                        try {\n                            const { loadChatConfig, getDeploymentUrl } = await __webpack_require__.e(/*! import() */ \"_ssr_lib_config_chatConfig_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../config/chatConfig */ \"(ssr)/./lib/config/chatConfig.ts\"));\n                            const config = await loadChatConfig();\n                            const deploymentUrl = getDeploymentUrl(config);\n                            await loginWithToken(accessToken, deploymentUrl);\n                        } catch (error) {\n                            console.error('Auto-login failed:', error);\n                            localStorage.removeItem('livechatAccessToken');\n                            localStorage.removeItem('livechatRefreshToken');\n                        }\n                    }\n                }\n            }[\"UserProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"UserProvider.useEffect\"], [\n        loginWithToken\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: {\n            pipeline,\n            setPipeline,\n            selectedModel,\n            setSelectedModel,\n            isAuthenticated: authState.isAuthenticated,\n            login,\n            loginWithToken,\n            logout,\n            unsetCredentials,\n            register,\n            authState,\n            getClient,\n            client,\n            viewMode,\n            setViewMode,\n            isSuperUser,\n            createUser\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\lib\\\\context\\\\UserContext.tsx\",\n        lineNumber: 308,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/context/UserContext.tsx\n");

/***/ }),

/***/ "(rsc)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"e3312b5d8704\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zdHlsZXMvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXHN0eWxlc1xcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJlMzMxMmI1ZDg3MDRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next-intl */ \"(rsc)/./node_modules/.pnpm/next-intl@3.23.5_next@15.0._fbda60b06d7929e72c7e0b0ddfa53d11/node_modules/next-intl/dist/esm/react-server/NextIntlClientProviderServer.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/.pnpm/next-intl@3.23.5_next@15.0._fbda60b06d7929e72c7e0b0ddfa53d11/node_modules/next-intl/dist/esm/server/react-server/getLocale.js\");\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/.pnpm/next-intl@3.23.5_next@15.0._fbda60b06d7929e72c7e0b0ddfa53d11/node_modules/next-intl/dist/esm/server/react-server/getMessages.js\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n/* harmony import */ var _lib_path__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/path */ \"(rsc)/./lib/path.ts\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./styles/globals.css\");\n\n\n\n\n\n\n\n\nconst metadata = {\n    title: '沐光而行',\n    icons: 'favicon.icon'\n};\nasync function RootLayout({ children }) {\n    const locale = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n    const messages = await (0,next_intl_server__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: locale,\n        className: \"dark\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"head\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    src: (0,_lib_path__WEBPACK_IMPORTED_MODULE_3__.getSrcPath)('sentio/core/live2dcubismcore.min.js')\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\layout.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\layout.tsx\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                className: (0,clsx__WEBPACK_IMPORTED_MODULE_1__[\"default\"])((next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_7___default().className)),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_intl__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    messages: messages,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\layout.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\layout.tsx\",\n                        lineNumber: 33,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\layout.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\layout.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\layout.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/not-found.tsx":
/*!***************************!*\
  !*** ./app/not-found.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/api/link.js\");\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-center justify-center min-h-screen bg-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md p-6 bg-white rounded-lg shadow-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-3xl font-bold text-gray-800 mb-4\",\n                    children: \"Not Found\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\not-found.tsx\",\n                    lineNumber: 7,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg text-gray-600 mb-6\",\n                    children: \"Could not find requested resource\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\not-found.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: \"/\",\n                    className: \"inline-block px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-300\",\n                    children: \"Return Home\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\not-found.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\not-found.tsx\",\n            lineNumber: 6,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\not-found.tsx\",\n        lineNumber: 5,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbm90LWZvdW5kLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE0QjtBQUViLFNBQVNDO0lBQ3BCLHFCQUNJLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNYLDRFQUFDRDtZQUFJQyxXQUFVOzs4QkFDWCw4REFBQ0M7b0JBQUdELFdBQVU7OEJBQXdDOzs7Ozs7OEJBQ3RELDhEQUFDRTtvQkFBRUYsV0FBVTs4QkFBNkI7Ozs7Ozs4QkFDMUMsOERBQUNILGlEQUFJQTtvQkFBQ00sTUFBSztvQkFBSUgsV0FBVTs4QkFBNEc7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXJKIiwic291cmNlcyI6WyJEOlxcTExNXFxMZWFybmluZ1xcYWlfY29kaW5nXFxhbnl0aGluZ2NoYXRcXGxpdmVjaGF0XFxhcHBcXG5vdC1mb3VuZC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOb3RGb3VuZCgpIHtcbiAgICByZXR1cm4gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1pbi1oLXNjcmVlbiBiZy1ncmF5LTEwMFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1tZCBwLTYgYmctd2hpdGUgcm91bmRlZC1sZyBzaGFkb3ctbGdcIj5cbiAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRleHQtZ3JheS04MDAgbWItNFwiPk5vdCBGb3VuZDwvaDI+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1sZyB0ZXh0LWdyYXktNjAwIG1iLTZcIj5Db3VsZCBub3QgZmluZCByZXF1ZXN0ZWQgcmVzb3VyY2U8L3A+XG4gICAgICAgICAgICAgICAgPExpbmsgaHJlZj1cIi9cIiBjbGFzc05hbWU9XCJpbmxpbmUtYmxvY2sgcHgtNiBweS0yIGJnLWJsdWUtNTAwIHRleHQtd2hpdGUgcm91bmRlZC1sZyBob3ZlcjpiZy1ibHVlLTYwMCB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0zMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgUmV0dXJuIEhvbWVcbiAgICAgICAgICAgICAgICA8L0xpbms+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgKVxufSJdLCJuYW1lcyI6WyJMaW5rIiwiTm90Rm91bmQiLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsInAiLCJocmVmIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\LLM\\Learning\\ai_coding\\anythingchat\\livechat\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js\");\n/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);\n\nconst Providers = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(\nfunction() { throw new Error(\"Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n\"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\providers.tsx\",\n\"Providers\",\n);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvcHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBb0IiLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXGFwcFxccHJvdmlkZXJzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBhcHAvcHJvdmlkZXJzLnRzeFxuXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IEhlcm9VSVByb3ZpZGVyIH0gZnJvbSBcIkBoZXJvdWkvcmVhY3RcIjtcbmltcG9ydCB7IFRvYXN0UHJvdmlkZXIgfSBmcm9tIFwiQGhlcm91aS90b2FzdFwiO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xuaW1wb3J0IHsgVXNlclByb3ZpZGVyIH0gZnJvbSBcIkAvbGliL2NvbnRleHQvVXNlckNvbnRleHRcIjtcbi8vIGltcG9ydCB7VGhlbWVQcm92aWRlciBhcyBOZXh0VGhlbWVzUHJvdmlkZXJ9IGZyb20gXCJuZXh0LXRoZW1lc1wiO1xuXG5leHBvcnQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG5cbiAgcmV0dXJuIChcbiAgICA8SGVyb1VJUHJvdmlkZXIgbmF2aWdhdGU9e3JvdXRlci5wdXNofT5cbiAgICAgIHsvKiA8TmV4dFRoZW1lc1Byb3ZpZGVyIGF0dHJpYnV0ZT1cImNsYXNzXCIgZGVmYXVsdFRoZW1lPVwiZGFya1wiPiAqL31cbiAgICAgIDxUb2FzdFByb3ZpZGVyIHBsYWNlbWVudD1cInRvcC1yaWdodFwiIHRvYXN0UHJvcHM9e3sgdGltZW91dDogMzAwMCB9fSAvPlxuICAgICAgPFVzZXJQcm92aWRlcj5cbiAgICAgICAge2NoaWxkcmVufVxuICAgICAgPC9Vc2VyUHJvdmlkZXI+XG4gICAgICB7LyogPC9OZXh0VGhlbWVzUHJvdmlkZXI+ICovfVxuICAgIDwvSGVyb1VJUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/providers.tsx\n");

/***/ }),

/***/ "(rsc)/./i18n/config.ts":
/*!************************!*\
  !*** ./i18n/config.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLocale: () => (/* binding */ defaultLocale),\n/* harmony export */   locales: () => (/* binding */ locales)\n/* harmony export */ });\nconst locales = [\n    'zh',\n    'en'\n];\nconst defaultLocale = 'zh';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9pMThuL2NvbmZpZy50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUVPLE1BQU1BLFVBQVU7SUFBQztJQUFNO0NBQUssQ0FBVTtBQUN0QyxNQUFNQyxnQkFBd0IsS0FBSyIsInNvdXJjZXMiOlsiRDpcXExMTVxcTGVhcm5pbmdcXGFpX2NvZGluZ1xcYW55dGhpbmdjaGF0XFxsaXZlY2hhdFxcaTE4blxcY29uZmlnLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB0eXBlIExvY2FsZSA9ICh0eXBlb2YgbG9jYWxlcylbbnVtYmVyXTtcblxuZXhwb3J0IGNvbnN0IGxvY2FsZXMgPSBbJ3poJywgJ2VuJ10gYXMgY29uc3Q7XG5leHBvcnQgY29uc3QgZGVmYXVsdExvY2FsZTogTG9jYWxlID0gJ3poJzsiXSwibmFtZXMiOlsibG9jYWxlcyIsImRlZmF1bHRMb2NhbGUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./i18n/config.ts\n");

/***/ }),

/***/ "(rsc)/./i18n/request.ts":
/*!*************************!*\
  !*** ./i18n/request.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_intl_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-intl/server */ \"(rsc)/./node_modules/.pnpm/next-intl@3.23.5_next@15.0._fbda60b06d7929e72c7e0b0ddfa53d11/node_modules/next-intl/dist/esm/server/react-server/getRequestConfig.js\");\n/* harmony import */ var _lib_locale__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/locale */ \"(rsc)/./lib/locale.ts\");\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_intl_server__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(async ()=>{\n    const locale = await (0,_lib_locale__WEBPACK_IMPORTED_MODULE_0__.getUserLocale)();\n    return {\n        locale,\n        messages: (await __webpack_require__(\"(rsc)/./i18n/locales lazy recursive ^\\\\.\\\\/.*\\\\.json$\")(`./${locale}.json`)).default\n    };\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9pMThuL3JlcXVlc3QudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWtEO0FBQ1A7QUFFM0MsaUVBQWVBLDREQUFnQkEsQ0FBQztJQUM5QixNQUFNRSxTQUFTLE1BQU1ELDBEQUFhQTtJQUVsQyxPQUFPO1FBQ0xDO1FBQ0FDLFVBQVUsQ0FBQyxNQUFNLDZFQUFPLEdBQVcsRUFBRUQsT0FBTyxNQUFNLEdBQUdFLE9BQU87SUFDOUQ7QUFDRixFQUFFLEVBQUMiLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXGkxOG5cXHJlcXVlc3QudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHtnZXRSZXF1ZXN0Q29uZmlnfSBmcm9tICduZXh0LWludGwvc2VydmVyJztcbmltcG9ydCB7Z2V0VXNlckxvY2FsZX0gZnJvbSAnQC9saWIvbG9jYWxlJztcblxuZXhwb3J0IGRlZmF1bHQgZ2V0UmVxdWVzdENvbmZpZyhhc3luYyAoKSA9PiB7XG4gIGNvbnN0IGxvY2FsZSA9IGF3YWl0IGdldFVzZXJMb2NhbGUoKTtcblxuICByZXR1cm4ge1xuICAgIGxvY2FsZSxcbiAgICBtZXNzYWdlczogKGF3YWl0IGltcG9ydChgLi9sb2NhbGVzLyR7bG9jYWxlfS5qc29uYCkpLmRlZmF1bHRcbiAgfTtcbn0pOyJdLCJuYW1lcyI6WyJnZXRSZXF1ZXN0Q29uZmlnIiwiZ2V0VXNlckxvY2FsZSIsImxvY2FsZSIsIm1lc3NhZ2VzIiwiZGVmYXVsdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./i18n/request.ts\n");

/***/ }),

/***/ "(rsc)/./lib/constants.ts":
/*!**************************!*\
  !*** ./lib/constants.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ABOUT_US: () => (/* binding */ ABOUT_US),\n/* harmony export */   BUSINESS_COOPERATION_URL: () => (/* binding */ BUSINESS_COOPERATION_URL),\n/* harmony export */   DROPDOWN_DELAY: () => (/* binding */ DROPDOWN_DELAY),\n/* harmony export */   FEEDBACK_URL: () => (/* binding */ FEEDBACK_URL),\n/* harmony export */   JOIN_US_URL: () => (/* binding */ JOIN_US_URL),\n/* harmony export */   LANUSAGE_EN: () => (/* binding */ LANUSAGE_EN),\n/* harmony export */   LANUSAGE_ZH: () => (/* binding */ LANUSAGE_ZH),\n/* harmony export */   PUBLIC_SRC_PATH: () => (/* binding */ PUBLIC_SRC_PATH),\n/* harmony export */   SENTIO_BACKGROUND_DYNAMIC_IMAGES: () => (/* binding */ SENTIO_BACKGROUND_DYNAMIC_IMAGES),\n/* harmony export */   SENTIO_BACKGROUND_DYNAMIC_PATH: () => (/* binding */ SENTIO_BACKGROUND_DYNAMIC_PATH),\n/* harmony export */   SENTIO_BACKGROUND_PATH: () => (/* binding */ SENTIO_BACKGROUND_PATH),\n/* harmony export */   SENTIO_BACKGROUND_STATIC_IMAGES: () => (/* binding */ SENTIO_BACKGROUND_STATIC_IMAGES),\n/* harmony export */   SENTIO_BACKGROUND_STATIC_PATH: () => (/* binding */ SENTIO_BACKGROUND_STATIC_PATH),\n/* harmony export */   SENTIO_CHARACTER_DEFAULT: () => (/* binding */ SENTIO_CHARACTER_DEFAULT),\n/* harmony export */   SENTIO_CHARACTER_DEFAULT_PORTRAIT: () => (/* binding */ SENTIO_CHARACTER_DEFAULT_PORTRAIT),\n/* harmony export */   SENTIO_CHARACTER_FREE_MODELS: () => (/* binding */ SENTIO_CHARACTER_FREE_MODELS),\n/* harmony export */   SENTIO_CHARACTER_FREE_PATH: () => (/* binding */ SENTIO_CHARACTER_FREE_PATH),\n/* harmony export */   SENTIO_CHARACTER_IP_MODELS: () => (/* binding */ SENTIO_CHARACTER_IP_MODELS),\n/* harmony export */   SENTIO_CHARACTER_IP_PATH: () => (/* binding */ SENTIO_CHARACTER_IP_PATH),\n/* harmony export */   SENTIO_CHARACTER_PATH: () => (/* binding */ SENTIO_CHARACTER_PATH),\n/* harmony export */   SENTIO_CHATMODE_DEFULT: () => (/* binding */ SENTIO_CHATMODE_DEFULT),\n/* harmony export */   SENTIO_GITHUB_URL: () => (/* binding */ SENTIO_GITHUB_URL),\n/* harmony export */   SENTIO_GUIDE_URL: () => (/* binding */ SENTIO_GUIDE_URL),\n/* harmony export */   SENTIO_LIPFACTOR_DEFAULT: () => (/* binding */ SENTIO_LIPFACTOR_DEFAULT),\n/* harmony export */   SENTIO_LIPFACTOR_MAX: () => (/* binding */ SENTIO_LIPFACTOR_MAX),\n/* harmony export */   SENTIO_LIPFACTOR_MIN: () => (/* binding */ SENTIO_LIPFACTOR_MIN),\n/* harmony export */   SENTIO_RECODER_MAX_TIME: () => (/* binding */ SENTIO_RECODER_MAX_TIME),\n/* harmony export */   SENTIO_RECODER_MIN_TIME: () => (/* binding */ SENTIO_RECODER_MIN_TIME),\n/* harmony export */   SENTIO_THENE_DEFAULT: () => (/* binding */ SENTIO_THENE_DEFAULT),\n/* harmony export */   SENTIO_TTS_PUNC: () => (/* binding */ SENTIO_TTS_PUNC),\n/* harmony export */   SENTIO_TTS_SENTENCE_LENGTH_MIN: () => (/* binding */ SENTIO_TTS_SENTENCE_LENGTH_MIN),\n/* harmony export */   SENTIO_VOICE_TEST_EN: () => (/* binding */ SENTIO_VOICE_TEST_EN),\n/* harmony export */   SENTIO_VOICE_TEST_ZH: () => (/* binding */ SENTIO_VOICE_TEST_ZH)\n/* harmony export */ });\n/* harmony import */ var _protocol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./protocol */ \"(rsc)/./lib/protocol.ts\");\n\nconst DROPDOWN_DELAY = 500;\nconst PUBLIC_SRC_PATH = \"/\";\n// 关于\nconst ABOUT_US = `\n每天我们都在探索AI如何改变世界，然而这个世界真正的不同是我们每个人看待世界的内心不同。在这样一个科技爆炸的时代里，似乎给AI提供各种提示词的人类才更像是活在提示词下的机器，但每一个你作为生命独一无二的个体，需要的从来都不是AI在统计学下计算的概率结果，而是对这个世界每一次探索的体验。我们想AI不是为了取代工作，不是为了危言耸听，更不是为了制造焦虑，或许AI也能在这感性的世界里让人们更加热爱这个世界？\n我们将聚焦于AI如何在一个快节奏的时代放大人们对世界每一次探索的体验。当你想要看一朵花时，AI也可以在生成之后告诉你今天阳光正好，温度适宜，门外公园的一朵小野花正在微风中摇曳盛开。(于是你推开门走向了世界)\nAI是爱的中文拼音，AI不是你需要的全部，对这个世界的热爱才是你需要的全部。\n愿你沐光而行。\n`;\n// 语言选项\nconst LANUSAGE_ZH = \"中文\";\nconst LANUSAGE_EN = \"English\";\n// url\nconst BUSINESS_COOPERATION_URL = \"https://light4ai.feishu.cn/share/base/form/shrcnb0d1Au4dvMaswHNGDbUNTR\";\nconst JOIN_US_URL = \"https://light4ai.feishu.cn/share/base/form/shrcn5zSQvM4c8kjK69LXvtdgqh\";\nconst FEEDBACK_URL = \"https://light4ai.feishu.cn/share/base/form/shrcnL4pzrdKlED6oB94nT9Yiyg\";\n// sentio\nconst SENTIO_GUIDE_URL = \"https://light4ai.feishu.cn/docx/XmGFd5QJwoBdDox8M7zcAcRJnje\";\nconst SENTIO_GITHUB_URL = \"https://github.com/wan-h/awesome-digital-human-live2d\";\nconst SENTIO_BACKGROUND_PATH = \"sentio/backgrounds/\";\nconst SENTIO_BACKGROUND_STATIC_PATH = \"sentio/backgrounds/static\";\nconst SENTIO_BACKGROUND_DYNAMIC_PATH = \"sentio/backgrounds/dynamic\";\nconst SENTIO_BACKGROUND_STATIC_IMAGES = [\n    \"夜晚街道.jpg\",\n    \"赛博朋克.jpg\",\n    \"火影忍者.jpg\",\n    \"插画.jpg\",\n    \"艺术.jpg\",\n    \"简约.jpg\",\n    \"抽象.jpg\"\n];\nconst SENTIO_BACKGROUND_DYNAMIC_IMAGES = [\n    \"太空站.mp4\",\n    \"赛博朋克.mp4\",\n    \"可爱城市.mp4\",\n    \"悟空.mp4\",\n    \"火影忍者.mp4\",\n    \"几何线条.mp4\",\n    \"公式.mp4\"\n];\nconst SENTIO_CHARACTER_PATH = \"sentio/characters/\";\nconst SENTIO_CHARACTER_IP_PATH = \"sentio/characters/ip\";\nconst SENTIO_CHARACTER_FREE_PATH = \"sentio/characters/free\";\nconst SENTIO_CHARACTER_IP_MODELS = [];\nconst SENTIO_CHARACTER_FREE_MODELS = [\n    \"HaruGreeter\",\n    \"Haru\",\n    \"Kei\",\n    \"Chitose\",\n    \"Epsilon\",\n    \"Hibiki\",\n    \"Hiyori\",\n    \"Izumi\",\n    \"Mao\",\n    \"Rice\",\n    \"Shizuku\",\n    \"Tsumiki\"\n];\nconst SENTIO_CHARACTER_DEFAULT = \"HaruGreeter\";\nconst SENTIO_CHARACTER_DEFAULT_PORTRAIT = `${SENTIO_CHARACTER_FREE_PATH}/${SENTIO_CHARACTER_DEFAULT}/${SENTIO_CHARACTER_DEFAULT}.png`;\nconst SENTIO_TTS_PUNC = [\n    '；',\n    '！',\n    '？',\n    '。',\n    '?'\n];\nconst SENTIO_TTS_SENTENCE_LENGTH_MIN = 6;\nconst SENTIO_RECODER_MIN_TIME = 1000 // 1s\n;\nconst SENTIO_RECODER_MAX_TIME = 30000 // 30s\n;\nconst SENTIO_LIPFACTOR_MIN = 0.0;\nconst SENTIO_LIPFACTOR_DEFAULT = 5.0;\nconst SENTIO_LIPFACTOR_MAX = 10.0;\nconst SENTIO_VOICE_TEST_ZH = [\n    \"今天最浪漫的事就是遇见你。\",\n    \"你有百般模样，我也会百般喜欢。\",\n    \"这里什么都好，因为这就是你。\"\n];\nconst SENTIO_VOICE_TEST_EN = [\n    \"Someone said you were looking for me?\"\n];\nconst SENTIO_CHATMODE_DEFULT = _protocol__WEBPACK_IMPORTED_MODULE_0__.CHAT_MODE.DIALOGUE;\nconst SENTIO_THENE_DEFAULT = _protocol__WEBPACK_IMPORTED_MODULE_0__.APP_TYPE.FREEDOM;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/constants.ts\n");

/***/ }),

/***/ "(rsc)/./lib/locale.ts":
/*!***********************!*\
  !*** ./lib/locale.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getUserLocale: () => (/* binding */ getUserLocale),\n/* harmony export */   setUserLocale: () => (/* binding */ setUserLocale)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-server-reference */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! private-next-rsc-action-encryption */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/app-render/encryption.js\");\n/* harmony import */ var private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_encryption__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _i18n_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/i18n/config */ \"(rsc)/./i18n/config.ts\");\n/* harmony import */ var private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-rsc-action-validate */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js\");\n/* __next_internal_action_entry_do_not_use__ {\"00710cb852dc46949f6337cc8e295416e52998adcf\":\"getUserLocale\",\"400e6bc43a9f5734884e1c8701d037dde4cf563a27\":\"setUserLocale\"} */ \n\n\n\n// In this example the locale is read from a cookie. You could alternatively\n// also read it from a database, backend service, or any other source.\nconst COOKIE_NAME = 'NEXT_LOCALE';\nasync function getUserLocale() {\n    const c = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n    const cookieLocale = c.get(COOKIE_NAME)?.value;\n    if (!cookieLocale) {\n        return _i18n_config__WEBPACK_IMPORTED_MODULE_3__.defaultLocale;\n    }\n    // Use type guard to ensure type safety\n    const isValidLocale = (locale)=>{\n        return _i18n_config__WEBPACK_IMPORTED_MODULE_3__.locales.includes(locale);\n    };\n    return isValidLocale(cookieLocale) ? cookieLocale : _i18n_config__WEBPACK_IMPORTED_MODULE_3__.defaultLocale;\n}\nasync function setUserLocale(locale) {\n    const c = await (0,next_headers__WEBPACK_IMPORTED_MODULE_2__.cookies)();\n    c.set(COOKIE_NAME, locale);\n}\n\n(0,private_next_rsc_action_validate__WEBPACK_IMPORTED_MODULE_4__.ensureServerEntryExports)([\n    getUserLocale,\n    setUserLocale\n]);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(getUserLocale, \"00710cb852dc46949f6337cc8e295416e52998adcf\", null);\n(0,private_next_rsc_server_reference__WEBPACK_IMPORTED_MODULE_0__.registerServerReference)(setUserLocale, \"400e6bc43a9f5734884e1c8701d037dde4cf563a27\", null);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/locale.ts\n");

/***/ }),

/***/ "(rsc)/./lib/path.ts":
/*!*********************!*\
  !*** ./lib/path.ts ***!
  \*********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSrcPath: () => (/* binding */ getSrcPath)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"(rsc)/./lib/constants.ts\");\n\nfunction getSrcPath(src) {\n    return `${_constants__WEBPACK_IMPORTED_MODULE_0__.PUBLIC_SRC_PATH}${src}`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvcGF0aC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUE4QztBQUV2QyxTQUFTQyxXQUFXQyxHQUFXO0lBQ3BDLE9BQU8sR0FBR0YsdURBQWVBLEdBQUdFLEtBQUs7QUFDbkMiLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXGxpYlxccGF0aC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBQVUJMSUNfU1JDX1BBVEggfSBmcm9tIFwiLi9jb25zdGFudHNcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIGdldFNyY1BhdGgoc3JjOiBzdHJpbmcpIHtcbiAgcmV0dXJuIGAke1BVQkxJQ19TUkNfUEFUSH0ke3NyY31gO1xufSJdLCJuYW1lcyI6WyJQVUJMSUNfU1JDX1BBVEgiLCJnZXRTcmNQYXRoIiwic3JjIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/path.ts\n");

/***/ }),

/***/ "(rsc)/./lib/protocol.ts":
/*!*************************!*\
  !*** ./lib/protocol.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   APP_TYPE: () => (/* binding */ APP_TYPE),\n/* harmony export */   AUDIO_TYPE: () => (/* binding */ AUDIO_TYPE),\n/* harmony export */   BACKGROUND_TYPE: () => (/* binding */ BACKGROUND_TYPE),\n/* harmony export */   CHARACTER_TYPE: () => (/* binding */ CHARACTER_TYPE),\n/* harmony export */   CHAT_MODE: () => (/* binding */ CHAT_MODE),\n/* harmony export */   CHAT_ROLE: () => (/* binding */ CHAT_ROLE),\n/* harmony export */   ENGINE_TYPE: () => (/* binding */ ENGINE_TYPE),\n/* harmony export */   GENDER_TYPE: () => (/* binding */ GENDER_TYPE),\n/* harmony export */   IFER_TYPE: () => (/* binding */ IFER_TYPE),\n/* harmony export */   PARAM_TYPE: () => (/* binding */ PARAM_TYPE),\n/* harmony export */   RESOURCE_TYPE: () => (/* binding */ RESOURCE_TYPE),\n/* harmony export */   STREAMING_EVENT_TYPE: () => (/* binding */ STREAMING_EVENT_TYPE)\n/* harmony export */ });\n// 枚举\nvar ENGINE_TYPE = /*#__PURE__*/ function(ENGINE_TYPE) {\n    ENGINE_TYPE[\"ASR\"] = \"ASR\";\n    ENGINE_TYPE[\"TTS\"] = \"TTS\";\n    ENGINE_TYPE[\"LLM\"] = \"LLM\";\n    ENGINE_TYPE[\"AGENT\"] = \"AGENT\";\n    return ENGINE_TYPE;\n}({});\nvar IFER_TYPE = /*#__PURE__*/ function(IFER_TYPE) {\n    IFER_TYPE[\"NORMAL\"] = \"normal\";\n    IFER_TYPE[\"STREAM\"] = \"stream\";\n    return IFER_TYPE;\n}({});\nvar PARAM_TYPE = /*#__PURE__*/ function(PARAM_TYPE) {\n    PARAM_TYPE[\"STRING\"] = \"string\";\n    PARAM_TYPE[\"INT\"] = \"int\";\n    PARAM_TYPE[\"FLOAT\"] = \"float\";\n    PARAM_TYPE[\"BOOL\"] = \"bool\";\n    return PARAM_TYPE;\n}({});\nvar GENDER_TYPE = /*#__PURE__*/ function(GENDER_TYPE) {\n    GENDER_TYPE[\"MALE\"] = \"MALE\";\n    GENDER_TYPE[\"FEMALE\"] = \"FEMALE\";\n    return GENDER_TYPE;\n}({});\nvar BACKGROUND_TYPE = /*#__PURE__*/ function(BACKGROUND_TYPE) {\n    BACKGROUND_TYPE[\"STATIC\"] = \"STATIC\";\n    BACKGROUND_TYPE[\"DYNAMIC\"] = \"DYNAMIC\";\n    BACKGROUND_TYPE[\"CUSTOM\"] = \"CUSTOM\";\n    BACKGROUND_TYPE[\"ALL\"] = \"ALL\";\n    return BACKGROUND_TYPE;\n}({});\nvar CHARACTER_TYPE = /*#__PURE__*/ function(CHARACTER_TYPE) {\n    CHARACTER_TYPE[\"IP\"] = \"IP\";\n    CHARACTER_TYPE[\"FREE\"] = \"FREE\";\n    CHARACTER_TYPE[\"CUSTOM\"] = \"CUSTOM\";\n    CHARACTER_TYPE[\"ALL\"] = \"ALL\";\n    return CHARACTER_TYPE;\n}({});\nvar APP_TYPE = /*#__PURE__*/ function(APP_TYPE) {\n    APP_TYPE[\"FREEDOM\"] = \"Freedom\";\n    return APP_TYPE;\n}({});\nvar CHAT_ROLE = /*#__PURE__*/ function(CHAT_ROLE) {\n    CHAT_ROLE[\"HUMAN\"] = \"HUMAN\";\n    CHAT_ROLE[\"AI\"] = \"AI\";\n    return CHAT_ROLE;\n}({});\nvar CHAT_MODE = /*#__PURE__*/ function(CHAT_MODE) {\n    CHAT_MODE[\"DIALOGUE\"] = \"DIALOGUE\";\n    CHAT_MODE[\"IMMSERSIVE\"] = \"IMMSERSIVE\";\n    return CHAT_MODE;\n}({});\nvar AUDIO_TYPE = /*#__PURE__*/ function(AUDIO_TYPE) {\n    AUDIO_TYPE[\"MP3\"] = \"mp3\";\n    AUDIO_TYPE[\"WAV\"] = \"wav\";\n    return AUDIO_TYPE;\n}({});\nvar STREAMING_EVENT_TYPE = /*#__PURE__*/ function(STREAMING_EVENT_TYPE) {\n    STREAMING_EVENT_TYPE[\"CONVERSATION_ID\"] = \"CONVERSATION_ID\";\n    STREAMING_EVENT_TYPE[\"MESSAGE_ID\"] = \"MESSAGE_ID\";\n    STREAMING_EVENT_TYPE[\"THINK\"] = \"THINK\";\n    STREAMING_EVENT_TYPE[\"TEXT\"] = \"TEXT\";\n    STREAMING_EVENT_TYPE[\"TASK\"] = \"TASK\";\n    STREAMING_EVENT_TYPE[\"DONE\"] = \"DONE\";\n    STREAMING_EVENT_TYPE[\"ERROR\"] = \"ERROR\";\n    return STREAMING_EVENT_TYPE;\n}({});\nvar RESOURCE_TYPE = /*#__PURE__*/ function(RESOURCE_TYPE) {\n    RESOURCE_TYPE[\"BACKGROUND\"] = \"background\";\n    RESOURCE_TYPE[\"CHARACTER\"] = \"character\";\n    RESOURCE_TYPE[\"ICON\"] = \"icon\";\n    return RESOURCE_TYPE;\n}({});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/protocol.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"256x256\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmV4dEAxNS4wLjNfQGJhYmVsK2NvcmVANy4yX2JlYTRiOGY4NjcwZDgxNDg3M2VmYjY5YWUxYmJlM2U1L25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtbWV0YWRhdGEtaW1hZ2UtbG9hZGVyLmpzP3R5cGU9ZmF2aWNvbiZzZWdtZW50PSZiYXNlUGF0aD0mcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyEuL2FwcC9mYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSxFQUFpRjs7QUFFakYsRUFBRSxpRUFBZTtBQUNqQix1QkFBdUI7QUFDdkIscUJBQXFCLDhGQUFtQjs7QUFFeEM7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMIiwic291cmNlcyI6WyJEOlxcTExNXFxMZWFybmluZ1xcYWlfY29kaW5nXFxhbnl0aGluZ2NoYXRcXGxpdmVjaGF0XFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjI1NngyNTZcIn1cbiAgICBjb25zdCBpbWFnZVVybCA9IGZpbGxNZXRhZGF0YVNlZ21lbnQoXCIuXCIsIGF3YWl0IHByb3BzLnBhcmFtcywgXCJmYXZpY29uLmljb1wiKVxuXG4gICAgcmV0dXJuIFt7XG4gICAgICAuLi5pbWFnZURhdGEsXG4gICAgICB1cmw6IGltYWdlVXJsICsgXCJcIixcbiAgICB9XVxuICB9Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/framer-motion@11.11.17_reac_abbc4774c585884cba6a3ef630bc223c","vendor-chunks/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5","vendor-chunks/@heroui+toast@2.0.6_@heroui_99af8d3b985385384198473e3104e5b8","vendor-chunks/@formatjs+icu-messageformat-parser@2.9.1","vendor-chunks/@heroui+theme@2.4.12_tailwindcss@3.4.14","vendor-chunks/mime-db@1.52.0","vendor-chunks/r2r-js@0.4.43","vendor-chunks/axios@1.10.0","vendor-chunks/@react-aria+interactions@3._f4c4e47fdd38dbbc2317f9013c56c4ca","vendor-chunks/@react-aria+overlays@3.26.0_f683e2f5d8a049041921877a4d68e6f4","vendor-chunks/tailwind-merge@2.5.4","vendor-chunks/use-intl@3.23.5_react@19.0.0-rc-69d4b800-20241021","vendor-chunks/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d","vendor-chunks/intl-messageformat@10.7.3","vendor-chunks/tslib@2.8.0","vendor-chunks/@formatjs+icu-skeleton-parser@1.8.5","vendor-chunks/@react-aria+toast@3.0.0-bet_f355aa234231811d8d70f972d8cde058","vendor-chunks/follow-redirects@1.15.9","vendor-chunks/uuid@10.0.0","vendor-chunks/@react-aria+landmark@3.0.0-_fc68496ff6799296b90d71a4420b6356","vendor-chunks/debug@4.3.7","vendor-chunks/form-data@4.0.4","vendor-chunks/@react-aria+i18n@3.12.6_rea_7cd1cdf6254dc58bdf128724b391fcad","vendor-chunks/get-intrinsic@1.3.0","vendor-chunks/next-intl@3.23.5_next@15.0._fbda60b06d7929e72c7e0b0ddfa53d11","vendor-chunks/@react-aria+i18n@3.12.7_rea_e4b9ec774c1595ed697c15570238dc19","vendor-chunks/@heroui+shared-utils@2.1.7","vendor-chunks/@react-aria+ssr@3.9.7_react@19.0.0-rc-69d4b800-20241021","vendor-chunks/asynckit@0.4.0","vendor-chunks/@heroui+spinner@2.2.13_@her_9aabee10e0b998f909d9a94e1b2a40f5","vendor-chunks/@heroui+shared-icons@2.1.6_react@19.0.0-rc-69d4b800-20241021","vendor-chunks/@react-stately+toast@3.0.0-_d4a8f27530a9e613b5c891bcd4b4a5fb","vendor-chunks/@internationalized+string@3.2.5","vendor-chunks/@heroui+react-utils@2.1.8_react@19.0.0-rc-69d4b800-20241021","vendor-chunks/tailwind-variants@0.3.0_tailwindcss@3.4.14","vendor-chunks/@formatjs+fast-memoize@2.2.2","vendor-chunks/@heroui+react-rsc-utils@2.1_51d4c870eb3b4610a182923b679d4482","vendor-chunks/combined-stream@1.0.8","vendor-chunks/@swc+helpers@0.5.13","vendor-chunks/use-sync-external-store@1.4_06df1f58d3499d54e9960e81cb270864","vendor-chunks/mime-types@2.1.35","vendor-chunks/@heroui+system@2.4.12_@hero_6d545e84cf25acf49df02fdc8d5c89ed","vendor-chunks/proxy-from-env@1.1.0","vendor-chunks/ms@2.1.3","vendor-chunks/supports-color@7.2.0","vendor-chunks/has-symbols@1.1.0","vendor-chunks/delayed-stream@1.0.0","vendor-chunks/function-bind@1.1.2","vendor-chunks/@heroui+system-rsc@2.3.11_@_437cb1bf856c2b4989e527bf94992063","vendor-chunks/has-symbols@1.0.3","vendor-chunks/@react-stately+flags@3.1.0","vendor-chunks/es-set-tostringtag@2.1.0","vendor-chunks/get-proto@1.0.1","vendor-chunks/call-bind-apply-helpers@1.0.2","vendor-chunks/dunder-proto@1.0.1","vendor-chunks/math-intrinsics@1.1.0","vendor-chunks/clsx@2.1.1","vendor-chunks/es-errors@1.3.0","vendor-chunks/has-flag@4.0.0","vendor-chunks/gopd@1.2.0","vendor-chunks/@heroui+use-is-mobile@2.2.7_866d8f49aa3d9a6c0b0767b54d20a60c","vendor-chunks/es-define-property@1.0.1","vendor-chunks/hasown@2.0.2","vendor-chunks/has-tostringtag@1.0.2","vendor-chunks/es-object-atoms@1.1.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=D%3A%5CLLM%5CLearning%5Cai_coding%5Canythingchat%5Clivechat%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CLLM%5CLearning%5Cai_coding%5Canythingchat%5Clivechat&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();