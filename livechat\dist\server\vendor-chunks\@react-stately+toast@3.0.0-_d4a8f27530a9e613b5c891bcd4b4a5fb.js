"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-stately+toast@3.0.0-_d4a8f27530a9e613b5c891bcd4b4a5fb";
exports.ids = ["vendor-chunks/@react-stately+toast@3.0.0-_d4a8f27530a9e613b5c891bcd4b4a5fb"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+toast@3.0.0-_d4a8f27530a9e613b5c891bcd4b4a5fb/node_modules/@react-stately/toast/dist/useToastState.mjs":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+toast@3.0.0-_d4a8f27530a9e613b5c891bcd4b4a5fb/node_modules/@react-stately/toast/dist/useToastState.mjs ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastQueue: () => (/* binding */ $77b352cf12efcf73$export$f1f8569633bbbec4),\n/* harmony export */   useToastQueue: () => (/* binding */ $77b352cf12efcf73$export$84726ef35ca2129a),\n/* harmony export */   useToastState: () => (/* binding */ $77b352cf12efcf73$export$c7b26b20d3ced9c5)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/.pnpm/use-sync-external-store@1.4_06df1f58d3499d54e9960e81cb270864/node_modules/use-sync-external-store/shim/index.js\");\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\nfunction $77b352cf12efcf73$export$c7b26b20d3ced9c5(props = {}) {\n    let { maxVisibleToasts: maxVisibleToasts = 1, hasExitAnimation: hasExitAnimation = false } = props;\n    let queue = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>new $77b352cf12efcf73$export$f1f8569633bbbec4({\n            maxVisibleToasts: maxVisibleToasts,\n            hasExitAnimation: hasExitAnimation\n        }), [\n        maxVisibleToasts,\n        hasExitAnimation\n    ]);\n    return $77b352cf12efcf73$export$84726ef35ca2129a(queue);\n}\nfunction $77b352cf12efcf73$export$84726ef35ca2129a(queue) {\n    let subscribe = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((fn)=>queue.subscribe(fn), [\n        queue\n    ]);\n    let getSnapshot = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>queue.visibleToasts, [\n        queue\n    ]);\n    let visibleToasts = (0, use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_1__.useSyncExternalStore)(subscribe, getSnapshot, getSnapshot);\n    return {\n        visibleToasts: visibleToasts,\n        add: (content, options)=>queue.add(content, options),\n        close: (key)=>queue.close(key),\n        remove: (key)=>queue.remove(key),\n        pauseAll: ()=>queue.pauseAll(),\n        resumeAll: ()=>queue.resumeAll()\n    };\n}\nclass $77b352cf12efcf73$export$f1f8569633bbbec4 {\n    /** Subscribes to updates to the visible toasts. */ subscribe(fn) {\n        this.subscriptions.add(fn);\n        return ()=>this.subscriptions.delete(fn);\n    }\n    /** Adds a new toast to the queue. */ add(content, options = {}) {\n        let toastKey = Math.random().toString(36);\n        let toast = {\n            ...options,\n            content: content,\n            key: toastKey,\n            timer: options.timeout ? new $77b352cf12efcf73$var$Timer(()=>this.close(toastKey), options.timeout) : undefined\n        };\n        let low = 0;\n        let high = this.queue.length;\n        while(low < high){\n            let mid = Math.floor((low + high) / 2);\n            if ((toast.priority || 0) > (this.queue[mid].priority || 0)) high = mid;\n            else low = mid + 1;\n        }\n        this.queue.splice(low, 0, toast);\n        toast.animation = low < this.maxVisibleToasts ? 'entering' : 'queued';\n        let i = this.maxVisibleToasts;\n        while(i < this.queue.length)this.queue[i++].animation = 'queued';\n        this.updateVisibleToasts({\n            action: 'add'\n        });\n        return toastKey;\n    }\n    /**\n   * Closes a toast. If `hasExitAnimation` is true, the toast\n   * transitions to an \"exiting\" state instead of being removed immediately.\n   */ close(key) {\n        let index = this.queue.findIndex((t)=>t.key === key);\n        if (index >= 0) {\n            var _this_queue_index_onClose, _this_queue_index;\n            (_this_queue_index_onClose = (_this_queue_index = this.queue[index]).onClose) === null || _this_queue_index_onClose === void 0 ? void 0 : _this_queue_index_onClose.call(_this_queue_index);\n            this.queue.splice(index, 1);\n        }\n        this.updateVisibleToasts({\n            action: 'close',\n            key: key\n        });\n    }\n    /** Removes a toast from the visible toasts after an exiting animation. */ remove(key) {\n        this.updateVisibleToasts({\n            action: 'remove',\n            key: key\n        });\n    }\n    updateVisibleToasts(options) {\n        let { action: action, key: key } = options;\n        let toasts = this.queue.slice(0, this.maxVisibleToasts);\n        if (action === 'add' && this.hasExitAnimation) {\n            let prevToasts = this.visibleToasts.filter((t)=>!toasts.some((t2)=>t.key === t2.key)).map((t)=>({\n                    ...t,\n                    animation: 'exiting'\n                }));\n            this.visibleToasts = prevToasts.concat(toasts).sort((a, b)=>{\n                var _b_priority, _a_priority;\n                return ((_b_priority = b.priority) !== null && _b_priority !== void 0 ? _b_priority : 0) - ((_a_priority = a.priority) !== null && _a_priority !== void 0 ? _a_priority : 0);\n            });\n        } else if (action === 'close' && this.hasExitAnimation) // Cause a rerender to happen for exit animation\n        this.visibleToasts = this.visibleToasts.map((t)=>{\n            if (t.key !== key) return t;\n            else return {\n                ...t,\n                animation: 'exiting'\n            };\n        });\n        else this.visibleToasts = toasts;\n        for (let fn of this.subscriptions)fn();\n    }\n    /** Pauses the timers for all visible toasts. */ pauseAll() {\n        for (let toast of this.visibleToasts)if (toast.timer) toast.timer.pause();\n    }\n    /** Resumes the timers for all visible toasts. */ resumeAll() {\n        for (let toast of this.visibleToasts)if (toast.timer) toast.timer.resume();\n    }\n    constructor(options){\n        this.queue = [];\n        this.subscriptions = new Set();\n        /** The currently visible toasts. */ this.visibleToasts = [];\n        var _options_maxVisibleToasts;\n        this.maxVisibleToasts = (_options_maxVisibleToasts = options === null || options === void 0 ? void 0 : options.maxVisibleToasts) !== null && _options_maxVisibleToasts !== void 0 ? _options_maxVisibleToasts : 1;\n        var _options_hasExitAnimation;\n        this.hasExitAnimation = (_options_hasExitAnimation = options === null || options === void 0 ? void 0 : options.hasExitAnimation) !== null && _options_hasExitAnimation !== void 0 ? _options_hasExitAnimation : false;\n    }\n}\nclass $77b352cf12efcf73$var$Timer {\n    reset(delay) {\n        this.remaining = delay;\n        this.resume();\n    }\n    pause() {\n        if (this.timerId == null) return;\n        clearTimeout(this.timerId);\n        this.timerId = null;\n        this.remaining -= Date.now() - this.startTime;\n    }\n    resume() {\n        if (this.remaining <= 0) return;\n        this.startTime = Date.now();\n        this.timerId = setTimeout(()=>{\n            this.timerId = null;\n            this.remaining = 0;\n            this.callback();\n        }, this.remaining);\n    }\n    constructor(callback, delay){\n        this.startTime = null;\n        this.remaining = delay;\n        this.callback = callback;\n    }\n}\n\n\n\n//# sourceMappingURL=useToastState.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+toast@3.0.0-_d4a8f27530a9e613b5c891bcd4b4a5fb/node_modules/@react-stately/toast/dist/useToastState.mjs\n");

/***/ })

};
;