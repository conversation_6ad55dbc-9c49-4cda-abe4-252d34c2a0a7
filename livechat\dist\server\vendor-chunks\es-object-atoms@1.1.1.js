"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/es-object-atoms@1.1.1";
exports.ids = ["vendor-chunks/es-object-atoms@1.1.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/es-object-atoms@1.1.1/node_modules/es-object-atoms/index.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/es-object-atoms@1.1.1/node_modules/es-object-atoms/index.js ***!
  \****************************************************************************************/
/***/ ((module) => {

eval("\n\n/** @type {import('.')} */\nmodule.exports = Object;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vZXMtb2JqZWN0LWF0b21zQDEuMS4xL25vZGVfbW9kdWxlcy9lcy1vYmplY3QtYXRvbXMvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsV0FBVyxhQUFhO0FBQ3hCIiwic291cmNlcyI6WyJEOlxcTExNXFxMZWFybmluZ1xcYWlfY29kaW5nXFxhbnl0aGluZ2NoYXRcXGxpdmVjaGF0XFxub2RlX21vZHVsZXNcXC5wbnBtXFxlcy1vYmplY3QtYXRvbXNAMS4xLjFcXG5vZGVfbW9kdWxlc1xcZXMtb2JqZWN0LWF0b21zXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbi8qKiBAdHlwZSB7aW1wb3J0KCcuJyl9ICovXG5tb2R1bGUuZXhwb3J0cyA9IE9iamVjdDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/es-object-atoms@1.1.1/node_modules/es-object-atoms/index.js\n");

/***/ })

};
;