{"Version": 3, "Meta": {"Duration": 1.57, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 22, "TotalSegmentCount": 72, "TotalPointCount": 186, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 29, 1, 0.111, 29, 0.222, 26, 0.333, 26, 1, 0.433, 26, 0.533, 28, 0.633, 28, 1, 0.7, 28, 0.767, 23, 0.833, 23, 1, 0.9, 23, 0.967, 27.081, 1.033, 28.68, 1, 1.122, 30.813, 1.211, 31, 1.3, 31, 0, 1.567, 31]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -9, 1, 0.111, -9, 0.222, 30, 0.333, 30, 1, 0.433, 30, 0.533, 0, 0.633, 0, 1, 0.7, 0, 0.767, 28, 0.833, 28, 1, 0.9, 28, 0.967, 18.884, 1.033, 4.985, 1, 1.122, -13.546, 1.211, -21, 1.3, -21, 0, 1.567, -21]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -6, 1, 0.111, -6, 0.222, -6, 0.333, -6, 1, 0.567, -6, 0.8, 8.119, 1.033, 15.828, 1, 1.122, 18.765, 1.211, 18, 1.3, 18, 0, 1.567, 18]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 0.89, 1, 0.056, 0.89, 0.111, 0.89, 0.167, 0.89, 1, 0.222, 0.89, 0.278, 0, 0.333, 0, 1, 0.4, 0, 0.467, 0.81, 0.533, 0.81, 1, 0.633, 0.81, 0.733, 0.81, 0.833, 0.81, 1, 0.9, 0.81, 0.967, 0, 1.033, 0, 0, 1.567, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 0.88, 1, 0.056, 0.88, 0.111, 0.88, 0.167, 0.88, 1, 0.222, 0.88, 0.278, 0, 0.333, 0, 1, 0.4, 0, 0.467, 0.84, 0.533, 0.84, 1, 0.633, 0.84, 0.733, 0.84, 0.833, 0.84, 1, 0.9, 0.84, 0.967, 0, 1.033, 0, 0, 1.567, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, -0.65, 1, 0.178, -0.65, 0.356, 0.29, 0.533, 0.29, 0, 1.567, 0.29]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.5, 1, 0.178, -0.5, 0.356, -0.42, 0.533, -0.42, 0, 1.567, -0.42]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.133, 0, 0.267, 1.81, 0.4, 1.81, 1, 0.456, 1.81, 0.511, 0.96, 0.567, 0.96, 1, 0.633, 0.96, 0.7, 1.95, 0.767, 1.95, 1, 0.844, 1.95, 0.922, 0, 1, 0, 0, 1.567, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -0.67, 1, 0.067, -0.67, 0.133, -0.67, 0.2, -0.67, 1, 0.267, -0.67, 0.333, -0.17, 0.4, -0.17, 1, 0.456, -0.17, 0.511, -0.462, 0.567, -0.5, 1, 0.633, -0.545, 0.7, -0.54, 0.767, -0.54, 1, 0.844, -0.54, 0.922, -0.42, 1, -0.42, 0, 1.567, -0.42]}, {"Target": "Parameter", "Id": "ParamBodyX", "Segments": [0, 0.69, 0, 1.567, 0.69]}, {"Target": "Parameter", "Id": "ParamBodyZ", "Segments": [0, -0.85, 0, 1.567, -0.85]}, {"Target": "Parameter", "Id": "ParamBodyY", "Segments": [0, 0, 0, 1.567, 0]}, {"Target": "Parameter", "Id": "ParamArm02L01", "Segments": [0, 0.55, 1, 0.044, 0.55, 0.089, 0.55, 0.133, 0.55, 1, 0.211, 0.55, 0.289, 1, 0.367, 1, 1, 0.444, 1, 0.522, 0.45, 0.6, 0.45, 1, 0.689, 0.45, 0.778, 0.92, 0.867, 0.92, 1, 0.944, 0.92, 1.022, 0.28, 1.1, 0.28, 0, 1.567, 0.28]}, {"Target": "Parameter", "Id": "ParamArm02L02", "Segments": [0, -0.75, 1, 0.044, -0.75, 0.089, -0.75, 0.133, -0.75, 1, 0.211, -0.75, 0.289, -0.25, 0.367, -0.25, 1, 0.444, -0.25, 0.522, -1, 0.6, -1, 1, 0.689, -1, 0.778, -0.67, 0.867, -0.67, 1, 0.944, -0.67, 1.022, -1, 1.1, -1, 0, 1.567, -1]}, {"Target": "Parameter", "Id": "ParamHand02L", "Segments": [0, -0.47, 1, 0.267, -0.47, 0.533, -0.197, 0.8, -0.04, 1, 0.9, 0.019, 1, 0, 1.1, 0, 0, 1.567, 0]}, {"Target": "Parameter", "Id": "ParamArm02R01", "Segments": [0, -0.37, 0, 1.567, -0.37]}, {"Target": "Parameter", "Id": "ParamArm02R02", "Segments": [0, -0.83, 0, 1.567, -0.83]}, {"Target": "Parameter", "Id": "ParamHand02R", "Segments": [0, -1, 0, 1.567, -1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_02", "Segments": [0, 0, 2, 1.53, 0, 0, 1.57, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_02", "Segments": [0, 1, 2, 1.53, 1, 0, 1.57, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_01", "Segments": [0, 1, 2, 1.53, 1, 0, 1.57, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_01", "Segments": [0, 0, 2, 1.53, 0, 0, 1.57, 0]}]}