{"Version": 3, "Meta": {"Duration": 3, "Fps": 30.0, "FadeInTime": 1.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 31, "TotalSegmentCount": 142, "TotalPointCount": 395, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.4, 0, 0.633, -30, 0.867, -30, 1, 1, -30, 1.133, -10, 1.267, -10, 1, 1.367, -10, 1.467, -30, 1.567, -30, 1, 1.678, -30, 1.789, -20, 1.9, -20, 1, 2.144, -20, 2.389, -30, 2.633, -30, 1, 2.689, -30, 2.744, -30, 2.8, -30, 0, 3, -30]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.278, 0, 0.389, -2, 0.5, -2, 1, 0.667, -2, 0.833, 30, 1, 30, 1, 1.022, 30, 1.044, 30, 1.067, 30, 1, 1.133, 30, 1.2, 30, 1.267, 30, 1, 1.611, 30, 1.956, 30, 2.3, 30, 1, 2.356, 30, 2.411, 30, 2.467, 30, 0, 3, 30]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.4, 0, 0.633, -15, 0.867, -15, 1, 1.344, -15, 1.822, -15, 2.3, -15, 1, 2.356, -15, 2.411, -15, 2.467, -15, 0, 3, -15]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.278, 1, 0.389, 0.8, 0.5, 0.8, 1, 0.689, 0.8, 0.878, 0.8, 1.067, 0.8, 1, 1.1, 0.8, 1.133, 0, 1.167, 0, 1, 1.189, 0, 1.211, 0, 1.233, 0, 1, 1.3, 0, 1.367, 0.8, 1.433, 0.8, 0, 3, 0.8]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.278, 0, 0.389, 0.5, 0.5, 0.5, 1, 0.689, 0.5, 0.878, 0.5, 1.067, 0.5, 1, 1.1, 0.5, 1.133, 0, 1.167, 0, 1, 1.189, 0, 1.211, 0, 1.233, 0, 1, 1.3, 0, 1.367, 0.5, 1.433, 0.5, 0, 3, 0.5]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.278, 1, 0.389, 0.81, 0.5, 0.81, 1, 0.689, 0.81, 0.878, 0.81, 1.067, 0.81, 1, 1.1, 0.81, 1.133, 0, 1.167, 0, 1, 1.189, 0, 1.211, 0, 1.233, 0, 1, 1.3, 0, 1.367, 0.81, 1.433, 0.81, 0, 3, 0.81]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "FadeInTime": 0.2, "FadeOutTime": 0.2, "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.278, 0, 0.389, 0.5, 0.5, 0.5, 1, 0.689, 0.5, 0.878, 0.5, 1.067, 0.5, 1, 1.1, 0.5, 1.133, 0, 1.167, 0, 1, 1.189, 0, 1.211, 0, 1.233, 0, 1, 1.3, 0, 1.367, 0.5, 1.433, 0.5, 0, 3, 0.5]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.411, 0, 0.656, 1, 0.9, 1, 1, 1.367, 1, 1.833, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.5, 1, 0.056, -0.5, 0.111, -0.5, 0.167, -0.5, 1, 0.411, -0.5, 0.656, -1, 0.9, -1, 1, 1.367, -1, 1.833, -1, 2.3, -1, 1, 2.356, -1, 2.411, -1, 2.467, -1, 0, 3, -1]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 1, 1, 0.056, 1, 0.111, 1, 0.167, 1, 1, 0.878, 1, 1.589, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, -0.58, 0, 3, -0.58]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, -0.6, 0, 3, -0.6]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, -0.56, 0, 3, -0.56]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, -0.56, 0, 3, -0.56]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, -0.07, 0, 3, -0.07]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, -0.07, 0, 3, -0.07]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -0.6, 0, 3, -0.6]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -0.6, 0, 3, -0.6]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0, 0.4, 0, 1, 0.5, 0, 0.6, 0, 0.7, 0, 1, 0.811, 0, 0.922, 0, 1.033, 0, 1, 1.3, 0, 1.567, -1, 1.833, -1, 1, 1.989, -1, 2.144, -1, 2.3, -1, 1, 2.356, -1, 2.411, -1, 2.467, -1, 0, 3, -1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.133, 0, 0.267, 0, 0.4, 0, 1, 0.5, 0, 0.6, 0.611, 0.7, 0.8, 1, 0.811, 1.01, 0.922, 1, 1.033, 1, 1, 1.189, 1, 1.344, 0, 1.5, 0, 1, 1.767, 0, 2.033, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.389, 0, 0.611, -10, 0.833, -10, 1, 0.967, -10, 1.1, -8, 1.233, -8, 1, 1.333, -8, 1.433, -10, 1.533, -10, 1, 1.644, -10, 1.756, -9, 1.867, -9, 1, 2.111, -9, 2.356, -10, 2.6, -10, 1, 2.656, -10, 2.711, -10, 2.767, -10, 0, 3, -10]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.433, 0, 0.7, 10, 0.967, 10, 1, 1.511, 10, 2.056, 0, 2.6, 0, 1, 2.656, 0, 2.711, 0, 2.767, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.389, 0, 0.611, -3, 0.833, -3, 1, 1.322, -3, 1.811, -3, 2.3, -3, 1, 2.356, -3, 2.411, -3, 2.467, -3, 0, 3, -3]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.433, 0, 0.7, 1, 0.967, 1, 1, 1.411, 1, 1.856, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.389, 0, 0.611, -1, 0.833, -1, 1, 1.322, -1, 1.811, -1, 2.3, -1, 1, 2.356, -1, 2.411, -1, 2.467, -1, 0, 3, -1]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamHairAho", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.467, 0, 0.767, -0.3, 1.067, -0.3, 1, 1.367, -0.3, 1.667, 1, 1.967, 1, 1, 2.078, 1, 2.189, 1, 2.3, 1, 1, 2.356, 1, 2.411, 1, 2.467, 1, 0, 3, 1]}, {"Target": "Parameter", "Id": "ParamArm", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.244, 0, 0.322, 0, 0.4, 0, 1, 0.567, 0, 0.733, -0.5, 0.9, -0.5, 1, 1.233, -0.5, 1.567, 0, 1.9, 0, 1, 2.111, 0, 2.322, 0, 2.533, 0, 1, 2.589, 0, 2.644, 0, 2.7, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0, 0.167, 0, 1, 0.878, 0, 1.589, 0, 2.3, 0, 1, 2.356, 0, 2.411, 0, 2.467, 0, 0, 3, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.5, 1, 0.056, 0.5, 0.111, 0.5, 0.167, 0.5, 1, 0.878, 0.5, 1.589, 0.5, 2.3, 0.5, 1, 2.356, 0.5, 2.411, 0.5, 2.467, 0.5, 0, 3, 0.5]}]}