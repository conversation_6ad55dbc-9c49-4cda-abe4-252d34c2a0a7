"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@heroui+use-safe-layout-eff_e82c068bf0f21d27b91df3c83af0fa0c";
exports.ids = ["vendor-chunks/@heroui+use-safe-layout-eff_e82c068bf0f21d27b91df3c83af0fa0c"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@heroui+use-safe-layout-eff_e82c068bf0f21d27b91df3c83af0fa0c/node_modules/@heroui/use-safe-layout-effect/dist/index.mjs":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@heroui+use-safe-layout-eff_e82c068bf0f21d27b91df3c83af0fa0c/node_modules/@heroui/use-safe-layout-effect/dist/index.mjs ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSafeLayoutEffect: () => (/* binding */ useSafeLayoutEffect)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/index.ts\n\nvar useSafeLayoutEffect = Boolean(globalThis == null ? void 0 : globalThis.document) ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGhlcm91aSt1c2Utc2FmZS1sYXlvdXQtZWZmX2U4MmMwNjhiZjBmMjFkMjdiOTFkZjNjODNhZjBmYTBjL25vZGVfbW9kdWxlcy9AaGVyb3VpL3VzZS1zYWZlLWxheW91dC1lZmZlY3QvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNtRDtBQUNuRCx1RkFBdUYsa0RBQWUsR0FBRyw0Q0FBUztBQUdoSCIsInNvdXJjZXMiOlsiRDpcXExMTVxcTGVhcm5pbmdcXGFpX2NvZGluZ1xcYW55dGhpbmdjaGF0XFxsaXZlY2hhdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGhlcm91aSt1c2Utc2FmZS1sYXlvdXQtZWZmX2U4MmMwNjhiZjBmMjFkMjdiOTFkZjNjODNhZjBmYTBjXFxub2RlX21vZHVsZXNcXEBoZXJvdWlcXHVzZS1zYWZlLWxheW91dC1lZmZlY3RcXGRpc3RcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvaW5kZXgudHNcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlTGF5b3V0RWZmZWN0IH0gZnJvbSBcInJlYWN0XCI7XG52YXIgdXNlU2FmZUxheW91dEVmZmVjdCA9IEJvb2xlYW4oZ2xvYmFsVGhpcyA9PSBudWxsID8gdm9pZCAwIDogZ2xvYmFsVGhpcy5kb2N1bWVudCkgPyB1c2VMYXlvdXRFZmZlY3QgOiB1c2VFZmZlY3Q7XG5leHBvcnQge1xuICB1c2VTYWZlTGF5b3V0RWZmZWN0XG59O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@heroui+use-safe-layout-eff_e82c068bf0f21d27b91df3c83af0fa0c/node_modules/@heroui/use-safe-layout-effect/dist/index.mjs\n");

/***/ })

};
;