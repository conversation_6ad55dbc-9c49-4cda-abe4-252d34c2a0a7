'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { useUser } from '@/lib/context/UserContext';
import { Button, Input, Card, CardBody, CardHeader, Spinner } from '@heroui/react';
import { getDeploymentUrl, loadChatConfig } from '@/lib/config/chatConfig';

const LoginPage: React.FC = () => {
  const router = useRouter();
  const { login, isAuthenticated } = useUser();
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [deploymentUrl, setDeploymentUrl] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [loginSuccess, setLoginSuccess] = useState(false);
  const [serverHealth, setServerHealth] = useState<boolean | null>(null);

  // Load configuration on mount
  useEffect(() => {
    const initializeConfig = async () => {
      try {
        const config = await loadChatConfig();
        const defaultUrl = getDeploymentUrl(config);
        setDeploymentUrl(defaultUrl);
      } catch (error) {
        console.error('Failed to load configuration:', error);
        setDeploymentUrl('http://localhost:7272');
      }
    };

    initializeConfig();
  }, []);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push('/sentio');
    }
  }, [isAuthenticated, router]);

  // Redirect after successful login
  useEffect(() => {
    if (loginSuccess) {
      const timer = setTimeout(() => {
        router.push('/sentio');
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [loginSuccess, router]);

  const checkDeploymentHealth = async (): Promise<boolean> => {
    try {
      const response = await fetch(`${deploymentUrl}/v3/health`);
      const isHealthy = response.ok;
      setServerHealth(isHealthy);
      return isHealthy;
    } catch (error) {
      console.error('Health check failed:', error);
      setServerHealth(false);
      return false;
    }
  };

  // Handle login submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);
    try {
      const result = await login(email, password, deploymentUrl);
      setLoginSuccess(true);
    } catch (error) {
      console.error('Login failed:', error);

      // Only check server health after a failed login attempt
      const isServerHealthy = await checkDeploymentHealth();

      let errorMessage = 'An unknown error occurred';
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }

      // Provide appropriate error message based on server health
      const serverStatusMessage = isServerHealthy
        ? '服务器运行正常，请检查您的凭据后重试。'
        : '无法与服务器通信，请检查配置文件中的API地址是否正确。';

      setError(`${errorMessage} ${serverStatusMessage}`);
    } finally {
      setIsLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  if (loginSuccess) {
    return (
      <div className="min-h-screen bg-zinc-900 flex flex-col items-center justify-center p-8">
        <Card className="w-full max-w-md">
          <CardBody className="pt-6">
            <div className="text-center">
              <div className="text-green-600 text-lg font-semibold mb-2">
                登录成功！
              </div>
              <div className="text-gray-600 text-sm">
                正在跳转到聊天页面...
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-zinc-900 flex flex-col items-center justify-center p-8">
      <Card className="w-full max-w-md shadow-2xl">
        <CardHeader className="text-center pb-2">
          <div className="flex flex-col items-center space-y-4">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent">
              LiveChat
            </h1>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
              欢迎登录
            </h2>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              请输入您的邮箱和密码
            </p>
          </div>
          {serverHealth === false && (
            <div className="mt-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md">
              <span className="text-red-600 dark:text-red-400 text-sm font-medium">
                无法连接到服务器，请检查网络连接或联系管理员。
              </span>
            </div>
          )}
        </CardHeader>
        <CardBody>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Input
                id="email"
                name="email"
                type="email"
                label="邮箱"
                placeholder="请输入邮箱"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                autoComplete="email"
                required
                variant="bordered"
                classNames={{
                  label: "text-gray-700 dark:text-gray-300",
                  input: "text-gray-900 dark:text-white",
                }}
              />
            </div>

            <div>
              <Input
                id="password"
                name="password"
                type={showPassword ? "text" : "password"}
                label="密码"
                placeholder="请输入密码"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                autoComplete="current-password"
                required
                variant="bordered"
                classNames={{
                  label: "text-gray-700 dark:text-gray-300",
                  input: "text-gray-900 dark:text-white",
                }}
                endContent={
                  <button
                    type="button"
                    onClick={togglePasswordVisibility}
                    className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none"
                    aria-label={showPassword ? "隐藏密码" : "显示密码"}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5" />
                    ) : (
                      <EyeIcon className="h-5 w-5" />
                    )}
                  </button>
                }
              />
            </div>

            <Button
              type="submit"
              className="w-full py-3 text-base font-medium bg-gradient-to-r from-blue-500 to-purple-600 text-white"
              disabled={isLoading}
              size="lg"
            >
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <Spinner size="sm" color="white" />
                  <span>登录中...</span>
                </div>
              ) : (
                '登录'
              )}
            </Button>

            {error && (
              <div className="mt-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md">
                <span className="text-red-600 dark:text-red-400 text-sm">
                  {error}
                </span>
              </div>
            )}
          </form>
        </CardBody>
      </Card>
    </div>
  );
};

export default LoginPage;
