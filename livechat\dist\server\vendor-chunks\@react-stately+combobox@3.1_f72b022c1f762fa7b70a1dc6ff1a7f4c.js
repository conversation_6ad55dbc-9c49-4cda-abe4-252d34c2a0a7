"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-stately+combobox@3.1_f72b022c1f762fa7b70a1dc6ff1a7f4c";
exports.ids = ["vendor-chunks/@react-stately+combobox@3.1_f72b022c1f762fa7b70a1dc6ff1a7f4c"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+combobox@3.1_f72b022c1f762fa7b70a1dc6ff1a7f4c/node_modules/@react-stately/combobox/dist/useComboBoxState.mjs":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+combobox@3.1_f72b022c1f762fa7b70a1dc6ff1a7f4c/node_modules/@react-stately/combobox/dist/useComboBoxState.mjs ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useComboBoxState: () => (/* binding */ $a9e7382a7d111cb5$export$b453a3bfd4a5fa9e)\n/* harmony export */ });\n/* harmony import */ var _react_stately_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-stately/form */ \"(ssr)/./node_modules/.pnpm/@react-stately+form@3.1.2_react@19.0.0-rc-69d4b800-20241021/node_modules/@react-stately/form/dist/useFormValidationState.mjs\");\n/* harmony import */ var _react_stately_collections__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @react-stately/collections */ \"(ssr)/./node_modules/.pnpm/@react-stately+collections@_1ed9bb58ba6c8998638f0bad1166ce91/node_modules/@react-stately/collections/dist/getChildNodes.mjs\");\n/* harmony import */ var _react_stately_list__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-stately/list */ \"(ssr)/./node_modules/.pnpm/@react-stately+list@3.12.0_react@19.0.0-rc-69d4b800-20241021/node_modules/@react-stately/list/dist/useSingleSelectListState.mjs\");\n/* harmony import */ var _react_stately_list__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-stately/list */ \"(ssr)/./node_modules/.pnpm/@react-stately+list@3.12.0_react@19.0.0-rc-69d4b800-20241021/node_modules/@react-stately/list/dist/ListCollection.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_stately_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-stately/utils */ \"(ssr)/./node_modules/.pnpm/@react-stately+utils@3.10.5_643de144daf18c30f9f7e3eb5c877f70/node_modules/@react-stately/utils/dist/useControlledState.mjs\");\n/* harmony import */ var _react_stately_overlays__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-stately/overlays */ \"(ssr)/./node_modules/.pnpm/@react-stately+overlays@3.6_8ca4e08d61b6a94891b76d2efe1c2a09/node_modules/@react-stately/overlays/dist/useOverlayTriggerState.mjs\");\n\n\n\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\n\n\n\nfunction $a9e7382a7d111cb5$export$b453a3bfd4a5fa9e(props) {\n    var _collection_getItem;\n    let { defaultFilter: defaultFilter, menuTrigger: menuTrigger = 'input', allowsEmptyCollection: allowsEmptyCollection = false, allowsCustomValue: allowsCustomValue, shouldCloseOnBlur: shouldCloseOnBlur = true } = props;\n    let [showAllItems, setShowAllItems] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    let [isFocused, setFocusedState] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    let [focusStrategy, setFocusStrategy] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    let onSelectionChange = (key)=>{\n        if (props.onSelectionChange) props.onSelectionChange(key);\n        // If key is the same, reset the inputValue and close the menu\n        // (scenario: user clicks on already selected option)\n        if (key === selectedKey) {\n            resetInputValue();\n            closeMenu();\n        }\n    };\n    var _props_items;\n    let { collection: collection, selectionManager: selectionManager, selectedKey: selectedKey, setSelectedKey: setSelectedKey, selectedItem: selectedItem, disabledKeys: disabledKeys } = (0, _react_stately_list__WEBPACK_IMPORTED_MODULE_1__.useSingleSelectListState)({\n        ...props,\n        onSelectionChange: onSelectionChange,\n        items: (_props_items = props.items) !== null && _props_items !== void 0 ? _props_items : props.defaultItems\n    });\n    let defaultInputValue = props.defaultInputValue;\n    if (defaultInputValue == null) {\n        var _collection_getItem1;\n        var _collection_getItem_textValue;\n        if (selectedKey == null) defaultInputValue = '';\n        else defaultInputValue = (_collection_getItem_textValue = (_collection_getItem1 = collection.getItem(selectedKey)) === null || _collection_getItem1 === void 0 ? void 0 : _collection_getItem1.textValue) !== null && _collection_getItem_textValue !== void 0 ? _collection_getItem_textValue : '';\n    }\n    let [inputValue, setInputValue] = (0, _react_stately_utils__WEBPACK_IMPORTED_MODULE_2__.useControlledState)(props.inputValue, defaultInputValue, props.onInputChange);\n    // Preserve original collection so we can show all items on demand\n    let originalCollection = collection;\n    let filteredCollection = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>// No default filter if items are controlled.\n        props.items != null || !defaultFilter ? collection : $a9e7382a7d111cb5$var$filterCollection(collection, inputValue, defaultFilter), [\n        collection,\n        inputValue,\n        defaultFilter,\n        props.items\n    ]);\n    let [lastCollection, setLastCollection] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(filteredCollection);\n    // Track what action is attempting to open the menu\n    let menuOpenTrigger = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)('focus');\n    let onOpenChange = (open)=>{\n        if (props.onOpenChange) props.onOpenChange(open, open ? menuOpenTrigger.current : undefined);\n        selectionManager.setFocused(open);\n        if (!open) selectionManager.setFocusedKey(null);\n    };\n    let triggerState = (0, _react_stately_overlays__WEBPACK_IMPORTED_MODULE_3__.useOverlayTriggerState)({\n        ...props,\n        onOpenChange: onOpenChange,\n        isOpen: undefined,\n        defaultOpen: undefined\n    });\n    let open = (focusStrategy = null, trigger)=>{\n        let displayAllItems = trigger === 'manual' || trigger === 'focus' && menuTrigger === 'focus';\n        // Prevent open operations from triggering if there is nothing to display\n        // Also prevent open operations from triggering if items are uncontrolled but defaultItems is empty, even if displayAllItems is true.\n        // This is to prevent comboboxes with empty defaultItems from opening but allow controlled items comboboxes to open even if the inital list is empty (assumption is user will provide swap the empty list with a base list via onOpenChange returning `menuTrigger` manual)\n        if (allowsEmptyCollection || filteredCollection.size > 0 || displayAllItems && originalCollection.size > 0 || props.items) {\n            if (displayAllItems && !triggerState.isOpen && props.items === undefined) // Show all items if menu is manually opened. Only care about this if items are undefined\n            setShowAllItems(true);\n            menuOpenTrigger.current = trigger;\n            setFocusStrategy(focusStrategy);\n            triggerState.open();\n        }\n    };\n    let toggle = (focusStrategy = null, trigger)=>{\n        let displayAllItems = trigger === 'manual' || trigger === 'focus' && menuTrigger === 'focus';\n        // If the menu is closed and there is nothing to display, early return so toggle isn't called to prevent extraneous onOpenChange\n        if (!(allowsEmptyCollection || filteredCollection.size > 0 || displayAllItems && originalCollection.size > 0 || props.items) && !triggerState.isOpen) return;\n        if (displayAllItems && !triggerState.isOpen && props.items === undefined) // Show all items if menu is toggled open. Only care about this if items are undefined\n        setShowAllItems(true);\n        // Only update the menuOpenTrigger if menu is currently closed\n        if (!triggerState.isOpen) menuOpenTrigger.current = trigger;\n        toggleMenu(focusStrategy);\n    };\n    let updateLastCollection = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setLastCollection(showAllItems ? originalCollection : filteredCollection);\n    }, [\n        showAllItems,\n        originalCollection,\n        filteredCollection\n    ]);\n    // If menu is going to close, save the current collection so we can freeze the displayed collection when the\n    // user clicks outside the popover to close the menu. Prevents the menu contents from updating as the menu closes.\n    let toggleMenu = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((focusStrategy = null)=>{\n        if (triggerState.isOpen) updateLastCollection();\n        setFocusStrategy(focusStrategy);\n        triggerState.toggle();\n    }, [\n        triggerState,\n        updateLastCollection\n    ]);\n    let closeMenu = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (triggerState.isOpen) {\n            updateLastCollection();\n            triggerState.close();\n        }\n    }, [\n        triggerState,\n        updateLastCollection\n    ]);\n    let [lastValue, setLastValue] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(inputValue);\n    let resetInputValue = ()=>{\n        var _collection_getItem;\n        var _collection_getItem_textValue;\n        let itemText = selectedKey != null ? (_collection_getItem_textValue = (_collection_getItem = collection.getItem(selectedKey)) === null || _collection_getItem === void 0 ? void 0 : _collection_getItem.textValue) !== null && _collection_getItem_textValue !== void 0 ? _collection_getItem_textValue : '' : '';\n        setLastValue(itemText);\n        setInputValue(itemText);\n    };\n    var _props_selectedKey, _ref;\n    let lastSelectedKey = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)((_ref = (_props_selectedKey = props.selectedKey) !== null && _props_selectedKey !== void 0 ? _props_selectedKey : props.defaultSelectedKey) !== null && _ref !== void 0 ? _ref : null);\n    var _collection_getItem_textValue1;\n    let lastSelectedKeyText = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(selectedKey != null ? (_collection_getItem_textValue1 = (_collection_getItem = collection.getItem(selectedKey)) === null || _collection_getItem === void 0 ? void 0 : _collection_getItem.textValue) !== null && _collection_getItem_textValue1 !== void 0 ? _collection_getItem_textValue1 : '' : '');\n    // intentional omit dependency array, want this to happen on every render\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _collection_getItem;\n        // Open and close menu automatically when the input value changes if the input is focused,\n        // and there are items in the collection or allowEmptyCollection is true.\n        if (isFocused && (filteredCollection.size > 0 || allowsEmptyCollection) && !triggerState.isOpen && inputValue !== lastValue && menuTrigger !== 'manual') open(null, 'input');\n        // Close the menu if the collection is empty. Don't close menu if filtered collection size is 0\n        // but we are currently showing all items via button press\n        if (!showAllItems && !allowsEmptyCollection && triggerState.isOpen && filteredCollection.size === 0) closeMenu();\n        // Close when an item is selected.\n        if (selectedKey != null && selectedKey !== lastSelectedKey.current) closeMenu();\n        // Clear focused key when input value changes and display filtered collection again.\n        if (inputValue !== lastValue) {\n            selectionManager.setFocusedKey(null);\n            setShowAllItems(false);\n            // Set selectedKey to null when the user clears the input.\n            // If controlled, this is the application developer's responsibility.\n            if (inputValue === '' && (props.inputValue === undefined || props.selectedKey === undefined)) setSelectedKey(null);\n        }\n        // If the selectedKey changed, update the input value.\n        // Do nothing if both inputValue and selectedKey are controlled.\n        // In this case, it's the user's responsibility to update inputValue in onSelectionChange.\n        if (selectedKey !== lastSelectedKey.current && (props.inputValue === undefined || props.selectedKey === undefined)) resetInputValue();\n        else if (lastValue !== inputValue) setLastValue(inputValue);\n        var _collection_getItem_textValue;\n        // Update the inputValue if the selected item's text changes from its last tracked value.\n        // This is to handle cases where a selectedKey is specified but the items aren't available (async loading) or the selected item's text value updates.\n        // Only reset if the user isn't currently within the field so we don't erroneously modify user input.\n        // If inputValue is controlled, it is the user's responsibility to update the inputValue when items change.\n        let selectedItemText = selectedKey != null ? (_collection_getItem_textValue = (_collection_getItem = collection.getItem(selectedKey)) === null || _collection_getItem === void 0 ? void 0 : _collection_getItem.textValue) !== null && _collection_getItem_textValue !== void 0 ? _collection_getItem_textValue : '' : '';\n        if (!isFocused && selectedKey != null && props.inputValue === undefined && selectedKey === lastSelectedKey.current) {\n            if (lastSelectedKeyText.current !== selectedItemText) {\n                setLastValue(selectedItemText);\n                setInputValue(selectedItemText);\n            }\n        }\n        lastSelectedKey.current = selectedKey;\n        lastSelectedKeyText.current = selectedItemText;\n    });\n    let validation = (0, _react_stately_form__WEBPACK_IMPORTED_MODULE_4__.useFormValidationState)({\n        ...props,\n        value: (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n                inputValue: inputValue,\n                selectedKey: selectedKey\n            }), [\n            inputValue,\n            selectedKey\n        ])\n    });\n    // Revert input value and close menu\n    let revert = ()=>{\n        if (allowsCustomValue && selectedKey == null) commitCustomValue();\n        else commitSelection();\n    };\n    let commitCustomValue = ()=>{\n        lastSelectedKey.current = null;\n        setSelectedKey(null);\n        closeMenu();\n    };\n    let commitSelection = ()=>{\n        // If multiple things are controlled, call onSelectionChange\n        if (props.selectedKey !== undefined && props.inputValue !== undefined) {\n            var _props_onSelectionChange, _collection_getItem;\n            (_props_onSelectionChange = props.onSelectionChange) === null || _props_onSelectionChange === void 0 ? void 0 : _props_onSelectionChange.call(props, selectedKey);\n            var _collection_getItem_textValue;\n            // Stop menu from reopening from useEffect\n            let itemText = selectedKey != null ? (_collection_getItem_textValue = (_collection_getItem = collection.getItem(selectedKey)) === null || _collection_getItem === void 0 ? void 0 : _collection_getItem.textValue) !== null && _collection_getItem_textValue !== void 0 ? _collection_getItem_textValue : '' : '';\n            setLastValue(itemText);\n            closeMenu();\n        } else {\n            // If only a single aspect of combobox is controlled, reset input value and close menu for the user\n            resetInputValue();\n            closeMenu();\n        }\n    };\n    const commitValue = ()=>{\n        if (allowsCustomValue) {\n            var _collection_getItem;\n            var _collection_getItem_textValue;\n            const itemText = selectedKey != null ? (_collection_getItem_textValue = (_collection_getItem = collection.getItem(selectedKey)) === null || _collection_getItem === void 0 ? void 0 : _collection_getItem.textValue) !== null && _collection_getItem_textValue !== void 0 ? _collection_getItem_textValue : '' : '';\n            inputValue === itemText ? commitSelection() : commitCustomValue();\n        } else // Reset inputValue and close menu\n        commitSelection();\n    };\n    let commit = ()=>{\n        if (triggerState.isOpen && selectionManager.focusedKey != null) {\n            // Reset inputValue and close menu here if the selected key is already the focused key. Otherwise\n            // fire onSelectionChange to allow the application to control the closing.\n            if (selectedKey === selectionManager.focusedKey) commitSelection();\n            else setSelectedKey(selectionManager.focusedKey);\n        } else commitValue();\n    };\n    let valueOnFocus = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(inputValue);\n    let setFocused = (isFocused)=>{\n        if (isFocused) {\n            valueOnFocus.current = inputValue;\n            if (menuTrigger === 'focus' && !props.isReadOnly) open(null, 'focus');\n        } else {\n            if (shouldCloseOnBlur) commitValue();\n            if (inputValue !== valueOnFocus.current) validation.commitValidation();\n        }\n        setFocusedState(isFocused);\n    };\n    let displayedCollection = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        if (triggerState.isOpen) {\n            if (showAllItems) return originalCollection;\n            else return filteredCollection;\n        } else return lastCollection;\n    }, [\n        triggerState.isOpen,\n        originalCollection,\n        filteredCollection,\n        showAllItems,\n        lastCollection\n    ]);\n    return {\n        ...validation,\n        ...triggerState,\n        focusStrategy: focusStrategy,\n        toggle: toggle,\n        open: open,\n        close: commitValue,\n        selectionManager: selectionManager,\n        selectedKey: selectedKey,\n        setSelectedKey: setSelectedKey,\n        disabledKeys: disabledKeys,\n        isFocused: isFocused,\n        setFocused: setFocused,\n        selectedItem: selectedItem,\n        collection: displayedCollection,\n        inputValue: inputValue,\n        setInputValue: setInputValue,\n        commit: commit,\n        revert: revert\n    };\n}\nfunction $a9e7382a7d111cb5$var$filterCollection(collection, inputValue, filter) {\n    return new (0, _react_stately_list__WEBPACK_IMPORTED_MODULE_5__.ListCollection)($a9e7382a7d111cb5$var$filterNodes(collection, collection, inputValue, filter));\n}\nfunction $a9e7382a7d111cb5$var$filterNodes(collection, nodes, inputValue, filter) {\n    let filteredNode = [];\n    for (let node of nodes){\n        if (node.type === 'section' && node.hasChildNodes) {\n            let filtered = $a9e7382a7d111cb5$var$filterNodes(collection, (0, _react_stately_collections__WEBPACK_IMPORTED_MODULE_6__.getChildNodes)(node, collection), inputValue, filter);\n            if ([\n                ...filtered\n            ].some((node)=>node.type === 'item')) filteredNode.push({\n                ...node,\n                childNodes: filtered\n            });\n        } else if (node.type === 'item' && filter(node.textValue, inputValue)) filteredNode.push({\n            ...node\n        });\n        else if (node.type !== 'item') filteredNode.push({\n            ...node\n        });\n    }\n    return filteredNode;\n}\n\n\n\n//# sourceMappingURL=useComboBoxState.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJlYWN0LXN0YXRlbHkrY29tYm9ib3hAMy4xX2Y3MmIwMjJjMWY3NjJmYTdiNzBhMWRjNmZmMWE3ZjRjL25vZGVfbW9kdWxlcy9AcmVhY3Qtc3RhdGVseS9jb21ib2JveC9kaXN0L3VzZUNvbWJvQm94U3RhdGUubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQTRGO0FBQ1g7QUFDd0Q7QUFDK0I7QUFDbkY7QUFDVzs7QUFFaEc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7Ozs7O0FBTUE7QUFDQTtBQUNBLFVBQVUsNE1BQTRNO0FBQ3ROLDhDQUE4QywyQ0FBZTtBQUM3RCwyQ0FBMkMsMkNBQWU7QUFDMUQsZ0RBQWdELDJDQUFlO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSwrS0FBK0ssTUFBTSx5RUFBK0I7QUFDOU47QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLG9FQUF5QjtBQUNuRTtBQUNBO0FBQ0EsaUNBQWlDLDBDQUFjO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtEQUFrRCwyQ0FBZTtBQUNqRTtBQUNBLDhCQUE4Qix5Q0FBYTtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLDJFQUE2QjtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUNBQW1DLDhDQUFrQjtBQUNyRDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsOENBQWtCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsOENBQWtCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLHdDQUF3QywyQ0FBZTtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEJBQThCLHlDQUFhO0FBQzNDO0FBQ0Esa0NBQWtDLHlDQUFhO0FBQy9DO0FBQ0E7QUFDQSxRQUFRLDRDQUFnQjtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wseUJBQXlCLHVFQUE2QjtBQUN0RDtBQUNBLG1CQUFtQiwwQ0FBYztBQUNqQztBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBLDJCQUEyQix5Q0FBYTtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLDBDQUFjO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVixLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsK0RBQXFCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2RUFBNkUscUVBQW9CO0FBQ2pHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsVUFBVTtBQUNWO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBOzs7QUFHdUU7QUFDdkUiLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEByZWFjdC1zdGF0ZWx5K2NvbWJvYm94QDMuMV9mNzJiMDIyYzFmNzYyZmE3YjcwYTFkYzZmZjFhN2Y0Y1xcbm9kZV9tb2R1bGVzXFxAcmVhY3Qtc3RhdGVseVxcY29tYm9ib3hcXGRpc3RcXHVzZUNvbWJvQm94U3RhdGUubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7dXNlRm9ybVZhbGlkYXRpb25TdGF0ZSBhcyAkNDlCSlAkdXNlRm9ybVZhbGlkYXRpb25TdGF0ZX0gZnJvbSBcIkByZWFjdC1zdGF0ZWx5L2Zvcm1cIjtcbmltcG9ydCB7Z2V0Q2hpbGROb2RlcyBhcyAkNDlCSlAkZ2V0Q2hpbGROb2Rlc30gZnJvbSBcIkByZWFjdC1zdGF0ZWx5L2NvbGxlY3Rpb25zXCI7XG5pbXBvcnQge3VzZVNpbmdsZVNlbGVjdExpc3RTdGF0ZSBhcyAkNDlCSlAkdXNlU2luZ2xlU2VsZWN0TGlzdFN0YXRlLCBMaXN0Q29sbGVjdGlvbiBhcyAkNDlCSlAkTGlzdENvbGxlY3Rpb259IGZyb20gXCJAcmVhY3Qtc3RhdGVseS9saXN0XCI7XG5pbXBvcnQge3VzZVN0YXRlIGFzICQ0OUJKUCR1c2VTdGF0ZSwgdXNlTWVtbyBhcyAkNDlCSlAkdXNlTWVtbywgdXNlUmVmIGFzICQ0OUJKUCR1c2VSZWYsIHVzZUNhbGxiYWNrIGFzICQ0OUJKUCR1c2VDYWxsYmFjaywgdXNlRWZmZWN0IGFzICQ0OUJKUCR1c2VFZmZlY3R9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHt1c2VDb250cm9sbGVkU3RhdGUgYXMgJDQ5QkpQJHVzZUNvbnRyb2xsZWRTdGF0ZX0gZnJvbSBcIkByZWFjdC1zdGF0ZWx5L3V0aWxzXCI7XG5pbXBvcnQge3VzZU92ZXJsYXlUcmlnZ2VyU3RhdGUgYXMgJDQ5QkpQJHVzZU92ZXJsYXlUcmlnZ2VyU3RhdGV9IGZyb20gXCJAcmVhY3Qtc3RhdGVseS9vdmVybGF5c1wiO1xuXG4vKlxuICogQ29weXJpZ2h0IDIwMjAgQWRvYmUuIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4gKiBUaGlzIGZpbGUgaXMgbGljZW5zZWQgdG8geW91IHVuZGVyIHRoZSBBcGFjaGUgTGljZW5zZSwgVmVyc2lvbiAyLjAgKHRoZSBcIkxpY2Vuc2VcIik7XG4gKiB5b3UgbWF5IG5vdCB1c2UgdGhpcyBmaWxlIGV4Y2VwdCBpbiBjb21wbGlhbmNlIHdpdGggdGhlIExpY2Vuc2UuIFlvdSBtYXkgb2J0YWluIGEgY29weVxuICogb2YgdGhlIExpY2Vuc2UgYXQgaHR0cDovL3d3dy5hcGFjaGUub3JnL2xpY2Vuc2VzL0xJQ0VOU0UtMi4wXG4gKlxuICogVW5sZXNzIHJlcXVpcmVkIGJ5IGFwcGxpY2FibGUgbGF3IG9yIGFncmVlZCB0byBpbiB3cml0aW5nLCBzb2Z0d2FyZSBkaXN0cmlidXRlZCB1bmRlclxuICogdGhlIExpY2Vuc2UgaXMgZGlzdHJpYnV0ZWQgb24gYW4gXCJBUyBJU1wiIEJBU0lTLCBXSVRIT1VUIFdBUlJBTlRJRVMgT1IgUkVQUkVTRU5UQVRJT05TXG4gKiBPRiBBTlkgS0lORCwgZWl0aGVyIGV4cHJlc3Mgb3IgaW1wbGllZC4gU2VlIHRoZSBMaWNlbnNlIGZvciB0aGUgc3BlY2lmaWMgbGFuZ3VhZ2VcbiAqIGdvdmVybmluZyBwZXJtaXNzaW9ucyBhbmQgbGltaXRhdGlvbnMgdW5kZXIgdGhlIExpY2Vuc2UuXG4gKi8gXG5cblxuXG5cblxuZnVuY3Rpb24gJGE5ZTczODJhN2QxMTFjYjUkZXhwb3J0JGI0NTNhM2JmZDRhNWZhOWUocHJvcHMpIHtcbiAgICB2YXIgX2NvbGxlY3Rpb25fZ2V0SXRlbTtcbiAgICBsZXQgeyBkZWZhdWx0RmlsdGVyOiBkZWZhdWx0RmlsdGVyLCBtZW51VHJpZ2dlcjogbWVudVRyaWdnZXIgPSAnaW5wdXQnLCBhbGxvd3NFbXB0eUNvbGxlY3Rpb246IGFsbG93c0VtcHR5Q29sbGVjdGlvbiA9IGZhbHNlLCBhbGxvd3NDdXN0b21WYWx1ZTogYWxsb3dzQ3VzdG9tVmFsdWUsIHNob3VsZENsb3NlT25CbHVyOiBzaG91bGRDbG9zZU9uQmx1ciA9IHRydWUgfSA9IHByb3BzO1xuICAgIGxldCBbc2hvd0FsbEl0ZW1zLCBzZXRTaG93QWxsSXRlbXNdID0gKDAsICQ0OUJKUCR1c2VTdGF0ZSkoZmFsc2UpO1xuICAgIGxldCBbaXNGb2N1c2VkLCBzZXRGb2N1c2VkU3RhdGVdID0gKDAsICQ0OUJKUCR1c2VTdGF0ZSkoZmFsc2UpO1xuICAgIGxldCBbZm9jdXNTdHJhdGVneSwgc2V0Rm9jdXNTdHJhdGVneV0gPSAoMCwgJDQ5QkpQJHVzZVN0YXRlKShudWxsKTtcbiAgICBsZXQgb25TZWxlY3Rpb25DaGFuZ2UgPSAoa2V5KT0+e1xuICAgICAgICBpZiAocHJvcHMub25TZWxlY3Rpb25DaGFuZ2UpIHByb3BzLm9uU2VsZWN0aW9uQ2hhbmdlKGtleSk7XG4gICAgICAgIC8vIElmIGtleSBpcyB0aGUgc2FtZSwgcmVzZXQgdGhlIGlucHV0VmFsdWUgYW5kIGNsb3NlIHRoZSBtZW51XG4gICAgICAgIC8vIChzY2VuYXJpbzogdXNlciBjbGlja3Mgb24gYWxyZWFkeSBzZWxlY3RlZCBvcHRpb24pXG4gICAgICAgIGlmIChrZXkgPT09IHNlbGVjdGVkS2V5KSB7XG4gICAgICAgICAgICByZXNldElucHV0VmFsdWUoKTtcbiAgICAgICAgICAgIGNsb3NlTWVudSgpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICB2YXIgX3Byb3BzX2l0ZW1zO1xuICAgIGxldCB7IGNvbGxlY3Rpb246IGNvbGxlY3Rpb24sIHNlbGVjdGlvbk1hbmFnZXI6IHNlbGVjdGlvbk1hbmFnZXIsIHNlbGVjdGVkS2V5OiBzZWxlY3RlZEtleSwgc2V0U2VsZWN0ZWRLZXk6IHNldFNlbGVjdGVkS2V5LCBzZWxlY3RlZEl0ZW06IHNlbGVjdGVkSXRlbSwgZGlzYWJsZWRLZXlzOiBkaXNhYmxlZEtleXMgfSA9ICgwLCAkNDlCSlAkdXNlU2luZ2xlU2VsZWN0TGlzdFN0YXRlKSh7XG4gICAgICAgIC4uLnByb3BzLFxuICAgICAgICBvblNlbGVjdGlvbkNoYW5nZTogb25TZWxlY3Rpb25DaGFuZ2UsXG4gICAgICAgIGl0ZW1zOiAoX3Byb3BzX2l0ZW1zID0gcHJvcHMuaXRlbXMpICE9PSBudWxsICYmIF9wcm9wc19pdGVtcyAhPT0gdm9pZCAwID8gX3Byb3BzX2l0ZW1zIDogcHJvcHMuZGVmYXVsdEl0ZW1zXG4gICAgfSk7XG4gICAgbGV0IGRlZmF1bHRJbnB1dFZhbHVlID0gcHJvcHMuZGVmYXVsdElucHV0VmFsdWU7XG4gICAgaWYgKGRlZmF1bHRJbnB1dFZhbHVlID09IG51bGwpIHtcbiAgICAgICAgdmFyIF9jb2xsZWN0aW9uX2dldEl0ZW0xO1xuICAgICAgICB2YXIgX2NvbGxlY3Rpb25fZ2V0SXRlbV90ZXh0VmFsdWU7XG4gICAgICAgIGlmIChzZWxlY3RlZEtleSA9PSBudWxsKSBkZWZhdWx0SW5wdXRWYWx1ZSA9ICcnO1xuICAgICAgICBlbHNlIGRlZmF1bHRJbnB1dFZhbHVlID0gKF9jb2xsZWN0aW9uX2dldEl0ZW1fdGV4dFZhbHVlID0gKF9jb2xsZWN0aW9uX2dldEl0ZW0xID0gY29sbGVjdGlvbi5nZXRJdGVtKHNlbGVjdGVkS2V5KSkgPT09IG51bGwgfHwgX2NvbGxlY3Rpb25fZ2V0SXRlbTEgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9jb2xsZWN0aW9uX2dldEl0ZW0xLnRleHRWYWx1ZSkgIT09IG51bGwgJiYgX2NvbGxlY3Rpb25fZ2V0SXRlbV90ZXh0VmFsdWUgIT09IHZvaWQgMCA/IF9jb2xsZWN0aW9uX2dldEl0ZW1fdGV4dFZhbHVlIDogJyc7XG4gICAgfVxuICAgIGxldCBbaW5wdXRWYWx1ZSwgc2V0SW5wdXRWYWx1ZV0gPSAoMCwgJDQ5QkpQJHVzZUNvbnRyb2xsZWRTdGF0ZSkocHJvcHMuaW5wdXRWYWx1ZSwgZGVmYXVsdElucHV0VmFsdWUsIHByb3BzLm9uSW5wdXRDaGFuZ2UpO1xuICAgIC8vIFByZXNlcnZlIG9yaWdpbmFsIGNvbGxlY3Rpb24gc28gd2UgY2FuIHNob3cgYWxsIGl0ZW1zIG9uIGRlbWFuZFxuICAgIGxldCBvcmlnaW5hbENvbGxlY3Rpb24gPSBjb2xsZWN0aW9uO1xuICAgIGxldCBmaWx0ZXJlZENvbGxlY3Rpb24gPSAoMCwgJDQ5QkpQJHVzZU1lbW8pKCgpPT4vLyBObyBkZWZhdWx0IGZpbHRlciBpZiBpdGVtcyBhcmUgY29udHJvbGxlZC5cbiAgICAgICAgcHJvcHMuaXRlbXMgIT0gbnVsbCB8fCAhZGVmYXVsdEZpbHRlciA/IGNvbGxlY3Rpb24gOiAkYTllNzM4MmE3ZDExMWNiNSR2YXIkZmlsdGVyQ29sbGVjdGlvbihjb2xsZWN0aW9uLCBpbnB1dFZhbHVlLCBkZWZhdWx0RmlsdGVyKSwgW1xuICAgICAgICBjb2xsZWN0aW9uLFxuICAgICAgICBpbnB1dFZhbHVlLFxuICAgICAgICBkZWZhdWx0RmlsdGVyLFxuICAgICAgICBwcm9wcy5pdGVtc1xuICAgIF0pO1xuICAgIGxldCBbbGFzdENvbGxlY3Rpb24sIHNldExhc3RDb2xsZWN0aW9uXSA9ICgwLCAkNDlCSlAkdXNlU3RhdGUpKGZpbHRlcmVkQ29sbGVjdGlvbik7XG4gICAgLy8gVHJhY2sgd2hhdCBhY3Rpb24gaXMgYXR0ZW1wdGluZyB0byBvcGVuIHRoZSBtZW51XG4gICAgbGV0IG1lbnVPcGVuVHJpZ2dlciA9ICgwLCAkNDlCSlAkdXNlUmVmKSgnZm9jdXMnKTtcbiAgICBsZXQgb25PcGVuQ2hhbmdlID0gKG9wZW4pPT57XG4gICAgICAgIGlmIChwcm9wcy5vbk9wZW5DaGFuZ2UpIHByb3BzLm9uT3BlbkNoYW5nZShvcGVuLCBvcGVuID8gbWVudU9wZW5UcmlnZ2VyLmN1cnJlbnQgOiB1bmRlZmluZWQpO1xuICAgICAgICBzZWxlY3Rpb25NYW5hZ2VyLnNldEZvY3VzZWQob3Blbik7XG4gICAgICAgIGlmICghb3Blbikgc2VsZWN0aW9uTWFuYWdlci5zZXRGb2N1c2VkS2V5KG51bGwpO1xuICAgIH07XG4gICAgbGV0IHRyaWdnZXJTdGF0ZSA9ICgwLCAkNDlCSlAkdXNlT3ZlcmxheVRyaWdnZXJTdGF0ZSkoe1xuICAgICAgICAuLi5wcm9wcyxcbiAgICAgICAgb25PcGVuQ2hhbmdlOiBvbk9wZW5DaGFuZ2UsXG4gICAgICAgIGlzT3BlbjogdW5kZWZpbmVkLFxuICAgICAgICBkZWZhdWx0T3BlbjogdW5kZWZpbmVkXG4gICAgfSk7XG4gICAgbGV0IG9wZW4gPSAoZm9jdXNTdHJhdGVneSA9IG51bGwsIHRyaWdnZXIpPT57XG4gICAgICAgIGxldCBkaXNwbGF5QWxsSXRlbXMgPSB0cmlnZ2VyID09PSAnbWFudWFsJyB8fCB0cmlnZ2VyID09PSAnZm9jdXMnICYmIG1lbnVUcmlnZ2VyID09PSAnZm9jdXMnO1xuICAgICAgICAvLyBQcmV2ZW50IG9wZW4gb3BlcmF0aW9ucyBmcm9tIHRyaWdnZXJpbmcgaWYgdGhlcmUgaXMgbm90aGluZyB0byBkaXNwbGF5XG4gICAgICAgIC8vIEFsc28gcHJldmVudCBvcGVuIG9wZXJhdGlvbnMgZnJvbSB0cmlnZ2VyaW5nIGlmIGl0ZW1zIGFyZSB1bmNvbnRyb2xsZWQgYnV0IGRlZmF1bHRJdGVtcyBpcyBlbXB0eSwgZXZlbiBpZiBkaXNwbGF5QWxsSXRlbXMgaXMgdHJ1ZS5cbiAgICAgICAgLy8gVGhpcyBpcyB0byBwcmV2ZW50IGNvbWJvYm94ZXMgd2l0aCBlbXB0eSBkZWZhdWx0SXRlbXMgZnJvbSBvcGVuaW5nIGJ1dCBhbGxvdyBjb250cm9sbGVkIGl0ZW1zIGNvbWJvYm94ZXMgdG8gb3BlbiBldmVuIGlmIHRoZSBpbml0YWwgbGlzdCBpcyBlbXB0eSAoYXNzdW1wdGlvbiBpcyB1c2VyIHdpbGwgcHJvdmlkZSBzd2FwIHRoZSBlbXB0eSBsaXN0IHdpdGggYSBiYXNlIGxpc3QgdmlhIG9uT3BlbkNoYW5nZSByZXR1cm5pbmcgYG1lbnVUcmlnZ2VyYCBtYW51YWwpXG4gICAgICAgIGlmIChhbGxvd3NFbXB0eUNvbGxlY3Rpb24gfHwgZmlsdGVyZWRDb2xsZWN0aW9uLnNpemUgPiAwIHx8IGRpc3BsYXlBbGxJdGVtcyAmJiBvcmlnaW5hbENvbGxlY3Rpb24uc2l6ZSA+IDAgfHwgcHJvcHMuaXRlbXMpIHtcbiAgICAgICAgICAgIGlmIChkaXNwbGF5QWxsSXRlbXMgJiYgIXRyaWdnZXJTdGF0ZS5pc09wZW4gJiYgcHJvcHMuaXRlbXMgPT09IHVuZGVmaW5lZCkgLy8gU2hvdyBhbGwgaXRlbXMgaWYgbWVudSBpcyBtYW51YWxseSBvcGVuZWQuIE9ubHkgY2FyZSBhYm91dCB0aGlzIGlmIGl0ZW1zIGFyZSB1bmRlZmluZWRcbiAgICAgICAgICAgIHNldFNob3dBbGxJdGVtcyh0cnVlKTtcbiAgICAgICAgICAgIG1lbnVPcGVuVHJpZ2dlci5jdXJyZW50ID0gdHJpZ2dlcjtcbiAgICAgICAgICAgIHNldEZvY3VzU3RyYXRlZ3koZm9jdXNTdHJhdGVneSk7XG4gICAgICAgICAgICB0cmlnZ2VyU3RhdGUub3BlbigpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBsZXQgdG9nZ2xlID0gKGZvY3VzU3RyYXRlZ3kgPSBudWxsLCB0cmlnZ2VyKT0+e1xuICAgICAgICBsZXQgZGlzcGxheUFsbEl0ZW1zID0gdHJpZ2dlciA9PT0gJ21hbnVhbCcgfHwgdHJpZ2dlciA9PT0gJ2ZvY3VzJyAmJiBtZW51VHJpZ2dlciA9PT0gJ2ZvY3VzJztcbiAgICAgICAgLy8gSWYgdGhlIG1lbnUgaXMgY2xvc2VkIGFuZCB0aGVyZSBpcyBub3RoaW5nIHRvIGRpc3BsYXksIGVhcmx5IHJldHVybiBzbyB0b2dnbGUgaXNuJ3QgY2FsbGVkIHRvIHByZXZlbnQgZXh0cmFuZW91cyBvbk9wZW5DaGFuZ2VcbiAgICAgICAgaWYgKCEoYWxsb3dzRW1wdHlDb2xsZWN0aW9uIHx8IGZpbHRlcmVkQ29sbGVjdGlvbi5zaXplID4gMCB8fCBkaXNwbGF5QWxsSXRlbXMgJiYgb3JpZ2luYWxDb2xsZWN0aW9uLnNpemUgPiAwIHx8IHByb3BzLml0ZW1zKSAmJiAhdHJpZ2dlclN0YXRlLmlzT3BlbikgcmV0dXJuO1xuICAgICAgICBpZiAoZGlzcGxheUFsbEl0ZW1zICYmICF0cmlnZ2VyU3RhdGUuaXNPcGVuICYmIHByb3BzLml0ZW1zID09PSB1bmRlZmluZWQpIC8vIFNob3cgYWxsIGl0ZW1zIGlmIG1lbnUgaXMgdG9nZ2xlZCBvcGVuLiBPbmx5IGNhcmUgYWJvdXQgdGhpcyBpZiBpdGVtcyBhcmUgdW5kZWZpbmVkXG4gICAgICAgIHNldFNob3dBbGxJdGVtcyh0cnVlKTtcbiAgICAgICAgLy8gT25seSB1cGRhdGUgdGhlIG1lbnVPcGVuVHJpZ2dlciBpZiBtZW51IGlzIGN1cnJlbnRseSBjbG9zZWRcbiAgICAgICAgaWYgKCF0cmlnZ2VyU3RhdGUuaXNPcGVuKSBtZW51T3BlblRyaWdnZXIuY3VycmVudCA9IHRyaWdnZXI7XG4gICAgICAgIHRvZ2dsZU1lbnUoZm9jdXNTdHJhdGVneSk7XG4gICAgfTtcbiAgICBsZXQgdXBkYXRlTGFzdENvbGxlY3Rpb24gPSAoMCwgJDQ5QkpQJHVzZUNhbGxiYWNrKSgoKT0+e1xuICAgICAgICBzZXRMYXN0Q29sbGVjdGlvbihzaG93QWxsSXRlbXMgPyBvcmlnaW5hbENvbGxlY3Rpb24gOiBmaWx0ZXJlZENvbGxlY3Rpb24pO1xuICAgIH0sIFtcbiAgICAgICAgc2hvd0FsbEl0ZW1zLFxuICAgICAgICBvcmlnaW5hbENvbGxlY3Rpb24sXG4gICAgICAgIGZpbHRlcmVkQ29sbGVjdGlvblxuICAgIF0pO1xuICAgIC8vIElmIG1lbnUgaXMgZ29pbmcgdG8gY2xvc2UsIHNhdmUgdGhlIGN1cnJlbnQgY29sbGVjdGlvbiBzbyB3ZSBjYW4gZnJlZXplIHRoZSBkaXNwbGF5ZWQgY29sbGVjdGlvbiB3aGVuIHRoZVxuICAgIC8vIHVzZXIgY2xpY2tzIG91dHNpZGUgdGhlIHBvcG92ZXIgdG8gY2xvc2UgdGhlIG1lbnUuIFByZXZlbnRzIHRoZSBtZW51IGNvbnRlbnRzIGZyb20gdXBkYXRpbmcgYXMgdGhlIG1lbnUgY2xvc2VzLlxuICAgIGxldCB0b2dnbGVNZW51ID0gKDAsICQ0OUJKUCR1c2VDYWxsYmFjaykoKGZvY3VzU3RyYXRlZ3kgPSBudWxsKT0+e1xuICAgICAgICBpZiAodHJpZ2dlclN0YXRlLmlzT3BlbikgdXBkYXRlTGFzdENvbGxlY3Rpb24oKTtcbiAgICAgICAgc2V0Rm9jdXNTdHJhdGVneShmb2N1c1N0cmF0ZWd5KTtcbiAgICAgICAgdHJpZ2dlclN0YXRlLnRvZ2dsZSgpO1xuICAgIH0sIFtcbiAgICAgICAgdHJpZ2dlclN0YXRlLFxuICAgICAgICB1cGRhdGVMYXN0Q29sbGVjdGlvblxuICAgIF0pO1xuICAgIGxldCBjbG9zZU1lbnUgPSAoMCwgJDQ5QkpQJHVzZUNhbGxiYWNrKSgoKT0+e1xuICAgICAgICBpZiAodHJpZ2dlclN0YXRlLmlzT3Blbikge1xuICAgICAgICAgICAgdXBkYXRlTGFzdENvbGxlY3Rpb24oKTtcbiAgICAgICAgICAgIHRyaWdnZXJTdGF0ZS5jbG9zZSgpO1xuICAgICAgICB9XG4gICAgfSwgW1xuICAgICAgICB0cmlnZ2VyU3RhdGUsXG4gICAgICAgIHVwZGF0ZUxhc3RDb2xsZWN0aW9uXG4gICAgXSk7XG4gICAgbGV0IFtsYXN0VmFsdWUsIHNldExhc3RWYWx1ZV0gPSAoMCwgJDQ5QkpQJHVzZVN0YXRlKShpbnB1dFZhbHVlKTtcbiAgICBsZXQgcmVzZXRJbnB1dFZhbHVlID0gKCk9PntcbiAgICAgICAgdmFyIF9jb2xsZWN0aW9uX2dldEl0ZW07XG4gICAgICAgIHZhciBfY29sbGVjdGlvbl9nZXRJdGVtX3RleHRWYWx1ZTtcbiAgICAgICAgbGV0IGl0ZW1UZXh0ID0gc2VsZWN0ZWRLZXkgIT0gbnVsbCA/IChfY29sbGVjdGlvbl9nZXRJdGVtX3RleHRWYWx1ZSA9IChfY29sbGVjdGlvbl9nZXRJdGVtID0gY29sbGVjdGlvbi5nZXRJdGVtKHNlbGVjdGVkS2V5KSkgPT09IG51bGwgfHwgX2NvbGxlY3Rpb25fZ2V0SXRlbSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2NvbGxlY3Rpb25fZ2V0SXRlbS50ZXh0VmFsdWUpICE9PSBudWxsICYmIF9jb2xsZWN0aW9uX2dldEl0ZW1fdGV4dFZhbHVlICE9PSB2b2lkIDAgPyBfY29sbGVjdGlvbl9nZXRJdGVtX3RleHRWYWx1ZSA6ICcnIDogJyc7XG4gICAgICAgIHNldExhc3RWYWx1ZShpdGVtVGV4dCk7XG4gICAgICAgIHNldElucHV0VmFsdWUoaXRlbVRleHQpO1xuICAgIH07XG4gICAgdmFyIF9wcm9wc19zZWxlY3RlZEtleSwgX3JlZjtcbiAgICBsZXQgbGFzdFNlbGVjdGVkS2V5ID0gKDAsICQ0OUJKUCR1c2VSZWYpKChfcmVmID0gKF9wcm9wc19zZWxlY3RlZEtleSA9IHByb3BzLnNlbGVjdGVkS2V5KSAhPT0gbnVsbCAmJiBfcHJvcHNfc2VsZWN0ZWRLZXkgIT09IHZvaWQgMCA/IF9wcm9wc19zZWxlY3RlZEtleSA6IHByb3BzLmRlZmF1bHRTZWxlY3RlZEtleSkgIT09IG51bGwgJiYgX3JlZiAhPT0gdm9pZCAwID8gX3JlZiA6IG51bGwpO1xuICAgIHZhciBfY29sbGVjdGlvbl9nZXRJdGVtX3RleHRWYWx1ZTE7XG4gICAgbGV0IGxhc3RTZWxlY3RlZEtleVRleHQgPSAoMCwgJDQ5QkpQJHVzZVJlZikoc2VsZWN0ZWRLZXkgIT0gbnVsbCA/IChfY29sbGVjdGlvbl9nZXRJdGVtX3RleHRWYWx1ZTEgPSAoX2NvbGxlY3Rpb25fZ2V0SXRlbSA9IGNvbGxlY3Rpb24uZ2V0SXRlbShzZWxlY3RlZEtleSkpID09PSBudWxsIHx8IF9jb2xsZWN0aW9uX2dldEl0ZW0gPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9jb2xsZWN0aW9uX2dldEl0ZW0udGV4dFZhbHVlKSAhPT0gbnVsbCAmJiBfY29sbGVjdGlvbl9nZXRJdGVtX3RleHRWYWx1ZTEgIT09IHZvaWQgMCA/IF9jb2xsZWN0aW9uX2dldEl0ZW1fdGV4dFZhbHVlMSA6ICcnIDogJycpO1xuICAgIC8vIGludGVudGlvbmFsIG9taXQgZGVwZW5kZW5jeSBhcnJheSwgd2FudCB0aGlzIHRvIGhhcHBlbiBvbiBldmVyeSByZW5kZXJcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgcmVhY3QtaG9va3MvZXhoYXVzdGl2ZS1kZXBzXG4gICAgKDAsICQ0OUJKUCR1c2VFZmZlY3QpKCgpPT57XG4gICAgICAgIHZhciBfY29sbGVjdGlvbl9nZXRJdGVtO1xuICAgICAgICAvLyBPcGVuIGFuZCBjbG9zZSBtZW51IGF1dG9tYXRpY2FsbHkgd2hlbiB0aGUgaW5wdXQgdmFsdWUgY2hhbmdlcyBpZiB0aGUgaW5wdXQgaXMgZm9jdXNlZCxcbiAgICAgICAgLy8gYW5kIHRoZXJlIGFyZSBpdGVtcyBpbiB0aGUgY29sbGVjdGlvbiBvciBhbGxvd0VtcHR5Q29sbGVjdGlvbiBpcyB0cnVlLlxuICAgICAgICBpZiAoaXNGb2N1c2VkICYmIChmaWx0ZXJlZENvbGxlY3Rpb24uc2l6ZSA+IDAgfHwgYWxsb3dzRW1wdHlDb2xsZWN0aW9uKSAmJiAhdHJpZ2dlclN0YXRlLmlzT3BlbiAmJiBpbnB1dFZhbHVlICE9PSBsYXN0VmFsdWUgJiYgbWVudVRyaWdnZXIgIT09ICdtYW51YWwnKSBvcGVuKG51bGwsICdpbnB1dCcpO1xuICAgICAgICAvLyBDbG9zZSB0aGUgbWVudSBpZiB0aGUgY29sbGVjdGlvbiBpcyBlbXB0eS4gRG9uJ3QgY2xvc2UgbWVudSBpZiBmaWx0ZXJlZCBjb2xsZWN0aW9uIHNpemUgaXMgMFxuICAgICAgICAvLyBidXQgd2UgYXJlIGN1cnJlbnRseSBzaG93aW5nIGFsbCBpdGVtcyB2aWEgYnV0dG9uIHByZXNzXG4gICAgICAgIGlmICghc2hvd0FsbEl0ZW1zICYmICFhbGxvd3NFbXB0eUNvbGxlY3Rpb24gJiYgdHJpZ2dlclN0YXRlLmlzT3BlbiAmJiBmaWx0ZXJlZENvbGxlY3Rpb24uc2l6ZSA9PT0gMCkgY2xvc2VNZW51KCk7XG4gICAgICAgIC8vIENsb3NlIHdoZW4gYW4gaXRlbSBpcyBzZWxlY3RlZC5cbiAgICAgICAgaWYgKHNlbGVjdGVkS2V5ICE9IG51bGwgJiYgc2VsZWN0ZWRLZXkgIT09IGxhc3RTZWxlY3RlZEtleS5jdXJyZW50KSBjbG9zZU1lbnUoKTtcbiAgICAgICAgLy8gQ2xlYXIgZm9jdXNlZCBrZXkgd2hlbiBpbnB1dCB2YWx1ZSBjaGFuZ2VzIGFuZCBkaXNwbGF5IGZpbHRlcmVkIGNvbGxlY3Rpb24gYWdhaW4uXG4gICAgICAgIGlmIChpbnB1dFZhbHVlICE9PSBsYXN0VmFsdWUpIHtcbiAgICAgICAgICAgIHNlbGVjdGlvbk1hbmFnZXIuc2V0Rm9jdXNlZEtleShudWxsKTtcbiAgICAgICAgICAgIHNldFNob3dBbGxJdGVtcyhmYWxzZSk7XG4gICAgICAgICAgICAvLyBTZXQgc2VsZWN0ZWRLZXkgdG8gbnVsbCB3aGVuIHRoZSB1c2VyIGNsZWFycyB0aGUgaW5wdXQuXG4gICAgICAgICAgICAvLyBJZiBjb250cm9sbGVkLCB0aGlzIGlzIHRoZSBhcHBsaWNhdGlvbiBkZXZlbG9wZXIncyByZXNwb25zaWJpbGl0eS5cbiAgICAgICAgICAgIGlmIChpbnB1dFZhbHVlID09PSAnJyAmJiAocHJvcHMuaW5wdXRWYWx1ZSA9PT0gdW5kZWZpbmVkIHx8IHByb3BzLnNlbGVjdGVkS2V5ID09PSB1bmRlZmluZWQpKSBzZXRTZWxlY3RlZEtleShudWxsKTtcbiAgICAgICAgfVxuICAgICAgICAvLyBJZiB0aGUgc2VsZWN0ZWRLZXkgY2hhbmdlZCwgdXBkYXRlIHRoZSBpbnB1dCB2YWx1ZS5cbiAgICAgICAgLy8gRG8gbm90aGluZyBpZiBib3RoIGlucHV0VmFsdWUgYW5kIHNlbGVjdGVkS2V5IGFyZSBjb250cm9sbGVkLlxuICAgICAgICAvLyBJbiB0aGlzIGNhc2UsIGl0J3MgdGhlIHVzZXIncyByZXNwb25zaWJpbGl0eSB0byB1cGRhdGUgaW5wdXRWYWx1ZSBpbiBvblNlbGVjdGlvbkNoYW5nZS5cbiAgICAgICAgaWYgKHNlbGVjdGVkS2V5ICE9PSBsYXN0U2VsZWN0ZWRLZXkuY3VycmVudCAmJiAocHJvcHMuaW5wdXRWYWx1ZSA9PT0gdW5kZWZpbmVkIHx8IHByb3BzLnNlbGVjdGVkS2V5ID09PSB1bmRlZmluZWQpKSByZXNldElucHV0VmFsdWUoKTtcbiAgICAgICAgZWxzZSBpZiAobGFzdFZhbHVlICE9PSBpbnB1dFZhbHVlKSBzZXRMYXN0VmFsdWUoaW5wdXRWYWx1ZSk7XG4gICAgICAgIHZhciBfY29sbGVjdGlvbl9nZXRJdGVtX3RleHRWYWx1ZTtcbiAgICAgICAgLy8gVXBkYXRlIHRoZSBpbnB1dFZhbHVlIGlmIHRoZSBzZWxlY3RlZCBpdGVtJ3MgdGV4dCBjaGFuZ2VzIGZyb20gaXRzIGxhc3QgdHJhY2tlZCB2YWx1ZS5cbiAgICAgICAgLy8gVGhpcyBpcyB0byBoYW5kbGUgY2FzZXMgd2hlcmUgYSBzZWxlY3RlZEtleSBpcyBzcGVjaWZpZWQgYnV0IHRoZSBpdGVtcyBhcmVuJ3QgYXZhaWxhYmxlIChhc3luYyBsb2FkaW5nKSBvciB0aGUgc2VsZWN0ZWQgaXRlbSdzIHRleHQgdmFsdWUgdXBkYXRlcy5cbiAgICAgICAgLy8gT25seSByZXNldCBpZiB0aGUgdXNlciBpc24ndCBjdXJyZW50bHkgd2l0aGluIHRoZSBmaWVsZCBzbyB3ZSBkb24ndCBlcnJvbmVvdXNseSBtb2RpZnkgdXNlciBpbnB1dC5cbiAgICAgICAgLy8gSWYgaW5wdXRWYWx1ZSBpcyBjb250cm9sbGVkLCBpdCBpcyB0aGUgdXNlcidzIHJlc3BvbnNpYmlsaXR5IHRvIHVwZGF0ZSB0aGUgaW5wdXRWYWx1ZSB3aGVuIGl0ZW1zIGNoYW5nZS5cbiAgICAgICAgbGV0IHNlbGVjdGVkSXRlbVRleHQgPSBzZWxlY3RlZEtleSAhPSBudWxsID8gKF9jb2xsZWN0aW9uX2dldEl0ZW1fdGV4dFZhbHVlID0gKF9jb2xsZWN0aW9uX2dldEl0ZW0gPSBjb2xsZWN0aW9uLmdldEl0ZW0oc2VsZWN0ZWRLZXkpKSA9PT0gbnVsbCB8fCBfY29sbGVjdGlvbl9nZXRJdGVtID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfY29sbGVjdGlvbl9nZXRJdGVtLnRleHRWYWx1ZSkgIT09IG51bGwgJiYgX2NvbGxlY3Rpb25fZ2V0SXRlbV90ZXh0VmFsdWUgIT09IHZvaWQgMCA/IF9jb2xsZWN0aW9uX2dldEl0ZW1fdGV4dFZhbHVlIDogJycgOiAnJztcbiAgICAgICAgaWYgKCFpc0ZvY3VzZWQgJiYgc2VsZWN0ZWRLZXkgIT0gbnVsbCAmJiBwcm9wcy5pbnB1dFZhbHVlID09PSB1bmRlZmluZWQgJiYgc2VsZWN0ZWRLZXkgPT09IGxhc3RTZWxlY3RlZEtleS5jdXJyZW50KSB7XG4gICAgICAgICAgICBpZiAobGFzdFNlbGVjdGVkS2V5VGV4dC5jdXJyZW50ICE9PSBzZWxlY3RlZEl0ZW1UZXh0KSB7XG4gICAgICAgICAgICAgICAgc2V0TGFzdFZhbHVlKHNlbGVjdGVkSXRlbVRleHQpO1xuICAgICAgICAgICAgICAgIHNldElucHV0VmFsdWUoc2VsZWN0ZWRJdGVtVGV4dCk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgbGFzdFNlbGVjdGVkS2V5LmN1cnJlbnQgPSBzZWxlY3RlZEtleTtcbiAgICAgICAgbGFzdFNlbGVjdGVkS2V5VGV4dC5jdXJyZW50ID0gc2VsZWN0ZWRJdGVtVGV4dDtcbiAgICB9KTtcbiAgICBsZXQgdmFsaWRhdGlvbiA9ICgwLCAkNDlCSlAkdXNlRm9ybVZhbGlkYXRpb25TdGF0ZSkoe1xuICAgICAgICAuLi5wcm9wcyxcbiAgICAgICAgdmFsdWU6ICgwLCAkNDlCSlAkdXNlTWVtbykoKCk9Pih7XG4gICAgICAgICAgICAgICAgaW5wdXRWYWx1ZTogaW5wdXRWYWx1ZSxcbiAgICAgICAgICAgICAgICBzZWxlY3RlZEtleTogc2VsZWN0ZWRLZXlcbiAgICAgICAgICAgIH0pLCBbXG4gICAgICAgICAgICBpbnB1dFZhbHVlLFxuICAgICAgICAgICAgc2VsZWN0ZWRLZXlcbiAgICAgICAgXSlcbiAgICB9KTtcbiAgICAvLyBSZXZlcnQgaW5wdXQgdmFsdWUgYW5kIGNsb3NlIG1lbnVcbiAgICBsZXQgcmV2ZXJ0ID0gKCk9PntcbiAgICAgICAgaWYgKGFsbG93c0N1c3RvbVZhbHVlICYmIHNlbGVjdGVkS2V5ID09IG51bGwpIGNvbW1pdEN1c3RvbVZhbHVlKCk7XG4gICAgICAgIGVsc2UgY29tbWl0U2VsZWN0aW9uKCk7XG4gICAgfTtcbiAgICBsZXQgY29tbWl0Q3VzdG9tVmFsdWUgPSAoKT0+e1xuICAgICAgICBsYXN0U2VsZWN0ZWRLZXkuY3VycmVudCA9IG51bGw7XG4gICAgICAgIHNldFNlbGVjdGVkS2V5KG51bGwpO1xuICAgICAgICBjbG9zZU1lbnUoKTtcbiAgICB9O1xuICAgIGxldCBjb21taXRTZWxlY3Rpb24gPSAoKT0+e1xuICAgICAgICAvLyBJZiBtdWx0aXBsZSB0aGluZ3MgYXJlIGNvbnRyb2xsZWQsIGNhbGwgb25TZWxlY3Rpb25DaGFuZ2VcbiAgICAgICAgaWYgKHByb3BzLnNlbGVjdGVkS2V5ICE9PSB1bmRlZmluZWQgJiYgcHJvcHMuaW5wdXRWYWx1ZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICB2YXIgX3Byb3BzX29uU2VsZWN0aW9uQ2hhbmdlLCBfY29sbGVjdGlvbl9nZXRJdGVtO1xuICAgICAgICAgICAgKF9wcm9wc19vblNlbGVjdGlvbkNoYW5nZSA9IHByb3BzLm9uU2VsZWN0aW9uQ2hhbmdlKSA9PT0gbnVsbCB8fCBfcHJvcHNfb25TZWxlY3Rpb25DaGFuZ2UgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9wcm9wc19vblNlbGVjdGlvbkNoYW5nZS5jYWxsKHByb3BzLCBzZWxlY3RlZEtleSk7XG4gICAgICAgICAgICB2YXIgX2NvbGxlY3Rpb25fZ2V0SXRlbV90ZXh0VmFsdWU7XG4gICAgICAgICAgICAvLyBTdG9wIG1lbnUgZnJvbSByZW9wZW5pbmcgZnJvbSB1c2VFZmZlY3RcbiAgICAgICAgICAgIGxldCBpdGVtVGV4dCA9IHNlbGVjdGVkS2V5ICE9IG51bGwgPyAoX2NvbGxlY3Rpb25fZ2V0SXRlbV90ZXh0VmFsdWUgPSAoX2NvbGxlY3Rpb25fZ2V0SXRlbSA9IGNvbGxlY3Rpb24uZ2V0SXRlbShzZWxlY3RlZEtleSkpID09PSBudWxsIHx8IF9jb2xsZWN0aW9uX2dldEl0ZW0gPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9jb2xsZWN0aW9uX2dldEl0ZW0udGV4dFZhbHVlKSAhPT0gbnVsbCAmJiBfY29sbGVjdGlvbl9nZXRJdGVtX3RleHRWYWx1ZSAhPT0gdm9pZCAwID8gX2NvbGxlY3Rpb25fZ2V0SXRlbV90ZXh0VmFsdWUgOiAnJyA6ICcnO1xuICAgICAgICAgICAgc2V0TGFzdFZhbHVlKGl0ZW1UZXh0KTtcbiAgICAgICAgICAgIGNsb3NlTWVudSgpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgLy8gSWYgb25seSBhIHNpbmdsZSBhc3BlY3Qgb2YgY29tYm9ib3ggaXMgY29udHJvbGxlZCwgcmVzZXQgaW5wdXQgdmFsdWUgYW5kIGNsb3NlIG1lbnUgZm9yIHRoZSB1c2VyXG4gICAgICAgICAgICByZXNldElucHV0VmFsdWUoKTtcbiAgICAgICAgICAgIGNsb3NlTWVudSgpO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBjb25zdCBjb21taXRWYWx1ZSA9ICgpPT57XG4gICAgICAgIGlmIChhbGxvd3NDdXN0b21WYWx1ZSkge1xuICAgICAgICAgICAgdmFyIF9jb2xsZWN0aW9uX2dldEl0ZW07XG4gICAgICAgICAgICB2YXIgX2NvbGxlY3Rpb25fZ2V0SXRlbV90ZXh0VmFsdWU7XG4gICAgICAgICAgICBjb25zdCBpdGVtVGV4dCA9IHNlbGVjdGVkS2V5ICE9IG51bGwgPyAoX2NvbGxlY3Rpb25fZ2V0SXRlbV90ZXh0VmFsdWUgPSAoX2NvbGxlY3Rpb25fZ2V0SXRlbSA9IGNvbGxlY3Rpb24uZ2V0SXRlbShzZWxlY3RlZEtleSkpID09PSBudWxsIHx8IF9jb2xsZWN0aW9uX2dldEl0ZW0gPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9jb2xsZWN0aW9uX2dldEl0ZW0udGV4dFZhbHVlKSAhPT0gbnVsbCAmJiBfY29sbGVjdGlvbl9nZXRJdGVtX3RleHRWYWx1ZSAhPT0gdm9pZCAwID8gX2NvbGxlY3Rpb25fZ2V0SXRlbV90ZXh0VmFsdWUgOiAnJyA6ICcnO1xuICAgICAgICAgICAgaW5wdXRWYWx1ZSA9PT0gaXRlbVRleHQgPyBjb21taXRTZWxlY3Rpb24oKSA6IGNvbW1pdEN1c3RvbVZhbHVlKCk7XG4gICAgICAgIH0gZWxzZSAvLyBSZXNldCBpbnB1dFZhbHVlIGFuZCBjbG9zZSBtZW51XG4gICAgICAgIGNvbW1pdFNlbGVjdGlvbigpO1xuICAgIH07XG4gICAgbGV0IGNvbW1pdCA9ICgpPT57XG4gICAgICAgIGlmICh0cmlnZ2VyU3RhdGUuaXNPcGVuICYmIHNlbGVjdGlvbk1hbmFnZXIuZm9jdXNlZEtleSAhPSBudWxsKSB7XG4gICAgICAgICAgICAvLyBSZXNldCBpbnB1dFZhbHVlIGFuZCBjbG9zZSBtZW51IGhlcmUgaWYgdGhlIHNlbGVjdGVkIGtleSBpcyBhbHJlYWR5IHRoZSBmb2N1c2VkIGtleS4gT3RoZXJ3aXNlXG4gICAgICAgICAgICAvLyBmaXJlIG9uU2VsZWN0aW9uQ2hhbmdlIHRvIGFsbG93IHRoZSBhcHBsaWNhdGlvbiB0byBjb250cm9sIHRoZSBjbG9zaW5nLlxuICAgICAgICAgICAgaWYgKHNlbGVjdGVkS2V5ID09PSBzZWxlY3Rpb25NYW5hZ2VyLmZvY3VzZWRLZXkpIGNvbW1pdFNlbGVjdGlvbigpO1xuICAgICAgICAgICAgZWxzZSBzZXRTZWxlY3RlZEtleShzZWxlY3Rpb25NYW5hZ2VyLmZvY3VzZWRLZXkpO1xuICAgICAgICB9IGVsc2UgY29tbWl0VmFsdWUoKTtcbiAgICB9O1xuICAgIGxldCB2YWx1ZU9uRm9jdXMgPSAoMCwgJDQ5QkpQJHVzZVJlZikoaW5wdXRWYWx1ZSk7XG4gICAgbGV0IHNldEZvY3VzZWQgPSAoaXNGb2N1c2VkKT0+e1xuICAgICAgICBpZiAoaXNGb2N1c2VkKSB7XG4gICAgICAgICAgICB2YWx1ZU9uRm9jdXMuY3VycmVudCA9IGlucHV0VmFsdWU7XG4gICAgICAgICAgICBpZiAobWVudVRyaWdnZXIgPT09ICdmb2N1cycgJiYgIXByb3BzLmlzUmVhZE9ubHkpIG9wZW4obnVsbCwgJ2ZvY3VzJyk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBpZiAoc2hvdWxkQ2xvc2VPbkJsdXIpIGNvbW1pdFZhbHVlKCk7XG4gICAgICAgICAgICBpZiAoaW5wdXRWYWx1ZSAhPT0gdmFsdWVPbkZvY3VzLmN1cnJlbnQpIHZhbGlkYXRpb24uY29tbWl0VmFsaWRhdGlvbigpO1xuICAgICAgICB9XG4gICAgICAgIHNldEZvY3VzZWRTdGF0ZShpc0ZvY3VzZWQpO1xuICAgIH07XG4gICAgbGV0IGRpc3BsYXllZENvbGxlY3Rpb24gPSAoMCwgJDQ5QkpQJHVzZU1lbW8pKCgpPT57XG4gICAgICAgIGlmICh0cmlnZ2VyU3RhdGUuaXNPcGVuKSB7XG4gICAgICAgICAgICBpZiAoc2hvd0FsbEl0ZW1zKSByZXR1cm4gb3JpZ2luYWxDb2xsZWN0aW9uO1xuICAgICAgICAgICAgZWxzZSByZXR1cm4gZmlsdGVyZWRDb2xsZWN0aW9uO1xuICAgICAgICB9IGVsc2UgcmV0dXJuIGxhc3RDb2xsZWN0aW9uO1xuICAgIH0sIFtcbiAgICAgICAgdHJpZ2dlclN0YXRlLmlzT3BlbixcbiAgICAgICAgb3JpZ2luYWxDb2xsZWN0aW9uLFxuICAgICAgICBmaWx0ZXJlZENvbGxlY3Rpb24sXG4gICAgICAgIHNob3dBbGxJdGVtcyxcbiAgICAgICAgbGFzdENvbGxlY3Rpb25cbiAgICBdKTtcbiAgICByZXR1cm4ge1xuICAgICAgICAuLi52YWxpZGF0aW9uLFxuICAgICAgICAuLi50cmlnZ2VyU3RhdGUsXG4gICAgICAgIGZvY3VzU3RyYXRlZ3k6IGZvY3VzU3RyYXRlZ3ksXG4gICAgICAgIHRvZ2dsZTogdG9nZ2xlLFxuICAgICAgICBvcGVuOiBvcGVuLFxuICAgICAgICBjbG9zZTogY29tbWl0VmFsdWUsXG4gICAgICAgIHNlbGVjdGlvbk1hbmFnZXI6IHNlbGVjdGlvbk1hbmFnZXIsXG4gICAgICAgIHNlbGVjdGVkS2V5OiBzZWxlY3RlZEtleSxcbiAgICAgICAgc2V0U2VsZWN0ZWRLZXk6IHNldFNlbGVjdGVkS2V5LFxuICAgICAgICBkaXNhYmxlZEtleXM6IGRpc2FibGVkS2V5cyxcbiAgICAgICAgaXNGb2N1c2VkOiBpc0ZvY3VzZWQsXG4gICAgICAgIHNldEZvY3VzZWQ6IHNldEZvY3VzZWQsXG4gICAgICAgIHNlbGVjdGVkSXRlbTogc2VsZWN0ZWRJdGVtLFxuICAgICAgICBjb2xsZWN0aW9uOiBkaXNwbGF5ZWRDb2xsZWN0aW9uLFxuICAgICAgICBpbnB1dFZhbHVlOiBpbnB1dFZhbHVlLFxuICAgICAgICBzZXRJbnB1dFZhbHVlOiBzZXRJbnB1dFZhbHVlLFxuICAgICAgICBjb21taXQ6IGNvbW1pdCxcbiAgICAgICAgcmV2ZXJ0OiByZXZlcnRcbiAgICB9O1xufVxuZnVuY3Rpb24gJGE5ZTczODJhN2QxMTFjYjUkdmFyJGZpbHRlckNvbGxlY3Rpb24oY29sbGVjdGlvbiwgaW5wdXRWYWx1ZSwgZmlsdGVyKSB7XG4gICAgcmV0dXJuIG5ldyAoMCwgJDQ5QkpQJExpc3RDb2xsZWN0aW9uKSgkYTllNzM4MmE3ZDExMWNiNSR2YXIkZmlsdGVyTm9kZXMoY29sbGVjdGlvbiwgY29sbGVjdGlvbiwgaW5wdXRWYWx1ZSwgZmlsdGVyKSk7XG59XG5mdW5jdGlvbiAkYTllNzM4MmE3ZDExMWNiNSR2YXIkZmlsdGVyTm9kZXMoY29sbGVjdGlvbiwgbm9kZXMsIGlucHV0VmFsdWUsIGZpbHRlcikge1xuICAgIGxldCBmaWx0ZXJlZE5vZGUgPSBbXTtcbiAgICBmb3IgKGxldCBub2RlIG9mIG5vZGVzKXtcbiAgICAgICAgaWYgKG5vZGUudHlwZSA9PT0gJ3NlY3Rpb24nICYmIG5vZGUuaGFzQ2hpbGROb2Rlcykge1xuICAgICAgICAgICAgbGV0IGZpbHRlcmVkID0gJGE5ZTczODJhN2QxMTFjYjUkdmFyJGZpbHRlck5vZGVzKGNvbGxlY3Rpb24sICgwLCAkNDlCSlAkZ2V0Q2hpbGROb2Rlcykobm9kZSwgY29sbGVjdGlvbiksIGlucHV0VmFsdWUsIGZpbHRlcik7XG4gICAgICAgICAgICBpZiAoW1xuICAgICAgICAgICAgICAgIC4uLmZpbHRlcmVkXG4gICAgICAgICAgICBdLnNvbWUoKG5vZGUpPT5ub2RlLnR5cGUgPT09ICdpdGVtJykpIGZpbHRlcmVkTm9kZS5wdXNoKHtcbiAgICAgICAgICAgICAgICAuLi5ub2RlLFxuICAgICAgICAgICAgICAgIGNoaWxkTm9kZXM6IGZpbHRlcmVkXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgfSBlbHNlIGlmIChub2RlLnR5cGUgPT09ICdpdGVtJyAmJiBmaWx0ZXIobm9kZS50ZXh0VmFsdWUsIGlucHV0VmFsdWUpKSBmaWx0ZXJlZE5vZGUucHVzaCh7XG4gICAgICAgICAgICAuLi5ub2RlXG4gICAgICAgIH0pO1xuICAgICAgICBlbHNlIGlmIChub2RlLnR5cGUgIT09ICdpdGVtJykgZmlsdGVyZWROb2RlLnB1c2goe1xuICAgICAgICAgICAgLi4ubm9kZVxuICAgICAgICB9KTtcbiAgICB9XG4gICAgcmV0dXJuIGZpbHRlcmVkTm9kZTtcbn1cblxuXG5leHBvcnQgeyRhOWU3MzgyYTdkMTExY2I1JGV4cG9ydCRiNDUzYTNiZmQ0YTVmYTllIGFzIHVzZUNvbWJvQm94U3RhdGV9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXNlQ29tYm9Cb3hTdGF0ZS5tb2R1bGUuanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+combobox@3.1_f72b022c1f762fa7b70a1dc6ff1a7f4c/node_modules/@react-stately/combobox/dist/useComboBoxState.mjs\n");

/***/ })

};
;