'use client'

import { useState } from "react";
import { 
    Dropdown, 
    DropdownMenu,
    DropdownItem, 
    DropdownTrigger, 
    Button
} from "@heroui/react";
import { 
    Cog8ToothIcon, 
    ChevronDownIcon, 
    Bars3Icon, 
    PhotoIcon
} from "@heroicons/react/24/solid";
import { useTranslations } from 'next-intl';
import { Settings } from "./settings";
import { Gallery } from "./gallery";


export function Items() {
    const t = useTranslations('Products.sentio.items');
    const [isOpen, setIsOpen] = useState(false);
    const [isSettingsOpen, setIsSettingsOpen] = useState(false);
    const [isGalleryOpen, setIsGalleryOpen] = useState(false);

    return (
        <div>
            <Dropdown 
                placement="bottom-start"
                onOpenChange={(isOpen) => setIsOpen(isOpen)}
            >
                <DropdownTrigger>
                    <Button
                        isIconOnly
                        variant="light"
                    > 
                        {isOpen? <ChevronDownIcon className="size-6"/> : <Bars3Icon className="size-6"/>}
                    </Button>

                </DropdownTrigger>
                <DropdownMenu
                    aria-label="Items Actions" 
                    variant="flat"
                >   
                    <DropdownItem 
                        key="setting"
                        startContent={<Cog8ToothIcon className="size-6"/>}
                        onPress={() => setIsSettingsOpen(true)}
                    >
                        {t('setting')}
                    </DropdownItem>

                    <DropdownItem 
                        key="gallery"
                        startContent={<PhotoIcon className="size-6"/>}
                        onPress={() => setIsGalleryOpen(true)}
                    >
                        {t('gallery')}
                    </DropdownItem>
                </DropdownMenu>
            </Dropdown>
            <Settings isOpen={isSettingsOpen} onClose={() => setIsSettingsOpen(false)}/>
            <Gallery isOpen={isGalleryOpen} onClose={() => setIsGalleryOpen(false)}/>
        </div>
    )
}