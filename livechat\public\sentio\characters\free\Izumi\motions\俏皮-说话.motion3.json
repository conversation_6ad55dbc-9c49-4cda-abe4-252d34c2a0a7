{"Version": 3, "Meta": {"Duration": 2.633, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 29, "TotalSegmentCount": 126, "TotalPointCount": 313, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -15, 1, 0.1, -15, 0.2, -15, 0.3, -15, 1, 0.578, -15, 0.856, -15, 1.133, -15, 1, 1.222, -15, 1.311, -14, 1.4, -14, 1, 1.5, -14, 1.6, -15, 1.7, -15, 0, 2.633, -15]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 1, 1.222, 0, 1.311, 14, 1.4, 14, 1, 1.5, 14, 1.6, 0, 1.7, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -17, 1, 0.1, -17, 0.2, -17, 0.3, -17, 1, 0.578, -17, 0.856, -17, 1.133, -17, 1, 1.322, -17, 1.511, 11, 1.7, 11, 0, 2.633, 11]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 1.3, 1, 1, 1.344, 1.008, 1.389, -0.008, 1.433, 0, 0, 1.5, 0, 1, 1.556, -0.008, 1.611, 1.008, 1.667, 1, 0, 2.6, 1, 0, 2.633, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 1.3, 1, 1, 1.344, 1.008, 1.389, -0.008, 1.433, 0, 0, 1.5, 0, 1, 1.556, -0.008, 1.611, 1.008, 1.667, 1, 0, 2.6, 1, 0, 2.633, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0.7, 1, 0.1, 0.7, 0.2, 0.7, 0.3, 0.7, 1, 0.578, 0.7, 0.856, 0.7, 1.133, 0.7, 0, 2.633, 0.7]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.1, 1, 0.2, 1, 0.3, 1, 1, 0.578, 1, 0.856, 1, 1.133, 1, 0, 2.633, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 0.467, 0, 1, 0.478, -0.016, 0.489, 0.354, 0.5, 0.671, 1, 0.511, 1.241, 0.522, 0.907, 0.533, 1, 2, 0.567, 0.729, 1, 0.578, 0.44, 0.589, 0.103, 0.6, 0.118, 1, 0.611, 0.1, 0.622, 0.523, 0.633, 0.886, 3, 0.667, 1, 2, 0.7, 0.871, 1, 0.711, 0.5, 0.722, 0.068, 0.733, 0.086, 1, 0.744, 0.066, 0.756, 0.552, 0.767, 0.969, 3, 0.8, 1, 0, 0.833, 1, 2, 0.867, 0.839, 1, 0.889, 0.262, 0.911, 0.109, 0.933, 0.102, 1, 0.956, 0.11, 0.978, 0.543, 1, 0.722, 1, 1.022, 0.88, 1.044, 0.901, 1.067, 1, 0, 1.333, 1, 1, 1.344, 0.992, 1.356, 1.022, 1.367, 0.966, 1, 1.378, 0.941, 1.389, 0.933, 1.4, 0.933, 1, 1.411, 0.944, 1.422, 1.034, 1.433, 1, 0, 1.467, 1, 2, 1.5, 0.71, 1, 1.511, 0.411, 1.522, 0.064, 1.533, 0.078, 1, 1.567, 0.006, 1.6, 1.018, 1.633, 1, 0, 1.733, 1, 1, 1.744, 0.993, 1.756, 1.05, 1.767, 0.781, 1, 1.778, 0.611, 1.789, 0.398, 1.8, 0.29, 1, 1.822, 0.047, 1.844, 0.003, 1.867, 0, 0, 2.6, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, -5, 1, 0.1, -5, 0.2, -5, 0.3, -5, 1, 0.578, -5, 0.856, -5, 1.133, -5, 1, 1.322, -5, 1.511, -3, 1.7, -3, 0, 2.633, -3]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.1, 0, 0.2, 0, 0.3, 0, 1, 0.578, 0, 0.856, 0, 1.133, 0, 0, 2.633, 0]}]}