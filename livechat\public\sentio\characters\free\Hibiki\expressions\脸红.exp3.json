{"Type": "Live2D Expression", "Parameters": [{"Id": "ParamAngleZ", "Value": 0, "Blend": "Add"}, {"Id": "ParamEyeLOpen", "Value": 0, "Blend": "Add"}, {"Id": "ParamEyeROpen", "Value": 0, "Blend": "Add"}, {"Id": "ParamBrowLY", "Value": -0.25, "Blend": "Add"}, {"Id": "ParamBrowRY", "Value": -0.25, "Blend": "Add"}, {"Id": "ParamBrowLX", "Value": 0, "Blend": "Add"}, {"Id": "ParamBrowRX", "Value": 0, "Blend": "Add"}, {"Id": "ParamBrowLAngle", "Value": 0.54, "Blend": "Add"}, {"Id": "ParamBrowRAngle", "Value": 0.59, "Blend": "Add"}, {"Id": "ParamBrowLForm", "Value": -0.42, "Blend": "Add"}, {"Id": "ParamBrowRForm", "Value": -0.45, "Blend": "Add"}, {"Id": "ParamMouthForm", "Value": -0.21, "Blend": "Add"}, {"Id": "ParamMouthOpenY", "Value": 0, "Blend": "Add"}, {"Id": "ParamTere", "Value": 1, "Blend": "Add"}]}