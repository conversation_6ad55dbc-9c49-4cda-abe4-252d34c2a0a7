[INFO]2025-07-16 11:53:57,773 File 'main.py',line 11: [System] Welcome to Awesome digitalHuman System
[INFO]2025-07-16 11:53:57,773 File 'main.py',line 12: [System] Runing config:
COMMON:
  LOG_LEVEL: DEBUG
  NAME: Awesome-Digital-Human
  VERSION: v3.0.0
SERVER:
  AGENTS:
    DEFAULT: Repeater
    SUPPORT_LIST: [CfgNode({'NAME': 'Repeater', 'VERSION': 'v0.0.1', 'DESC': '复读机', 'META': CfgNode({'official': '', 'configuration': '', 'tips': '测试使用', 'fee': ''})}), CfgNode({'NAME': 'OpenAI', 'VERSION': 'v0.0.1', 'DESC': '接入Openai协议的服务', 'META': CfgNode({'official': '', 'configuration': '', 'tips': '兼容所有符合Openai协议的API', 'fee': ''}), 'PARAMETERS': [{'name': 'model', 'description': 'ID of the model to use.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'base_url', 'description': 'The base url for request.', 'type': 'string', 'required': False, 'choices': [], 'default': 'https://api.openai.com/v1'}, {'name': 'api_key', 'description': 'The api key for request.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用', 'fee': ''}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'FastGPT', 'VERSION': 'v0.0.1', 'DESC': '接入FastGPT应用', 'META': CfgNode({'official': 'https://fastgpt.cn', 'configuration': 'FastGPT云服务: https://cloud.fastgpt.cn', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'base_url', 'description': 'FastGPT base url.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'FastGPT API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'uid', 'description': 'FastGPT customUid.', 'type': 'string', 'required': False, 'choices': [], 'default': 'adh'}]})]
  ENGINES:
    ASR:
      DEFAULT: Tencent-API
      SUPPORT_LIST: [CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用', 'fee': 'free'}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'Tencent-API', 'VERSION': 'v0.0.1', 'DESC': '接入腾讯服务', 'META': CfgNode({'official': '', 'configuration': 'https://console.cloud.tencent.com/asr', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'secret_id', 'description': 'tencent secret_id.', 'type': 'string', 'required': False, 'choices': ['AKID7372kEVYNzNXVGo8F8nFVZP2SfeUBmDS'], 'default': 'AKID7372kEVYNzNXVGo8F8nFVZP2SfeUBmDS'}, {'name': 'secret_key', 'description': 'tencent secret_key.', 'type': 'string', 'required': False, 'choices': ['5wZyA9vZ2mMkvzrVwBUlqiQxHNL9lANO'], 'default': '5wZyA9vZ2mMkvzrVwBUlqiQxHNL9lANO'}]}), CfgNode({'NAME': 'funasrStreaming', 'VERSION': 'v0.0.1', 'DESC': '接入Stream ASR', 'META': CfgNode({'official': 'https://github.com/modelscope/FunASR', 'tips': '支持本地部署的FunAsrStream应用', 'fee': 'free', 'infer_type': 'stream'}), 'PARAMETERS': [{'name': 'api_url', 'description': 'Funasr Streaming API URL', 'type': 'string', 'required': False, 'choices': [], 'default': 'ws://adh-funasr:10095'}, {'name': 'mode', 'description': 'Funasr Streaming mode', 'type': 'string', 'required': False, 'choices': ['2pass'], 'default': '2pass'}]})]
    LLM:
      DEFAULT: None
      SUPPORT_LIST: []
    TTS:
      DEFAULT: Tencent-API
      SUPPORT_LIST: [CfgNode({'NAME': 'EdgeTTS', 'VERSION': 'v0.0.1', 'DESC': '适配EdgeTTS', 'META': CfgNode({'official': 'https://github.com/rany2/edge-tts', 'configuration': '', 'tips': '开源项目可能存在不稳定的情况', 'fee': 'free'}), 'PARAMETERS': [{'name': 'voice', 'description': 'Voice for TTS.', 'type': 'string', 'required': False, 'choices': ['Getting from voice api...'], 'default': 'zh-CN-XiaoxiaoNeural'}, {'name': 'rate', 'description': 'Set rate, default +0%.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}, {'name': 'volume', 'description': 'Set volume, default +0%.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}, {'name': 'pitch', 'description': 'Set pitch, default +0Hz.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}]}), CfgNode({'NAME': 'Tencent-API', 'VERSION': 'v0.0.1', 'DESC': '接入腾讯服务', 'META': CfgNode({'official': '', 'configuration': 'https://console.cloud.tencent.com/tts', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'secret_id', 'description': 'tencent secret_id.', 'type': 'string', 'required': False, 'choices': ['AKID7372kEVYNzNXVGo8F8nFVZP2SfeUBmDS'], 'default': 'AKID7372kEVYNzNXVGo8F8nFVZP2SfeUBmDS'}, {'name': 'secret_key', 'description': 'tencent secret_key.', 'type': 'string', 'required': False, 'choices': ['5wZyA9vZ2mMkvzrVwBUlqiQxHNL9lANO'], 'default': '5wZyA9vZ2mMkvzrVwBUlqiQxHNL9lANO'}, {'name': 'voice', 'description': 'Voice for TTS.', 'type': 'string', 'required': False, 'choices': [502001], 'default': '502001'}, {'name': 'volume', 'description': 'Set volume, default +0%.', 'type': 'float', 'required': False, 'range': [-10, 10], 'default': 5}, {'name': 'speed', 'description': 'Set speed, default +0%.', 'type': 'float', 'required': False, 'range': [-2, 6], 'default': 0.0}]}), CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用全', 'fee': ''}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]})]
  IP: 0.0.0.0
  PORT: 8880
  WORKSPACE_PATH: ./outputs
[INFO]2025-07-16 11:53:57,774 File 'asrFactory.py',line 23: [ASRFactory] Create engine: Dify
[INFO]2025-07-16 11:53:57,775 File 'enginePool.py',line 44: [EnginePool] ASR Engine Dify is created.
[INFO]2025-07-16 11:53:57,775 File 'asrFactory.py',line 23: [ASRFactory] Create engine: Tencent-API
[INFO]2025-07-16 11:53:57,775 File 'enginePool.py',line 44: [EnginePool] ASR Engine Tencent-API is created.
[INFO]2025-07-16 11:53:57,776 File 'asrFactory.py',line 23: [ASRFactory] Create engine: funasrStreaming
[INFO]2025-07-16 11:53:57,776 File 'enginePool.py',line 44: [EnginePool] ASR Engine funasrStreaming is created.
[INFO]2025-07-16 11:53:57,776 File 'enginePool.py',line 45: [EnginePool] ASR Engine default is Tencent-API.
[INFO]2025-07-16 11:53:57,776 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: EdgeTTS
[INFO]2025-07-16 11:53:57,777 File 'enginePool.py',line 49: [EnginePool] TTS Engine EdgeTTS is created.
[INFO]2025-07-16 11:53:57,777 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: Tencent-API
[INFO]2025-07-16 11:53:57,777 File 'enginePool.py',line 49: [EnginePool] TTS Engine Tencent-API is created.
[INFO]2025-07-16 11:53:57,778 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: Dify
[INFO]2025-07-16 11:53:57,778 File 'enginePool.py',line 49: [EnginePool] TTS Engine Dify is created.
[INFO]2025-07-16 11:53:57,778 File 'enginePool.py',line 50: [EnginePool] TTS Engine default is Tencent-API.
[INFO]2025-07-16 11:53:57,779 File 'enginePool.py',line 55: [EnginePool] LLM Engine default is None.
[INFO]2025-07-16 11:53:57,779 File 'agentFactory.py',line 21: [AgentFactory] Create instance: Repeater
[INFO]2025-07-16 11:53:57,779 File 'agentPool.py',line 39: [AgentPool] AGENT Engine Repeater is created.
[INFO]2025-07-16 11:53:57,780 File 'agentFactory.py',line 21: [AgentFactory] Create instance: OpenAI
[INFO]2025-07-16 11:53:57,780 File 'agentPool.py',line 39: [AgentPool] AGENT Engine OpenAI is created.
[INFO]2025-07-16 11:53:57,780 File 'agentFactory.py',line 21: [AgentFactory] Create instance: Dify
[INFO]2025-07-16 11:53:57,781 File 'agentPool.py',line 39: [AgentPool] AGENT Engine Dify is created.
[INFO]2025-07-16 11:53:57,781 File 'agentFactory.py',line 21: [AgentFactory] Create instance: FastGPT
[INFO]2025-07-16 11:53:57,781 File 'agentPool.py',line 39: [AgentPool] AGENT Engine FastGPT is created.
[INFO]2025-07-16 11:53:57,781 File 'agentPool.py',line 40: [AgentPool] AGENT Engine default is Repeater.
[DEBUG]2025-07-16 11:54:49,817 File 'tencentASR.py',line 115: [ASR] Engine response: 你好，请问你是谁？
[ERROR]2025-07-16 11:55:02,469 File 'reponse.py',line 34: voice not found
Traceback (most recent call last):
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\digitalHuman\server\api\tts\tts_api_v0.py", line 89, in api_tts_infer
    output: AudioMessage = await tts_infer(header, item)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\digitalHuman\server\core\api_tts_v0_impl.py", line 37, in tts_infer
    output: AudioMessage = await engine.run(input=input, user=user, **item.config)
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\digitalHuman\engine\tts\tencentTTS.py", line 184, in run
    headers, payload = self._buildRequest(input, tencentCloudApiKey, voice, volume, speed)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\digitalHuman\engine\tts\tencentTTS.py", line 113, in _buildRequest
    raise ValueError("voice not found")
ValueError: voice not found
[DEBUG]2025-07-16 11:57:01,271 File 'tencentASR.py',line 115: [ASR] Engine response: 你好，请问你是谁？
[DEBUG]2025-07-16 11:57:10,605 File 'tencentTTS.py',line 185: [TTS] Engine input: 你好！我是DeepSeek Chat，由深度求索公司开发的智能AI助手。
[DEBUG]2025-07-16 11:57:14,573 File 'tencentTTS.py',line 185: [TTS] Engine input: 我可以帮助你解答各种问题，比如学习 工作 生活上的疑问，或者陪你聊天 提供建议。
[DEBUG]2025-07-16 11:57:19,168 File 'tencentTTS.py',line 185: [TTS] Engine input: 如果有任何需要，随时问我哦！
[DEBUG]2025-07-16 12:03:03,972 File 'tencentASR.py',line 115: [ASR] Engine response: 长风破浪会有时，下一句是什么？
[DEBUG]2025-07-16 12:03:10,694 File 'tencentTTS.py',line 185: [TTS] Engine input: 长风破浪会有时 的下一句是：   直挂云帆济沧海。
[DEBUG]2025-07-16 12:03:14,140 File 'tencentTTS.py',line 185: [TTS] Engine input: 这两句诗出自唐代诗人李白的 行路难 其一 ，全诗展现了诗人虽遭遇困境，仍对未来充满信心的豪迈情怀。
[DEBUG]2025-07-16 12:03:20,136 File 'tencentTTS.py',line 185: [TTS] Engine input: 原诗节选：  行路难，行路难，多歧路，今安在？
[DEBUG]2025-07-16 12:03:23,320 File 'tencentTTS.py',line 185: [TTS] Engine input: 长风破浪会有时，直挂云帆济沧海。
[DEBUG]2025-07-16 12:03:25,847 File 'tencentTTS.py',line 185: [TTS] Engine input: 释义：   长风破浪 比喻冲破艰难 实现抱负
[DEBUG]2025-07-16 12:03:28,492 File 'tencentTTS.py',line 185: [TTS] Engine input: 直挂云帆济沧海 则形象地表达了扬帆远航 直达理想的壮志。
[DEBUG]2025-07-16 12:03:31,958 File 'tencentTTS.py',line 185: [TTS] Engine input: 整句传递了乐观进取的精神力量。
[DEBUG]2025-07-16 12:03:34,072 File 'tencentTTS.py',line 185: [TTS] Engine input: 如需进一步解读或全诗赏析，可以随时告诉我哦！
