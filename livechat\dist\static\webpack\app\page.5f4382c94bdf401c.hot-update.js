"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./lib/context/UserContext.tsx":
/*!*************************************!*\
  !*** ./lib/context/UserContext.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! r2r-js */ \"(app-pages-browser)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/index.js\");\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(r2r_js__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ useUser,UserProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    pipeline: null,\n    setPipeline: ()=>{},\n    selectedModel: \"null\",\n    setSelectedModel: ()=>{},\n    isAuthenticated: false,\n    login: async ()=>({\n            success: false,\n            userRole: \"user\"\n        }),\n    loginWithToken: async ()=>({\n            success: false,\n            userRole: \"user\"\n        }),\n    logout: async ()=>{},\n    unsetCredentials: async ()=>{},\n    register: async ()=>{},\n    authState: {\n        isAuthenticated: false,\n        email: null,\n        userRole: null,\n        userId: null\n    },\n    getClient: ()=>null,\n    client: null,\n    viewMode: \"user\",\n    setViewMode: ()=>{},\n    isSuperUser: ()=>false,\n    createUser: async ()=>{\n        throw new Error(\"createUser is not implemented in the default context\");\n    }\n});\nconst useUser = ()=>{\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (!context) {\n        throw new Error('useUser must be used within a UserProvider');\n    }\n    return context;\n};\n_s(useUser, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nconst UserProvider = (param)=>{\n    let { children } = param;\n    _s1();\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [pipeline, setPipeline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"null\");\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('user');\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isAuthenticated: false,\n        email: null,\n        userRole: null,\n        userId: null\n    });\n    const isSuperUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[isSuperUser]\": ()=>{\n            return authState.userRole === 'admin' && viewMode === 'admin';\n        }\n    }[\"UserProvider.useCallback[isSuperUser]\"], [\n        authState.userRole,\n        viewMode\n    ]);\n    const [lastLoginTime, setLastLoginTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const login = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[login]\": async (email, password, instanceUrl)=>{\n            const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(instanceUrl);\n            try {\n                const tokens = await newClient.users.login({\n                    email: email,\n                    password: password\n                });\n                localStorage.setItem(\"livechatAccessToken\", tokens.results.accessToken.token);\n                localStorage.setItem(\"livechatRefreshToken\", tokens.results.refreshToken.token);\n                newClient.setTokens(tokens.results.accessToken.token, tokens.results.refreshToken.token);\n                setClient(newClient);\n                // Get user info\n                const userInfo = await newClient.users.me();\n                if (!userInfo.results) {\n                    throw new Error('Failed to get user information');\n                }\n                let userRole = \"user\";\n                try {\n                    await newClient.system.settings();\n                    userRole = \"admin\";\n                } catch (error) {\n                    if (error instanceof Error && \"status\" in error && error.status === 403) {\n                    // User doesn't have admin access\n                    } else {\n                        console.error(\"Unexpected error when checking user role:\", error);\n                    }\n                }\n                setAuthState({\n                    isAuthenticated: true,\n                    email: userInfo.results.email,\n                    userRole: userRole,\n                    userId: userInfo.results.id\n                });\n                setLastLoginTime(Date.now());\n                return {\n                    success: true,\n                    userRole\n                };\n            } catch (error) {\n                console.error('Login error:', error);\n                throw error;\n            }\n        }\n    }[\"UserProvider.useCallback[login]\"], []);\n    const loginWithToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[loginWithToken]\": async (token, instanceUrl)=>{\n            const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(instanceUrl);\n            try {\n                const result = await newClient.users.loginWithToken({\n                    accessToken: token\n                });\n                const userInfo = await newClient.users.me();\n                localStorage.setItem(\"livechatAccessToken\", result.accessToken.token);\n                newClient.setTokens(result.accessToken.token, \"\");\n                setClient(newClient);\n                let userRole = \"user\";\n                try {\n                    await newClient.system.settings();\n                    userRole = \"admin\";\n                } catch (error) {\n                    if (error instanceof Error && \"status\" in error && error.status === 403) {\n                    // User doesn't have admin access\n                    } else {\n                        console.error(\"Unexpected error when checking user role:\", error);\n                    }\n                }\n                setAuthState({\n                    isAuthenticated: true,\n                    email: userInfo.results.email,\n                    userRole: userRole,\n                    userId: userInfo.results.id\n                });\n                return {\n                    success: true,\n                    userRole\n                };\n            } catch (error) {\n                console.error('Token login error:', error);\n                throw error;\n            }\n        }\n    }[\"UserProvider.useCallback[loginWithToken]\"], []);\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[logout]\": async ()=>{\n            try {\n                if (client) {\n                    await client.users.logout();\n                }\n            } catch (error) {\n                console.error('Logout error:', error);\n            } finally{\n                localStorage.removeItem(\"livechatAccessToken\");\n                localStorage.removeItem(\"livechatRefreshToken\");\n                setClient(null);\n                setAuthState({\n                    isAuthenticated: false,\n                    email: null,\n                    userRole: null,\n                    userId: null\n                });\n                setLastLoginTime(null);\n            }\n        }\n    }[\"UserProvider.useCallback[logout]\"], [\n        client\n    ]);\n    const unsetCredentials = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[unsetCredentials]\": async ()=>{\n            localStorage.removeItem(\"livechatAccessToken\");\n            localStorage.removeItem(\"livechatRefreshToken\");\n            setClient(null);\n            setAuthState({\n                isAuthenticated: false,\n                email: null,\n                userRole: null,\n                userId: null\n            });\n            setLastLoginTime(null);\n        }\n    }[\"UserProvider.useCallback[unsetCredentials]\"], []);\n    const register = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[register]\": async (email, password, instanceUrl)=>{\n            const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_2__.r2rClient(instanceUrl);\n            try {\n                await newClient.users.create({\n                    email: email,\n                    password: password\n                });\n                // After successful registration, log in\n                await login(email, password, instanceUrl);\n            } catch (error) {\n                console.error('Registration error:', error);\n                throw error;\n            }\n        }\n    }[\"UserProvider.useCallback[register]\"], [\n        login\n    ]);\n    const getClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[getClient]\": ()=>{\n            return client;\n        }\n    }[\"UserProvider.useCallback[getClient]\"], [\n        client\n    ]);\n    const createUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UserProvider.useCallback[createUser]\": async function(email, password) {\n            let isAdmin = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false;\n            if (!client) {\n                throw new Error('No authenticated client available');\n            }\n            try {\n                await client.users.create({\n                    email: email,\n                    password: password\n                });\n            } catch (error) {\n                console.error('Create user error:', error);\n                throw error;\n            }\n        }\n    }[\"UserProvider.useCallback[createUser]\"], [\n        client\n    ]);\n    // Initialize authentication on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserProvider.useEffect\": ()=>{\n            const initializeAuth = {\n                \"UserProvider.useEffect.initializeAuth\": async ()=>{\n                    const accessToken = localStorage.getItem('livechatAccessToken');\n                    if (accessToken) {\n                        try {\n                            const { loadChatConfig, getDeploymentUrl } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_lib_config_chatConfig_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../config/chatConfig */ \"(app-pages-browser)/./lib/config/chatConfig.ts\"));\n                            const config = await loadChatConfig();\n                            const deploymentUrl = getDeploymentUrl(config);\n                            await loginWithToken(accessToken, deploymentUrl);\n                        } catch (error) {\n                            console.error('Auto-login failed:', error);\n                            localStorage.removeItem('livechatAccessToken');\n                            localStorage.removeItem('livechatRefreshToken');\n                        }\n                    }\n                }\n            }[\"UserProvider.useEffect.initializeAuth\"];\n            initializeAuth();\n        }\n    }[\"UserProvider.useEffect\"], [\n        loginWithToken\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: {\n            pipeline,\n            setPipeline,\n            selectedModel,\n            setSelectedModel,\n            isAuthenticated: authState.isAuthenticated,\n            login,\n            loginWithToken,\n            logout,\n            unsetCredentials,\n            register,\n            authState,\n            getClient,\n            client,\n            viewMode,\n            setViewMode,\n            isSuperUser,\n            createUser\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\lib\\\\context\\\\UserContext.tsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, undefined);\n};\n_s1(UserProvider, \"9iU24D0JZWkjsYOD/CiBJFrbF1M=\");\n_c = UserProvider;\nvar _c;\n$RefreshReg$(_c, \"UserProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/context/UserContext.tsx\n"));

/***/ })

});