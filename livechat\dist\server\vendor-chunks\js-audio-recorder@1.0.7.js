/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/js-audio-recorder@1.0.7";
exports.ids = ["vendor-chunks/js-audio-recorder@1.0.7"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/js-audio-recorder@1.0.7/node_modules/js-audio-recorder/dist/recorder.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/js-audio-recorder@1.0.7/node_modules/js-audio-recorder/dist/recorder.js ***!
  \****************************************************************************************************/
/***/ (function(module) {

eval("/*!\r\n * \r\n * js-audio-recorder - js audio recorder plugin\r\n * \r\n * @version v1.0.7\r\n * @homepage https://github.com/2fps/recorder\r\n * <AUTHOR> <<EMAIL>> (https://www.zhuyuntao.cn)\r\n * @license MIT\r\n *         \r\n */\r\n!function(t,e){ true?module.exports=e():0}(this,function(){return function(t){var e={};function n(i){if(e[i])return e[i].exports;var o=e[i]={i:i,l:!1,exports:{}};return t[i].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){\"undefined\"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:\"Module\"}),Object.defineProperty(t,\"__esModule\",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&\"object\"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,\"default\",{enumerable:!0,value:t}),2&e&&\"string\"!=typeof t)for(var o in t)n.d(i,o,function(e){return t[e]}.bind(null,o));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,\"a\",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p=\"\",n(n.s=1)}([function(t,e,n){\"use strict\";function i(t,e,n){for(var i=0;i<n.length;i++)t.setUint8(e+i,n.charCodeAt(i))}Object.defineProperty(e,\"__esModule\",{value:!0}),e.compress=function(t,e,n){for(var i=e/n,o=Math.max(i,1),r=t.left,a=t.right,s=Math.floor((r.length+a.length)/i),u=new Float32Array(s),c=0,l=0;c<s;){var f=Math.floor(l);u[c]=r[f],c++,a.length&&(u[c]=a[f],c++),l+=o}return u},e.encodePCM=function(t,e,n){void 0===n&&(n=!0);var i=0,o=t.length*(e/8),r=new ArrayBuffer(o),a=new DataView(r);if(8===e)for(var s=0;s<t.length;s++,i++){var u=(c=Math.max(-1,Math.min(1,t[s])))<0?128*c:127*c;u=+u+128,a.setInt8(i,u)}else for(s=0;s<t.length;s++,i+=2){var c=Math.max(-1,Math.min(1,t[s]));a.setInt16(i,c<0?32768*c:32767*c,n)}return a},e.encodeWAV=function(t,e,n,o,r,a){void 0===a&&(a=!0);var s=n>e?e:n,u=r,c=new ArrayBuffer(44+t.byteLength),l=new DataView(c),f=o,p=0;i(l,p,\"RIFF\"),p+=4,l.setUint32(p,36+t.byteLength,a),i(l,p+=4,\"WAVE\"),i(l,p+=4,\"fmt \"),p+=4,l.setUint32(p,16,a),p+=4,l.setUint16(p,1,a),p+=2,l.setUint16(p,f,a),p+=2,l.setUint32(p,s,a),p+=4,l.setUint32(p,f*s*(u/8),a),p+=4,l.setUint16(p,f*(u/8),a),p+=2,l.setUint16(p,u,a),i(l,p+=2,\"data\"),p+=4,l.setUint32(p,t.byteLength,a),p+=4;for(var d=0;d<t.byteLength;)l.setUint8(p,t.getUint8(d)),p++,d++;return l}},function(t,e,n){\"use strict\";var i,o=this&&this.__extends||(i=function(t,e){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])})(t,e)},function(t,e){function n(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,\"__esModule\",{value:!0});var r=n(2),a=n(0),s=n(3),u=function(t){function e(e){void 0===e&&(e={});var n=t.call(this,e)||this;return n.isrecording=!1,n.ispause=!1,n.isplaying=!1,n}return o(e,t),e.prototype.setOption=function(t){void 0===t&&(t={}),this.setNewOption(t)},e.prototype.start=function(){return this.isrecording?Promise.reject():(this.isrecording=!0,this.startRecord())},e.prototype.pause=function(){this.isrecording&&!this.ispause&&(this.ispause=!0,this.pauseRecord())},e.prototype.resume=function(){this.isrecording&&this.ispause&&(this.ispause=!1,this.resumeRecord())},e.prototype.stop=function(){this.isrecording&&(this.isrecording=!1,this.ispause=!1,this.stopRecord())},e.prototype.play=function(){this.stop(),this.isplaying=!0,this.onplay&&this.onplay(),s.default.addPlayEnd(this.onplayend);var t=this.getWAV();t.byteLength>44&&s.default.play(t.buffer)},e.prototype.getPlayTime=function(){return s.default.getPlayTime()},e.prototype.pausePlay=function(){!this.isrecording&&this.isplaying&&(this.isplaying=!1,this.onpauseplay&&this.onpauseplay(),s.default.pausePlay())},e.prototype.resumePlay=function(){this.isrecording||this.isplaying||(this.isplaying=!0,this.onresumeplay&&this.onresumeplay(),s.default.resumePlay())},e.prototype.stopPlay=function(){this.isrecording||(this.isplaying=!1,this.onstopplay&&this.onstopplay(),s.default.stopPlay())},e.prototype.destroy=function(){return s.default.destroyPlay(),this.destroyRecord()},e.prototype.getRecordAnalyseData=function(){return this.getAnalyseData()},e.prototype.getPlayAnalyseData=function(){return s.default.getAnalyseData()},e.prototype.getPCM=function(){this.stop();var t=this.getData();return t=a.compress(t,this.inputSampleRate,this.outputSampleRate),a.encodePCM(t,this.oututSampleBits,this.littleEdian)},e.prototype.getPCMBlob=function(){return new Blob([this.getPCM()])},e.prototype.downloadPCM=function(t){void 0===t&&(t=\"recorder\");var e=this.getPCMBlob();r.downloadPCM(e,t)},e.prototype.getWAV=function(){var t=this.getPCM();return a.encodeWAV(t,this.inputSampleRate,this.outputSampleRate,this.config.numChannels,this.oututSampleBits,this.littleEdian)},e.prototype.getWAVBlob=function(){return new Blob([this.getWAV()],{type:\"audio/wav\"})},e.prototype.downloadWAV=function(t){void 0===t&&(t=\"recorder\");var e=this.getWAVBlob();r.downloadWAV(e,t)},e.prototype.download=function(t,e,n){r.download(t,e,n)},e.prototype.getChannelData=function(){var t=this.getPCM(),e=t.byteLength,n=this.littleEdian,i={left:null,right:null};if(2===this.config.numChannels){var o=new DataView(new ArrayBuffer(e/2)),r=new DataView(new ArrayBuffer(e/2));if(16===this.config.sampleBits)for(var a=0;a<e/2;a+=2)o.setInt16(a,t.getInt16(2*a,n),n),r.setInt16(a,t.getInt16(2*a+2,n),n);else for(a=0;a<e/2;a+=2)o.setInt8(a,t.getInt8(2*a)),r.setInt8(a,t.getInt8(2*a+1));i.left=o,i.right=r}else i.left=t;return i},e}(n(5).default);e.default=u},function(t,e,n){\"use strict\";function i(t,e,n){var i=document.createElement(\"a\");i.href=window.URL.createObjectURL(t),i.download=e+\".\"+n,i.click()}Object.defineProperty(e,\"__esModule\",{value:!0}),e.downloadWAV=function(t,e){void 0===e&&(e=\"recorder\"),i(t,e,\"wav\")},e.downloadPCM=function(t,e){void 0===e&&(e=\"recorder\"),i(t,e,\"pcm\")},e.download=function(t,e,n){return i(t,e,n)}},function(t,e,n){\"use strict\";Object.defineProperty(e,\"__esModule\",{value:!0});var i=n(4),o=null,r=0,a=0,s=null,u=null,c=null,l=!1,f=0,p=function(){};function d(){return l=!1,s.decodeAudioData(c.slice(0),function(t){(o=s.createBufferSource()).onended=function(){l||(f=s.currentTime-a+r,p())},o.buffer=t,o.connect(u),u.connect(s.destination),o.start(0,r),a=s.currentTime},function(t){i.throwError(t)})}function h(){o&&(o.stop(),o=null)}var y=function(){function t(){}return t.play=function(t){return s||(s=new(window.AudioContext||window.webkitAudioContext),(u=s.createAnalyser()).fftSize=2048),this.stopPlay(),c=t,f=0,d()},t.pausePlay=function(){h(),r+=s.currentTime-a,l=!0},t.resumePlay=function(){return d()},t.stopPlay=function(){r=0,c=null,h()},t.destroyPlay=function(){this.stopPlay()},t.getAnalyseData=function(){var t=new Uint8Array(u.frequencyBinCount);return u.getByteTimeDomainData(t),t},t.addPlayEnd=function(t){void 0===t&&(t=function(){}),p=t},t.getPlayTime=function(){var t=l?r:s.currentTime-a+r;return f||t},t}();e.default=y},function(t,e,n){\"use strict\";Object.defineProperty(e,\"__esModule\",{value:!0}),e.throwError=function(t){throw new Error(t)}},function(t,e,n){\"use strict\";Object.defineProperty(e,\"__esModule\",{value:!0});var i=n(0),o=function(){function t(e){void 0===e&&(e={}),this.size=0,this.lBuffer=[],this.rBuffer=[],this.tempPCM=[],this.inputSampleBits=16,this.fileSize=0,this.duration=0,this.needRecord=!0;var n,i=new(window.AudioContext||window.webkitAudioContext);this.inputSampleRate=i.sampleRate,this.setNewOption(e),this.littleEdian=(n=new ArrayBuffer(2),new DataView(n).setInt16(0,256,!0),256===new Int16Array(n)[0]),t.initUserMedia()}return t.prototype.setNewOption=function(t){void 0===t&&(t={}),this.config={sampleBits:~[8,16].indexOf(t.sampleBits)?t.sampleBits:16,sampleRate:~[8e3,11025,16e3,22050,24e3,44100,48e3].indexOf(t.sampleRate)?t.sampleRate:this.inputSampleRate,numChannels:~[1,2].indexOf(t.numChannels)?t.numChannels:1},this.outputSampleRate=this.config.sampleRate,this.oututSampleBits=this.config.sampleBits},t.prototype.startRecord=function(){var t=this;return this.context&&this.destroyRecord(),this.initRecorder(),navigator.mediaDevices.getUserMedia({audio:!0}).then(function(e){t.audioInput=t.context.createMediaStreamSource(e),t.stream=e}).then(function(){t.audioInput.connect(t.analyser),t.analyser.connect(t.recorder),t.recorder.connect(t.context.destination)})},t.prototype.pauseRecord=function(){this.needRecord=!1},t.prototype.resumeRecord=function(){this.needRecord=!0},t.prototype.stopRecord=function(){this.audioInput&&this.audioInput.disconnect(),this.source&&this.source.stop(),this.recorder.disconnect(),this.analyser.disconnect(),this.needRecord=!0},t.prototype.destroyRecord=function(){return this.clearRecordStatus(),this.stopStream(),this.closeAudioContext()},t.prototype.getAnalyseData=function(){var t=new Uint8Array(this.analyser.frequencyBinCount);return this.analyser.getByteTimeDomainData(t),t},t.prototype.getData=function(){return this.flat()},t.prototype.clearRecordStatus=function(){this.lBuffer.length=0,this.rBuffer.length=0,this.size=0,this.fileSize=0,this.PCM=null,this.audioInput=null,this.duration=0},t.prototype.flat=function(){var t=null,e=new Float32Array(0);1===this.config.numChannels?t=new Float32Array(this.size):(t=new Float32Array(this.size/2),e=new Float32Array(this.size/2));for(var n=0,i=0;i<this.lBuffer.length;i++)t.set(this.lBuffer[i],n),n+=this.lBuffer[i].length;n=0;for(i=0;i<this.rBuffer.length;i++)e.set(this.rBuffer[i],n),n+=this.rBuffer[i].length;return{left:t,right:e}},t.prototype.initRecorder=function(){var t=this;this.clearRecordStatus(),this.context=new(window.AudioContext||window.webkitAudioContext),this.analyser=this.context.createAnalyser(),this.analyser.fftSize=2048;var e=this.context.createScriptProcessor||this.context.createJavaScriptNode;this.recorder=e.apply(this.context,[4096,this.config.numChannels,this.config.numChannels]),this.recorder.onaudioprocess=function(e){if(t.needRecord){var n,i=e.inputBuffer.getChannelData(0),o=null;t.lBuffer.push(new Float32Array(i)),t.size+=i.length,2===t.config.numChannels&&(o=e.inputBuffer.getChannelData(1),t.rBuffer.push(new Float32Array(o)),t.size+=o.length),t.fileSize=Math.floor(t.size/Math.max(t.inputSampleRate/t.outputSampleRate,1))*(t.oututSampleBits/8),n=100*Math.max.apply(Math,i),t.duration+=4096/t.inputSampleRate,t.onprocess&&t.onprocess(t.duration),t.onprogress&&t.onprogress({duration:t.duration,fileSize:t.fileSize,vol:n})}}},t.prototype.stopStream=function(){this.stream&&this.stream.getTracks&&(this.stream.getTracks().forEach(function(t){return t.stop()}),this.stream=null)},t.prototype.closeAudioContext=function(){return this.context&&this.context.close&&\"closed\"!==this.context.state?this.context.close():new Promise(function(t){t()})},t.initUserMedia=function(){void 0===navigator.mediaDevices&&(navigator.mediaDevices={}),void 0===navigator.mediaDevices.getUserMedia&&(navigator.mediaDevices.getUserMedia=function(t){var e=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia;return e?new Promise(function(n,i){e.call(navigator,t,n,i)}):Promise.reject(new Error(\"浏览器不支持 getUserMedia !\"))})},t.prototype.transformIntoPCM=function(t,e){var n=new Float32Array(t),o=new Float32Array(e),r=i.compress({left:n,right:o},this.inputSampleRate,this.outputSampleRate);return i.encodePCM(r,this.oututSampleBits,this.littleEdian)},t.getPermission=function(){return this.initUserMedia(),navigator.mediaDevices.getUserMedia({audio:!0}).then(function(t){t&&t.getTracks().forEach(function(t){return t.stop()})})},t}();e.default=o}]).default});\r\n//# sourceMappingURL=recorder.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/js-audio-recorder@1.0.7/node_modules/js-audio-recorder/dist/recorder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/js-audio-recorder@1.0.7/node_modules/js-audio-recorder/index.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/js-audio-recorder@1.0.7/node_modules/js-audio-recorder/index.js ***!
  \********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/recorder.js */ \"(ssr)/./node_modules/.pnpm/js-audio-recorder@1.0.7/node_modules/js-audio-recorder/dist/recorder.js\");\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vanMtYXVkaW8tcmVjb3JkZXJAMS4wLjcvbm9kZV9tb2R1bGVzL2pzLWF1ZGlvLXJlY29yZGVyL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFBLG9LQUE4QyIsInNvdXJjZXMiOlsiRDpcXExMTVxcTGVhcm5pbmdcXGFpX2NvZGluZ1xcYW55dGhpbmdjaGF0XFxsaXZlY2hhdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcanMtYXVkaW8tcmVjb3JkZXJAMS4wLjdcXG5vZGVfbW9kdWxlc1xcanMtYXVkaW8tcmVjb3JkZXJcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZShcIi4vZGlzdC9yZWNvcmRlci5qc1wiKTtcclxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/js-audio-recorder@1.0.7/node_modules/js-audio-recorder/index.js\n");

/***/ })

};
;