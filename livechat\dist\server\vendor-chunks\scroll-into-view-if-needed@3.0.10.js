"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/scroll-into-view-if-needed@3.0.10";
exports.ids = ["vendor-chunks/scroll-into-view-if-needed@3.0.10"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/scroll-into-view-if-needed@3.0.10/node_modules/scroll-into-view-if-needed/dist/index.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/scroll-into-view-if-needed@3.0.10/node_modules/scroll-into-view-if-needed/dist/index.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var compute_scroll_into_view__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! compute-scroll-into-view */ \"(ssr)/./node_modules/.pnpm/compute-scroll-into-view@3.1.1/node_modules/compute-scroll-into-view/dist/index.js\");\nconst o=e=>!1===e?{block:\"end\",inline:\"nearest\"}:(e=>e===Object(e)&&0!==Object.keys(e).length)(e)?e:{block:\"start\",inline:\"nearest\"};function t(t,n){if(!t.isConnected||!(e=>{let o=e;for(;o&&o.parentNode;){if(o.parentNode===document)return!0;o=o.parentNode instanceof ShadowRoot?o.parentNode.host:o.parentNode}return!1})(t))return;if((e=>\"object\"==typeof e&&\"function\"==typeof e.behavior)(n))return n.behavior((0,compute_scroll_into_view__WEBPACK_IMPORTED_MODULE_0__.compute)(t,n));const r=\"boolean\"==typeof n||null==n?void 0:n.behavior;for(const{el:i,top:a,left:l}of (0,compute_scroll_into_view__WEBPACK_IMPORTED_MODULE_0__.compute)(t,o(n)))i.scroll({top:a,left:l,behavior:r})}//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vc2Nyb2xsLWludG8tdmlldy1pZi1uZWVkZWRAMy4wLjEwL25vZGVfbW9kdWxlcy9zY3JvbGwtaW50by12aWV3LWlmLW5lZWRlZC9kaXN0L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQW1ELG1CQUFtQiw2QkFBNkIscURBQXFELGdDQUFnQyxnQkFBZ0IseUJBQXlCLFFBQVEsS0FBSyxnQkFBZ0IsRUFBRSxvQ0FBb0Msb0VBQW9FLFNBQVMsWUFBWSwrRUFBK0UsaUVBQUMsT0FBTyx1REFBdUQsVUFBVSxrQkFBa0IsR0FBRyxpRUFBQyxtQkFBbUIsd0JBQXdCLEVBQXVCIiwic291cmNlcyI6WyJEOlxcTExNXFxMZWFybmluZ1xcYWlfY29kaW5nXFxhbnl0aGluZ2NoYXRcXGxpdmVjaGF0XFxub2RlX21vZHVsZXNcXC5wbnBtXFxzY3JvbGwtaW50by12aWV3LWlmLW5lZWRlZEAzLjAuMTBcXG5vZGVfbW9kdWxlc1xcc2Nyb2xsLWludG8tdmlldy1pZi1uZWVkZWRcXGRpc3RcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydHtjb21wdXRlIGFzIGV9ZnJvbVwiY29tcHV0ZS1zY3JvbGwtaW50by12aWV3XCI7Y29uc3Qgbz1lPT4hMT09PWU/e2Jsb2NrOlwiZW5kXCIsaW5saW5lOlwibmVhcmVzdFwifTooZT0+ZT09PU9iamVjdChlKSYmMCE9PU9iamVjdC5rZXlzKGUpLmxlbmd0aCkoZSk/ZTp7YmxvY2s6XCJzdGFydFwiLGlubGluZTpcIm5lYXJlc3RcIn07ZnVuY3Rpb24gdCh0LG4pe2lmKCF0LmlzQ29ubmVjdGVkfHwhKGU9PntsZXQgbz1lO2Zvcig7byYmby5wYXJlbnROb2RlOyl7aWYoby5wYXJlbnROb2RlPT09ZG9jdW1lbnQpcmV0dXJuITA7bz1vLnBhcmVudE5vZGUgaW5zdGFuY2VvZiBTaGFkb3dSb290P28ucGFyZW50Tm9kZS5ob3N0Om8ucGFyZW50Tm9kZX1yZXR1cm4hMX0pKHQpKXJldHVybjtpZigoZT0+XCJvYmplY3RcIj09dHlwZW9mIGUmJlwiZnVuY3Rpb25cIj09dHlwZW9mIGUuYmVoYXZpb3IpKG4pKXJldHVybiBuLmJlaGF2aW9yKGUodCxuKSk7Y29uc3Qgcj1cImJvb2xlYW5cIj09dHlwZW9mIG58fG51bGw9PW4/dm9pZCAwOm4uYmVoYXZpb3I7Zm9yKGNvbnN0e2VsOmksdG9wOmEsbGVmdDpsfW9mIGUodCxvKG4pKSlpLnNjcm9sbCh7dG9wOmEsbGVmdDpsLGJlaGF2aW9yOnJ9KX1leHBvcnR7dCBhcyBkZWZhdWx0fTsvLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/scroll-into-view-if-needed@3.0.10/node_modules/scroll-into-view-if-needed/dist/index.js\n");

/***/ })

};
;