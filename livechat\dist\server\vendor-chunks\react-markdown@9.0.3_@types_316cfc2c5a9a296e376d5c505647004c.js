"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-markdown@9.0.3_@types_316cfc2c5a9a296e376d5c505647004c";
exports.ids = ["vendor-chunks/react-markdown@9.0.3_@types_316cfc2c5a9a296e376d5c505647004c"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-markdown@9.0.3_@types_316cfc2c5a9a296e376d5c505647004c/node_modules/react-markdown/lib/index.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-markdown@9.0.3_@types_316cfc2c5a9a296e376d5c505647004c/node_modules/react-markdown/lib/index.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Markdown: () => (/* binding */ Markdown),\n/* harmony export */   defaultUrlTransform: () => (/* binding */ defaultUrlTransform)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/.pnpm/devlop@1.1.0/node_modules/devlop/lib/development.js\");\n/* harmony import */ var hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! hast-util-to-jsx-runtime */ \"(ssr)/./node_modules/.pnpm/hast-util-to-jsx-runtime@2.3.2/node_modules/hast-util-to-jsx-runtime/lib/index.js\");\n/* harmony import */ var html_url_attributes__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! html-url-attributes */ \"(ssr)/./node_modules/.pnpm/html-url-attributes@3.0.1/node_modules/html-url-attributes/lib/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var remark_parse__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remark-parse */ \"(ssr)/./node_modules/.pnpm/remark-parse@11.0.0/node_modules/remark-parse/lib/index.js\");\n/* harmony import */ var remark_rehype__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remark-rehype */ \"(ssr)/./node_modules/.pnpm/remark-rehype@11.1.1/node_modules/remark-rehype/lib/index.js\");\n/* harmony import */ var unified__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unified */ \"(ssr)/./node_modules/.pnpm/unified@11.0.5/node_modules/unified/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/.pnpm/unist-util-visit@5.0.0/node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/.pnpm/vfile@6.0.3/node_modules/vfile/lib/index.js\");\n/**\n * @import {Element, ElementContent, Nodes, Parents, Root} from 'hast'\n * @import {ComponentProps, ElementType, ReactElement} from 'react'\n * @import {Options as RemarkRehypeOptions} from 'remark-rehype'\n * @import {BuildVisitor} from 'unist-util-visit'\n * @import {PluggableList} from 'unified'\n */\n\n/**\n * @callback AllowElement\n *   Filter elements.\n * @param {Readonly<Element>} element\n *   Element to check.\n * @param {number} index\n *   Index of `element` in `parent`.\n * @param {Readonly<Parents> | undefined} parent\n *   Parent of `element`.\n * @returns {boolean | null | undefined}\n *   Whether to allow `element` (default: `false`).\n */\n\n/**\n * @typedef ExtraProps\n *   Extra fields we pass.\n * @property {Element | undefined} [node]\n *   passed when `passNode` is on.\n */\n\n/**\n * @typedef {{\n *   [Key in Extract<ElementType, string>]?: ElementType<ComponentProps<Key> & ExtraProps>\n * }} Components\n *   Map tag names to components.\n */\n\n/**\n * @typedef Deprecation\n *   Deprecation.\n * @property {string} from\n *   Old field.\n * @property {string} id\n *   ID in readme.\n * @property {keyof Options} [to]\n *   New field.\n */\n\n/**\n * @typedef Options\n *   Configuration.\n * @property {AllowElement | null | undefined} [allowElement]\n *   Filter elements (optional);\n *   `allowedElements` / `disallowedElements` is used first.\n * @property {ReadonlyArray<string> | null | undefined} [allowedElements]\n *   Tag names to allow (default: all tag names);\n *   cannot combine w/ `disallowedElements`.\n * @property {string | null | undefined} [children]\n *   Markdown.\n * @property {string | null | undefined} [className]\n *   Wrap in a `div` with this class name.\n * @property {Components | null | undefined} [components]\n *   Map tag names to components.\n * @property {ReadonlyArray<string> | null | undefined} [disallowedElements]\n *   Tag names to disallow (default: `[]`);\n *   cannot combine w/ `allowedElements`.\n * @property {PluggableList | null | undefined} [rehypePlugins]\n *   List of rehype plugins to use.\n * @property {PluggableList | null | undefined} [remarkPlugins]\n *   List of remark plugins to use.\n * @property {Readonly<RemarkRehypeOptions> | null | undefined} [remarkRehypeOptions]\n *   Options to pass through to `remark-rehype`.\n * @property {boolean | null | undefined} [skipHtml=false]\n *   Ignore HTML in markdown completely (default: `false`).\n * @property {boolean | null | undefined} [unwrapDisallowed=false]\n *   Extract (unwrap) what’s in disallowed elements (default: `false`);\n *   normally when say `strong` is not allowed, it and it’s children are dropped,\n *   with `unwrapDisallowed` the element itself is replaced by its children.\n * @property {UrlTransform | null | undefined} [urlTransform]\n *   Change URLs (default: `defaultUrlTransform`)\n */\n\n/**\n * @callback UrlTransform\n *   Transform all URLs.\n * @param {string} url\n *   URL.\n * @param {string} key\n *   Property name (example: `'href'`).\n * @param {Readonly<Element>} node\n *   Node.\n * @returns {string | null | undefined}\n *   Transformed URL (optional).\n */\n\n\n\n\n\n\n\n\n\n\n\nconst changelog =\n  'https://github.com/remarkjs/react-markdown/blob/main/changelog.md'\n\n/** @type {PluggableList} */\nconst emptyPlugins = []\n/** @type {Readonly<RemarkRehypeOptions>} */\nconst emptyRemarkRehypeOptions = {allowDangerousHtml: true}\nconst safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i\n\n// Mutable because we `delete` any time it’s used and a message is sent.\n/** @type {ReadonlyArray<Readonly<Deprecation>>} */\nconst deprecations = [\n  {from: 'astPlugins', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'allowDangerousHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {\n    from: 'allowNode',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowElement'\n  },\n  {\n    from: 'allowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'allowedElements'\n  },\n  {\n    from: 'disallowedTypes',\n    id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n    to: 'disallowedElements'\n  },\n  {from: 'escapeHtml', id: 'remove-buggy-html-in-markdown-parser'},\n  {from: 'includeElementIndex', id: '#remove-includeelementindex'},\n  {\n    from: 'includeNodeIndex',\n    id: 'change-includenodeindex-to-includeelementindex'\n  },\n  {from: 'linkTarget', id: 'remove-linktarget'},\n  {from: 'plugins', id: 'change-plugins-to-remarkplugins', to: 'remarkPlugins'},\n  {from: 'rawSourcePos', id: '#remove-rawsourcepos'},\n  {from: 'renderers', id: 'change-renderers-to-components', to: 'components'},\n  {from: 'source', id: 'change-source-to-children', to: 'children'},\n  {from: 'sourcePos', id: '#remove-sourcepos'},\n  {from: 'transformImageUri', id: '#add-urltransform', to: 'urlTransform'},\n  {from: 'transformLinkUri', id: '#add-urltransform', to: 'urlTransform'}\n]\n\n/**\n * Component to render markdown.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */\nfunction Markdown(options) {\n  const allowedElements = options.allowedElements\n  const allowElement = options.allowElement\n  const children = options.children || ''\n  const className = options.className\n  const components = options.components\n  const disallowedElements = options.disallowedElements\n  const rehypePlugins = options.rehypePlugins || emptyPlugins\n  const remarkPlugins = options.remarkPlugins || emptyPlugins\n  const remarkRehypeOptions = options.remarkRehypeOptions\n    ? {...options.remarkRehypeOptions, ...emptyRemarkRehypeOptions}\n    : emptyRemarkRehypeOptions\n  const skipHtml = options.skipHtml\n  const unwrapDisallowed = options.unwrapDisallowed\n  const urlTransform = options.urlTransform || defaultUrlTransform\n\n  const processor = (0,unified__WEBPACK_IMPORTED_MODULE_1__.unified)()\n    .use(remark_parse__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n    .use(remarkPlugins)\n    .use(remark_rehype__WEBPACK_IMPORTED_MODULE_3__[\"default\"], remarkRehypeOptions)\n    .use(rehypePlugins)\n\n  const file = new vfile__WEBPACK_IMPORTED_MODULE_4__.VFile()\n\n  if (typeof children === 'string') {\n    file.value = children\n  } else {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_5__.unreachable)(\n      'Unexpected value `' +\n        children +\n        '` for `children` prop, expected `string`'\n    )\n  }\n\n  if (allowedElements && disallowedElements) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_5__.unreachable)(\n      'Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other'\n    )\n  }\n\n  for (const deprecation of deprecations) {\n    if (Object.hasOwn(options, deprecation.from)) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_5__.unreachable)(\n        'Unexpected `' +\n          deprecation.from +\n          '` prop, ' +\n          (deprecation.to\n            ? 'use `' + deprecation.to + '` instead'\n            : 'remove it') +\n          ' (see <' +\n          changelog +\n          '#' +\n          deprecation.id +\n          '> for more info)'\n      )\n    }\n  }\n\n  const mdastTree = processor.parse(file)\n  /** @type {Nodes} */\n  let hastTree = processor.runSync(mdastTree, file)\n\n  // Wrap in `div` if there’s a class name.\n  if (className) {\n    hastTree = {\n      type: 'element',\n      tagName: 'div',\n      properties: {className},\n      // Assume no doctypes.\n      children: /** @type {Array<ElementContent>} */ (\n        hastTree.type === 'root' ? hastTree.children : [hastTree]\n      )\n    }\n  }\n\n  (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_6__.visit)(hastTree, transform)\n\n  return (0,hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_7__.toJsxRuntime)(hastTree, {\n    Fragment: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment,\n    // @ts-expect-error\n    // React components are allowed to return numbers,\n    // but not according to the types in hast-util-to-jsx-runtime\n    components,\n    ignoreInvalidStyle: true,\n    jsx: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx,\n    jsxs: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs,\n    passKeys: true,\n    passNode: true\n  })\n\n  /** @type {BuildVisitor<Root>} */\n  function transform(node, index, parent) {\n    if (node.type === 'raw' && parent && typeof index === 'number') {\n      if (skipHtml) {\n        parent.children.splice(index, 1)\n      } else {\n        parent.children[index] = {type: 'text', value: node.value}\n      }\n\n      return index\n    }\n\n    if (node.type === 'element') {\n      /** @type {string} */\n      let key\n\n      for (key in html_url_attributes__WEBPACK_IMPORTED_MODULE_8__.urlAttributes) {\n        if (\n          Object.hasOwn(html_url_attributes__WEBPACK_IMPORTED_MODULE_8__.urlAttributes, key) &&\n          Object.hasOwn(node.properties, key)\n        ) {\n          const value = node.properties[key]\n          const test = html_url_attributes__WEBPACK_IMPORTED_MODULE_8__.urlAttributes[key]\n          if (test === null || test.includes(node.tagName)) {\n            node.properties[key] = urlTransform(String(value || ''), key, node)\n          }\n        }\n      }\n    }\n\n    if (node.type === 'element') {\n      let remove = allowedElements\n        ? !allowedElements.includes(node.tagName)\n        : disallowedElements\n          ? disallowedElements.includes(node.tagName)\n          : false\n\n      if (!remove && allowElement && typeof index === 'number') {\n        remove = !allowElement(node, index, parent)\n      }\n\n      if (remove && parent && typeof index === 'number') {\n        if (unwrapDisallowed && node.children) {\n          parent.children.splice(index, 1, ...node.children)\n        } else {\n          parent.children.splice(index, 1)\n        }\n\n        return index\n      }\n    }\n  }\n}\n\n/**\n * Make a URL safe.\n *\n * @satisfies {UrlTransform}\n * @param {string} value\n *   URL.\n * @returns {string}\n *   Safe URL.\n */\nfunction defaultUrlTransform(value) {\n  // Same as:\n  // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>\n  // But without the `encode` part.\n  const colon = value.indexOf(':')\n  const questionMark = value.indexOf('?')\n  const numberSign = value.indexOf('#')\n  const slash = value.indexOf('/')\n\n  if (\n    // If there is no protocol, it’s relative.\n    colon === -1 ||\n    // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    (slash !== -1 && colon > slash) ||\n    (questionMark !== -1 && colon > questionMark) ||\n    (numberSign !== -1 && colon > numberSign) ||\n    // It is a protocol, it should be allowed.\n    safeProtocol.test(value.slice(0, colon))\n  ) {\n    return value\n  }\n\n  return ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-markdown@9.0.3_@types_316cfc2c5a9a296e376d5c505647004c/node_modules/react-markdown/lib/index.js\n");

/***/ })

};
;