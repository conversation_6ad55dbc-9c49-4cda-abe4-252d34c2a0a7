<!DOCTYPE html>
<html>
<head>
    <title>Test Authentication</title>
</head>
<body>
    <h1>Test Authentication</h1>
    <button onclick="clearTokens()">Clear Tokens</button>
    <button onclick="testLogin()">Test Login</button>
    <div id="result"></div>

    <script>
        function clearTokens() {
            localStorage.removeItem('livechatAccessToken');
            localStorage.removeItem('livechatRefreshToken');
            document.getElementById('result').innerHTML = 'Tokens cleared';
        }

        async function testLogin() {
            try {
                const response = await fetch('http://*************:7272/v3/users/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'username=<EMAIL>&password=testpassword123'
                });
                
                const data = await response.json();
                document.getElementById('result').innerHTML = '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('result').innerHTML = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>
