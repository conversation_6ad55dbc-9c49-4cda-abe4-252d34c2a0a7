{"compilerOptions": {"target": "es6", "moduleResolution": "node", "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "declaration": true, "declarationMap": true, "sourceMap": true, "emitDecoratorMetadata": true, "noImplicitAny": true, "useUnknownInCatchVariables": true}, "include": ["src/**/*.ts", "../Core/*.ts"], "exclude": ["node_modules", "dist"]}