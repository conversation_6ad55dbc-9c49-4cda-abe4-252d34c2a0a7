{"server": {"apiUrl": "http://*************:7272", "useHttps": false, "apiVersion": "v3", "timeout": 30000}, "app": {"appName": "LiveChat", "appDescription": "Live chat application with R2R integration", "version": "1.0.0", "defaultMode": "rag_agent", "conversationHistoryLimit": 10}, "vectorSearch": {"enabled": true, "searchLimit": 10, "searchFilters": "{}", "indexMeasure": "cosine_distance", "includeMetadatas": false}, "hybridSearch": {"enabled": false, "fullTextWeight": 1.0, "semanticWeight": 5.0, "fullTextLimit": 200, "rrfK": 50}, "graphSearch": {"enabled": true, "kgSearchLevel": null, "maxCommunityDescriptionLength": 100, "localSearchLimits": {}, "maxLlmQueries": 1}, "ragGeneration": {"temperature": 0.1, "topP": 1.0, "topK": 100, "maxTokensToSample": 1024, "kgTemperature": 0.1, "kgTopP": 1.0, "kgTopK": 100, "kgMaxTokensToSample": 1024}}