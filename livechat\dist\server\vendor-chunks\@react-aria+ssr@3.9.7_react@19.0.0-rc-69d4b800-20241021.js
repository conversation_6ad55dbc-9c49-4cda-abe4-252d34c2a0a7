"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+ssr@3.9.7_react@19.0.0-rc-69d4b800-20241021";
exports.ids = ["vendor-chunks/@react-aria+ssr@3.9.7_react@19.0.0-rc-69d4b800-20241021"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+ssr@3.9.7_react@19.0.0-rc-69d4b800-20241021/node_modules/@react-aria/ssr/dist/SSRProvider.mjs":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+ssr@3.9.7_react@19.0.0-rc-69d4b800-20241021/node_modules/@react-aria/ssr/dist/SSRProvider.mjs ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SSRProvider: () => (/* binding */ $b5e257d569688ac6$export$9f8ac96af4b1b2ae),\n/* harmony export */   useIsSSR: () => (/* binding */ $b5e257d569688ac6$export$535bd6ca7f90a273),\n/* harmony export */   useSSRSafeId: () => (/* binding */ $b5e257d569688ac6$export$619500959fc48b26)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ // We must avoid a circular dependency with @react-aria/utils, and this useLayoutEffect is\n// guarded by a check that it only runs on the client side.\n// eslint-disable-next-line rulesdir/useLayoutEffectRule\n\n// Default context value to use in case there is no SSRProvider. This is fine for\n// client-only apps. In order to support multiple copies of React Aria potentially\n// being on the page at once, the prefix is set to a random number. SSRProvider\n// will reset this to zero for consistency between server and client, so in the\n// SSR case multiple copies of React Aria is not supported.\nconst $b5e257d569688ac6$var$defaultContext = {\n    prefix: String(Math.round(Math.random() * 10000000000)),\n    current: 0\n};\nconst $b5e257d569688ac6$var$SSRContext = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createContext($b5e257d569688ac6$var$defaultContext);\nconst $b5e257d569688ac6$var$IsSSRContext = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createContext(false);\n// This is only used in React < 18.\nfunction $b5e257d569688ac6$var$LegacySSRProvider(props) {\n    let cur = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($b5e257d569688ac6$var$SSRContext);\n    let counter = $b5e257d569688ac6$var$useCounter(cur === $b5e257d569688ac6$var$defaultContext);\n    let [isSSR, setIsSSR] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    let value = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            // If this is the first SSRProvider, start with an empty string prefix, otherwise\n            // append and increment the counter.\n            prefix: cur === $b5e257d569688ac6$var$defaultContext ? '' : `${cur.prefix}-${counter}`,\n            current: 0\n        }), [\n        cur,\n        counter\n    ]);\n    // If on the client, and the component was initially server rendered,\n    // then schedule a layout effect to update the component after hydration.\n    if (typeof document !== 'undefined') // This if statement technically breaks the rules of hooks, but is safe\n    // because the condition never changes after mounting.\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(()=>{\n        setIsSSR(false);\n    }, []);\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($b5e257d569688ac6$var$SSRContext.Provider, {\n        value: value\n    }, /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($b5e257d569688ac6$var$IsSSRContext.Provider, {\n        value: isSSR\n    }, props.children));\n}\nlet $b5e257d569688ac6$var$warnedAboutSSRProvider = false;\nfunction $b5e257d569688ac6$export$9f8ac96af4b1b2ae(props) {\n    if (typeof (0, react__WEBPACK_IMPORTED_MODULE_0__)['useId'] === 'function') {\n        if ( true && !$b5e257d569688ac6$var$warnedAboutSSRProvider) {\n            console.warn('In React 18, SSRProvider is not necessary and is a noop. You can remove it from your app.');\n            $b5e257d569688ac6$var$warnedAboutSSRProvider = true;\n        }\n        return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement((0, react__WEBPACK_IMPORTED_MODULE_0__).Fragment, null, props.children);\n    }\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($b5e257d569688ac6$var$LegacySSRProvider, props);\n}\nlet $b5e257d569688ac6$var$canUseDOM = Boolean(typeof window !== 'undefined' && window.document && window.document.createElement);\nlet $b5e257d569688ac6$var$componentIds = new WeakMap();\nfunction $b5e257d569688ac6$var$useCounter(isDisabled = false) {\n    let ctx = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($b5e257d569688ac6$var$SSRContext);\n    let ref = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    // eslint-disable-next-line rulesdir/pure-render\n    if (ref.current === null && !isDisabled) {\n        var _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner, _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n        // In strict mode, React renders components twice, and the ref will be reset to null on the second render.\n        // This means our id counter will be incremented twice instead of once. This is a problem because on the\n        // server, components are only rendered once and so ids generated on the server won't match the client.\n        // In React 18, useId was introduced to solve this, but it is not available in older versions. So to solve this\n        // we need to use some React internals to access the underlying Fiber instance, which is stable between renders.\n        // This is exposed as ReactCurrentOwner in development, which is all we need since StrictMode only runs in development.\n        // To ensure that we only increment the global counter once, we store the starting id for this component in\n        // a weak map associated with the Fiber. On the second render, we reset the global counter to this value.\n        // Since React runs the second render immediately after the first, this is safe.\n        // @ts-ignore\n        let currentOwner = (_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED = (0, react__WEBPACK_IMPORTED_MODULE_0__).__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) === null || _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED === void 0 ? void 0 : (_React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner = _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner) === null || _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner === void 0 ? void 0 : _React___SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED_ReactCurrentOwner.current;\n        if (currentOwner) {\n            let prevComponentValue = $b5e257d569688ac6$var$componentIds.get(currentOwner);\n            if (prevComponentValue == null) // On the first render, and first call to useId, store the id and state in our weak map.\n            $b5e257d569688ac6$var$componentIds.set(currentOwner, {\n                id: ctx.current,\n                state: currentOwner.memoizedState\n            });\n            else if (currentOwner.memoizedState !== prevComponentValue.state) {\n                // On the second render, the memoizedState gets reset by React.\n                // Reset the counter, and remove from the weak map so we don't\n                // do this for subsequent useId calls.\n                ctx.current = prevComponentValue.id;\n                $b5e257d569688ac6$var$componentIds.delete(currentOwner);\n            }\n        }\n        // eslint-disable-next-line rulesdir/pure-render\n        ref.current = ++ctx.current;\n    }\n    // eslint-disable-next-line rulesdir/pure-render\n    return ref.current;\n}\nfunction $b5e257d569688ac6$var$useLegacySSRSafeId(defaultId) {\n    let ctx = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($b5e257d569688ac6$var$SSRContext);\n    // If we are rendering in a non-DOM environment, and there's no SSRProvider,\n    // provide a warning to hint to the developer to add one.\n    if (ctx === $b5e257d569688ac6$var$defaultContext && !$b5e257d569688ac6$var$canUseDOM) console.warn('When server rendering, you must wrap your application in an <SSRProvider> to ensure consistent ids are generated between the client and server.');\n    let counter = $b5e257d569688ac6$var$useCounter(!!defaultId);\n    let prefix = ctx === $b5e257d569688ac6$var$defaultContext && \"development\" === 'test' ? 0 : `react-aria${ctx.prefix}`;\n    return defaultId || `${prefix}-${counter}`;\n}\nfunction $b5e257d569688ac6$var$useModernSSRSafeId(defaultId) {\n    let id = (0, react__WEBPACK_IMPORTED_MODULE_0__).useId();\n    let [didSSR] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)($b5e257d569688ac6$export$535bd6ca7f90a273());\n    let prefix = didSSR || \"development\" === 'test' ? 'react-aria' : `react-aria${$b5e257d569688ac6$var$defaultContext.prefix}`;\n    return defaultId || `${prefix}-${id}`;\n}\nconst $b5e257d569688ac6$export$619500959fc48b26 = typeof (0, react__WEBPACK_IMPORTED_MODULE_0__)['useId'] === 'function' ? $b5e257d569688ac6$var$useModernSSRSafeId : $b5e257d569688ac6$var$useLegacySSRSafeId;\nfunction $b5e257d569688ac6$var$getSnapshot() {\n    return false;\n}\nfunction $b5e257d569688ac6$var$getServerSnapshot() {\n    return true;\n}\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nfunction $b5e257d569688ac6$var$subscribe(onStoreChange) {\n    // noop\n    return ()=>{};\n}\nfunction $b5e257d569688ac6$export$535bd6ca7f90a273() {\n    // In React 18, we can use useSyncExternalStore to detect if we're server rendering or hydrating.\n    if (typeof (0, react__WEBPACK_IMPORTED_MODULE_0__)['useSyncExternalStore'] === 'function') return (0, react__WEBPACK_IMPORTED_MODULE_0__)['useSyncExternalStore']($b5e257d569688ac6$var$subscribe, $b5e257d569688ac6$var$getSnapshot, $b5e257d569688ac6$var$getServerSnapshot);\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    return (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($b5e257d569688ac6$var$IsSSRContext);\n}\n\n\n\n//# sourceMappingURL=SSRProvider.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+ssr@3.9.7_react@19.0.0-rc-69d4b800-20241021/node_modules/@react-aria/ssr/dist/SSRProvider.mjs\n");

/***/ })

};
;