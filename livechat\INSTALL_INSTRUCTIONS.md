# LiveChat 安装说明

本文档提供了LiveChat应用的安装和配置说明。

## 系统要求

- Node.js 18.x 或更高版本
- npm, yarn 或 pnpm 包管理器

## 安装步骤

1. 克隆仓库（如果尚未完成）
2. 安装依赖：

```bash
# 使用npm
npm install

# 或使用yarn
yarn install

# 或使用pnpm
pnpm install
```

3. 配置环境变量：
   - 复制`.env.example`文件并重命名为`.env`
   - 根据需要修改环境变量

4. 启动开发服务器：

```bash
# 使用npm
npm run dev

# 或使用yarn
yarn dev

# 或使用pnpm
pnpm dev
```

5. 构建生产版本：

```bash
# 使用npm
npm run build

# 或使用yarn
yarn build

# 或使用pnpm
pnpm build
```

## 服务器配置

确保服务器配置正确，以便应用能够正常连接到后端API。

## 注意事项

- 如果遇到构建或运行问题，请检查Node.js版本和依赖包版本是否兼容。
- 确保所有必要的环境变量都已正确设置。