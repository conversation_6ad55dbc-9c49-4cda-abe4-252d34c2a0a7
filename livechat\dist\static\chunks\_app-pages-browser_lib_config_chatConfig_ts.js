"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_lib_config_chatConfig_ts"],{

/***/ "(app-pages-browser)/./lib/config/chatConfig.ts":
/*!**********************************!*\
  !*** ./lib/config/chatConfig.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultChatConfig: () => (/* binding */ defaultChatConfig),\n/* harmony export */   getDeploymentUrl: () => (/* binding */ getDeploymentUrl),\n/* harmony export */   loadChatConfig: () => (/* binding */ loadChatConfig)\n/* harmony export */ });\n/**\r\n * Default configuration values\r\n */ const defaultChatConfig = {\n    server: {\n        apiUrl: \"http://localhost:7272\",\n        useHttps: false,\n        apiVersion: \"v3\",\n        timeout: 30000\n    },\n    app: {\n        appName: \"LiveChat\",\n        appDescription: \"Live chat application with R2R integration\",\n        version: \"1.0.0\",\n        defaultMode: \"rag_agent\",\n        conversationHistoryLimit: 10\n    },\n    vectorSearch: {\n        enabled: true,\n        searchLimit: 10,\n        searchFilters: \"{}\",\n        indexMeasure: \"cosine_distance\",\n        includeMetadatas: false,\n        probes: undefined,\n        efSearch: undefined\n    },\n    hybridSearch: {\n        enabled: false,\n        fullTextWeight: undefined,\n        semanticWeight: undefined,\n        fullTextLimit: undefined,\n        rrfK: undefined\n    },\n    graphSearch: {\n        enabled: true,\n        kgSearchLevel: null,\n        maxCommunityDescriptionLength: 100,\n        localSearchLimits: {},\n        maxLlmQueries: undefined\n    },\n    ragGeneration: {\n        temperature: 0.1,\n        topP: 1.0,\n        topK: 100,\n        maxTokensToSample: 1024,\n        kgTemperature: 0.1,\n        kgTopP: 1.0,\n        kgTopK: 100,\n        kgMaxTokensToSample: 1024\n    }\n};\n/**\r\n * Load configuration from public/config.json with fallback to defaults\r\n */ const loadChatConfig = async ()=>{\n    try {\n        const response = await fetch('/config.json');\n        if (!response.ok) {\n            console.warn('Failed to load config.json, using default configuration');\n            return defaultChatConfig;\n        }\n        const config = await response.json();\n        // Merge with defaults to ensure all required fields are present\n        return {\n            server: {\n                ...defaultChatConfig.server,\n                ...config.server\n            },\n            app: {\n                ...defaultChatConfig.app,\n                ...config.app\n            },\n            vectorSearch: {\n                ...defaultChatConfig.vectorSearch,\n                ...config.vectorSearch\n            },\n            hybridSearch: {\n                ...defaultChatConfig.hybridSearch,\n                ...config.hybridSearch\n            },\n            graphSearch: {\n                ...defaultChatConfig.graphSearch,\n                ...config.graphSearch\n            },\n            ragGeneration: {\n                ...defaultChatConfig.ragGeneration,\n                ...config.ragGeneration\n            }\n        };\n    } catch (error) {\n        console.error('Error loading configuration:', error);\n        return defaultChatConfig;\n    }\n};\n/**\r\n * Get the deployment URL from configuration\r\n */ const getDeploymentUrl = (config)=>{\n    const cfg = config || defaultChatConfig;\n    // If apiUrl already includes protocol, use it as-is\n    if (cfg.server.apiUrl.startsWith('http://') || cfg.server.apiUrl.startsWith('https://')) {\n        return cfg.server.apiUrl;\n    }\n    // Otherwise, construct URL with protocol\n    const protocol = cfg.server.useHttps ? 'https' : 'http';\n    return \"\".concat(protocol, \"://\").concat(cfg.server.apiUrl);\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/config/chatConfig.ts\n"));

/***/ })

}]);