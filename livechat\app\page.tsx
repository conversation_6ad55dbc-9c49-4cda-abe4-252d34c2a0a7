'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useUser } from '@/lib/context/UserContext'

// 根据认证状态跳转页面
export default function Page(): JSX.Element | null {
  const router = useRouter();
  const { isAuthenticated } = useUser();

  useEffect(() => {
    // Check authentication and redirect accordingly
    if (isAuthenticated) {
      router.push('/sentio');
    } else {
      router.push('/auth/login');
    }
  }, [isAuthenticated, router]);

  return null;
}