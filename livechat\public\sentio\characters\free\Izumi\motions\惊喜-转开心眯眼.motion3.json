{"Version": 3, "Meta": {"Duration": 4.667, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 29, "TotalSegmentCount": 142, "TotalPointCount": 369, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -30, 1, 0.089, -30, 0.178, -30, 0.267, -30, 1, 0.6, -30, 0.933, -30, 1.267, -30, 1, 1.733, -30, 2.2, -30, 2.667, -30, 1, 2.789, -30, 2.911, -21.88, 3.033, -20, 1, 3.311, -15.728, 3.589, -15, 3.867, -15, 0, 4.667, -15]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.089, 0, 0.178, 30, 0.267, 30, 1, 0.6, 30, 0.933, 30, 1.267, 30, 1, 1.733, 30, 2.2, 30, 2.667, 30, 1, 2.789, 30, 2.911, 30, 3.033, 30, 1, 3.311, 30, 3.589, 30, 3.867, 30, 0, 4.667, 30]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.089, 0, 0.178, 19, 0.267, 19, 1, 0.6, 19, 0.933, 19, 1.267, 19, 1, 1.344, 19, 1.422, 27, 1.5, 27, 1, 2.022, 27, 2.544, 27, 3.067, 27, 1, 3.267, 27, 3.467, 10.204, 3.667, -18, 1, 3.733, -27.401, 3.8, -30, 3.867, -30, 0, 4.667, -30]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.5, 1, 1, 1.5, 1.5, 1.5, 1, 1.589, 1.5, 1.678, 1.468, 1.767, 1.347, 1, 1.811, 1.285, 1.856, 1.204, 1.9, 1.089, 1, 1.922, 1.017, 1.944, 1.022, 1.967, 0.79, 1, 2, 0.491, 2.033, 0, 2.067, 0, 0, 2.133, 0, 1, 2.189, -0.008, 2.244, 1.008, 2.3, 1, 0, 3.4, 1, 1, 3.489, 0.996, 3.578, 0.004, 3.667, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 1.133, 0, 2.267, 0, 3.4, 0, 1, 3.489, 0, 3.578, 1, 3.667, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 1.933, 1, 1, 1.978, 1.008, 2.022, -0.008, 2.067, 0, 0, 2.133, 0, 1, 2.189, -0.008, 2.244, 1.008, 2.3, 1, 0, 3.4, 1, 1, 3.489, 0.996, 3.578, 0.004, 3.667, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 1.133, 0, 2.267, 0, 3.4, 0, 1, 3.489, 0, 3.578, 1, 3.667, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.089, 0, 0.178, -1, 0.267, -1, 1, 0.6, -1, 0.933, -1, 1.267, -1, 1, 1.344, -1, 1.422, 0.7, 1.5, 0.7, 0, 4.667, 0.7]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.089, 0, 0.178, 1, 0.267, 1, 1, 0.6, 1, 0.933, 1, 1.267, 1, 1, 1.344, 1, 1.422, -1, 1.5, -1, 0, 4.667, -1]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.467, 0, 0.933, 0, 1.4, 0, 1, 1.433, 0, 1.467, -1, 1.5, -1, 1, 1.678, -1, 1.856, -1, 2.033, -1, 1, 2.078, -1, 2.122, 0, 2.167, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.089, 0, 0.178, 1, 0.267, 1, 1, 0.6, 1, 0.933, 1, 1.267, 1, 1, 2.067, 1, 2.867, 0, 3.667, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.089, 0, 0.178, 1, 0.267, 1, 1, 0.6, 1, 0.933, 1, 1.267, 1, 1, 2.067, 1, 2.867, 0, 3.667, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.089, 1, 0.178, 1, 0.267, 1, 1, 0.6, 1, 0.933, 1, 1.267, 1, 1, 1.344, 1, 1.422, 0.001, 1.5, 0, 1, 2.133, -0.007, 2.767, -0.01, 3.4, -0.01, 1, 3.456, -0.01, 3.511, 1, 3.567, 1, 0, 4.667, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.056, 0, 0.111, 0.001, 0.167, 0.002, 1, 0.189, 0.003, 0.211, 0.004, 0.233, 0.006, 1, 0.267, -0.142, 0.3, 0.249, 0.333, 0.369, 1, 0.356, 0.366, 0.378, 0.191, 0.4, 0.188, 1, 0.411, 0.17, 0.422, 0.604, 0.433, 0.976, 3, 0.467, 1, 2, 0.5, 0.914, 1, 0.511, 0.512, 0.522, 0.043, 0.533, 0.063, 1, 0.567, -0.195, 0.6, 0.908, 0.633, 1, 0, 0.8, 1, 1, 0.811, 1.048, 0.822, 0.597, 0.833, 0.426, 1, 0.844, 0.208, 0.856, 0.007, 0.867, 0.016, 1, 0.9, 0.014, 0.933, 0.012, 0.967, 0.01, 1, 1.022, 0.008, 1.078, 0.006, 1.133, 0.004, 1, 1.244, 0.001, 1.356, 0, 1.467, 0, 1, 1.5, -0.297, 1.533, 0.911, 1.567, 1, 0, 1.7, 1, 1, 1.756, 0.191, 1.811, 0.002, 1.867, 0, 1, 1.889, 0.002, 1.911, 0.023, 1.933, 0.212, 1, 1.956, 0.398, 1.978, 0.797, 2, 1, 0, 2.233, 1, 1, 2.256, 0.899, 2.278, 0.562, 2.3, 0.431, 1, 2.333, 0.207, 2.367, 0.311, 2.4, 0.255, 1, 2.467, 0.215, 2.533, 0.185, 2.6, 0.162, 1, 2.733, 0.117, 2.867, 0.102, 3, 0.102, 1, 3.011, 0.083, 3.022, 0.532, 3.033, 0.918, 3, 3.067, 1, 2, 3.1, 0.898, 1, 3.111, 0.503, 3.122, 0.043, 3.133, 0.063, 1, 3.156, 0.065, 3.178, 0.083, 3.2, 0.267, 1, 3.222, 0.453, 3.244, 0.67, 3.267, 0.675, 1, 3.289, 0.674, 3.311, 0.677, 3.333, 0.58, 1, 3.356, 0.48, 3.378, 0.279, 3.4, 0.275, 1, 3.422, 0.277, 3.444, 0.413, 3.467, 0.416, 1, 3.489, 0.414, 3.511, 0.315, 3.533, 0.314, 1, 3.578, 0.31, 3.622, 0.477, 3.667, 0.988, 3, 3.7, 1, 0, 3.767, 1, 1, 3.778, 1.117, 3.789, 0.699, 3.8, 0.714, 1, 3.811, 0.666, 3.822, 0.645, 3.833, 0.63, 1, 3.867, 0.764, 3.9, 0.103, 3.933, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, -10, 1, 0.867, -10, 1.733, -10, 2.6, -10, 1, 2.822, -10, 3.044, -5, 3.267, -5, 0, 4.667, -5]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.089, 0, 0.178, 6, 0.267, 6, 1, 0.456, 6, 0.644, 6, 0.833, 6, 1, 0.944, 6, 1.056, 2.402, 1.167, 2, 1, 1.611, 0.39, 2.056, 0, 2.5, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.089, 0, 0.178, 2, 0.267, 2, 1, 0.6, 2, 0.933, 2, 1.267, 2, 1, 1.889, 2, 2.511, 1.687, 3.133, 0, 1, 3.333, -0.542, 3.533, -5, 3.733, -5, 0, 4.667, -5]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.089, 0, 0.178, 10, 0.267, 10, 1, 0.6, 10, 0.933, 10, 1.267, 10, 1, 1.489, 10, 1.711, 0, 1.933, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.089, 0, 0.178, -10, 0.267, -10, 1, 0.6, -10, 0.933, -10, 1.267, -10, 1, 1.489, -10, 1.711, 0, 1.933, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 4.667, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 4.667, 0]}]}