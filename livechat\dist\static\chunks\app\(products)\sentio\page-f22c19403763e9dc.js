(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[795],{3767:()=>{},2208:()=>{},33961:(e,t,i)=>{Promise.resolve().then(i.bind(i,32708)),Promise.resolve().then(i.bind(i,52599))},32708:(e,t,i)=>{"use strict";let s,r,a,n,l,o,u;i.d(t,{default:()=>aO});var h,g,d,c,_,m,p,f,y,x,S,C,b,v,M,P,w,B,I,T,E,R,V,A,F,L,D,k,N,O,j,U,z,G,X,Y,H,W,q,J,Z,K=i(12389),Q=i(41473);class ${at(e){return this._ptr[e]}set(e,t){this._ptr[e]=t}get(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=[];for(let i=e;i<this._size;i++)t.push(this._ptr[i]);return t}pushBack(e){this._size>=this._capacity&&this.prepareCapacity(0==this._capacity?$.DefaultSize:2*this._capacity),this._ptr[this._size++]=e}clear(){this._ptr.length=0,this._size=0}getSize(){return this._size}assign(e,t){this._size<e&&this.prepareCapacity(e);for(let i=0;i<e;i++)this._ptr[i]=t;this._size=e}resize(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.updateSize(e,t,!0)}updateSize(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(this._size<e){if(this.prepareCapacity(e),i)for(let i=this._size;i<e;i++)"function"==typeof t?this._ptr[i]=JSON.parse(JSON.stringify(new t)):this._ptr[i]=t;else for(let i=this._size;i<e;i++)this._ptr[i]=t}else{let t=this._size-e;this._ptr.splice(this._size-t,t)}this._size=e}insert(e,t,i){let s=e._index,r=t._index,a=i._index,n=a-r;this.prepareCapacity(this._size+n);let l=this._size-s;if(l>0)for(let e=0;e<l;e++)this._ptr.splice(s+e,0,null);for(let e=r;e<a;e++,s++)this._ptr[s]=t._vector._ptr[e];this._size=this._size+n}remove(e){return!(e<0)&&!(this._size<=e)&&(this._ptr.splice(e,1),--this._size,!0)}erase(e){let t=e._index;return t<0||this._size<=t?e:(this._ptr.splice(t,1),--this._size,new ee(this,t))}prepareCapacity(e){e>this._capacity&&(0==this._capacity?this._ptr=Array(e):this._ptr.length=e,this._capacity=e)}begin(){return 0==this._size?this.end():new ee(this,0)}end(){return new ee(this,this._size)}getOffset(e){let t=new $;return t._ptr=this.get(e),t._size=this.get(e).length,t._capacity=this.get(e).length,t}constructor(e=0){e<1?(this._ptr=[],this._capacity=0):(this._ptr=Array(e),this._capacity=e),this._size=0}}$.DefaultSize=10;class ee{set(e){return this._index=e._index,this._vector=e._vector,this}preIncrement(){return++this._index,this}preDecrement(){return--this._index,this}increment(){return new ee(this._vector,this._index++)}decrement(){return new ee(this._vector,this._index--)}ptr(){return this._vector._ptr[this._index]}substitution(e){return this._index=e._index,this._vector=e._vector,this}notEqual(e){return this._index!=e._index||this._vector!=e._vector}constructor(e,t){this._vector=void 0!=e?e:null,this._index=void 0!=t?t:0}}!function(e){e.csmVector=$,e.iterator=ee}(h||(h={}));class et{append(e,t){return this.s+=void 0!==t?e.substr(0,t):e,this}expansion(e,t){for(let i=0;i<e;i++)this.append(t);return this}getBytes(){return encodeURIComponent(this.s).replace(/%../g,"x").length}getLength(){return this.s.length}isLess(e){return this.s<e.s}isGreat(e){return this.s>e.s}isEqual(e){return this.s==e}isEmpty(){return 0==this.s.length}constructor(e){this.s=e}}(g||(g={})).csmString=et;class ei{static createIdInternal(e){return new ei(e)}getString(){return this._id}isEqual(e){return"string"==typeof e?this._id.isEqual(e):e instanceof et?this._id.isEqual(e.s):e instanceof ei&&this._id.isEqual(e._id.s)}isNotEqual(e){return"string"==typeof e?!this._id.isEqual(e):e instanceof et?!this._id.isEqual(e.s):e instanceof ei&&!this._id.isEqual(e._id.s)}constructor(e){if("string"==typeof e){this._id=new et(e);return}this._id=e}}(d||(d={})).CubismId=ei;class es{release(){for(let e=0;e<this._ids.getSize();++e)this._ids.set(e,void 0);this._ids=null}registerIds(e){for(let t=0;t<e.length;t++)this.registerId(e[t])}registerId(e){let t=null;return"string"!=typeof e?this.registerId(e.s):(null!=(t=this.findId(e))||(t=ei.createIdInternal(e),this._ids.pushBack(t)),t)}getId(e){return this.registerId(e)}isExist(e){return"string"==typeof e?null!=this.findId(e):this.isExist(e.s)}findId(e){for(let t=0;t<this._ids.getSize();++t)if(this._ids.at(t).getString().isEqual(e))return this._ids.at(t);return null}constructor(){this._ids=new $}}(c||(c={})).CubismIdManager=es;class er{static multiply(e,t,i){let s=new Float32Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]);for(let i=0;i<4;++i)for(let r=0;r<4;++r)for(let a=0;a<4;++a)s[r+4*i]+=e[a+4*i]*t[r+4*a];for(let e=0;e<16;++e)i[e]=s[e]}loadIdentity(){let e=new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]);this.setMatrix(e)}setMatrix(e){for(let t=0;t<16;++t)this._tr[t]=e[t]}getArray(){return this._tr}getScaleX(){return this._tr[0]}getScaleY(){return this._tr[5]}getTranslateX(){return this._tr[12]}getTranslateY(){return this._tr[13]}transformX(e){return this._tr[0]*e+this._tr[12]}transformY(e){return this._tr[5]*e+this._tr[13]}invertTransformX(e){return(e-this._tr[12])/this._tr[0]}invertTransformY(e){return(e-this._tr[13])/this._tr[5]}translateRelative(e,t){let i=new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,e,t,0,1]);er.multiply(i,this._tr,this._tr)}translate(e,t){this._tr[12]=e,this._tr[13]=t}translateX(e){this._tr[12]=e}translateY(e){this._tr[13]=e}scaleRelative(e,t){let i=new Float32Array([e,0,0,0,0,t,0,0,0,0,1,0,0,0,0,1]);er.multiply(i,this._tr,this._tr)}scale(e,t){this._tr[0]=e,this._tr[5]=t}multiplyByMatrix(e){er.multiply(e.getArray(),this._tr,this._tr)}clone(){let e=new er;for(let t=0;t<this._tr.length;t++)e._tr[t]=this._tr[t];return e}constructor(){this._tr=new Float32Array(16),this.loadIdentity()}}(_||(_={})).CubismMatrix44=er;class ea{getCenterX(){return this.x+.5*this.width}getCenterY(){return this.y+.5*this.height}getRight(){return this.x+this.width}getBottom(){return this.y+this.height}setRect(e){this.x=e.x,this.y=e.y,this.width=e.width,this.height=e.height}expand(e,t){this.x-=e,this.y-=t,this.width+=2*e,this.height+=2*t}constructor(e,t,i,s){this.x=e,this.y=t,this.width=i,this.height=s}}(m||(m={})).csmRect=ea;class en{static create(){return null}static delete(e){}initialize(e){this._model=e}drawModel(){null!=this.getModel()&&(this.saveProfile(),this.doDrawModel(),this.restoreProfile())}setMvpMatrix(e){this._mvpMatrix4x4.setMatrix(e.getArray())}getMvpMatrix(){return this._mvpMatrix4x4}setModelColor(e,t,i,s){e<0?e=0:e>1&&(e=1),t<0?t=0:t>1&&(t=1),i<0?i=0:i>1&&(i=1),s<0?s=0:s>1&&(s=1),this._modelColor.r=e,this._modelColor.g=t,this._modelColor.b=i,this._modelColor.a=s}getModelColor(){return JSON.parse(JSON.stringify(this._modelColor))}getModelColorWithOpacity(e){let t=this.getModelColor();return t.a*=e,this.isPremultipliedAlpha()&&(t.r*=t.a,t.g*=t.a,t.b*=t.a),t}setIsPremultipliedAlpha(e){this._isPremultipliedAlpha=e}isPremultipliedAlpha(){return this._isPremultipliedAlpha}setIsCulling(e){this._isCulling=e}isCulling(){return this._isCulling}setAnisotropy(e){this._anisotropy=e}getAnisotropy(){return this._anisotropy}getModel(){return this._model}useHighPrecisionMask(e){this._useHighPrecisionMask=e}isUsingHighPrecisionMask(){return this._useHighPrecisionMask}constructor(){this._isCulling=!1,this._isPremultipliedAlpha=!1,this._anisotropy=0,this._model=null,this._modelColor=new eo,this._useHighPrecisionMask=!1,this._mvpMatrix4x4=new er,this._mvpMatrix4x4.loadIdentity()}}var el=function(e){return e[e.CubismBlendMode_Normal=0]="CubismBlendMode_Normal",e[e.CubismBlendMode_Additive=1]="CubismBlendMode_Additive",e[e.CubismBlendMode_Multiplicative=2]="CubismBlendMode_Multiplicative",e}({});class eo{constructor(e=1,t=1,i=1,s=1){this.r=e,this.g=t,this.b=i,this.a=s}}class eu{release(){null!=this._layoutBounds&&(this._layoutBounds=null),null!=this._allClippedDrawRect&&(this._allClippedDrawRect=null),null!=this._clippedDrawableIndexList&&(this._clippedDrawableIndexList=null)}addClippedDrawable(e){this._clippedDrawableIndexList.push(e)}constructor(e,t){this._clippingIdList=e,this._clippingIdCount=t,this._allClippedDrawRect=new ea,this._layoutBounds=new ea,this._clippedDrawableIndexList=[],this._matrixForMask=new er,this._matrixForDraw=new er,this._bufferIndex=0}}!function(e){e.CubismBlendMode=el,e.CubismRenderer=en,e.CubismTextureColor=eo}(p||(p={}));let eh=(e,t,i)=>{ec.print(e,"[CSM]"+t,i)},eg=(e,t,i)=>{eh(e,t+"\n",i)},ed=e=>{console.assert(e)};s=function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];eg(eD.LogLevel_Debug,"[D]"+e,i)},r=function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];eg(eD.LogLevel_Info,"[I]"+e,i)},a=function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];eg(eD.LogLevel_Warning,"[W]"+e,i)},n=function(e){for(var t=arguments.length,i=Array(t>1?t-1:0),s=1;s<t;s++)i[s-1]=arguments[s];eg(eD.LogLevel_Error,"[E]"+e,i)};class ec{static print(e,t,i){if(e<eF.getLoggingLevel())return;let s=eF.coreLogFunction;s&&s(t.replace(/\{(\d+)\}/g,(e,t)=>i[t]))}static dumpBytes(e,t,i){for(let s=0;s<i;s++)s%16==0&&s>0?this.print(e,"\n"):s%8==0&&s>0&&this.print(e,"  "),this.print(e,"{0} ",[255&t[s]]);this.print(e,"\n")}constructor(){}}(f||(f={})).CubismDebug=ec;class e_{constructor(e,t){this.first=void 0==e?null:e,this.second=void 0==t?null:t}}class em{release(){this.clear()}appendKey(e){let t=-1;for(let i=0;i<this._size;i++)if(this._keyValues[i].first==e){t=i;break}if(-1!=t){a("The key `{0}` is already append.",e);return}this.prepareCapacity(this._size+1,!1),this._keyValues[this._size]=new e_(e),this._size+=1}getValue(e){let t=-1;for(let i=0;i<this._size;i++)if(this._keyValues[i].first==e){t=i;break}return t>=0?this._keyValues[t].second:(this.appendKey(e),this._keyValues[this._size-1].second)}setValue(e,t){let i=-1;for(let t=0;t<this._size;t++)if(this._keyValues[t].first==e){i=t;break}i>=0?this._keyValues[i].second=t:(this.appendKey(e),this._keyValues[this._size-1].second=t)}isExist(e){for(let t=0;t<this._size;t++)if(this._keyValues[t].first==e)return!0;return!1}clear(){this._keyValues=void 0,this._keyValues=null,this._keyValues=[],this._size=0}getSize(){return this._size}prepareCapacity(e,t){e>this._keyValues.length&&(0==this._keyValues.length?!t&&e<em.DefaultSize&&(e=em.DefaultSize):!t&&e<2*this._keyValues.length&&(e=2*this._keyValues.length),this._keyValues.length=e)}begin(){return new ep(this,0)}end(){return new ep(this,this._size)}erase(e){let t=e._index;return t<0||this._size<=t?e:(this._keyValues.splice(t,1),--this._size,new ep(this,t))}dumpAsInt(){for(let e=0;e<this._size;e++)s("{0} ,",this._keyValues[e]),s("\n")}constructor(e){void 0!=e?e<1?(this._keyValues=[],this._dummyValue=null,this._size=0):(this._keyValues=Array(e),this._size=e):(this._keyValues=[],this._dummyValue=null,this._size=0)}}em.DefaultSize=10;class ep{set(e){return this._index=e._index,this._map=e._map,this}preIncrement(){return++this._index,this}preDecrement(){return--this._index,this}increment(){return new ep(this._map,this._index++)}decrement(){let e=new ep(this._map,this._index);return this._map=e._map,this._index=e._index,this}ptr(){return this._map._keyValues[this._index]}notEqual(e){return this._index!=e._index||this._map!=e._map}constructor(e,t){this._map=void 0!=e?e:new em,this._index=void 0!=t?t:0}}!function(e){e.csmMap=em,e.csmPair=e_,e.iterator=ep}(y||(y={}));class ef{static parseJsonObject(e,t){return Object.keys(e).forEach(i=>{if("boolean"==typeof e[i]){let s=!!e[i];t.put(i,new eb(s))}else if("string"==typeof e[i]){let s=String(e[i]);t.put(i,new ev(s))}else if("number"==typeof e[i]){let s=Number(e[i]);t.put(i,new eC(s))}else e[i]instanceof Array?t.put(i,ef.parseJsonArray(e[i])):e[i]instanceof Object?t.put(i,ef.parseJsonObject(e[i],new eB)):null==e[i]?t.put(i,new eP):t.put(i,e[i])}),t}static parseJsonArray(e){let t=new ew;return Object.keys(e).forEach(i=>{if("number"==typeof Number(i)){if("boolean"==typeof e[i]){let s=!!e[i];t.add(new eb(s))}else if("string"==typeof e[i]){let s=String(e[i]);t.add(new ev(s))}else if("number"==typeof e[i]){let s=Number(e[i]);t.add(new eC(s))}else e[i]instanceof Array?t.add(this.parseJsonArray(e[i])):e[i]instanceof Object?t.add(this.parseJsonObject(e[i],new eB)):null==e[i]?t.add(new eP):t.add(e[i])}else if(e[i]instanceof Array)t.add(this.parseJsonArray(e[i]));else if(e[i]instanceof Object)t.add(this.parseJsonObject(e[i],new eB));else if(null==e[i])t.add(new eP);else{let s=Array(e[i]);for(let e=0;e<s.length;e++)t.add(s[e])}}),t}}let ey="Error: type mismatch";class ex{getRawString(e,t){return this.getString(e,t)}toInt(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return e}toFloat(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return e}toBoolean(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return e}getSize(){return 0}getArray(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return e}getVector(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new $;return e}getMap(e){return e}getValueByIndex(e){return ex.errorValue.setErrorNotForClientCall(ey)}getValueByString(e){return ex.nullValue.setErrorNotForClientCall(ey)}getKeys(){return ex.dummyKeys}isError(){return!1}isNull(){return!1}isBool(){return!1}isFloat(){return!1}isString(){return!1}isArray(){return!1}isMap(){return!1}equals(e){return!1}isStatic(){return!1}setErrorNotForClientCall(e){return eM.errorValue}static staticInitializeNotForClientCall(){eb.trueValue=new eb(!0),eb.falseValue=new eb(!1),ex.errorValue=new eM("ERROR",!0),ex.nullValue=new eP,ex.dummyKeys=new $}static staticReleaseNotForClientCall(){eb.trueValue=null,eb.falseValue=null,ex.errorValue=null,ex.nullValue=null,ex.dummyKeys=null}constructor(){}}class eS{static create(e,t){let i=new eS;return i.parseBytes(e,t,i._parseCallback)?i:(eS.delete(i),null)}static delete(e){}getRoot(){return this._root}static arrayBufferToString(e){let t=new Uint8Array(e),i="";for(let e=0,s=t.length;e<s;++e)i+="%"+this.pad(t[e].toString(16));return decodeURIComponent(i)}static pad(e){return e.length<2?"0"+e:e}parseBytes(e,t,i){let s=eS.arrayBufferToString(e);if(void 0==i?this._root=this.parseValue(s,t,0,[,]):this._root=i(JSON.parse(s),new eB),this._error){let e="\0";return e="Json parse error : @line "+(this._lineCount+1)+"\n",this._root=new ev(e),r("{0}",this._root.getRawString()),!1}return null!=this._root||(this._root=new eM(new et(this._error),!1),!1)}getParseError(){return this._error}checkEndOfFile(){return this._root.getArray()[1].equals("EOF")}parseValue(e,t,i,s){let r;if(this._error)return null;let a=null,n=i;for(;n<t;n++)switch(e[n]){case"-":case".":case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":{let t=[,];return r=function(e,t){let i=0;for(let t=1;;t++){let s=e.slice(t-1,t);if("e"!=s&&"-"!=s&&"E"!=s){if(isNaN(Number(e.substring(0,t))))break;i=t}}let s=parseFloat(e);return isNaN(s)&&(s=NaN),t[0]=e.slice(i),s}(e.slice(n),t),s[0]=e.indexOf(t[0]),new eC(r)}case'"':return new ev(this.parseString(e,t,n+1,s));case"[":return this.parseArray(e,t,n+1,s);case"{":return this.parseObject(e,t,n+1,s);case"n":return n+3<t?(a=new eP,s[0]=n+4):this._error="parse null",a;case"t":return n+3<t?(a=eb.trueValue,s[0]=n+4):this._error="parse true",a;case"f":return n+4<t?(a=eb.falseValue,s[0]=n+5):this._error="illegal ',' position",a;case",":return this._error="illegal ',' position",null;case"]":return s[0]=n,null;case"\n":this._lineCount++}return this._error="illegal end of value",null}parseString(e,t,i,s){if(this._error)return null;if(!e)return this._error="string is null",null;let r=i,a=new et(""),n=i;for(;r<t;r++)switch(e[r]){case'"':return s[0]=r+1,a.append(e.slice(n),r-n),a.s;case"//":if(++r-1>n&&a.append(e.slice(n),r-n),n=r+1,r<t)switch(e[r]){case"\\":a.expansion(1,"\\");break;case'"':a.expansion(1,'"');break;case"/":a.expansion(1,"/");break;case"b":a.expansion(1,"\b");break;case"f":a.expansion(1,"\f");break;case"n":a.expansion(1,"\n");break;case"r":a.expansion(1,"\r");break;case"t":a.expansion(1,"	");break;case"u":this._error="parse string/unicord escape not supported"}else this._error="parse string/escape error"}return this._error="parse string/illegal end",null}parseObject(e,t,i,s){if(this._error)return null;if(!e)return this._error="buffer is null",null;let r=new eB,a="",n=i,l=[,],o=!1;for(;n<t;n++){e:for(;n<t;n++)switch(e[n]){case'"':if(a=this.parseString(e,t,n+1,l),this._error)return null;n=l[0],o=!0;break e;case"}":return s[0]=n+1,r;case":":this._error="illegal ':' position";break;case"\n":this._lineCount++}if(!o)return this._error="key not found",null;o=!1;t:for(;n<t;n++)switch(e[n]){case":":o=!0,n++;break t;case"}":this._error="illegal '}' position";break;case"\n":this._lineCount++}if(!o)return this._error="':' not found",null;let i=this.parseValue(e,t,n,l);if(this._error)return null;n=l[0],r.put(a,i);i:for(;n<t;n++)switch(e[n]){case",":break i;case"}":return s[0]=n+1,r;case"\n":this._lineCount++}}return this._error="illegal end of perseObject",null}parseArray(e,t,i,s){if(this._error)return null;if(!e)return this._error="buffer is null",null;let r=new ew,a=i,n=[,];for(;a<t;a++){let i=this.parseValue(e,t,a,n);if(this._error)return null;a=n[0],i&&r.add(i);e:for(;a<t;a++)switch(e[a]){case",":break e;case"]":return s[0]=a+1,r;case"\n":++this._lineCount}}return r=void 0,this._error="illegal end of parseObject",null}constructor(e,t){this._parseCallback=ef.parseJsonObject,this._error=null,this._lineCount=0,this._root=null,void 0!=e&&this.parseBytes(e,t,this._parseCallback)}}class eC extends ex{isFloat(){return!0}getString(e,t){return this._value=parseFloat("\0"),this._stringBuffer="\0",this._stringBuffer}toInt(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0],parseInt(this._value.toString())}toFloat(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0],this._value}equals(e){return!("number"!=typeof e||Math.round(e))&&e==this._value}constructor(e){super(),this._value=e}}class eb extends ex{isBool(){return!0}toBoolean(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0],this._boolValue}getString(e,t){return this._stringBuffer=this._boolValue?"true":"false",this._stringBuffer}equals(e){return"boolean"==typeof e&&e==this._boolValue}isStatic(){return!0}constructor(e){super(),this._boolValue=e}}class ev extends ex{isString(){return!0}getString(e,t){return this._stringBuffer}equals(e){return"string"==typeof e?this._stringBuffer==e:e instanceof et&&this._stringBuffer==e.s}constructor(e){super(),"string"==typeof e&&(this._stringBuffer=e),e instanceof et&&(this._stringBuffer=e.s)}}class eM extends ev{isStatic(){return this._isStatic}setErrorNotForClientCall(e){return this._stringBuffer=e,this}isError(){return!0}constructor(e,t){super(e),this._isStatic=t}}class eP extends ex{isNull(){return!0}getString(e,t){return this._stringBuffer}isStatic(){return!0}setErrorNotForClientCall(e){return this._stringBuffer=e,eM.nullValue}constructor(){super(),this._stringBuffer="NullValue"}}class ew extends ex{release(){for(let e=this._array.begin();e.notEqual(this._array.end());e.preIncrement()){let t=e.ptr();t&&!t.isStatic()&&(t=void 0,t=null)}}isArray(){return!0}getValueByIndex(e){if(e<0||this._array.getSize()<=e)return ex.errorValue.setErrorNotForClientCall("Error: index out of bounds");let t=this._array.at(e);return null==t?ex.nullValue:t}getValueByString(e){return ex.errorValue.setErrorNotForClientCall(ey)}getString(e,t){for(let e=this._array.begin();e.notEqual(this._array.end());e.increment()){let i=e.ptr();this._stringBuffer+=t+""+i.getString(t+" ")+"\n"}return this._stringBuffer=t+"[\n"+t+"]\n",this._stringBuffer}add(e){this._array.pushBack(e)}getVector(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0],this._array}getSize(){return this._array.getSize()}constructor(){super(),this._array=new $}}class eB extends ex{release(){let e=this._map.begin();for(;e.notEqual(this._map.end());){let t=e.ptr().second;t&&!t.isStatic()&&(t=void 0,t=null),e.preIncrement()}}isMap(){return!0}getValueByString(e){if(e instanceof et){let t=this._map.getValue(e.s);return null==t?ex.nullValue:t}for(let t=this._map.begin();t.notEqual(this._map.end());t.preIncrement())if(t.ptr().first==e){if(null==t.ptr().second)return ex.nullValue;return t.ptr().second}return ex.nullValue}getValueByIndex(e){return ex.errorValue.setErrorNotForClientCall(ey)}getString(e,t){this._stringBuffer=t+"{\n";let i=this._map.begin();for(;i.notEqual(this._map.end());){let e=i.ptr().first,s=i.ptr().second;this._stringBuffer+=t+" "+e+" : "+s.getString(t+"   ")+" \n",i.preIncrement()}return this._stringBuffer+=t+"}\n",this._stringBuffer}getMap(e){return this._map}put(e,t){this._map.setValue(e,t)}getKeys(){if(!this._keys){this._keys=new $;let e=this._map.begin();for(;e.notEqual(this._map.end());){let t=e.ptr().first;this._keys.pushBack(t),e.preIncrement()}}return this._keys}getSize(){return this._keys.getSize()}constructor(){super(),this._map=new em}}!function(e){e.CubismJson=eS,e.JsonArray=ew,e.JsonBoolean=eb,e.JsonError=eM,e.JsonFloat=eC,e.JsonMap=eB,e.JsonNullvalue=eP,e.JsonString=ev,e.Value=ex}(x||(x={}));let eI=!1,eT=!1,eE=null,eR=null,eV=Object.freeze({vertexOffset:0,vertexStep:2});function eA(e){e&&(e=void 0)}class eF{static startUp(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(eI)return r("CubismFramework.startUp() is already done."),eI;null!=(eE=e)&&Live2DCubismCore.Logging.csmSetLogFunction(eE.logFunction),eI=!0;{let e=Live2DCubismCore.Version.csmGetVersion(),t=(0xff000000&e)>>24,i=(0xff0000&e)>>16,s=65535&e;r("Live2D Cubism Core version: {0}.{1}.{2} ({3})",("00"+t).slice(-2),("00"+i).slice(-2),("0000"+s).slice(-4),e)}return r("CubismFramework.startUp() is complete."),eI}static cleanUp(){eI=!1,eT=!1,eE=null,eR=null}static initialize(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;if(ed(eI),!eI){a("CubismFramework is not started.");return}if(eT){a("CubismFramework.initialize() skipped, already initialized.");return}ex.staticInitializeNotForClientCall(),eR=new es,Live2DCubismCore.Memory.initializeAmountOfMemory(e),eT=!0,r("CubismFramework.initialize() is complete.")}static dispose(){if(ed(eI),!eI){a("CubismFramework is not started.");return}if(!eT){a("CubismFramework.dispose() skipped, not initialized.");return}ex.staticReleaseNotForClientCall(),eR.release(),eR=null,en.staticRelease(),eT=!1,r("CubismFramework.dispose() is complete.")}static isStarted(){return eI}static isInitialized(){return eT}static coreLogFunction(e){Live2DCubismCore.Logging.csmGetLogFunction()&&Live2DCubismCore.Logging.csmGetLogFunction()(e)}static getLoggingLevel(){return null!=eE?eE.loggingLevel:5}static getIdManager(){return eR}constructor(){}}class eL{}var eD=function(e){return e[e.LogLevel_Verbose=0]="LogLevel_Verbose",e[e.LogLevel_Debug=1]="LogLevel_Debug",e[e.LogLevel_Info=2]="LogLevel_Info",e[e.LogLevel_Warning=3]="LogLevel_Warning",e[e.LogLevel_Error=4]="LogLevel_Error",e[e.LogLevel_Off=5]="LogLevel_Off",e}({});!function(e){e.Constant=eV,e.csmDelete=eA,e.CubismFramework=eF}(S||(S={}));let ek=eD.LogLevel_Verbose;class eN{static loadFileAsBytes(e,t){fetch(e).then(e=>e.arrayBuffer()).then(e=>t(e,e.byteLength))}static getDeltaTime(){return this.deltaTime}static updateTime(){this.currentFrame=Date.now(),this.deltaTime=(this.currentFrame-this.lastFrame)/1e3,this.lastFrame=this.currentFrame}static printMessage(e){console.log(e)}}eN.lastUpdate=Date.now(),eN.currentFrame=0,eN.lastFrame=0,eN.deltaTime=0;class eO{initialize(e){return this._gl=e.getContext("webgl2"),!!this._gl||(alert("Cannot initialize WebGL. This browser does not support."),this._gl=null,!1)}release(){}getGl(){return this._gl}constructor(){this._gl=null,this._gl=null}}let ej=Object.freeze({HitAreaPrefix:"HitArea",HitAreaHead:"Head",HitAreaBody:"Body",PartsIdCore:"Parts01Core",PartsArmPrefix:"Parts01Arm_",PartsArmLPrefix:"Parts01ArmL_",PartsArmRPrefix:"Parts01ArmR_",ParamAngleX:"ParamAngleX",ParamAngleY:"ParamAngleY",ParamAngleZ:"ParamAngleZ",ParamEyeLOpen:"ParamEyeLOpen",ParamEyeLSmile:"ParamEyeLSmile",ParamEyeROpen:"ParamEyeROpen",ParamEyeRSmile:"ParamEyeRSmile",ParamEyeBallX:"ParamEyeBallX",ParamEyeBallY:"ParamEyeBallY",ParamEyeBallForm:"ParamEyeBallForm",ParamBrowLY:"ParamBrowLY",ParamBrowRY:"ParamBrowRY",ParamBrowLX:"ParamBrowLX",ParamBrowRX:"ParamBrowRX",ParamBrowLAngle:"ParamBrowLAngle",ParamBrowRAngle:"ParamBrowRAngle",ParamBrowLForm:"ParamBrowLForm",ParamBrowRForm:"ParamBrowRForm",ParamMouthForm:"ParamMouthForm",ParamMouthOpenY:"ParamMouthOpenY",ParamCheek:"ParamCheek",ParamBodyAngleX:"ParamBodyAngleX",ParamBodyAngleY:"ParamBodyAngleY",ParamBodyAngleZ:"ParamBodyAngleZ",ParamBreath:"ParamBreath",ParamArmLA:"ParamArmLA",ParamArmRA:"ParamArmRA",ParamArmLB:"ParamArmLB",ParamArmRB:"ParamArmRB",ParamHandL:"ParamHandL",ParamHandR:"ParamHandR",ParamHairFront:"ParamHairFront",ParamHairSide:"ParamHairSide",ParamHairBack:"ParamHairBack",ParamHairFluffy:"ParamHairFluffy",ParamShoulderY:"ParamShoulderY",ParamBustX:"ParamBustX",ParamBustY:"ParamBustY",ParamBaseX:"ParamBaseX",ParamBaseY:"ParamBaseY",ParamNONE:"NONE:"});!function(e){e.HitAreaBody=ej.HitAreaBody,e.HitAreaHead=ej.HitAreaHead,e.HitAreaPrefix=ej.HitAreaPrefix,e.ParamAngleX=ej.ParamAngleX,e.ParamAngleY=ej.ParamAngleY,e.ParamAngleZ=ej.ParamAngleZ,e.ParamArmLA=ej.ParamArmLA,e.ParamArmLB=ej.ParamArmLB,e.ParamArmRA=ej.ParamArmRA,e.ParamArmRB=ej.ParamArmRB,e.ParamBaseX=ej.ParamBaseX,e.ParamBaseY=ej.ParamBaseY,e.ParamBodyAngleX=ej.ParamBodyAngleX,e.ParamBodyAngleY=ej.ParamBodyAngleY,e.ParamBodyAngleZ=ej.ParamBodyAngleZ,e.ParamBreath=ej.ParamBreath,e.ParamBrowLAngle=ej.ParamBrowLAngle,e.ParamBrowLForm=ej.ParamBrowLForm,e.ParamBrowLX=ej.ParamBrowLX,e.ParamBrowLY=ej.ParamBrowLY,e.ParamBrowRAngle=ej.ParamBrowRAngle,e.ParamBrowRForm=ej.ParamBrowRForm,e.ParamBrowRX=ej.ParamBrowRX,e.ParamBrowRY=ej.ParamBrowRY,e.ParamBustX=ej.ParamBustX,e.ParamBustY=ej.ParamBustY,e.ParamCheek=ej.ParamCheek,e.ParamEyeBallForm=ej.ParamEyeBallForm,e.ParamEyeBallX=ej.ParamEyeBallX,e.ParamEyeBallY=ej.ParamEyeBallY,e.ParamEyeLOpen=ej.ParamEyeLOpen,e.ParamEyeLSmile=ej.ParamEyeLSmile,e.ParamEyeROpen=ej.ParamEyeROpen,e.ParamEyeRSmile=ej.ParamEyeRSmile,e.ParamHairBack=ej.ParamHairBack,e.ParamHairFluffy=ej.ParamHairFluffy,e.ParamHairFront=ej.ParamHairFront,e.ParamHairSide=ej.ParamHairSide,e.ParamHandL=ej.ParamHandL,e.ParamHandR=ej.ParamHandR,e.ParamMouthForm=ej.ParamMouthForm,e.ParamMouthOpenY=ej.ParamMouthOpenY,e.ParamNONE=ej.ParamNONE,e.ParamShoulderY=ej.ParamShoulderY,e.PartsArmLPrefix=ej.PartsArmLPrefix,e.PartsArmPrefix=ej.PartsArmPrefix,e.PartsArmRPrefix=ej.PartsArmRPrefix,e.PartsIdCore=ej.PartsIdCore}(C||(C={}));class eU{}(b||(b={})).ICubismModelSetting=eU;var ez=function(e){return e[e.FrequestNode_Groups=0]="FrequestNode_Groups",e[e.FrequestNode_Moc=1]="FrequestNode_Moc",e[e.FrequestNode_Motions=2]="FrequestNode_Motions",e[e.FrequestNode_Expressions=3]="FrequestNode_Expressions",e[e.FrequestNode_Textures=4]="FrequestNode_Textures",e[e.FrequestNode_Physics=5]="FrequestNode_Physics",e[e.FrequestNode_Pose=6]="FrequestNode_Pose",e[e.FrequestNode_HitAreas=7]="FrequestNode_HitAreas",e}({});class eG extends eU{release(){eS.delete(this._json),this._jsonValue=null}getJson(){return this._json}getModelFileName(){return this.isExistModelFile()?this._jsonValue.at(1).getRawString():""}getTextureCount(){return this.isExistTextureFiles()?this._jsonValue.at(4).getSize():0}getTextureDirectory(){let e=this._jsonValue.at(4).getValueByIndex(0).getRawString().split("/"),t=e.length-1,i="";for(let s=0;s<t;s++)i+=e[s],s<t-1&&(i+="/");return i}getTextureFileName(e){return this._jsonValue.at(4).getValueByIndex(e).getRawString()}getHitAreasCount(){return this.isExistHitAreas()?this._jsonValue.at(7).getSize():0}getHitAreaId(e){return eF.getIdManager().getId(this._jsonValue.at(7).getValueByIndex(e).getValueByString(this.id).getRawString())}getHitAreaName(e){return this._jsonValue.at(7).getValueByIndex(e).getValueByString(this.name).getRawString()}getPhysicsFileName(){return this.isExistPhysicsFile()?this._jsonValue.at(5).getRawString():""}getPoseFileName(){return this.isExistPoseFile()?this._jsonValue.at(6).getRawString():""}getExpressionCount(){return this.isExistExpressionFile()?this._jsonValue.at(3).getSize():0}getExpressionName(e){return this._jsonValue.at(3).getValueByIndex(e).getValueByString(this.name).getRawString()}getExpressionFileName(e){return this._jsonValue.at(3).getValueByIndex(e).getValueByString(this.filePath).getRawString()}getMotionGroupCount(){return this.isExistMotionGroups()?this._jsonValue.at(2).getKeys().getSize():0}getMotionGroupName(e){return this.isExistMotionGroups()?this._jsonValue.at(2).getKeys().at(e):null}getMotionCount(e){return this.isExistMotionGroupName(e)?this._jsonValue.at(2).getValueByString(e).getSize():0}getMotionFileName(e,t){return this.isExistMotionGroupName(e)?this._jsonValue.at(2).getValueByString(e).getValueByIndex(t).getValueByString(this.filePath).getRawString():""}getMotionSoundFileName(e,t){return this.isExistMotionSoundFile(e,t)?this._jsonValue.at(2).getValueByString(e).getValueByIndex(t).getValueByString(this.soundPath).getRawString():""}getMotionFadeInTimeValue(e,t){return this.isExistMotionFadeIn(e,t)?this._jsonValue.at(2).getValueByString(e).getValueByIndex(t).getValueByString(this.fadeInTime).toFloat():-1}getMotionFadeOutTimeValue(e,t){return this.isExistMotionFadeOut(e,t)?this._jsonValue.at(2).getValueByString(e).getValueByIndex(t).getValueByString(this.fadeOutTime).toFloat():-1}getUserDataFile(){return this.isExistUserDataFile()?this.getJson().getRoot().getValueByString(this.fileReferences).getValueByString(this.userData).getRawString():""}getLayoutMap(e){let t=this.getJson().getRoot().getValueByString(this.layout).getMap();if(null==t)return!1;let i=!1;for(let s=t.begin();s.notEqual(t.end());s.preIncrement())e.setValue(s.ptr().first,s.ptr().second.toFloat()),i=!0;return i}getEyeBlinkParameterCount(){if(!this.isExistEyeBlinkParameters())return 0;let e=0;for(let t=0;t<this._jsonValue.at(0).getSize();t++){let i=this._jsonValue.at(0).getValueByIndex(t);if(!(i.isNull()||i.isError())&&i.getValueByString(this.name).getRawString()==this.eyeBlink){e=i.getValueByString(this.ids).getVector().getSize();break}}return e}getEyeBlinkParameterId(e){if(!this.isExistEyeBlinkParameters())return null;for(let t=0;t<this._jsonValue.at(0).getSize();t++){let i=this._jsonValue.at(0).getValueByIndex(t);if(!(i.isNull()||i.isError())&&i.getValueByString(this.name).getRawString()==this.eyeBlink)return eF.getIdManager().getId(i.getValueByString(this.ids).getValueByIndex(e).getRawString())}return null}getLipSyncParameterCount(){if(!this.isExistLipSyncParameters())return 0;let e=0;for(let t=0;t<this._jsonValue.at(0).getSize();t++){let i=this._jsonValue.at(0).getValueByIndex(t);if(!(i.isNull()||i.isError())&&i.getValueByString(this.name).getRawString()==this.lipSync){e=i.getValueByString(this.ids).getVector().getSize();break}}return e}getLipSyncParameterId(e){if(!this.isExistLipSyncParameters())return null;for(let t=0;t<this._jsonValue.at(0).getSize();t++){let i=this._jsonValue.at(0).getValueByIndex(t);if(!(i.isNull()||i.isError())&&i.getValueByString(this.name).getRawString()==this.lipSync)return eF.getIdManager().getId(i.getValueByString(this.ids).getValueByIndex(e).getRawString())}return null}isExistModelFile(){let e=this._jsonValue.at(1);return!e.isNull()&&!e.isError()}isExistTextureFiles(){let e=this._jsonValue.at(4);return!e.isNull()&&!e.isError()}isExistHitAreas(){let e=this._jsonValue.at(7);return!e.isNull()&&!e.isError()}isExistPhysicsFile(){let e=this._jsonValue.at(5);return!e.isNull()&&!e.isError()}isExistPoseFile(){let e=this._jsonValue.at(6);return!e.isNull()&&!e.isError()}isExistExpressionFile(){let e=this._jsonValue.at(3);return!e.isNull()&&!e.isError()}isExistMotionGroups(){let e=this._jsonValue.at(2);return!e.isNull()&&!e.isError()}isExistMotionGroupName(e){let t=this._jsonValue.at(2).getValueByString(e);return!t.isNull()&&!t.isError()}isExistMotionSoundFile(e,t){let i=this._jsonValue.at(2).getValueByString(e).getValueByIndex(t).getValueByString(this.soundPath);return!i.isNull()&&!i.isError()}isExistMotionFadeIn(e,t){let i=this._jsonValue.at(2).getValueByString(e).getValueByIndex(t).getValueByString(this.fadeInTime);return!i.isNull()&&!i.isError()}isExistMotionFadeOut(e,t){let i=this._jsonValue.at(2).getValueByString(e).getValueByIndex(t).getValueByString(this.fadeOutTime);return!i.isNull()&&!i.isError()}isExistUserDataFile(){let e=this.getJson().getRoot().getValueByString(this.fileReferences).getValueByString(this.userData);return!e.isNull()&&!e.isError()}isExistEyeBlinkParameters(){if(this._jsonValue.at(0).isNull()||this._jsonValue.at(0).isError())return!1;for(let e=0;e<this._jsonValue.at(0).getSize();++e)if(this._jsonValue.at(0).getValueByIndex(e).getValueByString(this.name).getRawString()==this.eyeBlink)return!0;return!1}isExistLipSyncParameters(){if(this._jsonValue.at(0).isNull()||this._jsonValue.at(0).isError())return!1;for(let e=0;e<this._jsonValue.at(0).getSize();++e)if(this._jsonValue.at(0).getValueByIndex(e).getValueByString(this.name).getRawString()==this.lipSync)return!0;return!1}constructor(e,t){super(),this.version="Version",this.fileReferences="FileReferences",this.groups="Groups",this.layout="Layout",this.hitAreas="HitAreas",this.moc="Moc",this.textures="Textures",this.physics="Physics",this.pose="Pose",this.expressions="Expressions",this.motions="Motions",this.userData="UserData",this.name="Name",this.filePath="File",this.id="Id",this.ids="Ids",this.target="Target",this.idle="Idle",this.tapBody="TapBody",this.pinchIn="PinchIn",this.pinchOut="PinchOut",this.shake="Shake",this.flickHead="FlickHead",this.parameter="Parameter",this.soundPath="Sound",this.fadeInTime="FadeInTime",this.fadeOutTime="FadeOutTime",this.centerX="CenterX",this.centerY="CenterY",this.x="X",this.y="Y",this.width="Width",this.height="Height",this.lipSync="LipSync",this.eyeBlink="EyeBlink",this.initParameter="init_param",this.initPartsVisible="init_parts_visible",this.val="val",this._json=eS.create(e,t),this.getJson()&&(this._jsonValue=new $,this._jsonValue.pushBack(this.getJson().getRoot().getValueByString(this.groups)),this._jsonValue.pushBack(this.getJson().getRoot().getValueByString(this.fileReferences).getValueByString(this.moc)),this._jsonValue.pushBack(this.getJson().getRoot().getValueByString(this.fileReferences).getValueByString(this.motions)),this._jsonValue.pushBack(this.getJson().getRoot().getValueByString(this.fileReferences).getValueByString(this.expressions)),this._jsonValue.pushBack(this.getJson().getRoot().getValueByString(this.fileReferences).getValueByString(this.textures)),this._jsonValue.pushBack(this.getJson().getRoot().getValueByString(this.fileReferences).getValueByString(this.physics)),this._jsonValue.pushBack(this.getJson().getRoot().getValueByString(this.fileReferences).getValueByString(this.pose)),this._jsonValue.pushBack(this.getJson().getRoot().getValueByString(this.hitAreas)))}}!function(e){e.CubismModelSettingJson=eG,e.FrequestNode=ez}(v||(v={}));class eX{static create(){return new eX}static delete(e){null!=e&&(e=null)}setParameters(e){this._breathParameters=e}getParameters(){return this._breathParameters}updateParameters(e,t){this._currentTime+=t;let i=2*this._currentTime*Math.PI;for(let t=0;t<this._breathParameters.getSize();++t){let s=this._breathParameters.at(t);e.addParameterValueById(s.parameterId,s.offset+s.peak*Math.sin(i/s.cycle),s.weight)}}constructor(){this._currentTime=0}}class eY{constructor(e,t,i,s,r){this.parameterId=void 0==e?null:e,this.offset=void 0==t?0:t,this.peak=void 0==i?0:i,this.cycle=void 0==s?0:s,this.weight=void 0==r?0:r}}!function(e){e.BreathParameterData=eY,e.CubismBreath=eX}(M||(M={}));class eH{static create(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return new eH(e)}static delete(e){null!=e&&(e=null)}setBlinkingInterval(e){this._blinkingIntervalSeconds=e}setBlinkingSetting(e,t,i){this._closingSeconds=e,this._closedSeconds=t,this._openingSeconds=i}setParameterIds(e){this._parameterIds=e}getParameterIds(){return this._parameterIds}updateParameters(e,t){let i;this._userTimeSeconds+=t;let s=0;switch(this._blinkingState){case 2:(s=(this._userTimeSeconds-this._stateStartTimeSeconds)/this._closingSeconds)>=1&&(s=1,this._blinkingState=3,this._stateStartTimeSeconds=this._userTimeSeconds),i=1-s;break;case 3:(s=(this._userTimeSeconds-this._stateStartTimeSeconds)/this._closedSeconds)>=1&&(this._blinkingState=4,this._stateStartTimeSeconds=this._userTimeSeconds),i=0;break;case 4:(s=(this._userTimeSeconds-this._stateStartTimeSeconds)/this._openingSeconds)>=1&&(s=1,this._blinkingState=1,this._nextBlinkingTime=this.determinNextBlinkingTiming()),i=s;break;case 1:this._nextBlinkingTime<this._userTimeSeconds&&(this._blinkingState=2,this._stateStartTimeSeconds=this._userTimeSeconds),i=1;break;default:this._blinkingState=1,this._nextBlinkingTime=this.determinNextBlinkingTiming(),i=1}eH.CloseIfZero||(i=-i);for(let t=0;t<this._parameterIds.getSize();++t)e.setParameterValueById(this._parameterIds.at(t),i)}determinNextBlinkingTiming(){let e=Math.random();return this._userTimeSeconds+e*(2*this._blinkingIntervalSeconds-1)}constructor(e){if(this._blinkingState=0,this._nextBlinkingTime=0,this._stateStartTimeSeconds=0,this._blinkingIntervalSeconds=4,this._closingSeconds=.1,this._closedSeconds=.05,this._openingSeconds=.15,this._userTimeSeconds=0,this._parameterIds=new $,null==e)return;for(let t=0;t<e.getEyeBlinkParameterCount();++t)this._parameterIds.pushBack(e.getEyeBlinkParameterId(t))}}eH.CloseIfZero=!0;var eW=function(e){return e[e.EyeState_First=0]="EyeState_First",e[e.EyeState_Interval=1]="EyeState_Interval",e[e.EyeState_Closing=2]="EyeState_Closing",e[e.EyeState_Closed=3]="EyeState_Closed",e[e.EyeState_Opening=4]="EyeState_Opening",e}({});!function(e){e.CubismEyeBlink=eH,e.EyeState=eW}(P||(P={}));let eq="FadeInTime",eJ="Link";class eZ{static create(e,t){let i=eS.create(e,t);if(!i)return null;let s=new eZ,r=i.getRoot();!r.getValueByString(eq).isNull()&&(s._fadeTimeSeconds=r.getValueByString(eq).toFloat(.5),s._fadeTimeSeconds<0&&(s._fadeTimeSeconds=.5));let a=r.getValueByString("Groups"),n=a.getSize();for(let e=0;e<n;++e){let t=a.getValueByIndex(e),i=t.getSize(),r=0;for(let e=0;e<i;++e){let i=t.getValueByIndex(e),a=new eK,n=eF.getIdManager().getId(i.getValueByString("Id").getRawString());if(a.partId=n,!i.getValueByString(eJ).isNull()){let e=i.getValueByString(eJ),t=e.getSize();for(let i=0;i<t;++i){let t=new eK,s=eF.getIdManager().getId(e.getValueByIndex(i).getString());t.partId=s,a.link.pushBack(t)}}s._partGroups.pushBack(a.clone()),++r}s._partGroupCounts.pushBack(r)}return eS.delete(i),s}static delete(e){null!=e&&(e=null)}updateParameters(e,t){e!=this._lastModel&&this.reset(e),this._lastModel=e,t<0&&(t=0);let i=0;for(let s=0;s<this._partGroupCounts.getSize();s++){let r=this._partGroupCounts.at(s);this.doFade(e,t,i,r),i+=r}this.copyPartOpacities(e)}reset(e){let t=0;for(let i=0;i<this._partGroupCounts.getSize();++i){let s=this._partGroupCounts.at(i);for(let i=t;i<t+s;++i){this._partGroups.at(i).initialize(e);let s=this._partGroups.at(i).partIndex,r=this._partGroups.at(i).parameterIndex;if(!(s<0)){e.setPartOpacityByIndex(s,i==t?1:0),e.setParameterValueByIndex(r,i==t?1:0);for(let t=0;t<this._partGroups.at(i).link.getSize();++t)this._partGroups.at(i).link.at(t).initialize(e)}}t+=s}}copyPartOpacities(e){for(let t=0;t<this._partGroups.getSize();++t){let i=this._partGroups.at(t);if(0==i.link.getSize())continue;let s=this._partGroups.at(t).partIndex,r=e.getPartOpacityByIndex(s);for(let t=0;t<i.link.getSize();++t){let s=i.link.at(t).partIndex;s<0||e.setPartOpacityByIndex(s,r)}}}doFade(e,t,i,s){let r=-1,a=1;for(let n=i;n<i+s;++n){let i=this._partGroups.at(n).partIndex,s=this._partGroups.at(n).parameterIndex;if(e.getParameterValueByIndex(s)>.001){if(r>=0)break;if(r=n,0==this._fadeTimeSeconds){a=1;continue}(a=e.getPartOpacityByIndex(i)+t/this._fadeTimeSeconds)>1&&(a=1)}}r<0&&(r=0,a=1);for(let t=i;t<i+s;++t){let i=this._partGroups.at(t).partIndex;if(r==t)e.setPartOpacityByIndex(i,a);else{let t,s=e.getPartOpacityByIndex(i);(1-(t=a<.5?-.5*a/.5+1:(1-a)*.5/.5))*(1-a)>.15&&(t=1-.15/(1-a)),s>t&&(s=t),e.setPartOpacityByIndex(i,s)}}}constructor(){this._fadeTimeSeconds=.5,this._lastModel=null,this._partGroups=new $,this._partGroupCounts=new $}}class eK{assignment(e){this.partId=e.partId;for(let t=e.link.begin();t.notEqual(e.link.end());t.preIncrement())this.link.pushBack(t.ptr().clone());return this}initialize(e){this.parameterIndex=e.getParameterIndex(this.partId),this.partIndex=e.getPartIndex(this.partId),e.setParameterValueByIndex(this.parameterIndex,1)}clone(){let e=new eK;e.partId=this.partId,e.parameterIndex=this.parameterIndex,e.partIndex=this.partIndex,e.link=new $;for(let t=this.link.begin();t.notEqual(this.link.end());t.increment())e.link.pushBack(t.ptr().clone());return e}constructor(e){if(this.parameterIndex=0,this.partIndex=0,this.link=new $,void 0!=e){this.partId=e.partId;for(let t=e.link.begin();t.notEqual(e.link.end());t.preIncrement())this.link.pushBack(t.ptr().clone())}}}!function(e){e.CubismPose=eZ,e.PartData=eK}(w||(w={}));class eQ extends er{setWidth(e){let t=e/this._width;this.scale(t,t)}setHeight(e){let t=e/this._height;this.scale(t,t)}setPosition(e,t){this.translate(e,t)}setCenterPosition(e,t){this.centerX(e),this.centerY(t)}top(e){this.setY(e)}bottom(e){let t=this._height*this.getScaleY();this.translateY(e-t)}left(e){this.setX(e)}right(e){let t=this._width*this.getScaleX();this.translateX(e-t)}centerX(e){let t=this._width*this.getScaleX();this.translateX(e-t/2)}setX(e){this.translateX(e)}centerY(e){let t=this._height*this.getScaleY();this.translateY(e-t/2)}setY(e){this.translateY(e)}setupFromLayout(e){for(let t=e.begin();t.notEqual(e.end());t.preIncrement()){let e=t.ptr().first,i=t.ptr().second;"width"==e?this.setWidth(i):"height"==e&&this.setHeight(i)}for(let t=e.begin();t.notEqual(e.end());t.preIncrement()){let e=t.ptr().first,i=t.ptr().second;"x"==e?this.setX(i):"y"==e?this.setY(i):"center_x"==e?this.centerX(i):"center_y"==e?this.centerY(i):"top"==e?this.top(i):"bottom"==e?this.bottom(i):"left"==e?this.left(i):"right"==e&&this.right(i)}}constructor(e,t){super(),this._width=void 0!==e?e:0,this._height=void 0!==t?t:0,this.setHeight(2)}}(B||(B={})).CubismModelMatrix=eQ;class e${add(e){let t=new e$(0,0);return t.x=this.x+e.x,t.y=this.y+e.y,t}substract(e){let t=new e$(0,0);return t.x=this.x-e.x,t.y=this.y-e.y,t}multiply(e){let t=new e$(0,0);return t.x=this.x*e.x,t.y=this.y*e.y,t}multiplyByScaler(e){return this.multiply(new e$(e,e))}division(e){let t=new e$(0,0);return t.x=this.x/e.x,t.y=this.y/e.y,t}divisionByScalar(e){return this.division(new e$(e,e))}getLength(){return Math.sqrt(this.x*this.x+this.y*this.y)}getDistanceWith(e){return Math.sqrt((this.x-e.x)*(this.x-e.x)+(this.y-e.y)*(this.y-e.y))}dot(e){return this.x*e.x+this.y*e.y}normalize(){let e=Math.pow(this.x*this.x+this.y*this.y,.5);this.x=this.x/e,this.y=this.y/e}isEqual(e){return this.x==e.x&&this.y==e.y}isNotEqual(e){return!this.isEqual(e)}constructor(e,t){this.x=e,this.y=t,this.x=void 0==e?0:e,this.y=void 0==t?0:t}}(I||(I={})).CubismVector2=e$;class e0{static range(e,t,i){return e<t?e=t:e>i&&(e=i),e}static sin(e){return Math.sin(e)}static cos(e){return Math.cos(e)}static abs(e){return Math.abs(e)}static sqrt(e){return Math.sqrt(e)}static cbrt(e){let t;if(0===e)return e;let i=e,s=i<0;return s&&(i=-i),i===1/0?t=1/0:(t=Math.exp(Math.log(i)/3),t=(i/(t*t)+2*t)/3),s?-t:t}static getEasingSine(e){return e<0?0:e>1?1:.5-.5*this.cos(e*Math.PI)}static max(e,t){return e>t?e:t}static min(e,t){return e>t?t:e}static degreesToRadian(e){return e/180*Math.PI}static radianToDegrees(e){return 180*e/Math.PI}static directionToRadian(e,t){let i=Math.atan2(t.y,t.x)-Math.atan2(e.y,e.x);for(;i<-Math.PI;)i+=2*Math.PI;for(;i>Math.PI;)i-=2*Math.PI;return i}static directionToDegrees(e,t){let i=this.directionToRadian(e,t),s=this.radianToDegrees(i);return t.x-e.x>0&&(s=-s),s}static radianToDirection(e){let t=new e$;return t.x=this.sin(e),t.y=this.cos(e),t}static quadraticEquation(e,t,i){return this.abs(e)<e0.Epsilon?this.abs(t)<e0.Epsilon?-i:-i/t:-(t+this.sqrt(t*t-4*e*i))/(2*e)}static cardanoAlgorithmForBezier(e,t,i,s){if(this.abs(e)<e0.Epsilon)return this.range(this.quadraticEquation(t,i,s),0,1);let r=t/e,a=i/e,n=(3*a-r*r)/3,l=n/3,o=(2*r*r*r-9*r*a+s/e*27)/27,u=o/2,h=u*u+l*l*l;if(h<0){let e=-n/3,t=this.sqrt(e*e*e),i=Math.acos(this.range(-o/(2*t),-1,1)),s=2*this.cbrt(t),a=s*this.cos(i/3)-r/3;if(.51>this.abs(a-.5))return this.range(a,0,1);let l=s*this.cos((i+2*Math.PI)/3)-r/3;if(.51>this.abs(l-.5))return this.range(l,0,1);let u=s*this.cos((i+4*Math.PI)/3)-r/3;return this.range(u,0,1)}if(0==h){let e;let t=2*(e=u<0?this.cbrt(-u):-this.cbrt(u))-r/3;return .51>this.abs(t-.5)?this.range(t,0,1):this.range(-e-r/3,0,1)}let g=this.sqrt(h),d=this.cbrt(g-u),c=this.cbrt(g+u);return this.range(d-c-r/3,0,1)}static mod(e,t){if(!isFinite(e)||0===t||isNaN(e)||isNaN(t))return console.warn("divided: ".concat(e,", divisor: ").concat(t," mod() returns 'NaN'.")),NaN;let i=Math.abs(e),s=Math.abs(t);return(i-Math.floor(i/s)*s)*Math.sign(e)}constructor(){}}e0.Epsilon=1e-5,(T||(T={})).CubismMath=e0;class e1{update(e){if(this._userTimeSeconds+=e,0==this._lastTimeSeconds){this._lastTimeSeconds=this._userTimeSeconds;return}let t=(this._userTimeSeconds-this._lastTimeSeconds)*30;this._lastTimeSeconds=this._userTimeSeconds;let i=.13333333333333333*t/4.5,s=this._faceTargetX-this._faceX,r=this._faceTargetY-this._faceY;if(.01>=e0.abs(s)&&.01>=e0.abs(r))return;let a=e0.sqrt(s*s+r*r),n=.13333333333333333*s/a,l=.13333333333333333*r/a,o=n-this._faceVX,u=l-this._faceVY,h=e0.sqrt(o*o+u*u);(h<-i||h>i)&&(o*=i/h,u*=i/h),this._faceVX+=o,this._faceVY+=u;{let e=.5*(e0.sqrt(i*i+16*i*a-8*i*a)-i),t=e0.sqrt(this._faceVX*this._faceVX+this._faceVY*this._faceVY);t>e&&(this._faceVX*=e/t,this._faceVY*=e/t)}this._faceX+=this._faceVX,this._faceY+=this._faceVY}getX(){return this._faceX}getY(){return this._faceY}set(e,t){this._faceTargetX=e,this._faceTargetY=t}constructor(){this._faceTargetX=0,this._faceTargetY=0,this._faceX=0,this._faceY=0,this._faceVX=0,this._faceVY=0,this._lastTimeSeconds=0,this._userTimeSeconds=0}}(E||(E={})).CubismTargetPoint=e1;class e2{static delete(e){e.release(),e=null}release(){this._weight=0}updateParameters(e,t,i){if(!t.isAvailable()||t.isFinished())return;this.setupMotionQueueEntry(t,i);let s=this.updateFadeWeight(t,i);this.doUpdateParameters(e,i,s,t),t.getEndTime()>0&&t.getEndTime()<i&&t.setIsFinished(!0)}setupMotionQueueEntry(e,t){if(null==e||e.isStarted()||!e.isAvailable())return;e.setIsStarted(!0),e.setStartTime(t-this._offsetSeconds),e.setFadeInStartTime(t);let i=this.getDuration();0>e.getEndTime()&&e.setEndTime(i<=0?-1:e.getStartTime()+i),e._motion._onBeganMotion&&e._motion._onBeganMotion(e._motion)}updateFadeWeight(e,t){null==e&&ec.print(eD.LogLevel_Error,"motionQueueEntry is null.");let i=this._weight;return i=i*(0==this._fadeInSeconds?1:e0.getEasingSine((t-e.getFadeInStartTime())/this._fadeInSeconds))*(0==this._fadeOutSeconds||0>e.getEndTime()?1:e0.getEasingSine((e.getEndTime()-t)/this._fadeOutSeconds)),e.setState(t,i),ed(0<=i&&i<=1),i}setFadeInTime(e){this._fadeInSeconds=e}setFadeOutTime(e){this._fadeOutSeconds=e}getFadeOutTime(){return this._fadeOutSeconds}getFadeInTime(){return this._fadeInSeconds}setWeight(e){this._weight=e}getWeight(){return this._weight}getDuration(){return -1}getLoopDuration(){return -1}setOffsetTime(e){this._offsetSeconds=e}getFiredEvent(e,t){return this._firedEventValues}isExistModelOpacity(){return!1}getModelOpacityIndex(){return -1}getModelOpacityId(e){return null}getModelOpacityValue(){return 1}constructor(){this.setBeganMotionHandler=e=>this._onBeganMotion=e,this.getBeganMotionHandler=()=>this._onBeganMotion,this.setFinishedMotionHandler=e=>this._onFinishedMotion=e,this.getFinishedMotionHandler=()=>this._onFinishedMotion,this._fadeInSeconds=-1,this._fadeOutSeconds=-1,this._weight=1,this._offsetSeconds=0,this._firedEventValues=new $}}(R||(R={})).ACubismMotion=e2;let e3="Parameters",e4="Blend";class e5 extends e2{static create(e,t){let i=new e5;return i.parse(e,t),i}doUpdateParameters(e,t,i,s){for(let t=0;t<this._parameters.getSize();++t){let s=this._parameters.at(t);switch(s.blendType){case 0:e.addParameterValueById(s.parameterId,s.value,i);break;case 1:e.multiplyParameterValueById(s.parameterId,s.value,i);break;case 2:e.setParameterValueById(s.parameterId,s.value,i)}}}calculateExpressionParameters(e,t,i,s,r,a){if(null!=i&&null!=s&&i.isAvailable()){this._fadeWeight=this.updateFadeWeight(i,t);for(let t=0;t<s.getSize();++t){let i,n,l;let o=s.at(t);if(null==o.parameterId)continue;let u=o.overwriteValue=e.getParameterValueById(o.parameterId),h=this.getExpressionParameters(),g=-1;for(let e=0;e<h.getSize();++e)if(o.parameterId==h.at(e).parameterId){g=e;break}if(g<0){0==r?(o.additiveValue=e5.DefaultAdditiveValue,o.multiplyValue=e5.DefaultMultiplyValue,o.overwriteValue=u):(o.additiveValue=this.calculateValue(o.additiveValue,e5.DefaultAdditiveValue,a),o.multiplyValue=this.calculateValue(o.multiplyValue,e5.DefaultMultiplyValue,a),o.overwriteValue=this.calculateValue(o.overwriteValue,u,a));continue}let d=h.at(g).value;switch(h.at(g).blendType){case 0:i=d,n=e5.DefaultMultiplyValue,l=u;break;case 1:i=e5.DefaultAdditiveValue,n=d,l=u;break;case 2:i=e5.DefaultAdditiveValue,n=e5.DefaultMultiplyValue,l=d;break;default:return}0==r?(o.additiveValue=i,o.multiplyValue=n,o.overwriteValue=l):(o.additiveValue=o.additiveValue*(1-a)+i*a,o.multiplyValue=o.multiplyValue*(1-a)+n*a,o.overwriteValue=o.overwriteValue*(1-a)+l*a)}}}getExpressionParameters(){return this._parameters}getFadeWeight(){return this._fadeWeight}parse(e,t){let i=eS.create(e,t);if(!i)return;let s=i.getRoot();this.setFadeInTime(s.getValueByString("FadeInTime").toFloat(1)),this.setFadeOutTime(s.getValueByString("FadeOutTime").toFloat(1));let r=s.getValueByString(e3).getSize();this._parameters.prepareCapacity(r);for(let e=0;e<r;++e){let t;let i=s.getValueByString(e3).getValueByIndex(e),r=eF.getIdManager().getId(i.getValueByString("Id").getRawString()),a=i.getValueByString("Value").toFloat();t=i.getValueByString(e4).isNull()||"Add"==i.getValueByString(e4).getString()?0:"Multiply"==i.getValueByString(e4).getString()?1:"Overwrite"==i.getValueByString(e4).getString()?2:0;let n=new e8;n.parameterId=r,n.blendType=t,n.value=a,this._parameters.pushBack(n)}eS.delete(i)}calculateValue(e,t,i){return e*(1-i)+t*i}constructor(){super(),this._parameters=new $,this._fadeWeight=0}}e5.DefaultAdditiveValue=0,e5.DefaultMultiplyValue=1;var e6=function(e){return e[e.Additive=0]="Additive",e[e.Multiply=1]="Multiply",e[e.Overwrite=2]="Overwrite",e}({});class e8{}!function(e){e.CubismExpressionMotion=e5,e.ExpressionBlendType=e6,e.ExpressionParameter=e8}(V||(V={}));class e9{release(){this._autoDelete&&this._motion&&e2.delete(this._motion)}setFadeOut(e){this._fadeOutSeconds=e,this._isTriggeredFadeOut=!0}startFadeOut(e,t){let i=t+e;this._isTriggeredFadeOut=!0,(this._endTimeSeconds<0||i<this._endTimeSeconds)&&(this._endTimeSeconds=i)}isFinished(){return this._finished}isStarted(){return this._started}getStartTime(){return this._startTimeSeconds}getFadeInStartTime(){return this._fadeInStartTimeSeconds}getEndTime(){return this._endTimeSeconds}setStartTime(e){this._startTimeSeconds=e}setFadeInStartTime(e){this._fadeInStartTimeSeconds=e}setEndTime(e){this._endTimeSeconds=e}setIsFinished(e){this._finished=e}setIsStarted(e){this._started=e}isAvailable(){return this._available}setIsAvailable(e){this._available=e}setState(e,t){this._stateTimeSeconds=e,this._stateWeight=t}getStateTime(){return this._stateTimeSeconds}getStateWeight(){return this._stateWeight}getLastCheckEventSeconds(){return this._lastEventCheckSeconds}setLastCheckEventSeconds(e){this._lastEventCheckSeconds=e}isTriggeredFadeOut(){return this._isTriggeredFadeOut}getFadeOutSeconds(){return this._fadeOutSeconds}getCubismMotion(){return this._motion}constructor(){this._autoDelete=!1,this._motion=null,this._available=!0,this._finished=!1,this._started=!1,this._startTimeSeconds=-1,this._fadeInStartTimeSeconds=0,this._endTimeSeconds=-1,this._stateTimeSeconds=0,this._stateWeight=0,this._lastEventCheckSeconds=0,this._motionQueueEntryHandle=this,this._fadeOutSeconds=0,this._isTriggeredFadeOut=!1}}(A||(A={})).CubismMotionQueueEntry=e9;class e7{release(){for(let e=0;e<this._motions.getSize();++e)this._motions.at(e)&&(this._motions.at(e).release(),this._motions.set(e,null));this._motions=null}startMotion(e,t,i){if(null==e)return te;let s=null;for(let e=0;e<this._motions.getSize();++e)null!=(s=this._motions.at(e))&&s.setFadeOut(s._motion.getFadeOutTime());return(s=new e9)._autoDelete=t,s._motion=e,this._motions.pushBack(s),s._motionQueueEntryHandle}isFinished(){for(let e=this._motions.begin();e.notEqual(this._motions.end());){let t=e.ptr();if(null==t){e=this._motions.erase(e);continue}if(null==t._motion){t.release(),t=null,e=this._motions.erase(e);continue}if(!t.isFinished())return!1;e.preIncrement()}return!0}isFinishedByHandle(e){for(let t=this._motions.begin();t.notEqual(this._motions.end());t.increment()){let i=t.ptr();if(null!=i&&i._motionQueueEntryHandle==e&&!i.isFinished())return!1}return!0}stopAllMotions(){for(let e=this._motions.begin();e.notEqual(this._motions.end());){let t=e.ptr();if(null==t){e=this._motions.erase(e);continue}t.release(),t=null,e=this._motions.erase(e)}}getCubismMotionQueueEntries(){return this._motions}getCubismMotionQueueEntry(e){for(let t=this._motions.begin();t.notEqual(this._motions.end());t.preIncrement()){let i=t.ptr();if(null!=i&&i._motionQueueEntryHandle==e)return i}return null}setEventCallback(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this._eventCallBack=e,this._eventCustomData=t}doUpdateMotion(e,t){let i=!1;for(let s=this._motions.begin();s.notEqual(this._motions.end());){let r=s.ptr();if(null==r){s=this._motions.erase(s);continue}let a=r._motion;if(null==a){r.release(),r=null,s=this._motions.erase(s);continue}a.updateParameters(e,r,t),i=!0;let n=a.getFiredEvent(r.getLastCheckEventSeconds()-r.getStartTime(),t-r.getStartTime());for(let e=0;e<n.getSize();++e)this._eventCallBack(this,n.at(e),this._eventCustomData);r.setLastCheckEventSeconds(t),r.isFinished()?(r.release(),r=null,s=this._motions.erase(s)):(r.isTriggeredFadeOut()&&r.startFadeOut(r.getFadeOutSeconds(),t),s.preIncrement())}return i}constructor(){this._userTimeSeconds=0,this._eventCallBack=null,this._eventCustomData=null,this._motions=new $}}let te=-1;!function(e){e.CubismMotionQueueManager=e7,e.InvalidMotionQueueEntryHandleValue=te}(F||(F={}));class tt{}class ti extends e7{release(){this._expressionParameterValues&&(eA(this._expressionParameterValues),this._expressionParameterValues=null),this._fadeWeights&&(eA(this._fadeWeights),this._fadeWeights=null)}getCurrentPriority(){return r("CubismExpressionMotionManager.getCurrentPriority() is deprecated because a priority value is not actually used during expression motion playback."),this._currentPriority}getReservePriority(){return r("CubismExpressionMotionManager.getReservePriority() is deprecated because a priority value is not actually used during expression motion playback."),this._reservePriority}getFadeWeight(e){return e<0||1>this._fadeWeights.getSize()||e>=this._fadeWeights.getSize()?(console.warn("Failed to get the fade weight value. The element at that index does not exist."),-1):this._fadeWeights.at(e)}setFadeWeight(e,t){if(e<0||1>this._fadeWeights.getSize()||this._fadeWeights.getSize()<=e){console.warn("Failed to set the fade weight value. The element at that index does not exist.");return}this._fadeWeights.set(e,t)}setReservePriority(e){r("CubismExpressionMotionManager.setReservePriority() is deprecated because a priority value is not actually used during expression motion playback."),this._reservePriority=e}startMotionPriority(e,t,i){return r("CubismExpressionMotionManager.startMotionPriority() is deprecated because a priority value is not actually used during expression motion playback."),i==this.getReservePriority()&&this.setReservePriority(0),this._currentPriority=i,this.startMotion(e,t)}updateMotion(e,t){this._userTimeSeconds+=t;let i=!1,s=this.getCubismMotionQueueEntries(),r=0,a=0;if(this._fadeWeights.getSize()!==s.getSize()){let e=s.getSize()-this._fadeWeights.getSize();for(let t=0;t<e;t++)this._fadeWeights.pushBack(0)}for(let t=this._motions.begin();t.notEqual(this._motions.end());){let n=t.ptr();if(null==n){t=s.erase(t);continue}let l=n.getCubismMotion();if(null==l){eA(n),t=s.erase(t);continue}let o=l.getExpressionParameters();if(n.isAvailable())for(let t=0;t<o.getSize();++t){if(null==o.at(t).parameterId)continue;let i=-1;for(let e=0;e<this._expressionParameterValues.getSize();++e)if(this._expressionParameterValues.at(e).parameterId==o.at(t).parameterId){i=e;break}if(i>=0)continue;let s=new tt;s.parameterId=o.at(t).parameterId,s.additiveValue=e5.DefaultAdditiveValue,s.multiplyValue=e5.DefaultMultiplyValue,s.overwriteValue=e.getParameterValueById(s.parameterId),this._expressionParameterValues.pushBack(s)}l.setupMotionQueueEntry(n,this._userTimeSeconds),this.setFadeWeight(a,l.updateFadeWeight(n,this._userTimeSeconds)),l.calculateExpressionParameters(e,this._userTimeSeconds,n,this._expressionParameterValues,a,this.getFadeWeight(a)),r+=0==l.getFadeInTime()?1:e0.getEasingSine((this._userTimeSeconds-n.getFadeInStartTime())/l.getFadeInTime()),i=!0,n.isTriggeredFadeOut()&&n.startFadeOut(n.getFadeOutSeconds(),this._userTimeSeconds),t.preIncrement(),++a}if(s.getSize()>1&&this.getFadeWeight(this._fadeWeights.getSize()-1)>=1)for(let e=s.getSize()-2;e>=0;--e)eA(s.at(e)),s.remove(e),this._fadeWeights.remove(e);r>1&&(r=1);for(let t=0;t<this._expressionParameterValues.getSize();++t){let i=this._expressionParameterValues.at(t);e.setParameterValueById(i.parameterId,(i.overwriteValue+i.additiveValue)*i.multiplyValue,r),i.additiveValue=e5.DefaultAdditiveValue,i.multiplyValue=e5.DefaultMultiplyValue}return i}constructor(){super(),this._currentPriority=0,this._reservePriority=0,this._expressionParameterValues=new $,this._fadeWeights=new $}}(L||(L={})).CubismExpressionMotionManager=ti;var ts=function(e){return e[e.CubismMotionCurveTarget_Model=0]="CubismMotionCurveTarget_Model",e[e.CubismMotionCurveTarget_Parameter=1]="CubismMotionCurveTarget_Parameter",e[e.CubismMotionCurveTarget_PartOpacity=2]="CubismMotionCurveTarget_PartOpacity",e}({}),tr=function(e){return e[e.CubismMotionSegmentType_Linear=0]="CubismMotionSegmentType_Linear",e[e.CubismMotionSegmentType_Bezier=1]="CubismMotionSegmentType_Bezier",e[e.CubismMotionSegmentType_Stepped=2]="CubismMotionSegmentType_Stepped",e[e.CubismMotionSegmentType_InverseStepped=3]="CubismMotionSegmentType_InverseStepped",e}({});class ta{constructor(){this.time=0,this.value=0}}class tn{constructor(){this.evaluate=null,this.basePointIndex=0,this.segmentType=0}}class tl{constructor(){this.type=0,this.segmentCount=0,this.baseSegmentIndex=0,this.fadeInTime=0,this.fadeOutTime=0}}class to{constructor(){this.fireTime=0}}class tu{constructor(){this.duration=0,this.loop=!1,this.curveCount=0,this.eventCount=0,this.fps=0,this.curves=new $,this.segments=new $,this.points=new $,this.events=new $}}!function(e){e.CubismMotionCurve=tl,e.CubismMotionCurveTarget=ts,e.CubismMotionData=tu,e.CubismMotionEvent=to,e.CubismMotionPoint=ta,e.CubismMotionSegment=tn,e.CubismMotionSegmentType=tr}(D||(D={}));let th="Meta",tg="Curves",td="FadeInTime",tc="FadeOutTime",t_="Segments",tm="UserData";class tp{release(){eS.delete(this._json)}getMotionDuration(){return this._json.getRoot().getValueByString(th).getValueByString("Duration").toFloat()}isMotionLoop(){return this._json.getRoot().getValueByString(th).getValueByString("Loop").toBoolean()}hasConsistency(){let e=!0;if(!this._json||!this._json.getRoot())return!1;let t=this._json.getRoot().getValueByString(tg).getVector().getSize(),i=0,s=0;for(let e=0;e<t;++e)for(let t=0;t<this.getMotionCurveSegmentCount(e);){switch(0==t&&(s+=1,t+=2),this.getMotionCurveSegment(e,t)){case tr.CubismMotionSegmentType_Linear:s+=1,t+=3;break;case tr.CubismMotionSegmentType_Bezier:s+=3,t+=7;break;case tr.CubismMotionSegmentType_Stepped:case tr.CubismMotionSegmentType_InverseStepped:s+=1,t+=3;break;default:ed(0)}++i}return t!=this.getMotionCurveCount()&&(a("The number of curves does not match the metadata."),e=!1),i!=this.getMotionTotalSegmentCount()&&(a("The number of segment does not match the metadata."),e=!1),s!=this.getMotionTotalPointCount()&&(a("The number of point does not match the metadata."),e=!1),e}getEvaluationOptionFlag(e){return 0==e&&this._json.getRoot().getValueByString(th).getValueByString("AreBeziersRestricted").toBoolean()}getMotionCurveCount(){return this._json.getRoot().getValueByString(th).getValueByString("CurveCount").toInt()}getMotionFps(){return this._json.getRoot().getValueByString(th).getValueByString("Fps").toFloat()}getMotionTotalSegmentCount(){return this._json.getRoot().getValueByString(th).getValueByString("TotalSegmentCount").toInt()}getMotionTotalPointCount(){return this._json.getRoot().getValueByString(th).getValueByString("TotalPointCount").toInt()}isExistMotionFadeInTime(){return!this._json.getRoot().getValueByString(th).getValueByString(td).isNull()}isExistMotionFadeOutTime(){return!this._json.getRoot().getValueByString(th).getValueByString(tc).isNull()}getMotionFadeInTime(){return this._json.getRoot().getValueByString(th).getValueByString(td).toFloat()}getMotionFadeOutTime(){return this._json.getRoot().getValueByString(th).getValueByString(tc).toFloat()}getMotionCurveTarget(e){return this._json.getRoot().getValueByString(tg).getValueByIndex(e).getValueByString("Target").getRawString()}getMotionCurveId(e){return eF.getIdManager().getId(this._json.getRoot().getValueByString(tg).getValueByIndex(e).getValueByString("Id").getRawString())}isExistMotionCurveFadeInTime(e){return!this._json.getRoot().getValueByString(tg).getValueByIndex(e).getValueByString(td).isNull()}isExistMotionCurveFadeOutTime(e){return!this._json.getRoot().getValueByString(tg).getValueByIndex(e).getValueByString(tc).isNull()}getMotionCurveFadeInTime(e){return this._json.getRoot().getValueByString(tg).getValueByIndex(e).getValueByString(td).toFloat()}getMotionCurveFadeOutTime(e){return this._json.getRoot().getValueByString(tg).getValueByIndex(e).getValueByString(tc).toFloat()}getMotionCurveSegmentCount(e){return this._json.getRoot().getValueByString(tg).getValueByIndex(e).getValueByString(t_).getVector().getSize()}getMotionCurveSegment(e,t){return this._json.getRoot().getValueByString(tg).getValueByIndex(e).getValueByString(t_).getValueByIndex(t).toFloat()}getEventCount(){return this._json.getRoot().getValueByString(th).getValueByString("UserDataCount").toInt()}getTotalEventValueSize(){return this._json.getRoot().getValueByString(th).getValueByString("TotalUserDataSize").toInt()}getEventTime(e){return this._json.getRoot().getValueByString(tm).getValueByIndex(e).getValueByString("Time").toFloat()}getEventValue(e){return new et(this._json.getRoot().getValueByString(tm).getValueByIndex(e).getValueByString("Value").getRawString())}constructor(e,t){this._json=eS.create(e,t)}}var tf=function(e){return e[e.EvaluationOptionFlag_AreBeziersRistricted=0]="EvaluationOptionFlag_AreBeziersRistricted",e}({});(k||(k={})).CubismMotionJson=tp;let ty="Opacity";function tx(e,t,i){let s=new ta;return s.time=e.time+(t.time-e.time)*i,s.value=e.value+(t.value-e.value)*i,s}function tS(e,t){let i=(t-e[0].time)/(e[1].time-e[0].time);return i<0&&(i=0),e[0].value+(e[1].value-e[0].value)*i}function tC(e,t){let i=(t-e[0].time)/(e[3].time-e[0].time);i<0&&(i=0);let s=tx(e[0],e[1],i),r=tx(e[1],e[2],i),a=tx(e[2],e[3],i),n=tx(s,r,i),l=tx(r,a,i);return tx(n,l,i).value}function tb(e,t){let i=e[0].time,s=e[3].time,r=e[1].time,a=e[2].time,n=e0.cardanoAlgorithmForBezier(s-3*a+3*r-i,3*a-6*r+3*i,3*r-3*i,i-t),l=tx(e[0],e[1],n),o=tx(e[1],e[2],n),u=tx(e[2],e[3],n),h=tx(l,o,n),g=tx(o,u,n);return tx(h,g,n).value}function tv(e,t){return e[0].value}function tM(e,t){return e[1].value}function tP(e,t,i){let s=e.curves.at(t),r=-1,a=s.baseSegmentIndex+s.segmentCount,n=0;for(let t=s.baseSegmentIndex;t<a;++t)if(n=e.segments.at(t).basePointIndex+(e.segments.at(t).segmentType==tr.CubismMotionSegmentType_Bezier?3:1),e.points.at(n).time>i){r=t;break}if(-1==r)return e.points.at(n).value;let l=e.segments.at(r);return l.evaluate(e.points.get(l.basePointIndex),i)}class tw extends e2{static create(e,t,i,s){let r=new tw;return r.parse(e,t),r._sourceFrameRate=r._motionData.fps,r._loopDurationSeconds=r._motionData.duration,r._onFinishedMotion=i,r._onBeganMotion=s,r}doUpdateParameters(e,t,i,r){let a,n,l;null==this._modelCurveIdEyeBlink&&(this._modelCurveIdEyeBlink=eF.getIdManager().getId("EyeBlink")),null==this._modelCurveIdLipSync&&(this._modelCurveIdLipSync=eF.getIdManager().getId("LipSync")),null==this._modelCurveIdOpacity&&(this._modelCurveIdOpacity=eF.getIdManager().getId(ty));let o=t-r.getStartTime();o<0&&(o=0);let u=Number.MAX_VALUE,h=Number.MAX_VALUE,g=0,d=0;this._eyeBlinkParameterIds.getSize()>64&&s("too many eye blink targets : {0}",this._eyeBlinkParameterIds.getSize()),this._lipSyncParameterIds.getSize()>64&&s("too many lip sync targets : {0}",this._lipSyncParameterIds.getSize());let c=this._fadeInSeconds<=0?1:e0.getEasingSine((t-r.getFadeInStartTime())/this._fadeInSeconds),_=this._fadeOutSeconds<=0||0>r.getEndTime()?1:e0.getEasingSine((r.getEndTime()-t)/this._fadeOutSeconds),m=o;if(this._isLoop)for(;m>this._motionData.duration;)m-=this._motionData.duration;let p=this._motionData.curves;for(n=0;n<this._motionData.curveCount&&p.at(n).type==ts.CubismMotionCurveTarget_Model;++n)a=tP(this._motionData,n,m),p.at(n).id==this._modelCurveIdEyeBlink?h=a:p.at(n).id==this._modelCurveIdLipSync?u=a:p.at(n).id==this._modelCurveIdOpacity&&(this._modelOpacity=a,e.setModelOapcity(this.getModelOpacityValue()));for(;n<this._motionData.curveCount&&p.at(n).type==ts.CubismMotionCurveTarget_Parameter;++n){let s;if(-1==(l=e.getParameterIndex(p.at(n).id)))continue;let o=e.getParameterValueByIndex(l);if(a=tP(this._motionData,n,m),h!=Number.MAX_VALUE){for(let e=0;e<this._eyeBlinkParameterIds.getSize()&&e<64;++e)if(this._eyeBlinkParameterIds.at(e)==p.at(n).id){a*=h,d|=1<<e;break}}if(u!=Number.MAX_VALUE){for(let e=0;e<this._lipSyncParameterIds.getSize()&&e<64;++e)if(this._lipSyncParameterIds.at(e)==p.at(n).id){a+=u,g|=1<<e;break}}if(p.at(n).fadeInTime<0&&p.at(n).fadeOutTime<0)s=o+(a-o)*i;else{let e,i;e=p.at(n).fadeInTime<0?c:0==p.at(n).fadeInTime?1:e0.getEasingSine((t-r.getFadeInStartTime())/p.at(n).fadeInTime),i=p.at(n).fadeOutTime<0?_:0==p.at(n).fadeOutTime||0>r.getEndTime()?1:e0.getEasingSine((r.getEndTime()-t)/p.at(n).fadeOutTime),s=o+(a-o)*(this._weight*e*i)}e.setParameterValueByIndex(l,s,1)}if(h!=Number.MAX_VALUE)for(let t=0;t<this._eyeBlinkParameterIds.getSize()&&t<64;++t){let s=e.getParameterValueById(this._eyeBlinkParameterIds.at(t));if(d>>t&1)continue;let r=s+(h-s)*i;e.setParameterValueById(this._eyeBlinkParameterIds.at(t),r)}if(u!=Number.MAX_VALUE)for(let t=0;t<this._lipSyncParameterIds.getSize()&&t<64;++t){let s=e.getParameterValueById(this._lipSyncParameterIds.at(t));if(g>>t&1)continue;let r=s+(u-s)*i;e.setParameterValueById(this._lipSyncParameterIds.at(t),r)}for(;n<this._motionData.curveCount&&p.at(n).type==ts.CubismMotionCurveTarget_PartOpacity;++n)-1!=(l=e.getParameterIndex(p.at(n).id))&&(a=tP(this._motionData,n,m),e.setParameterValueByIndex(l,a));o>=this._motionData.duration&&(this._isLoop?(r.setStartTime(t),this._isLoopFadeIn&&r.setFadeInStartTime(t)):(this._onFinishedMotion&&this._onFinishedMotion(this),r.setIsFinished(!0))),this._lastWeight=i}setIsLoop(e){this._isLoop=e}isLoop(){return this._isLoop}setIsLoopFadeIn(e){this._isLoopFadeIn=e}isLoopFadeIn(){return this._isLoopFadeIn}getDuration(){return this._isLoop?-1:this._loopDurationSeconds}getLoopDuration(){return this._loopDurationSeconds}setParameterFadeInTime(e,t){let i=this._motionData.curves;for(let s=0;s<this._motionData.curveCount;++s)if(e==i.at(s).id){i.at(s).fadeInTime=t;return}}setParameterFadeOutTime(e,t){let i=this._motionData.curves;for(let s=0;s<this._motionData.curveCount;++s)if(e==i.at(s).id){i.at(s).fadeOutTime=t;return}}getParameterFadeInTime(e){let t=this._motionData.curves;for(let i=0;i<this._motionData.curveCount;++i)if(e==t.at(i).id)return t.at(i).fadeInTime;return -1}getParameterFadeOutTime(e){let t=this._motionData.curves;for(let i=0;i<this._motionData.curveCount;++i)if(e==t.at(i).id)return t.at(i).fadeOutTime;return -1}setEffectIds(e,t){this._eyeBlinkParameterIds=e,this._lipSyncParameterIds=t}release(){this._motionData=void 0,this._motionData=null}parse(e,t){this._motionData=new tu;let i=new tp(e,t);if(!i){i.release(),i=void 0;return}this._debugMode&&i.hasConsistency(),this._motionData.duration=i.getMotionDuration(),this._motionData.loop=i.isMotionLoop(),this._motionData.curveCount=i.getMotionCurveCount(),this._motionData.fps=i.getMotionFps(),this._motionData.eventCount=i.getEventCount();let s=i.getEvaluationOptionFlag(tf.EvaluationOptionFlag_AreBeziersRistricted);i.isExistMotionFadeInTime()?this._fadeInSeconds=0>i.getMotionFadeInTime()?1:i.getMotionFadeInTime():this._fadeInSeconds=1,i.isExistMotionFadeOutTime()?this._fadeOutSeconds=0>i.getMotionFadeOutTime()?1:i.getMotionFadeOutTime():this._fadeOutSeconds=1,this._motionData.curves.updateSize(this._motionData.curveCount,tl,!0),this._motionData.segments.updateSize(i.getMotionTotalSegmentCount(),tn,!0),this._motionData.points.updateSize(i.getMotionTotalPointCount(),ta,!0),this._motionData.events.updateSize(this._motionData.eventCount,to,!0);let r=0,n=0;for(let e=0;e<this._motionData.curveCount;++e){"Model"==i.getMotionCurveTarget(e)?this._motionData.curves.at(e).type=ts.CubismMotionCurveTarget_Model:"Parameter"==i.getMotionCurveTarget(e)?this._motionData.curves.at(e).type=ts.CubismMotionCurveTarget_Parameter:"PartOpacity"==i.getMotionCurveTarget(e)?this._motionData.curves.at(e).type=ts.CubismMotionCurveTarget_PartOpacity:a('Warning : Unable to get segment type from Curve! The number of "CurveCount" may be incorrect!'),this._motionData.curves.at(e).id=i.getMotionCurveId(e),this._motionData.curves.at(e).baseSegmentIndex=n,this._motionData.curves.at(e).fadeInTime=i.isExistMotionCurveFadeInTime(e)?i.getMotionCurveFadeInTime(e):-1,this._motionData.curves.at(e).fadeOutTime=i.isExistMotionCurveFadeOutTime(e)?i.getMotionCurveFadeOutTime(e):-1;for(let t=0;t<i.getMotionCurveSegmentCount(e);){switch(0==t?(this._motionData.segments.at(n).basePointIndex=r,this._motionData.points.at(r).time=i.getMotionCurveSegment(e,t),this._motionData.points.at(r).value=i.getMotionCurveSegment(e,t+1),r+=1,t+=2):this._motionData.segments.at(n).basePointIndex=r-1,i.getMotionCurveSegment(e,t)){case tr.CubismMotionSegmentType_Linear:this._motionData.segments.at(n).segmentType=tr.CubismMotionSegmentType_Linear,this._motionData.segments.at(n).evaluate=tS,this._motionData.points.at(r).time=i.getMotionCurveSegment(e,t+1),this._motionData.points.at(r).value=i.getMotionCurveSegment(e,t+2),r+=1,t+=3;break;case tr.CubismMotionSegmentType_Bezier:this._motionData.segments.at(n).segmentType=tr.CubismMotionSegmentType_Bezier,s?this._motionData.segments.at(n).evaluate=tC:this._motionData.segments.at(n).evaluate=tb,this._motionData.points.at(r).time=i.getMotionCurveSegment(e,t+1),this._motionData.points.at(r).value=i.getMotionCurveSegment(e,t+2),this._motionData.points.at(r+1).time=i.getMotionCurveSegment(e,t+3),this._motionData.points.at(r+1).value=i.getMotionCurveSegment(e,t+4),this._motionData.points.at(r+2).time=i.getMotionCurveSegment(e,t+5),this._motionData.points.at(r+2).value=i.getMotionCurveSegment(e,t+6),r+=3,t+=7;break;case tr.CubismMotionSegmentType_Stepped:this._motionData.segments.at(n).segmentType=tr.CubismMotionSegmentType_Stepped,this._motionData.segments.at(n).evaluate=tv,this._motionData.points.at(r).time=i.getMotionCurveSegment(e,t+1),this._motionData.points.at(r).value=i.getMotionCurveSegment(e,t+2),r+=1,t+=3;break;case tr.CubismMotionSegmentType_InverseStepped:this._motionData.segments.at(n).segmentType=tr.CubismMotionSegmentType_InverseStepped,this._motionData.segments.at(n).evaluate=tM,this._motionData.points.at(r).time=i.getMotionCurveSegment(e,t+1),this._motionData.points.at(r).value=i.getMotionCurveSegment(e,t+2),r+=1,t+=3;break;default:ed(0)}++this._motionData.curves.at(e).segmentCount,++n}}for(let e=0;e<i.getEventCount();++e)this._motionData.events.at(e).fireTime=i.getEventTime(e),this._motionData.events.at(e).value=i.getEventValue(e);i.release()}getFiredEvent(e,t){this._firedEventValues.updateSize(0);for(let i=0;i<this._motionData.eventCount;++i)this._motionData.events.at(i).fireTime>e&&this._motionData.events.at(i).fireTime<=t&&this._firedEventValues.pushBack(new et(this._motionData.events.at(i).value.s));return this._firedEventValues}isExistModelOpacity(){for(let e=0;e<this._motionData.curveCount;e++){let t=this._motionData.curves.at(e);if(t.type==ts.CubismMotionCurveTarget_Model&&0==t.id.getString().s.localeCompare(ty))return!0}return!1}getModelOpacityIndex(){if(this.isExistModelOpacity())for(let e=0;e<this._motionData.curveCount;e++){let t=this._motionData.curves.at(e);if(t.type==ts.CubismMotionCurveTarget_Model&&0==t.id.getString().s.localeCompare(ty))return e}return -1}getModelOpacityId(e){if(-1!=e){let t=this._motionData.curves.at(e);if(t.type==ts.CubismMotionCurveTarget_Model&&0==t.id.getString().s.localeCompare(ty))return eF.getIdManager().getId(t.id.getString().s)}return null}getModelOpacityValue(){return this._modelOpacity}setDebugMode(e){this._debugMode=e}constructor(){super(),this._sourceFrameRate=30,this._loopDurationSeconds=-1,this._isLoop=!1,this._isLoopFadeIn=!0,this._lastWeight=0,this._motionData=null,this._modelCurveIdEyeBlink=null,this._modelCurveIdLipSync=null,this._modelCurveIdOpacity=null,this._eyeBlinkParameterIds=null,this._lipSyncParameterIds=null,this._modelOpacity=1,this._debugMode=!1}}(N||(N={})).CubismMotion=tw;class tB extends e7{getCurrentPriority(){return this._currentPriority}getReservePriority(){return this._reservePriority}setReservePriority(e){this._reservePriority=e}startMotionPriority(e,t,i){return i==this._reservePriority&&(this._reservePriority=0),this._currentPriority=i,super.startMotion(e,t)}updateMotion(e,t){this._userTimeSeconds+=t;let i=super.doUpdateMotion(e,this._userTimeSeconds);return this.isFinished()&&(this._currentPriority=0),i}reserveMotion(e){return!(e<=this._reservePriority)&&!(e<=this._currentPriority)&&(this._reservePriority=e,!0)}constructor(){super(),this._currentPriority=0,this._reservePriority=0}}(O||(O={})).CubismMotionManager=tB;var tI=function(e){return e[e.CubismPhysicsTargetType_Parameter=0]="CubismPhysicsTargetType_Parameter",e}({}),tT=function(e){return e[e.CubismPhysicsSource_X=0]="CubismPhysicsSource_X",e[e.CubismPhysicsSource_Y=1]="CubismPhysicsSource_Y",e[e.CubismPhysicsSource_Angle=2]="CubismPhysicsSource_Angle",e}({});class tE{constructor(){this.gravity=new e$(0,0),this.wind=new e$(0,0)}}class tR{}class tV{}class tA{constructor(){this.initialPosition=new e$(0,0),this.position=new e$(0,0),this.lastPosition=new e$(0,0),this.lastGravity=new e$(0,0),this.force=new e$(0,0),this.velocity=new e$(0,0)}}class tF{constructor(){this.normalizationPosition=new tV,this.normalizationAngle=new tV}}class tL{constructor(){this.source=new tR}}class tD{constructor(){this.destination=new tR,this.translationScale=new e$(0,0)}}class tk{constructor(){this.settings=new $,this.inputs=new $,this.outputs=new $,this.particles=new $,this.gravity=new e$(0,0),this.wind=new e$(0,0),this.fps=0}}!function(e){e.CubismPhysicsInput=tL,e.CubismPhysicsNormalization=tV,e.CubismPhysicsOutput=tD,e.CubismPhysicsParameter=tR,e.CubismPhysicsParticle=tA,e.CubismPhysicsRig=tk,e.CubismPhysicsSource=tT,e.CubismPhysicsSubRig=tF,e.CubismPhysicsTargetType=tI,e.PhysicsJsonEffectiveForces=tE}(j||(j={}));let tN="Position",tO="Angle",tj="Type",tU="Meta",tz="EffectiveForces",tG="Gravity",tX="Wind",tY="PhysicsSettings",tH="Normalization",tW="Minimum",tq="Maximum",tJ="Default",tZ="Reflect",tK="Weight",tQ="Input",t$="Output",t0="Vertices";class t1{release(){eS.delete(this._json)}getGravity(){let e=new e$(0,0);return e.x=this._json.getRoot().getValueByString(tU).getValueByString(tz).getValueByString(tG).getValueByString("X").toFloat(),e.y=this._json.getRoot().getValueByString(tU).getValueByString(tz).getValueByString(tG).getValueByString("Y").toFloat(),e}getWind(){let e=new e$(0,0);return e.x=this._json.getRoot().getValueByString(tU).getValueByString(tz).getValueByString(tX).getValueByString("X").toFloat(),e.y=this._json.getRoot().getValueByString(tU).getValueByString(tz).getValueByString(tX).getValueByString("Y").toFloat(),e}getFps(){return this._json.getRoot().getValueByString(tU).getValueByString("Fps").toFloat(0)}getSubRigCount(){return this._json.getRoot().getValueByString(tU).getValueByString("PhysicsSettingCount").toInt()}getTotalInputCount(){return this._json.getRoot().getValueByString(tU).getValueByString("TotalInputCount").toInt()}getTotalOutputCount(){return this._json.getRoot().getValueByString(tU).getValueByString("TotalOutputCount").toInt()}getVertexCount(){return this._json.getRoot().getValueByString(tU).getValueByString("VertexCount").toInt()}getNormalizationPositionMinimumValue(e){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(tH).getValueByString(tN).getValueByString(tW).toFloat()}getNormalizationPositionMaximumValue(e){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(tH).getValueByString(tN).getValueByString(tq).toFloat()}getNormalizationPositionDefaultValue(e){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(tH).getValueByString(tN).getValueByString(tJ).toFloat()}getNormalizationAngleMinimumValue(e){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(tH).getValueByString(tO).getValueByString(tW).toFloat()}getNormalizationAngleMaximumValue(e){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(tH).getValueByString(tO).getValueByString(tq).toFloat()}getNormalizationAngleDefaultValue(e){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(tH).getValueByString(tO).getValueByString(tJ).toFloat()}getInputCount(e){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(tQ).getVector().getSize()}getInputWeight(e,t){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(tQ).getValueByIndex(t).getValueByString(tK).toFloat()}getInputReflect(e,t){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(tQ).getValueByIndex(t).getValueByString(tZ).toBoolean()}getInputType(e,t){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(tQ).getValueByIndex(t).getValueByString(tj).getRawString()}getInputSourceId(e,t){return eF.getIdManager().getId(this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(tQ).getValueByIndex(t).getValueByString("Source").getValueByString("Id").getRawString())}getOutputCount(e){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(t$).getVector().getSize()}getOutputVertexIndex(e,t){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(t$).getValueByIndex(t).getValueByString("VertexIndex").toInt()}getOutputAngleScale(e,t){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(t$).getValueByIndex(t).getValueByString("Scale").toFloat()}getOutputWeight(e,t){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(t$).getValueByIndex(t).getValueByString(tK).toFloat()}getOutputDestinationId(e,t){return eF.getIdManager().getId(this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(t$).getValueByIndex(t).getValueByString("Destination").getValueByString("Id").getRawString())}getOutputType(e,t){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(t$).getValueByIndex(t).getValueByString(tj).getRawString()}getOutputReflect(e,t){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(t$).getValueByIndex(t).getValueByString(tZ).toBoolean()}getParticleCount(e){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(t0).getVector().getSize()}getParticleMobility(e,t){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(t0).getValueByIndex(t).getValueByString("Mobility").toFloat()}getParticleDelay(e,t){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(t0).getValueByIndex(t).getValueByString("Delay").toFloat()}getParticleAcceleration(e,t){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(t0).getValueByIndex(t).getValueByString("Acceleration").toFloat()}getParticleRadius(e,t){return this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(t0).getValueByIndex(t).getValueByString("Radius").toFloat()}getParticlePosition(e,t){let i=new e$(0,0);return i.x=this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(t0).getValueByIndex(t).getValueByString(tN).getValueByString("X").toFloat(),i.y=this._json.getRoot().getValueByString(tY).getValueByIndex(e).getValueByString(t0).getValueByIndex(t).getValueByString(tN).getValueByString("Y").toFloat(),i}constructor(e,t){this._json=eS.create(e,t)}}(U||(U={})).CubismPhysicsJson=t1;let t2="Angle";class t3{static create(e,t){let i=new t3;return i.parse(e,t),i._physicsRig.gravity.y=0,i}static delete(e){null!=e&&(e.release(),e=null)}parse(e,t){this._physicsRig=new tk;let i=new t1(e,t);this._physicsRig.gravity=i.getGravity(),this._physicsRig.wind=i.getWind(),this._physicsRig.subRigCount=i.getSubRigCount(),this._physicsRig.fps=i.getFps(),this._physicsRig.settings.updateSize(this._physicsRig.subRigCount,tF,!0),this._physicsRig.inputs.updateSize(i.getTotalInputCount(),tL,!0),this._physicsRig.outputs.updateSize(i.getTotalOutputCount(),tD,!0),this._physicsRig.particles.updateSize(i.getVertexCount(),tA,!0),this._currentRigOutputs.clear(),this._previousRigOutputs.clear();let s=0,r=0,a=0;for(let e=0;e<this._physicsRig.settings.getSize();++e){this._physicsRig.settings.at(e).normalizationPosition.minimum=i.getNormalizationPositionMinimumValue(e),this._physicsRig.settings.at(e).normalizationPosition.maximum=i.getNormalizationPositionMaximumValue(e),this._physicsRig.settings.at(e).normalizationPosition.defalut=i.getNormalizationPositionDefaultValue(e),this._physicsRig.settings.at(e).normalizationAngle.minimum=i.getNormalizationAngleMinimumValue(e),this._physicsRig.settings.at(e).normalizationAngle.maximum=i.getNormalizationAngleMaximumValue(e),this._physicsRig.settings.at(e).normalizationAngle.defalut=i.getNormalizationAngleDefaultValue(e),this._physicsRig.settings.at(e).inputCount=i.getInputCount(e),this._physicsRig.settings.at(e).baseInputIndex=s;for(let t=0;t<this._physicsRig.settings.at(e).inputCount;++t)this._physicsRig.inputs.at(s+t).sourceParameterIndex=-1,this._physicsRig.inputs.at(s+t).weight=i.getInputWeight(e,t),this._physicsRig.inputs.at(s+t).reflect=i.getInputReflect(e,t),"X"==i.getInputType(e,t)?(this._physicsRig.inputs.at(s+t).type=tT.CubismPhysicsSource_X,this._physicsRig.inputs.at(s+t).getNormalizedParameterValue=t6):"Y"==i.getInputType(e,t)?(this._physicsRig.inputs.at(s+t).type=tT.CubismPhysicsSource_Y,this._physicsRig.inputs.at(s+t).getNormalizedParameterValue=t8):i.getInputType(e,t)==t2&&(this._physicsRig.inputs.at(s+t).type=tT.CubismPhysicsSource_Angle,this._physicsRig.inputs.at(s+t).getNormalizedParameterValue=t9),this._physicsRig.inputs.at(s+t).source.targetType=tI.CubismPhysicsTargetType_Parameter,this._physicsRig.inputs.at(s+t).source.id=i.getInputSourceId(e,t);s+=this._physicsRig.settings.at(e).inputCount,this._physicsRig.settings.at(e).outputCount=i.getOutputCount(e),this._physicsRig.settings.at(e).baseOutputIndex=r;let t=new t5;t.outputs.resize(this._physicsRig.settings.at(e).outputCount);let n=new t5;n.outputs.resize(this._physicsRig.settings.at(e).outputCount);for(let s=0;s<this._physicsRig.settings.at(e).outputCount;++s)t.outputs.set(s,0),n.outputs.set(s,0),this._physicsRig.outputs.at(r+s).destinationParameterIndex=-1,this._physicsRig.outputs.at(r+s).vertexIndex=i.getOutputVertexIndex(e,s),this._physicsRig.outputs.at(r+s).angleScale=i.getOutputAngleScale(e,s),this._physicsRig.outputs.at(r+s).weight=i.getOutputWeight(e,s),this._physicsRig.outputs.at(r+s).destination.targetType=tI.CubismPhysicsTargetType_Parameter,this._physicsRig.outputs.at(r+s).destination.id=i.getOutputDestinationId(e,s),"X"==i.getOutputType(e,s)?(this._physicsRig.outputs.at(r+s).type=tT.CubismPhysicsSource_X,this._physicsRig.outputs.at(r+s).getValue=t7,this._physicsRig.outputs.at(r+s).getScale=ii):"Y"==i.getOutputType(e,s)?(this._physicsRig.outputs.at(r+s).type=tT.CubismPhysicsSource_Y,this._physicsRig.outputs.at(r+s).getValue=ie,this._physicsRig.outputs.at(r+s).getScale=is):i.getOutputType(e,s)==t2&&(this._physicsRig.outputs.at(r+s).type=tT.CubismPhysicsSource_Angle,this._physicsRig.outputs.at(r+s).getValue=it,this._physicsRig.outputs.at(r+s).getScale=ir),this._physicsRig.outputs.at(r+s).reflect=i.getOutputReflect(e,s);this._currentRigOutputs.pushBack(t),this._previousRigOutputs.pushBack(n),r+=this._physicsRig.settings.at(e).outputCount,this._physicsRig.settings.at(e).particleCount=i.getParticleCount(e),this._physicsRig.settings.at(e).baseParticleIndex=a;for(let t=0;t<this._physicsRig.settings.at(e).particleCount;++t)this._physicsRig.particles.at(a+t).mobility=i.getParticleMobility(e,t),this._physicsRig.particles.at(a+t).delay=i.getParticleDelay(e,t),this._physicsRig.particles.at(a+t).acceleration=i.getParticleAcceleration(e,t),this._physicsRig.particles.at(a+t).radius=i.getParticleRadius(e,t),this._physicsRig.particles.at(a+t).position=i.getParticlePosition(e,t);a+=this._physicsRig.settings.at(e).particleCount}this.initialize(),i.release()}stabilization(e){var t,i,s,r;let a,n,l,o,u,h,g,d;let c=new e$,_=e.getModel().parameters.values,m=e.getModel().parameters.maximumValues,p=e.getModel().parameters.minimumValues,f=e.getModel().parameters.defaultValues;(null!==(s=null===(t=this._parameterCaches)||void 0===t?void 0:t.length)&&void 0!==s?s:0)<e.getParameterCount()&&(this._parameterCaches=new Float32Array(e.getParameterCount())),(null!==(r=null===(i=this._parameterInputCaches)||void 0===i?void 0:i.length)&&void 0!==r?r:0)<e.getParameterCount()&&(this._parameterInputCaches=new Float32Array(e.getParameterCount()));for(let t=0;t<e.getParameterCount();++t)this._parameterCaches[t]=_[t],this._parameterInputCaches[t]=_[t];for(let t=0;t<this._physicsRig.subRigCount;++t){a={angle:0},c.x=0,c.y=0,u=this._physicsRig.settings.at(t),h=this._physicsRig.inputs.get(u.baseInputIndex),g=this._physicsRig.outputs.get(u.baseOutputIndex),d=this._physicsRig.particles.get(u.baseParticleIndex);for(let t=0;t<u.inputCount;++t)n=h[t].weight/100,-1==h[t].sourceParameterIndex&&(h[t].sourceParameterIndex=e.getParameterIndex(h[t].source.id)),h[t].getNormalizedParameterValue(c,a,_[h[t].sourceParameterIndex],p[h[t].sourceParameterIndex],m[h[t].sourceParameterIndex],f[h[t].sourceParameterIndex],u.normalizationPosition,u.normalizationAngle,h[t].reflect,n),this._parameterCaches[h[t].sourceParameterIndex]=_[h[t].sourceParameterIndex];l=e0.degreesToRadian(-a.angle),c.x=c.x*e0.cos(l)-c.y*e0.sin(l),c.y=c.x*e0.sin(l)+c.y*e0.cos(l),function(e,t,i,s,r,a){let n=new e$(0,0);e[0].position=new e$(i.x,i.y);let l=e0.degreesToRadian(s),o=e0.radianToDirection(l);o.normalize();for(let i=1;i<t;++i)e[i].force=o.multiplyByScaler(e[i].acceleration).add(r),e[i].lastPosition=new e$(e[i].position.x,e[i].position.y),e[i].velocity=new e$(0,0),(n=e[i].force).normalize(),n=n.multiplyByScaler(e[i].radius),e[i].position=e[i-1].position.add(n),e0.abs(e[i].position.x)<a&&(e[i].position.x=0),e[i].force=new e$(0,0),e[i].lastGravity=new e$(o.x,o.y)}(d,u.particleCount,c,a.angle,this._options.wind,.001*u.normalizationPosition.maximum);for(let i=0;i<u.outputCount;++i){let s=g[i].vertexIndex;if(-1==g[i].destinationParameterIndex&&(g[i].destinationParameterIndex=e.getParameterIndex(g[i].destination.id)),s<1||s>=u.particleCount)continue;let r=new e$;r=d[s].position.substract(d[s-1].position),o=g[i].getValue(r,d,s,g[i].reflect,this._options.gravity),this._currentRigOutputs.at(t).outputs.set(i,o),this._previousRigOutputs.at(t).outputs.set(i,o);let a=g[i].destinationParameterIndex,n=!Float32Array.prototype.slice&&"subarray"in Float32Array.prototype?JSON.parse(JSON.stringify(_.subarray(a))):_.slice(a);ia(n,p[a],m[a],o,g[i]);for(let e=a,t=0;e<this._parameterCaches.length;e++,t++)_[e]=this._parameterCaches[e]=n[t]}}}evaluate(e,t){var i,s,r,a;let n,l,o,u,h,g,d,c,_;let m=new e$;if(0>=t)return;let p=e.getModel().parameters.values,f=e.getModel().parameters.maximumValues,y=e.getModel().parameters.minimumValues,x=e.getModel().parameters.defaultValues;if(this._currentRemainTime+=t,this._currentRemainTime>5&&(this._currentRemainTime=0),(null!==(r=null===(i=this._parameterCaches)||void 0===i?void 0:i.length)&&void 0!==r?r:0)<e.getParameterCount()&&(this._parameterCaches=new Float32Array(e.getParameterCount())),(null!==(a=null===(s=this._parameterInputCaches)||void 0===s?void 0:s.length)&&void 0!==a?a:0)<e.getParameterCount()){this._parameterInputCaches=new Float32Array(e.getParameterCount());for(let t=0;t<e.getParameterCount();++t)this._parameterInputCaches[t]=p[t]}for(_=this._physicsRig.fps>0?1/this._physicsRig.fps:t;this._currentRemainTime>=_;){for(let e=0;e<this._physicsRig.subRigCount;++e){h=this._physicsRig.settings.at(e),d=this._physicsRig.outputs.get(h.baseOutputIndex);for(let t=0;t<h.outputCount;++t)this._previousRigOutputs.at(e).outputs.set(t,this._currentRigOutputs.at(e).outputs.at(t))}let t=_/this._currentRemainTime;for(let i=0;i<e.getParameterCount();++i)this._parameterCaches[i]=this._parameterInputCaches[i]*(1-t)+p[i]*t,this._parameterInputCaches[i]=this._parameterCaches[i];for(let t=0;t<this._physicsRig.subRigCount;++t){n={angle:0},m.x=0,m.y=0,h=this._physicsRig.settings.at(t),g=this._physicsRig.inputs.get(h.baseInputIndex),d=this._physicsRig.outputs.get(h.baseOutputIndex),c=this._physicsRig.particles.get(h.baseParticleIndex);for(let t=0;t<h.inputCount;++t)l=g[t].weight/100,-1==g[t].sourceParameterIndex&&(g[t].sourceParameterIndex=e.getParameterIndex(g[t].source.id)),g[t].getNormalizedParameterValue(m,n,this._parameterCaches[g[t].sourceParameterIndex],y[g[t].sourceParameterIndex],f[g[t].sourceParameterIndex],x[g[t].sourceParameterIndex],h.normalizationPosition,h.normalizationAngle,g[t].reflect,l);o=e0.degreesToRadian(-n.angle),m.x=m.x*e0.cos(o)-m.y*e0.sin(o),m.y=m.x*e0.sin(o)+m.y*e0.cos(o),function(e,t,i,s,r,a,n,l){let o,u;let h=new e$(0,0),g=new e$(0,0),d=new e$(0,0),c=new e$(0,0);e[0].position=new e$(i.x,i.y);let _=e0.degreesToRadian(s),m=e0.radianToDirection(_);m.normalize();for(let i=1;i<t;++i)e[i].force=m.multiplyByScaler(e[i].acceleration).add(r),e[i].lastPosition=new e$(e[i].position.x,e[i].position.y),o=e[i].delay*n*30,h=e[i].position.substract(e[i-1].position),u=e0.directionToRadian(e[i].lastGravity,m)/5,h.x=e0.cos(u)*h.x-h.y*e0.sin(u),h.y=e0.sin(u)*h.x+h.y*e0.cos(u),e[i].position=e[i-1].position.add(h),g=e[i].velocity.multiplyByScaler(o),d=e[i].force.multiplyByScaler(o).multiplyByScaler(o),e[i].position=e[i].position.add(g).add(d),(c=e[i].position.substract(e[i-1].position)).normalize(),e[i].position=e[i-1].position.add(c.multiplyByScaler(e[i].radius)),e0.abs(e[i].position.x)<a&&(e[i].position.x=0),0!=o&&(e[i].velocity=e[i].position.substract(e[i].lastPosition),e[i].velocity=e[i].velocity.divisionByScalar(o),e[i].velocity=e[i].velocity.multiplyByScaler(e[i].mobility)),e[i].force=new e$(0,0),e[i].lastGravity=new e$(m.x,m.y)}(c,h.particleCount,m,n.angle,this._options.wind,.001*h.normalizationPosition.maximum,_,0);for(let i=0;i<h.outputCount;++i){let s=d[i].vertexIndex;if(-1==d[i].destinationParameterIndex&&(d[i].destinationParameterIndex=e.getParameterIndex(d[i].destination.id)),s<1||s>=h.particleCount)continue;let r=new e$;r.x=c[s].position.x-c[s-1].position.x,r.y=c[s].position.y-c[s-1].position.y,u=d[i].getValue(r,c,s,d[i].reflect,this._options.gravity),this._currentRigOutputs.at(t).outputs.set(i,u);let a=d[i].destinationParameterIndex,n=!Float32Array.prototype.slice&&"subarray"in Float32Array.prototype?JSON.parse(JSON.stringify(this._parameterCaches.subarray(a))):this._parameterCaches.slice(a);ia(n,y[a],f[a],u,d[i]);for(let e=a,t=0;e<this._parameterCaches.length;e++,t++)this._parameterCaches[e]=n[t]}}this._currentRemainTime-=_}let S=this._currentRemainTime/_;this.interpolate(e,S)}interpolate(e,t){let i,s;let r=e.getModel().parameters.values,a=e.getModel().parameters.maximumValues,n=e.getModel().parameters.minimumValues;for(let e=0;e<this._physicsRig.subRigCount;++e){s=this._physicsRig.settings.at(e),i=this._physicsRig.outputs.get(s.baseOutputIndex);for(let l=0;l<s.outputCount;++l){if(-1==i[l].destinationParameterIndex)continue;let s=i[l].destinationParameterIndex,o=!Float32Array.prototype.slice&&"subarray"in Float32Array.prototype?JSON.parse(JSON.stringify(r.subarray(s))):r.slice(s);ia(o,n[s],a[s],this._previousRigOutputs.at(e).outputs.at(l)*(1-t)+this._currentRigOutputs.at(e).outputs.at(l)*t,i[l]);for(let e=s,t=0;e<r.length;e++,t++)r[e]=o[t]}}}setOptions(e){this._options=e}getOption(){return this._options}release(){this._physicsRig=void 0,this._physicsRig=null}initialize(){let e,t,i;for(let s=0;s<this._physicsRig.subRigCount;++s){t=this._physicsRig.settings.at(s),(e=this._physicsRig.particles.get(t.baseParticleIndex))[0].initialPosition=new e$(0,0),e[0].lastPosition=new e$(e[0].initialPosition.x,e[0].initialPosition.y),e[0].lastGravity=new e$(0,-1),e[0].lastGravity.y*=-1,e[0].velocity=new e$(0,0),e[0].force=new e$(0,0);for(let s=1;s<t.particleCount;++s)(i=new e$(0,0)).y=e[s].radius,e[s].initialPosition=new e$(e[s-1].initialPosition.x+i.x,e[s-1].initialPosition.y+i.y),e[s].position=new e$(e[s].initialPosition.x,e[s].initialPosition.y),e[s].lastPosition=new e$(e[s].initialPosition.x,e[s].initialPosition.y),e[s].lastGravity=new e$(0,-1),e[s].lastGravity.y*=-1,e[s].velocity=new e$(0,0),e[s].force=new e$(0,0)}}constructor(){this._physicsRig=null,this._options=new t4,this._options.gravity.y=-1,this._options.gravity.x=0,this._options.wind.x=0,this._options.wind.y=0,this._currentRigOutputs=new $,this._previousRigOutputs=new $,this._currentRemainTime=0,this._parameterCaches=null,this._parameterInputCaches=null}}class t4{constructor(){this.gravity=new e$(0,0),this.wind=new e$(0,0)}}class t5{constructor(){this.outputs=new $(0)}}function t6(e,t,i,s,r,a,n,l,o,u){e.x+=il(i,s,r,a,n.minimum,n.maximum,n.defalut,o)*u}function t8(e,t,i,s,r,a,n,l,o,u){e.y+=il(i,s,r,a,n.minimum,n.maximum,n.defalut,o)*u}function t9(e,t,i,s,r,a,n,l,o,u){t.angle+=il(i,s,r,a,l.minimum,l.maximum,l.defalut,o)*u}function t7(e,t,i,s,r){let a=e.x;return s&&(a*=-1),a}function ie(e,t,i,s,r){let a=e.y;return s&&(a*=-1),a}function it(e,t,i,s,r){let a;return r=i>=2?t[i-1].position.substract(t[i-2].position):r.multiplyByScaler(-1),a=e0.directionToRadian(r,e),s&&(a*=-1),a}function ii(e,t){return JSON.parse(JSON.stringify(e.x))}function is(e,t){return JSON.parse(JSON.stringify(e.y))}function ir(e,t){return JSON.parse(JSON.stringify(t))}function ia(e,t,i,s,r){let a;(a=s*r.getScale(r.translationScale,r.angleScale))<t?(a<r.valueBelowMinimum&&(r.valueBelowMinimum=a),a=t):a>i&&(a>r.valueExceededMaximum&&(r.valueExceededMaximum=a),a=i);let n=r.weight/100;n>=1||(a=e[0]*(1-n)+a*n),e[0]=a}function il(e,t,i,s,r,a,n,l){let o,u=0,h=e0.max(i,t);h<e&&(e=h);let g=e0.min(i,t);g>e&&(e=g);let d=e0.min(r,a),c=e0.max(r,a),_=e0.min(g,h)+function(e,t){let i=e0.max(e,t),s=e0.min(e,t);return e0.abs(i-s)}(g,h)/2,m=e-_;switch(o=0,m>0?o=1:m<0&&(o=-1),o){case 1:{let e=h-_;0!=e&&(u=(c-n)/e*m+n);break}case -1:{let e=g-_;0!=e&&(u=(d-n)/e*m+n);break}case 0:u=n}return l?u:-1*u}!function(e){e.CubismPhysics=t3,e.Options=t4}(z||(z={}));class io{release(){for(let e=0;e<this._clippingContextListForMask.getSize();e++)this._clippingContextListForMask.at(e)&&(this._clippingContextListForMask.at(e).release(),this._clippingContextListForMask.set(e,void 0)),this._clippingContextListForMask.set(e,null);this._clippingContextListForMask=null;for(let e=0;e<this._clippingContextListForDraw.getSize();e++)this._clippingContextListForDraw.set(e,null);this._clippingContextListForDraw=null;for(let e=0;e<this._channelColors.getSize();e++)this._channelColors.set(e,null);this._channelColors=null,null!=this._clearedFrameBufferFlags&&this._clearedFrameBufferFlags.clear(),this._clearedFrameBufferFlags=null}initialize(e,t){t%1!=0&&(a("The number of render textures must be specified as an integer. The decimal point is rounded down and corrected to an integer."),t=~~t),t<1&&a("The number of render textures must be an integer greater than or equal to 1. Set the number of render textures to 1."),this._renderTextureCount=t<1?1:t,this._clearedFrameBufferFlags=new $(this._renderTextureCount);for(let t=0;t<e.getDrawableCount();t++){if(e.getDrawableMaskCounts()[t]<=0){this._clippingContextListForDraw.pushBack(null);continue}let i=this.findSameClip(e.getDrawableMasks()[t],e.getDrawableMaskCounts()[t]);null==i&&(i=new this._clippingContexttConstructor(this,e.getDrawableMasks()[t],e.getDrawableMaskCounts()[t]),this._clippingContextListForMask.pushBack(i)),i.addClippedDrawable(t),this._clippingContextListForDraw.pushBack(i)}}findSameClip(e,t){for(let i=0;i<this._clippingContextListForMask.getSize();i++){let s=this._clippingContextListForMask.at(i),r=s._clippingIdCount;if(r!=t)continue;let a=0;for(let t=0;t<r;t++){let i=s._clippingIdList[t];for(let t=0;t<r;t++)if(e[t]==i){a++;break}}if(a==r)return s}return null}setupMatrixForHighPrecision(e,t){let i=0;for(let t=0;t<this._clippingContextListForMask.getSize();t++){let s=this._clippingContextListForMask.at(t);this.calcClippedDrawTotalBounds(e,s),s._isUsing&&i++}if(i>0){if(this.setupLayoutBounds(0),this._clearedFrameBufferFlags.getSize()!=this._renderTextureCount){this._clearedFrameBufferFlags.clear();for(let e=0;e<this._renderTextureCount;e++)this._clearedFrameBufferFlags.pushBack(!1)}else for(let e=0;e<this._renderTextureCount;e++)this._clearedFrameBufferFlags.set(e,!1);for(let i=0;i<this._clippingContextListForMask.getSize();i++){let s=this._clippingContextListForMask.at(i),r=s._allClippedDrawRect,a=s._layoutBounds,n=0,l=0,o=e.getPixelsPerUnit(),u=s.getClippingManager().getClippingMaskBufferSize(),h=a.width*u,g=a.height*u;this._tmpBoundsOnModel.setRect(r),this._tmpBoundsOnModel.width*o>h?(this._tmpBoundsOnModel.expand(.05*r.width,0),n=a.width/this._tmpBoundsOnModel.width):n=o/h,this._tmpBoundsOnModel.height*o>g?(this._tmpBoundsOnModel.expand(0,.05*r.height),l=a.height/this._tmpBoundsOnModel.height):l=o/g,this.createMatrixForMask(t,a,n,l),s._matrixForMask.setMatrix(this._tmpMatrixForMask.getArray()),s._matrixForDraw.setMatrix(this._tmpMatrixForDraw.getArray())}}}createMatrixForMask(e,t,i,s){this._tmpMatrix.loadIdentity(),this._tmpMatrix.translateRelative(-1,-1),this._tmpMatrix.scaleRelative(2,2),this._tmpMatrix.translateRelative(t.x,t.y),this._tmpMatrix.scaleRelative(i,s),this._tmpMatrix.translateRelative(-this._tmpBoundsOnModel.x,-this._tmpBoundsOnModel.y),this._tmpMatrixForMask.setMatrix(this._tmpMatrix.getArray()),this._tmpMatrix.loadIdentity(),this._tmpMatrix.translateRelative(t.x,t.y*(e?-1:1)),this._tmpMatrix.scaleRelative(i,s*(e?-1:1)),this._tmpMatrix.translateRelative(-this._tmpBoundsOnModel.x,-this._tmpBoundsOnModel.y),this._tmpMatrixForDraw.setMatrix(this._tmpMatrix.getArray())}setupLayoutBounds(e){let t=this._renderTextureCount<=1?36:32*this._renderTextureCount;if(e<=0||e>t){e>t&&n("not supported mask count : {0}\n[Details] render texture count : {1}, mask count : {2}",e-t,this._renderTextureCount,e);for(let e=0;e<this._clippingContextListForMask.getSize();e++){let t=this._clippingContextListForMask.at(e);t._layoutChannelIndex=0,t._layoutBounds.x=0,t._layoutBounds.y=0,t._layoutBounds.width=1,t._layoutBounds.height=1,t._bufferIndex=0}return}let i=this._renderTextureCount<=1?9:8,s=e/this._renderTextureCount,r=e%this._renderTextureCount,a=(s=Math.ceil(s))/4,l=s%4;a=~~a;let o=0;for(let s=0;s<this._renderTextureCount;s++)for(let u=0;u<4;u++){let h=a+(u<l?1:0);if(u==l+(a<1?-1:0)&&r>0&&(h-=s<r?0:1),0==h);else if(1==h){let e=this._clippingContextListForMask.at(o++);e._layoutChannelIndex=u,e._layoutBounds.x=0,e._layoutBounds.y=0,e._layoutBounds.width=1,e._layoutBounds.height=1,e._bufferIndex=s}else if(2==h)for(let e=0;e<h;e++){let t=e%2;t=~~t;let i=this._clippingContextListForMask.at(o++);i._layoutChannelIndex=u,i._layoutBounds.x=.5*t,i._layoutBounds.y=0,i._layoutBounds.width=.5,i._layoutBounds.height=1,i._bufferIndex=s}else if(h<=4)for(let e=0;e<h;e++){let t=e%2,i=e/2;t=~~t,i=~~i;let r=this._clippingContextListForMask.at(o++);r._layoutChannelIndex=u,r._layoutBounds.x=.5*t,r._layoutBounds.y=.5*i,r._layoutBounds.width=.5,r._layoutBounds.height=.5,r._bufferIndex=s}else if(h<=i)for(let e=0;e<h;e++){let t=e%3,i=e/3;t=~~t,i=~~i;let r=this._clippingContextListForMask.at(o++);r._layoutChannelIndex=u,r._layoutBounds.x=t/3,r._layoutBounds.y=i/3,r._layoutBounds.width=1/3,r._layoutBounds.height=1/3,r._bufferIndex=s}else{n("not supported mask count : {0}\n[Details] render texture count : {1}, mask count : {2}",e-t,this._renderTextureCount,e);for(let e=0;e<h;e++){let e=this._clippingContextListForMask.at(o++);e._layoutChannelIndex=0,e._layoutBounds.x=0,e._layoutBounds.y=0,e._layoutBounds.width=1,e._layoutBounds.height=1,e._bufferIndex=0}}}}calcClippedDrawTotalBounds(e,t){let i=Number.MAX_VALUE,s=Number.MAX_VALUE,r=Number.MIN_VALUE,a=Number.MIN_VALUE,n=t._clippedDrawableIndexList.length;for(let l=0;l<n;l++){let n=t._clippedDrawableIndexList[l],o=e.getDrawableVertexCount(n),u=e.getDrawableVertices(n),h=Number.MAX_VALUE,g=Number.MAX_VALUE,d=-Number.MAX_VALUE,c=-Number.MAX_VALUE,_=o*eV.vertexStep;for(let e=eV.vertexOffset;e<_;e+=eV.vertexStep){let t=u[e],i=u[e+1];t<h&&(h=t),t>d&&(d=t),i<g&&(g=i),i>c&&(c=i)}if(h!=Number.MAX_VALUE){if(h<i&&(i=h),g<s&&(s=g),d>r&&(r=d),c>a&&(a=c),i==Number.MAX_VALUE)t._allClippedDrawRect.x=0,t._allClippedDrawRect.y=0,t._allClippedDrawRect.width=0,t._allClippedDrawRect.height=0,t._isUsing=!1;else{t._isUsing=!0;let e=r-i,n=a-s;t._allClippedDrawRect.x=i,t._allClippedDrawRect.y=s,t._allClippedDrawRect.width=e,t._allClippedDrawRect.height=n}}}}getClippingContextListForDraw(){return this._clippingContextListForDraw}getClippingMaskBufferSize(){return this._clippingMaskBufferSize}getRenderTextureCount(){return this._renderTextureCount}getChannelFlagAsColor(e){return this._channelColors.at(e)}setClippingMaskBufferSize(e){this._clippingMaskBufferSize=e}constructor(e){this._renderTextureCount=0,this._clippingMaskBufferSize=256,this._clippingContextListForMask=new $,this._clippingContextListForDraw=new $,this._channelColors=new $,this._tmpBoundsOnModel=new ea,this._tmpMatrix=new er,this._tmpMatrixForMask=new er,this._tmpMatrixForDraw=new er,this._clippingContexttConstructor=e;let t=new eo;t.r=1,t.g=0,t.b=0,t.a=0,this._channelColors.pushBack(t),(t=new eo).r=0,t.g=1,t.b=0,t.a=0,this._channelColors.pushBack(t),(t=new eo).r=0,t.g=0,t.b=1,t.a=0,this._channelColors.pushBack(t),(t=new eo).r=0,t.g=0,t.b=0,t.a=1,this._channelColors.pushBack(t)}}class iu{release(){this.releaseShaderProgram()}setupShaderProgramForDraw(e,t,i){let s,r,a,l,o;e.isPremultipliedAlpha()||n("NoPremultipliedAlpha is not allowed"),0==this._shaderSets.getSize()&&this.generateShaders();let u=null!=e.getClippingContextBufferForDraw(),h=t.getDrawableInvertedMaskBit(i),g=u?h?2:1:0;switch(t.getDrawableBlendMode(i)){case el.CubismBlendMode_Normal:default:o=this._shaderSets.at(1+g),s=this.gl.ONE,r=this.gl.ONE_MINUS_SRC_ALPHA,a=this.gl.ONE,l=this.gl.ONE_MINUS_SRC_ALPHA;break;case el.CubismBlendMode_Additive:o=this._shaderSets.at(4+g),s=this.gl.ONE,r=this.gl.ONE,a=this.gl.ZERO,l=this.gl.ONE;break;case el.CubismBlendMode_Multiplicative:o=this._shaderSets.at(7+g),s=this.gl.DST_COLOR,r=this.gl.ONE_MINUS_SRC_ALPHA,a=this.gl.ZERO,l=this.gl.ONE}this.gl.useProgram(o.shaderProgram),null==e._bufferData.vertex&&(e._bufferData.vertex=this.gl.createBuffer()),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,e._bufferData.vertex);let d=t.getDrawableVertices(i);this.gl.bufferData(this.gl.ARRAY_BUFFER,d,this.gl.DYNAMIC_DRAW),this.gl.enableVertexAttribArray(o.attributePositionLocation),this.gl.vertexAttribPointer(o.attributePositionLocation,2,this.gl.FLOAT,!1,0,0),null==e._bufferData.uv&&(e._bufferData.uv=this.gl.createBuffer()),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,e._bufferData.uv);let c=t.getDrawableVertexUvs(i);if(this.gl.bufferData(this.gl.ARRAY_BUFFER,c,this.gl.DYNAMIC_DRAW),this.gl.enableVertexAttribArray(o.attributeTexCoordLocation),this.gl.vertexAttribPointer(o.attributeTexCoordLocation,2,this.gl.FLOAT,!1,0,0),u){this.gl.activeTexture(this.gl.TEXTURE1);let t=e.getClippingContextBufferForDraw().getClippingManager().getColorBuffer().at(e.getClippingContextBufferForDraw()._bufferIndex);this.gl.bindTexture(this.gl.TEXTURE_2D,t),this.gl.uniform1i(o.samplerTexture1Location,1),this.gl.uniformMatrix4fv(o.uniformClipMatrixLocation,!1,e.getClippingContextBufferForDraw()._matrixForDraw.getArray());let i=e.getClippingContextBufferForDraw()._layoutChannelIndex,s=e.getClippingContextBufferForDraw().getClippingManager().getChannelFlagAsColor(i);this.gl.uniform4f(o.uniformChannelFlagLocation,s.r,s.g,s.b,s.a)}let _=t.getDrawableTextureIndex(i),m=e.getBindedTextures().getValue(_);this.gl.activeTexture(this.gl.TEXTURE0),this.gl.bindTexture(this.gl.TEXTURE_2D,m),this.gl.uniform1i(o.samplerTexture0Location,0);let p=e.getMvpMatrix();this.gl.uniformMatrix4fv(o.uniformMatrixLocation,!1,p.getArray());let f=e.getModelColorWithOpacity(t.getDrawableOpacity(i)),y=t.getMultiplyColor(i),x=t.getScreenColor(i);this.gl.uniform4f(o.uniformBaseColorLocation,f.r,f.g,f.b,f.a),this.gl.uniform4f(o.uniformMultiplyColorLocation,y.r,y.g,y.b,y.a),this.gl.uniform4f(o.uniformScreenColorLocation,x.r,x.g,x.b,x.a),null==e._bufferData.index&&(e._bufferData.index=this.gl.createBuffer());let S=t.getDrawableVertexIndices(i);this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER,e._bufferData.index),this.gl.bufferData(this.gl.ELEMENT_ARRAY_BUFFER,S,this.gl.DYNAMIC_DRAW),this.gl.blendFuncSeparate(s,r,a,l)}setupShaderProgramForMask(e,t,i){e.isPremultipliedAlpha()||n("NoPremultipliedAlpha is not allowed"),0==this._shaderSets.getSize()&&this.generateShaders();let s=this._shaderSets.at(0);this.gl.useProgram(s.shaderProgram),null==e._bufferData.vertex&&(e._bufferData.vertex=this.gl.createBuffer()),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,e._bufferData.vertex);let r=t.getDrawableVertices(i);this.gl.bufferData(this.gl.ARRAY_BUFFER,r,this.gl.DYNAMIC_DRAW),this.gl.enableVertexAttribArray(s.attributePositionLocation),this.gl.vertexAttribPointer(s.attributePositionLocation,2,this.gl.FLOAT,!1,0,0),null==e._bufferData.uv&&(e._bufferData.uv=this.gl.createBuffer()),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,e._bufferData.uv);let a=t.getDrawableTextureIndex(i),l=e.getBindedTextures().getValue(a);this.gl.activeTexture(this.gl.TEXTURE0),this.gl.bindTexture(this.gl.TEXTURE_2D,l),this.gl.uniform1i(s.samplerTexture0Location,0),null==e._bufferData.uv&&(e._bufferData.uv=this.gl.createBuffer()),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,e._bufferData.uv);let o=t.getDrawableVertexUvs(i);this.gl.bufferData(this.gl.ARRAY_BUFFER,o,this.gl.DYNAMIC_DRAW),this.gl.enableVertexAttribArray(s.attributeTexCoordLocation),this.gl.vertexAttribPointer(s.attributeTexCoordLocation,2,this.gl.FLOAT,!1,0,0),e.getClippingContextBufferForMask();let u=e.getClippingContextBufferForMask()._layoutChannelIndex,h=e.getClippingContextBufferForMask().getClippingManager().getChannelFlagAsColor(u);this.gl.uniform4f(s.uniformChannelFlagLocation,h.r,h.g,h.b,h.a),this.gl.uniformMatrix4fv(s.uniformClipMatrixLocation,!1,e.getClippingContextBufferForMask()._matrixForMask.getArray());let g=e.getClippingContextBufferForMask()._layoutBounds;this.gl.uniform4f(s.uniformBaseColorLocation,2*g.x-1,2*g.y-1,2*g.getRight()-1,2*g.getBottom()-1);let d=t.getMultiplyColor(i),c=t.getScreenColor(i);this.gl.uniform4f(s.uniformMultiplyColorLocation,d.r,d.g,d.b,d.a),this.gl.uniform4f(s.uniformScreenColorLocation,c.r,c.g,c.b,c.a);let _=this.gl.ZERO,m=this.gl.ONE_MINUS_SRC_COLOR,p=this.gl.ZERO,f=this.gl.ONE_MINUS_SRC_ALPHA;null==e._bufferData.index&&(e._bufferData.index=this.gl.createBuffer());let y=t.getDrawableVertexIndices(i);this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER,e._bufferData.index),this.gl.bufferData(this.gl.ELEMENT_ARRAY_BUFFER,y,this.gl.DYNAMIC_DRAW),this.gl.blendFuncSeparate(_,m,p,f)}releaseShaderProgram(){for(let e=0;e<this._shaderSets.getSize();e++)this.gl.deleteProgram(this._shaderSets.at(e).shaderProgram),this._shaderSets.at(e).shaderProgram=0,this._shaderSets.set(e,void 0),this._shaderSets.set(e,null)}generateShaders(){for(let e=0;e<10;e++)this._shaderSets.pushBack(new ig);this._shaderSets.at(0).shaderProgram=this.loadShaderProgram(ic,i_),this._shaderSets.at(1).shaderProgram=this.loadShaderProgram(im,iy),this._shaderSets.at(2).shaderProgram=this.loadShaderProgram(ip,ix),this._shaderSets.at(3).shaderProgram=this.loadShaderProgram(ip,iS),this._shaderSets.at(4).shaderProgram=this._shaderSets.at(1).shaderProgram,this._shaderSets.at(5).shaderProgram=this._shaderSets.at(2).shaderProgram,this._shaderSets.at(6).shaderProgram=this._shaderSets.at(3).shaderProgram,this._shaderSets.at(7).shaderProgram=this._shaderSets.at(1).shaderProgram,this._shaderSets.at(8).shaderProgram=this._shaderSets.at(2).shaderProgram,this._shaderSets.at(9).shaderProgram=this._shaderSets.at(3).shaderProgram,this._shaderSets.at(0).attributePositionLocation=this.gl.getAttribLocation(this._shaderSets.at(0).shaderProgram,"a_position"),this._shaderSets.at(0).attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets.at(0).shaderProgram,"a_texCoord"),this._shaderSets.at(0).samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets.at(0).shaderProgram,"s_texture0"),this._shaderSets.at(0).uniformClipMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(0).shaderProgram,"u_clipMatrix"),this._shaderSets.at(0).uniformChannelFlagLocation=this.gl.getUniformLocation(this._shaderSets.at(0).shaderProgram,"u_channelFlag"),this._shaderSets.at(0).uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets.at(0).shaderProgram,"u_baseColor"),this._shaderSets.at(0).uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets.at(0).shaderProgram,"u_multiplyColor"),this._shaderSets.at(0).uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets.at(0).shaderProgram,"u_screenColor"),this._shaderSets.at(1).attributePositionLocation=this.gl.getAttribLocation(this._shaderSets.at(1).shaderProgram,"a_position"),this._shaderSets.at(1).attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets.at(1).shaderProgram,"a_texCoord"),this._shaderSets.at(1).samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets.at(1).shaderProgram,"s_texture0"),this._shaderSets.at(1).uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(1).shaderProgram,"u_matrix"),this._shaderSets.at(1).uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets.at(1).shaderProgram,"u_baseColor"),this._shaderSets.at(1).uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets.at(1).shaderProgram,"u_multiplyColor"),this._shaderSets.at(1).uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets.at(1).shaderProgram,"u_screenColor"),this._shaderSets.at(2).attributePositionLocation=this.gl.getAttribLocation(this._shaderSets.at(2).shaderProgram,"a_position"),this._shaderSets.at(2).attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets.at(2).shaderProgram,"a_texCoord"),this._shaderSets.at(2).samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets.at(2).shaderProgram,"s_texture0"),this._shaderSets.at(2).samplerTexture1Location=this.gl.getUniformLocation(this._shaderSets.at(2).shaderProgram,"s_texture1"),this._shaderSets.at(2).uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(2).shaderProgram,"u_matrix"),this._shaderSets.at(2).uniformClipMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(2).shaderProgram,"u_clipMatrix"),this._shaderSets.at(2).uniformChannelFlagLocation=this.gl.getUniformLocation(this._shaderSets.at(2).shaderProgram,"u_channelFlag"),this._shaderSets.at(2).uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets.at(2).shaderProgram,"u_baseColor"),this._shaderSets.at(2).uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets.at(2).shaderProgram,"u_multiplyColor"),this._shaderSets.at(2).uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets.at(2).shaderProgram,"u_screenColor"),this._shaderSets.at(3).attributePositionLocation=this.gl.getAttribLocation(this._shaderSets.at(3).shaderProgram,"a_position"),this._shaderSets.at(3).attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets.at(3).shaderProgram,"a_texCoord"),this._shaderSets.at(3).samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets.at(3).shaderProgram,"s_texture0"),this._shaderSets.at(3).samplerTexture1Location=this.gl.getUniformLocation(this._shaderSets.at(3).shaderProgram,"s_texture1"),this._shaderSets.at(3).uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(3).shaderProgram,"u_matrix"),this._shaderSets.at(3).uniformClipMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(3).shaderProgram,"u_clipMatrix"),this._shaderSets.at(3).uniformChannelFlagLocation=this.gl.getUniformLocation(this._shaderSets.at(3).shaderProgram,"u_channelFlag"),this._shaderSets.at(3).uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets.at(3).shaderProgram,"u_baseColor"),this._shaderSets.at(3).uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets.at(3).shaderProgram,"u_multiplyColor"),this._shaderSets.at(3).uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets.at(3).shaderProgram,"u_screenColor"),this._shaderSets.at(4).attributePositionLocation=this.gl.getAttribLocation(this._shaderSets.at(4).shaderProgram,"a_position"),this._shaderSets.at(4).attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets.at(4).shaderProgram,"a_texCoord"),this._shaderSets.at(4).samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets.at(4).shaderProgram,"s_texture0"),this._shaderSets.at(4).uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(4).shaderProgram,"u_matrix"),this._shaderSets.at(4).uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets.at(4).shaderProgram,"u_baseColor"),this._shaderSets.at(4).uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets.at(4).shaderProgram,"u_multiplyColor"),this._shaderSets.at(4).uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets.at(4).shaderProgram,"u_screenColor"),this._shaderSets.at(5).attributePositionLocation=this.gl.getAttribLocation(this._shaderSets.at(5).shaderProgram,"a_position"),this._shaderSets.at(5).attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets.at(5).shaderProgram,"a_texCoord"),this._shaderSets.at(5).samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets.at(5).shaderProgram,"s_texture0"),this._shaderSets.at(5).samplerTexture1Location=this.gl.getUniformLocation(this._shaderSets.at(5).shaderProgram,"s_texture1"),this._shaderSets.at(5).uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(5).shaderProgram,"u_matrix"),this._shaderSets.at(5).uniformClipMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(5).shaderProgram,"u_clipMatrix"),this._shaderSets.at(5).uniformChannelFlagLocation=this.gl.getUniformLocation(this._shaderSets.at(5).shaderProgram,"u_channelFlag"),this._shaderSets.at(5).uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets.at(5).shaderProgram,"u_baseColor"),this._shaderSets.at(5).uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets.at(5).shaderProgram,"u_multiplyColor"),this._shaderSets.at(5).uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets.at(5).shaderProgram,"u_screenColor"),this._shaderSets.at(6).attributePositionLocation=this.gl.getAttribLocation(this._shaderSets.at(6).shaderProgram,"a_position"),this._shaderSets.at(6).attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets.at(6).shaderProgram,"a_texCoord"),this._shaderSets.at(6).samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets.at(6).shaderProgram,"s_texture0"),this._shaderSets.at(6).samplerTexture1Location=this.gl.getUniformLocation(this._shaderSets.at(6).shaderProgram,"s_texture1"),this._shaderSets.at(6).uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(6).shaderProgram,"u_matrix"),this._shaderSets.at(6).uniformClipMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(6).shaderProgram,"u_clipMatrix"),this._shaderSets.at(6).uniformChannelFlagLocation=this.gl.getUniformLocation(this._shaderSets.at(6).shaderProgram,"u_channelFlag"),this._shaderSets.at(6).uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets.at(6).shaderProgram,"u_baseColor"),this._shaderSets.at(6).uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets.at(6).shaderProgram,"u_multiplyColor"),this._shaderSets.at(6).uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets.at(6).shaderProgram,"u_screenColor"),this._shaderSets.at(7).attributePositionLocation=this.gl.getAttribLocation(this._shaderSets.at(7).shaderProgram,"a_position"),this._shaderSets.at(7).attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets.at(7).shaderProgram,"a_texCoord"),this._shaderSets.at(7).samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets.at(7).shaderProgram,"s_texture0"),this._shaderSets.at(7).uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(7).shaderProgram,"u_matrix"),this._shaderSets.at(7).uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets.at(7).shaderProgram,"u_baseColor"),this._shaderSets.at(7).uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets.at(7).shaderProgram,"u_multiplyColor"),this._shaderSets.at(7).uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets.at(7).shaderProgram,"u_screenColor"),this._shaderSets.at(8).attributePositionLocation=this.gl.getAttribLocation(this._shaderSets.at(8).shaderProgram,"a_position"),this._shaderSets.at(8).attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets.at(8).shaderProgram,"a_texCoord"),this._shaderSets.at(8).samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets.at(8).shaderProgram,"s_texture0"),this._shaderSets.at(8).samplerTexture1Location=this.gl.getUniformLocation(this._shaderSets.at(8).shaderProgram,"s_texture1"),this._shaderSets.at(8).uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(8).shaderProgram,"u_matrix"),this._shaderSets.at(8).uniformClipMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(8).shaderProgram,"u_clipMatrix"),this._shaderSets.at(8).uniformChannelFlagLocation=this.gl.getUniformLocation(this._shaderSets.at(8).shaderProgram,"u_channelFlag"),this._shaderSets.at(8).uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets.at(8).shaderProgram,"u_baseColor"),this._shaderSets.at(8).uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets.at(8).shaderProgram,"u_multiplyColor"),this._shaderSets.at(8).uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets.at(8).shaderProgram,"u_screenColor"),this._shaderSets.at(9).attributePositionLocation=this.gl.getAttribLocation(this._shaderSets.at(9).shaderProgram,"a_position"),this._shaderSets.at(9).attributeTexCoordLocation=this.gl.getAttribLocation(this._shaderSets.at(9).shaderProgram,"a_texCoord"),this._shaderSets.at(9).samplerTexture0Location=this.gl.getUniformLocation(this._shaderSets.at(9).shaderProgram,"s_texture0"),this._shaderSets.at(9).samplerTexture1Location=this.gl.getUniformLocation(this._shaderSets.at(9).shaderProgram,"s_texture1"),this._shaderSets.at(9).uniformMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(9).shaderProgram,"u_matrix"),this._shaderSets.at(9).uniformClipMatrixLocation=this.gl.getUniformLocation(this._shaderSets.at(9).shaderProgram,"u_clipMatrix"),this._shaderSets.at(9).uniformChannelFlagLocation=this.gl.getUniformLocation(this._shaderSets.at(9).shaderProgram,"u_channelFlag"),this._shaderSets.at(9).uniformBaseColorLocation=this.gl.getUniformLocation(this._shaderSets.at(9).shaderProgram,"u_baseColor"),this._shaderSets.at(9).uniformMultiplyColorLocation=this.gl.getUniformLocation(this._shaderSets.at(9).shaderProgram,"u_multiplyColor"),this._shaderSets.at(9).uniformScreenColorLocation=this.gl.getUniformLocation(this._shaderSets.at(9).shaderProgram,"u_screenColor")}loadShaderProgram(e,t){let i=this.gl.createProgram(),s=this.compileShaderSource(this.gl.VERTEX_SHADER,e);if(!s)return n("Vertex shader compile error!"),0;let r=this.compileShaderSource(this.gl.FRAGMENT_SHADER,t);return r?(this.gl.attachShader(i,s),this.gl.attachShader(i,r),this.gl.linkProgram(i),this.gl.getProgramParameter(i,this.gl.LINK_STATUS))?(this.gl.deleteShader(s),this.gl.deleteShader(r),i):(n("Failed to link program: {0}",i),this.gl.deleteShader(s),s=0,this.gl.deleteShader(r),r=0,i&&(this.gl.deleteProgram(i),i=0),0):(n("Vertex shader compile error!"),0)}compileShaderSource(e,t){let i=this.gl.createShader(e);return this.gl.shaderSource(i,t),this.gl.compileShader(i),i||n("Shader compile log: {0} ",this.gl.getShaderInfoLog(i)),this.gl.getShaderParameter(i,this.gl.COMPILE_STATUS)?i:(this.gl.deleteShader(i),null)}setGl(e){this.gl=e}constructor(){this._shaderSets=new $}}class ih{static getInstance(){return null==l&&(l=new ih),l}static deleteInstance(){l&&(l.release(),l=null)}release(){for(let e=this._shaderMap.begin();e.notEqual(this._shaderMap.end());e.preIncrement())e.ptr().second.release();this._shaderMap.clear()}getShader(e){return this._shaderMap.getValue(e)}setGlContext(e){if(!this._shaderMap.isExist(e)){let t=new iu;t.setGl(e),this._shaderMap.setValue(e,t)}}constructor(){this._shaderMap=new em}}class ig{}var id=function(e){return e[e.ShaderNames_SetupMask=0]="ShaderNames_SetupMask",e[e.ShaderNames_NormalPremultipliedAlpha=1]="ShaderNames_NormalPremultipliedAlpha",e[e.ShaderNames_NormalMaskedPremultipliedAlpha=2]="ShaderNames_NormalMaskedPremultipliedAlpha",e[e.ShaderNames_NomralMaskedInvertedPremultipliedAlpha=3]="ShaderNames_NomralMaskedInvertedPremultipliedAlpha",e[e.ShaderNames_AddPremultipliedAlpha=4]="ShaderNames_AddPremultipliedAlpha",e[e.ShaderNames_AddMaskedPremultipliedAlpha=5]="ShaderNames_AddMaskedPremultipliedAlpha",e[e.ShaderNames_AddMaskedPremultipliedAlphaInverted=6]="ShaderNames_AddMaskedPremultipliedAlphaInverted",e[e.ShaderNames_MultPremultipliedAlpha=7]="ShaderNames_MultPremultipliedAlpha",e[e.ShaderNames_MultMaskedPremultipliedAlpha=8]="ShaderNames_MultMaskedPremultipliedAlpha",e[e.ShaderNames_MultMaskedPremultipliedAlphaInverted=9]="ShaderNames_MultMaskedPremultipliedAlphaInverted",e}({});let ic="attribute vec4     a_position;attribute vec2     a_texCoord;varying vec2       v_texCoord;varying vec4       v_myPos;uniform mat4       u_clipMatrix;void main(){   gl_Position = u_clipMatrix * a_position;   v_myPos = u_clipMatrix * a_position;   v_texCoord = a_texCoord;   v_texCoord.y = 1.0 - v_texCoord.y;}",i_="precision mediump float;varying vec2       v_texCoord;varying vec4       v_myPos;uniform vec4       u_baseColor;uniform vec4       u_channelFlag;uniform sampler2D  s_texture0;void main(){   float isInside =        step(u_baseColor.x, v_myPos.x/v_myPos.w)       * step(u_baseColor.y, v_myPos.y/v_myPos.w)       * step(v_myPos.x/v_myPos.w, u_baseColor.z)       * step(v_myPos.y/v_myPos.w, u_baseColor.w);   gl_FragColor = u_channelFlag * texture2D(s_texture0, v_texCoord).a * isInside;}",im="attribute vec4     a_position;attribute vec2     a_texCoord;varying vec2       v_texCoord;uniform mat4       u_matrix;void main(){   gl_Position = u_matrix * a_position;   v_texCoord = a_texCoord;   v_texCoord.y = 1.0 - v_texCoord.y;}",ip="attribute vec4     a_position;attribute vec2     a_texCoord;varying vec2       v_texCoord;varying vec4       v_clipPos;uniform mat4       u_matrix;uniform mat4       u_clipMatrix;void main(){   gl_Position = u_matrix * a_position;   v_clipPos = u_clipMatrix * a_position;   v_texCoord = a_texCoord;   v_texCoord.y = 1.0 - v_texCoord.y;}",iy="precision mediump float;varying vec2       v_texCoord;uniform vec4       u_baseColor;uniform sampler2D  s_texture0;uniform vec4       u_multiplyColor;uniform vec4       u_screenColor;void main(){   vec4 texColor = texture2D(s_texture0, v_texCoord);   texColor.rgb = texColor.rgb * u_multiplyColor.rgb;   texColor.rgb = (texColor.rgb + u_screenColor.rgb * texColor.a) - (texColor.rgb * u_screenColor.rgb);   vec4 color = texColor * u_baseColor;   gl_FragColor = vec4(color.rgb, color.a);}",ix="precision mediump float;varying vec2       v_texCoord;varying vec4       v_clipPos;uniform vec4       u_baseColor;uniform vec4       u_channelFlag;uniform sampler2D  s_texture0;uniform sampler2D  s_texture1;uniform vec4       u_multiplyColor;uniform vec4       u_screenColor;void main(){   vec4 texColor = texture2D(s_texture0, v_texCoord);   texColor.rgb = texColor.rgb * u_multiplyColor.rgb;   texColor.rgb = (texColor.rgb + u_screenColor.rgb * texColor.a) - (texColor.rgb * u_screenColor.rgb);   vec4 col_formask = texColor * u_baseColor;   vec4 clipMask = (1.0 - texture2D(s_texture1, v_clipPos.xy / v_clipPos.w)) * u_channelFlag;   float maskVal = clipMask.r + clipMask.g + clipMask.b + clipMask.a;   col_formask = col_formask * maskVal;   gl_FragColor = col_formask;}",iS="precision mediump float;varying vec2      v_texCoord;varying vec4      v_clipPos;uniform sampler2D s_texture0;uniform sampler2D s_texture1;uniform vec4      u_channelFlag;uniform vec4      u_baseColor;uniform vec4      u_multiplyColor;uniform vec4      u_screenColor;void main(){   vec4 texColor = texture2D(s_texture0, v_texCoord);   texColor.rgb = texColor.rgb * u_multiplyColor.rgb;   texColor.rgb = (texColor.rgb + u_screenColor.rgb * texColor.a) - (texColor.rgb * u_screenColor.rgb);   vec4 col_formask = texColor * u_baseColor;   vec4 clipMask = (1.0 - texture2D(s_texture1, v_clipPos.xy / v_clipPos.w)) * u_channelFlag;   float maskVal = clipMask.r + clipMask.g + clipMask.b + clipMask.a;   col_formask = col_formask * (1.0 - maskVal);   gl_FragColor = col_formask;}";!function(e){e.CubismShaderSet=ig,e.CubismShader_WebGL=iu,e.CubismShaderManager_WebGL=ih,e.ShaderNames=id}(G||(G={}));class iC extends io{getMaskRenderTexture(){if(this._maskTexture&&null!=this._maskTexture.textures)this._maskTexture.frameNo=this._currentFrameNo;else{null!=this._maskRenderTextures&&this._maskRenderTextures.clear(),this._maskRenderTextures=new $,null!=this._maskColorBuffers&&this._maskColorBuffers.clear(),this._maskColorBuffers=new $;let e=this._clippingMaskBufferSize;for(let t=0;t<this._renderTextureCount;t++)this._maskColorBuffers.pushBack(this.gl.createTexture()),this.gl.bindTexture(this.gl.TEXTURE_2D,this._maskColorBuffers.at(t)),this.gl.texImage2D(this.gl.TEXTURE_2D,0,this.gl.RGBA,e,e,0,this.gl.RGBA,this.gl.UNSIGNED_BYTE,null),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_WRAP_S,this.gl.CLAMP_TO_EDGE),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_WRAP_T,this.gl.CLAMP_TO_EDGE),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_MIN_FILTER,this.gl.LINEAR),this.gl.texParameteri(this.gl.TEXTURE_2D,this.gl.TEXTURE_MAG_FILTER,this.gl.LINEAR),this.gl.bindTexture(this.gl.TEXTURE_2D,null),this._maskRenderTextures.pushBack(this.gl.createFramebuffer()),this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,this._maskRenderTextures.at(t)),this.gl.framebufferTexture2D(this.gl.FRAMEBUFFER,this.gl.COLOR_ATTACHMENT0,this.gl.TEXTURE_2D,this._maskColorBuffers.at(t),0);this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,u),this._maskTexture=new ib(this._currentFrameNo,this._maskRenderTextures)}return this._maskTexture.textures}setGL(e){this.gl=e}setupClippingContext(e,t){this._currentFrameNo++;let i=0;for(let t=0;t<this._clippingContextListForMask.getSize();t++){let s=this._clippingContextListForMask.at(t);this.calcClippedDrawTotalBounds(e,s),s._isUsing&&i++}if(i>0){this.gl.viewport(0,0,this._clippingMaskBufferSize,this._clippingMaskBufferSize),this._currentMaskRenderTexture=this.getMaskRenderTexture().at(0),t.preDraw(),this.setupLayoutBounds(i),this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,this._currentMaskRenderTexture),this._clearedFrameBufferFlags.getSize()!=this._renderTextureCount&&(this._clearedFrameBufferFlags.clear(),this._clearedFrameBufferFlags=new $(this._renderTextureCount));for(let e=0;e<this._clearedFrameBufferFlags.getSize();e++)this._clearedFrameBufferFlags.set(e,!1);for(let i=0;i<this._clippingContextListForMask.getSize();i++){let s=this._clippingContextListForMask.at(i),r=s._allClippedDrawRect,a=s._layoutBounds,n=0,l=0,o=this.getMaskRenderTexture().at(s._bufferIndex);this._currentMaskRenderTexture!=o&&(this._currentMaskRenderTexture=o,t.preDraw(),this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,this._currentMaskRenderTexture)),this._tmpBoundsOnModel.setRect(r),this._tmpBoundsOnModel.expand(.05*r.width,.05*r.height),n=a.width/this._tmpBoundsOnModel.width,l=a.height/this._tmpBoundsOnModel.height,this._tmpMatrix.loadIdentity(),this._tmpMatrix.translateRelative(-1,-1),this._tmpMatrix.scaleRelative(2,2),this._tmpMatrix.translateRelative(a.x,a.y),this._tmpMatrix.scaleRelative(n,l),this._tmpMatrix.translateRelative(-this._tmpBoundsOnModel.x,-this._tmpBoundsOnModel.y),this._tmpMatrixForMask.setMatrix(this._tmpMatrix.getArray()),this._tmpMatrix.loadIdentity(),this._tmpMatrix.translateRelative(a.x,a.y),this._tmpMatrix.scaleRelative(n,l),this._tmpMatrix.translateRelative(-this._tmpBoundsOnModel.x,-this._tmpBoundsOnModel.y),this._tmpMatrixForDraw.setMatrix(this._tmpMatrix.getArray()),s._matrixForMask.setMatrix(this._tmpMatrixForMask.getArray()),s._matrixForDraw.setMatrix(this._tmpMatrixForDraw.getArray());let u=s._clippingIdCount;for(let i=0;i<u;i++){let r=s._clippingIdList[i];e.getDrawableDynamicFlagVertexPositionsDidChange(r)&&(t.setIsCulling(!1!=e.getDrawableCulling(r)),this._clearedFrameBufferFlags.at(s._bufferIndex)||(this.gl.clearColor(1,1,1,1),this.gl.clear(this.gl.COLOR_BUFFER_BIT),this._clearedFrameBufferFlags.set(s._bufferIndex,!0)),t.setClippingContextBufferForMask(s),t.drawMeshWebGL(e,r))}}this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,u),t.setClippingContextBufferForMask(null),this.gl.viewport(o[0],o[1],o[2],o[3])}}getColorBuffer(){return this._maskColorBuffers}getClippingMaskCount(){return this._clippingContextListForMask.getSize()}constructor(){super(iv)}}class ib{constructor(e,t){this.frameNo=e,this.textures=t}}class iv extends eu{getClippingManager(){return this._owner}setGl(e){this._owner.setGL(e)}constructor(e,t,i){super(t,i),this._owner=e}}class iM{setGlEnable(e,t){t?this.gl.enable(e):this.gl.disable(e)}setGlEnableVertexAttribArray(e,t){t?this.gl.enableVertexAttribArray(e):this.gl.disableVertexAttribArray(e)}save(){if(null==this.gl){n("'gl' is null. WebGLRenderingContext is required.\nPlease call 'CubimRenderer_WebGL.startUp' function.");return}this._lastArrayBufferBinding=this.gl.getParameter(this.gl.ARRAY_BUFFER_BINDING),this._lastElementArrayBufferBinding=this.gl.getParameter(this.gl.ELEMENT_ARRAY_BUFFER_BINDING),this._lastProgram=this.gl.getParameter(this.gl.CURRENT_PROGRAM),this._lastActiveTexture=this.gl.getParameter(this.gl.ACTIVE_TEXTURE),this.gl.activeTexture(this.gl.TEXTURE1),this._lastTexture1Binding2D=this.gl.getParameter(this.gl.TEXTURE_BINDING_2D),this.gl.activeTexture(this.gl.TEXTURE0),this._lastTexture0Binding2D=this.gl.getParameter(this.gl.TEXTURE_BINDING_2D),this._lastVertexAttribArrayEnabled[0]=this.gl.getVertexAttrib(0,this.gl.VERTEX_ATTRIB_ARRAY_ENABLED),this._lastVertexAttribArrayEnabled[1]=this.gl.getVertexAttrib(1,this.gl.VERTEX_ATTRIB_ARRAY_ENABLED),this._lastVertexAttribArrayEnabled[2]=this.gl.getVertexAttrib(2,this.gl.VERTEX_ATTRIB_ARRAY_ENABLED),this._lastVertexAttribArrayEnabled[3]=this.gl.getVertexAttrib(3,this.gl.VERTEX_ATTRIB_ARRAY_ENABLED),this._lastScissorTest=this.gl.isEnabled(this.gl.SCISSOR_TEST),this._lastStencilTest=this.gl.isEnabled(this.gl.STENCIL_TEST),this._lastDepthTest=this.gl.isEnabled(this.gl.DEPTH_TEST),this._lastCullFace=this.gl.isEnabled(this.gl.CULL_FACE),this._lastBlend=this.gl.isEnabled(this.gl.BLEND),this._lastFrontFace=this.gl.getParameter(this.gl.FRONT_FACE),this._lastColorMask=this.gl.getParameter(this.gl.COLOR_WRITEMASK),this._lastBlending[0]=this.gl.getParameter(this.gl.BLEND_SRC_RGB),this._lastBlending[1]=this.gl.getParameter(this.gl.BLEND_DST_RGB),this._lastBlending[2]=this.gl.getParameter(this.gl.BLEND_SRC_ALPHA),this._lastBlending[3]=this.gl.getParameter(this.gl.BLEND_DST_ALPHA),this._lastFBO=this.gl.getParameter(this.gl.FRAMEBUFFER_BINDING),this._lastViewport=this.gl.getParameter(this.gl.VIEWPORT)}restore(){if(null==this.gl){n("'gl' is null. WebGLRenderingContext is required.\nPlease call 'CubimRenderer_WebGL.startUp' function.");return}this.gl.useProgram(this._lastProgram),this.setGlEnableVertexAttribArray(0,this._lastVertexAttribArrayEnabled[0]),this.setGlEnableVertexAttribArray(1,this._lastVertexAttribArrayEnabled[1]),this.setGlEnableVertexAttribArray(2,this._lastVertexAttribArrayEnabled[2]),this.setGlEnableVertexAttribArray(3,this._lastVertexAttribArrayEnabled[3]),this.setGlEnable(this.gl.SCISSOR_TEST,this._lastScissorTest),this.setGlEnable(this.gl.STENCIL_TEST,this._lastStencilTest),this.setGlEnable(this.gl.DEPTH_TEST,this._lastDepthTest),this.setGlEnable(this.gl.CULL_FACE,this._lastCullFace),this.setGlEnable(this.gl.BLEND,this._lastBlend),this.gl.frontFace(this._lastFrontFace),this.gl.colorMask(this._lastColorMask[0],this._lastColorMask[1],this._lastColorMask[2],this._lastColorMask[3]),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,this._lastArrayBufferBinding),this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER,this._lastElementArrayBufferBinding),this.gl.activeTexture(this.gl.TEXTURE1),this.gl.bindTexture(this.gl.TEXTURE_2D,this._lastTexture1Binding2D),this.gl.activeTexture(this.gl.TEXTURE0),this.gl.bindTexture(this.gl.TEXTURE_2D,this._lastTexture0Binding2D),this.gl.activeTexture(this._lastActiveTexture),this.gl.blendFuncSeparate(this._lastBlending[0],this._lastBlending[1],this._lastBlending[2],this._lastBlending[3])}setGl(e){this.gl=e}constructor(){this._lastVertexAttribArrayEnabled=[,,,,],this._lastColorMask=[,,,,],this._lastBlending=[,,,,],this._lastViewport=[,,,,]}}class iP extends en{initialize(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;e.isUsingMasking()&&(this._clippingManager=new iC,this._clippingManager.initialize(e,t)),this._sortedDrawableIndexList.resize(e.getDrawableCount(),0),super.initialize(e)}bindTexture(e,t){this._textures.setValue(e,t)}getBindedTextures(){return this._textures}setClippingMaskBufferSize(e){if(!this._model.isUsingMasking())return;let t=this._clippingManager.getRenderTextureCount();this._clippingManager.release(),this._clippingManager=void 0,this._clippingManager=null,this._clippingManager=new iC,this._clippingManager.setClippingMaskBufferSize(e),this._clippingManager.initialize(this.getModel(),t)}getClippingMaskBufferSize(){return this._model.isUsingMasking()?this._clippingManager.getClippingMaskBufferSize():-1}getRenderTextureCount(){return this._model.isUsingMasking()?this._clippingManager.getRenderTextureCount():-1}release(){this._clippingManager&&(this._clippingManager.release(),this._clippingManager=void 0,this._clippingManager=null),null!=this.gl&&(this.gl.deleteBuffer(this._bufferData.vertex),this._bufferData.vertex=null,this.gl.deleteBuffer(this._bufferData.uv),this._bufferData.uv=null,this.gl.deleteBuffer(this._bufferData.index),this._bufferData.index=null,this._bufferData=null,this._textures=null)}doDrawModel(){if(null==this.gl){n("'gl' is null. WebGLRenderingContext is required.\nPlease call 'CubimRenderer_WebGL.startUp' function.");return}null!=this._clippingManager&&(this.preDraw(),this.isUsingHighPrecisionMask()?this._clippingManager.setupMatrixForHighPrecision(this.getModel(),!1):this._clippingManager.setupClippingContext(this.getModel(),this)),this.preDraw();let e=this.getModel().getDrawableCount(),t=this.getModel().getDrawableRenderOrders();for(let i=0;i<e;++i){let e=t[i];this._sortedDrawableIndexList.set(e,i)}for(let t=0;t<e;++t){let e=this._sortedDrawableIndexList.at(t);if(!this.getModel().getDrawableDynamicFlagIsVisible(e))continue;let i=null!=this._clippingManager?this._clippingManager.getClippingContextListForDraw().at(e):null;if(null!=i&&this.isUsingHighPrecisionMask()){i._isUsing&&(this.gl.viewport(0,0,this._clippingManager.getClippingMaskBufferSize(),this._clippingManager.getClippingMaskBufferSize()),this.preDraw(),this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,i.getClippingManager().getMaskRenderTexture().at(i._bufferIndex)),this.gl.clearColor(1,1,1,1),this.gl.clear(this.gl.COLOR_BUFFER_BIT));{let e=i._clippingIdCount;for(let t=0;t<e;t++){let e=i._clippingIdList[t];this._model.getDrawableDynamicFlagVertexPositionsDidChange(e)&&(this.setIsCulling(!1!=this._model.getDrawableCulling(e)),this.setClippingContextBufferForMask(i),this.drawMeshWebGL(this._model,e))}}this.gl.bindFramebuffer(this.gl.FRAMEBUFFER,u),this.setClippingContextBufferForMask(null),this.gl.viewport(o[0],o[1],o[2],o[3]),this.preDraw()}this.setClippingContextBufferForDraw(i),this.setIsCulling(this.getModel().getDrawableCulling(e)),this.drawMeshWebGL(this._model,e)}}drawMeshWebGL(e,t){this.isCulling()?this.gl.enable(this.gl.CULL_FACE):this.gl.disable(this.gl.CULL_FACE),this.gl.frontFace(this.gl.CCW),this.isGeneratingMask()?ih.getInstance().getShader(this.gl).setupShaderProgramForMask(this,e,t):ih.getInstance().getShader(this.gl).setupShaderProgramForDraw(this,e,t);{let i=e.getDrawableVertexIndexCount(t);this.gl.drawElements(this.gl.TRIANGLES,i,this.gl.UNSIGNED_SHORT,0)}this.gl.useProgram(null),this.setClippingContextBufferForDraw(null),this.setClippingContextBufferForMask(null)}saveProfile(){this._rendererProfile.save()}restoreProfile(){this._rendererProfile.restore()}static doStaticRelease(){ih.deleteInstance()}setRenderState(e,t){u=e,o=t}preDraw(){if(this.firstDraw&&(this.firstDraw=!1),this.gl.disable(this.gl.SCISSOR_TEST),this.gl.disable(this.gl.STENCIL_TEST),this.gl.disable(this.gl.DEPTH_TEST),this.gl.frontFace(this.gl.CW),this.gl.enable(this.gl.BLEND),this.gl.colorMask(!0,!0,!0,!0),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,null),this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER,null),this.getAnisotropy()>0&&this._extension)for(let e=0;e<this._textures.getSize();++e)this.gl.bindTexture(this.gl.TEXTURE_2D,this._textures.getValue(e)),this.gl.texParameterf(this.gl.TEXTURE_2D,this._extension.TEXTURE_MAX_ANISOTROPY_EXT,this.getAnisotropy())}setClippingContextBufferForMask(e){this._clippingContextBufferForMask=e}getClippingContextBufferForMask(){return this._clippingContextBufferForMask}setClippingContextBufferForDraw(e){this._clippingContextBufferForDraw=e}getClippingContextBufferForDraw(){return this._clippingContextBufferForDraw}isGeneratingMask(){return null!=this.getClippingContextBufferForMask()}startUp(e){this.gl=e,this._clippingManager&&this._clippingManager.setGL(e),ih.getInstance().setGlContext(e),this._rendererProfile.setGl(e),this._extension=this.gl.getExtension("EXT_texture_filter_anisotropic")||this.gl.getExtension("WEBKIT_EXT_texture_filter_anisotropic")||this.gl.getExtension("MOZ_EXT_texture_filter_anisotropic")}constructor(){super(),this._clippingContextBufferForMask=null,this._clippingContextBufferForDraw=null,this._rendererProfile=new iM,this.firstDraw=!0,this._textures=new em,this._sortedDrawableIndexList=new $,this._bufferData={vertex:WebGLBuffer=null,uv:WebGLBuffer=null,index:WebGLBuffer=null},this._textures.prepareCapacity(32,!0)}}en.staticRelease=()=>{iP.doStaticRelease()},function(e){e.CubismClippingContext=iv,e.CubismClippingManager_WebGL=iC,e.CubismRenderTextureResource=ib,e.CubismRenderer_WebGL=iP}(X||(X={}));class iw{constructor(e=!1,t=new eo){this.isOverwritten=e,this.color=t}}class iB{constructor(e=!1,t=new eo){this.isOverwritten=e,this.color=t}}class iI{constructor(e=!1,t=!1){this.isOverwritten=e,this.isCulling=t}}class iT{update(){this._model.update(),this._model.drawables.resetDynamicFlags()}getPixelsPerUnit(){return null==this._model?0:this._model.canvasinfo.PixelsPerUnit}getCanvasWidth(){return null==this._model?0:this._model.canvasinfo.CanvasWidth/this._model.canvasinfo.PixelsPerUnit}getCanvasHeight(){return null==this._model?0:this._model.canvasinfo.CanvasHeight/this._model.canvasinfo.PixelsPerUnit}saveParameters(){let e=this._model.parameters.count,t=this._savedParameters.getSize();for(let i=0;i<e;++i)i<t?this._savedParameters.set(i,this._parameterValues[i]):this._savedParameters.pushBack(this._parameterValues[i])}getMultiplyColor(e){return this.getOverwriteFlagForModelMultiplyColors()||this.getOverwriteFlagForDrawableMultiplyColors(e)?this._userMultiplyColors.at(e).color:this.getDrawableMultiplyColor(e)}getScreenColor(e){return this.getOverwriteFlagForModelScreenColors()||this.getOverwriteFlagForDrawableScreenColors(e)?this._userScreenColors.at(e).color:this.getDrawableScreenColor(e)}setMultiplyColorByTextureColor(e,t){this.setMultiplyColorByRGBA(e,t.r,t.g,t.b,t.a)}setMultiplyColorByRGBA(e,t,i,s){let r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1;this._userMultiplyColors.at(e).color.r=t,this._userMultiplyColors.at(e).color.g=i,this._userMultiplyColors.at(e).color.b=s,this._userMultiplyColors.at(e).color.a=r}setScreenColorByTextureColor(e,t){this.setScreenColorByRGBA(e,t.r,t.g,t.b,t.a)}setScreenColorByRGBA(e,t,i,s){let r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1;this._userScreenColors.at(e).color.r=t,this._userScreenColors.at(e).color.g=i,this._userScreenColors.at(e).color.b=s,this._userScreenColors.at(e).color.a=r}getPartMultiplyColor(e){return this._userPartMultiplyColors.at(e).color}getPartScreenColor(e){return this._userPartScreenColors.at(e).color}setPartColor(e,t,i,s,r,a,n){if(a.at(e).color.r=t,a.at(e).color.g=i,a.at(e).color.b=s,a.at(e).color.a=r,a.at(e).isOverwritten)for(let a=0;a<this._partChildDrawables.at(e).getSize();++a){let l=this._partChildDrawables.at(e).at(a);n.at(l).color.r=t,n.at(l).color.g=i,n.at(l).color.b=s,n.at(l).color.a=r}}setPartMultiplyColorByTextureColor(e,t){this.setPartMultiplyColorByRGBA(e,t.r,t.g,t.b,t.a)}setPartMultiplyColorByRGBA(e,t,i,s,r){this.setPartColor(e,t,i,s,r,this._userPartMultiplyColors,this._userMultiplyColors)}setPartScreenColorByTextureColor(e,t){this.setPartScreenColorByRGBA(e,t.r,t.g,t.b,t.a)}setPartScreenColorByRGBA(e,t,i,s,r){this.setPartColor(e,t,i,s,r,this._userPartScreenColors,this._userScreenColors)}getOverwriteFlagForModelMultiplyColors(){return this._isOverwrittenModelMultiplyColors}getOverwriteFlagForModelScreenColors(){return this._isOverwrittenModelScreenColors}setOverwriteFlagForModelMultiplyColors(e){this._isOverwrittenModelMultiplyColors=e}setOverwriteFlagForModelScreenColors(e){this._isOverwrittenModelScreenColors=e}getOverwriteFlagForDrawableMultiplyColors(e){return this._userMultiplyColors.at(e).isOverwritten}getOverwriteFlagForDrawableScreenColors(e){return this._userScreenColors.at(e).isOverwritten}setOverwriteFlagForDrawableMultiplyColors(e,t){this._userMultiplyColors.at(e).isOverwritten=t}setOverwriteFlagForDrawableScreenColors(e,t){this._userScreenColors.at(e).isOverwritten=t}getOverwriteColorForPartMultiplyColors(e){return this._userPartMultiplyColors.at(e).isOverwritten}getOverwriteColorForPartScreenColors(e){return this._userPartScreenColors.at(e).isOverwritten}setOverwriteColorForPartColors(e,t,i,s){i.at(e).isOverwritten=t;for(let r=0;r<this._partChildDrawables.at(e).getSize();++r){let a=this._partChildDrawables.at(e).at(r);s.at(a).isOverwritten=t,t&&(s.at(a).color.r=i.at(e).color.r,s.at(a).color.g=i.at(e).color.g,s.at(a).color.b=i.at(e).color.b,s.at(a).color.a=i.at(e).color.a)}}setOverwriteColorForPartMultiplyColors(e,t){this._userPartMultiplyColors.at(e).isOverwritten=t,this.setOverwriteColorForPartColors(e,t,this._userPartMultiplyColors,this._userMultiplyColors)}setOverwriteColorForPartScreenColors(e,t){this._userPartScreenColors.at(e).isOverwritten=t,this.setOverwriteColorForPartColors(e,t,this._userPartScreenColors,this._userScreenColors)}getDrawableCulling(e){if(this.getOverwriteFlagForModelCullings()||this.getOverwriteFlagForDrawableCullings(e))return this._userCullings.at(e).isCulling;let t=this._model.drawables.constantFlags;return!Live2DCubismCore.Utils.hasIsDoubleSidedBit(t[e])}setDrawableCulling(e,t){this._userCullings.at(e).isCulling=t}getOverwriteFlagForModelCullings(){return this._isOverwrittenCullings}setOverwriteFlagForModelCullings(e){this._isOverwrittenCullings=e}getOverwriteFlagForDrawableCullings(e){return this._userCullings.at(e).isOverwritten}setOverwriteFlagForDrawableCullings(e,t){this._userCullings.at(e).isOverwritten=t}getModelOapcity(){return this._modelOpacity}setModelOapcity(e){this._modelOpacity=e}getModel(){return this._model}getPartIndex(e){let t;let i=this._model.parts.count;for(t=0;t<i;++t)if(e==this._partIds.at(t))return t;return this._notExistPartId.isExist(e)?this._notExistPartId.getValue(e):(t=i+this._notExistPartId.getSize(),this._notExistPartId.setValue(e,t),this._notExistPartOpacities.appendKey(t),t)}getPartId(e){let t=this._model.parts.ids[e];return eF.getIdManager().getId(t)}getPartCount(){return this._model.parts.count}setPartOpacityByIndex(e,t){if(this._notExistPartOpacities.isExist(e)){this._notExistPartOpacities.setValue(e,t);return}ed(0<=e&&e<this.getPartCount()),this._partOpacities[e]=t}setPartOpacityById(e,t){let i=this.getPartIndex(e);i<0||this.setPartOpacityByIndex(i,t)}getPartOpacityByIndex(e){return this._notExistPartOpacities.isExist(e)?this._notExistPartOpacities.getValue(e):(ed(0<=e&&e<this.getPartCount()),this._partOpacities[e])}getPartOpacityById(e){let t=this.getPartIndex(e);return t<0?0:this.getPartOpacityByIndex(t)}getParameterIndex(e){let t;let i=this._model.parameters.count;for(t=0;t<i;++t)if(e==this._parameterIds.at(t))return t;return this._notExistParameterId.isExist(e)?this._notExistParameterId.getValue(e):(t=this._model.parameters.count+this._notExistParameterId.getSize(),this._notExistParameterId.setValue(e,t),this._notExistParameterValues.appendKey(t),t)}getParameterCount(){return this._model.parameters.count}getParameterType(e){return this._model.parameters.types[e]}getParameterMaximumValue(e){return this._model.parameters.maximumValues[e]}getParameterMinimumValue(e){return this._model.parameters.minimumValues[e]}getParameterDefaultValue(e){return this._model.parameters.defaultValues[e]}getParameterId(e){return eF.getIdManager().getId(this._model.parameters.ids[e])}getParameterValueByIndex(e){return this._notExistParameterValues.isExist(e)?this._notExistParameterValues.getValue(e):(ed(0<=e&&e<this.getParameterCount()),this._parameterValues[e])}getParameterValueById(e){let t=this.getParameterIndex(e);return this.getParameterValueByIndex(t)}setParameterValueByIndex(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;if(this._notExistParameterValues.isExist(e)){this._notExistParameterValues.setValue(e,1==i?t:this._notExistParameterValues.getValue(e)*(1-i)+t*i);return}ed(0<=e&&e<this.getParameterCount()),this._model.parameters.maximumValues[e]<t&&(t=this._model.parameters.maximumValues[e]),this._model.parameters.minimumValues[e]>t&&(t=this._model.parameters.minimumValues[e]),this._parameterValues[e]=1==i?t:this._parameterValues[e]=this._parameterValues[e]*(1-i)+t*i}setParameterValueById(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,s=this.getParameterIndex(e);this.setParameterValueByIndex(s,t,i)}addParameterValueByIndex(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;this.setParameterValueByIndex(e,this.getParameterValueByIndex(e)+t*i)}addParameterValueById(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,s=this.getParameterIndex(e);this.addParameterValueByIndex(s,t,i)}multiplyParameterValueById(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,s=this.getParameterIndex(e);this.multiplyParameterValueByIndex(s,t,i)}multiplyParameterValueByIndex(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;this.setParameterValueByIndex(e,this.getParameterValueByIndex(e)*(1+(t-1)*i))}getDrawableIndex(e){let t=this._model.drawables.count;for(let i=0;i<t;++i)if(this._drawableIds.at(i)==e)return i;return -1}getDrawableCount(){return this._model.drawables.count}getDrawableId(e){let t=this._model.drawables.ids;return eF.getIdManager().getId(t[e])}getDrawableRenderOrders(){return this._model.drawables.renderOrders}getDrawableTextureIndices(e){return this.getDrawableTextureIndex(e)}getDrawableTextureIndex(e){return this._model.drawables.textureIndices[e]}getDrawableDynamicFlagVertexPositionsDidChange(e){let t=this._model.drawables.dynamicFlags;return Live2DCubismCore.Utils.hasVertexPositionsDidChangeBit(t[e])}getDrawableVertexIndexCount(e){return this._model.drawables.indexCounts[e]}getDrawableVertexCount(e){return this._model.drawables.vertexCounts[e]}getDrawableVertices(e){return this.getDrawableVertexPositions(e)}getDrawableVertexIndices(e){return this._model.drawables.indices[e]}getDrawableVertexPositions(e){return this._model.drawables.vertexPositions[e]}getDrawableVertexUvs(e){return this._model.drawables.vertexUvs[e]}getDrawableOpacity(e){return this._model.drawables.opacities[e]}getDrawableMultiplyColor(e){let t=this._model.drawables.multiplyColors,i=4*e,s=new eo;return s.r=t[i],s.g=t[i+1],s.b=t[i+2],s.a=t[i+3],s}getDrawableScreenColor(e){let t=this._model.drawables.screenColors,i=4*e,s=new eo;return s.r=t[i],s.g=t[i+1],s.b=t[i+2],s.a=t[i+3],s}getDrawableParentPartIndex(e){return this._model.drawables.parentPartIndices[e]}getDrawableBlendMode(e){let t=this._model.drawables.constantFlags;return Live2DCubismCore.Utils.hasBlendAdditiveBit(t[e])?el.CubismBlendMode_Additive:Live2DCubismCore.Utils.hasBlendMultiplicativeBit(t[e])?el.CubismBlendMode_Multiplicative:el.CubismBlendMode_Normal}getDrawableInvertedMaskBit(e){let t=this._model.drawables.constantFlags;return Live2DCubismCore.Utils.hasIsInvertedMaskBit(t[e])}getDrawableMasks(){return this._model.drawables.masks}getDrawableMaskCounts(){return this._model.drawables.maskCounts}isUsingMasking(){for(let e=0;e<this._model.drawables.count;++e)if(!(this._model.drawables.maskCounts[e]<=0))return!0;return!1}getDrawableDynamicFlagIsVisible(e){let t=this._model.drawables.dynamicFlags;return Live2DCubismCore.Utils.hasIsVisibleBit(t[e])}getDrawableDynamicFlagVisibilityDidChange(e){let t=this._model.drawables.dynamicFlags;return Live2DCubismCore.Utils.hasVisibilityDidChangeBit(t[e])}getDrawableDynamicFlagOpacityDidChange(e){let t=this._model.drawables.dynamicFlags;return Live2DCubismCore.Utils.hasOpacityDidChangeBit(t[e])}getDrawableDynamicFlagRenderOrderDidChange(e){let t=this._model.drawables.dynamicFlags;return Live2DCubismCore.Utils.hasRenderOrderDidChangeBit(t[e])}getDrawableDynamicFlagBlendColorDidChange(e){let t=this._model.drawables.dynamicFlags;return Live2DCubismCore.Utils.hasBlendColorDidChangeBit(t[e])}loadParameters(){let e=this._model.parameters.count,t=this._savedParameters.getSize();e>t&&(e=t);for(let t=0;t<e;++t)this._parameterValues[t]=this._savedParameters.at(t)}initialize(){ed(this._model),this._parameterValues=this._model.parameters.values,this._partOpacities=this._model.parts.opacities,this._parameterMaximumValues=this._model.parameters.maximumValues,this._parameterMinimumValues=this._model.parameters.minimumValues;{let e=this._model.parameters.ids,t=this._model.parameters.count;this._parameterIds.prepareCapacity(t);for(let i=0;i<t;++i)this._parameterIds.pushBack(eF.getIdManager().getId(e[i]))}let e=this._model.parts.count;{let t=this._model.parts.ids;this._partIds.prepareCapacity(e);for(let i=0;i<e;++i)this._partIds.pushBack(eF.getIdManager().getId(t[i]));this._userPartMultiplyColors.prepareCapacity(e),this._userPartScreenColors.prepareCapacity(e),this._partChildDrawables.prepareCapacity(e)}{let t=this._model.drawables.ids,i=this._model.drawables.count;this._userMultiplyColors.prepareCapacity(i),this._userScreenColors.prepareCapacity(i),this._userCullings.prepareCapacity(i);let s=new iI(!1,!1);for(let t=0;t<e;++t){let e=new eo(1,1,1,1),s=new eo(0,0,0,1),r=new iB(!1,e),a=new iB(!1,s);this._userPartMultiplyColors.pushBack(r),this._userPartScreenColors.pushBack(a),this._partChildDrawables.pushBack(new $),this._partChildDrawables.at(t).prepareCapacity(i)}for(let e=0;e<i;++e){let i=new eo(1,1,1,1),r=new eo(0,0,0,1),a=new iw(!1,i),n=new iw(!1,r);this._drawableIds.pushBack(eF.getIdManager().getId(t[e])),this._userMultiplyColors.pushBack(a),this._userScreenColors.pushBack(n),this._userCullings.pushBack(s);let l=this.getDrawableParentPartIndex(e);l>=0&&this._partChildDrawables.at(l).pushBack(e)}}}release(){this._model.release(),this._model=null}constructor(e){this._model=e,this._parameterValues=null,this._parameterMaximumValues=null,this._parameterMinimumValues=null,this._partOpacities=null,this._savedParameters=new $,this._parameterIds=new $,this._drawableIds=new $,this._partIds=new $,this._isOverwrittenModelMultiplyColors=!1,this._isOverwrittenModelScreenColors=!1,this._isOverwrittenCullings=!1,this._modelOpacity=1,this._userMultiplyColors=new $,this._userScreenColors=new $,this._userCullings=new $,this._userPartMultiplyColors=new $,this._userPartScreenColors=new $,this._partChildDrawables=new $,this._notExistPartId=new em,this._notExistParameterId=new em,this._notExistParameterValues=new em,this._notExistPartOpacities=new em}}(Y||(Y={})).CubismModel=iT;class iE{static create(e,t){let i=null;if(t&&!this.hasMocConsistency(e))return n("Inconsistent MOC3."),i;let s=Live2DCubismCore.Moc.fromArrayBuffer(e);return s&&((i=new iE(s))._mocVersion=Live2DCubismCore.Version.csmGetMocVersion(s,e)),i}static delete(e){e._moc._release(),e._moc=null,e=null}createModel(){let e=null,t=Live2DCubismCore.Model.fromMoc(this._moc);return t&&((e=new iT(t)).initialize(),++this._modelCount),e}deleteModel(e){null!=e&&(e.release(),e=null,--this._modelCount)}release(){ed(0==this._modelCount),this._moc._release(),this._moc=null}getLatestMocVersion(){return Live2DCubismCore.Version.csmGetLatestMocVersion()}getMocVersion(){return this._mocVersion}static hasMocConsistency(e){return 1===Live2DCubismCore.Moc.prototype.hasMocConsistency(e)}constructor(e){this._moc=e,this._modelCount=0,this._mocVersion=0}}(H||(H={})).CubismMoc=iE;let iR="Meta",iV="UserData";class iA{release(){eS.delete(this._json)}getUserDataCount(){return this._json.getRoot().getValueByString(iR).getValueByString("UserDataCount").toInt()}getTotalUserDataSize(){return this._json.getRoot().getValueByString(iR).getValueByString("TotalUserDataSize").toInt()}getUserDataTargetType(e){return this._json.getRoot().getValueByString(iV).getValueByIndex(e).getValueByString("Target").getRawString()}getUserDataId(e){return eF.getIdManager().getId(this._json.getRoot().getValueByString(iV).getValueByIndex(e).getValueByString("Id").getRawString())}getUserDataValue(e){return this._json.getRoot().getValueByString(iV).getValueByIndex(e).getValueByString("Value").getRawString()}constructor(e,t){this._json=eS.create(e,t)}}(W||(W={})).CubismModelUserDataJson=iA;class iF{}class iL{static create(e,t){let i=new iL;return i.parseUserData(e,t),i}static delete(e){null!=e&&(e.release(),e=null)}getArtMeshUserDatas(){return this._artMeshUserDataNode}parseUserData(e,t){let i=new iA(e,t);if(!i){i.release(),i=void 0;return}let s=eF.getIdManager().getId("ArtMesh"),r=i.getUserDataCount();for(let e=0;e<r;e++){let t=new iF;t.targetId=i.getUserDataId(e),t.targetType=eF.getIdManager().getId(i.getUserDataTargetType(e)),t.value=new et(i.getUserDataValue(e)),this._userDataNodes.pushBack(t),t.targetType==s&&this._artMeshUserDataNode.pushBack(t)}i.release()}release(){for(let e=0;e<this._userDataNodes.getSize();++e)this._userDataNodes.set(e,null);this._userDataNodes=null}constructor(){this._userDataNodes=new $,this._artMeshUserDataNode=new $}}!function(e){e.CubismModelUserData=iL,e.CubismModelUserDataNode=iF}(q||(q={}));class iD{isInitialized(){return this._initialized}setInitialized(e){this._initialized=e}isUpdating(){return this._updating}setUpdating(e){this._updating=e}setDragging(e,t){this._dragManager.set(e,t)}setAcceleration(e,t,i){this._accelerationX=e,this._accelerationY=t,this._accelerationZ=i}getModelMatrix(){return this._modelMatrix}setOpacity(e){this._opacity=e}getOpacity(){return this._opacity}loadModel(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this._moc=iE.create(e,t),null==this._moc){n("Failed to CubismMoc.create().");return}if(this._model=this._moc.createModel(),null==this._model){n("Failed to CreateModel().");return}this._model.saveParameters(),this._modelMatrix=new eQ(this._model.getCanvasWidth(),this._model.getCanvasHeight())}loadMotion(e,t,i,s,r,a,l,o){if(null==e||0==t)return n("Failed to loadMotion()."),null;let u=tw.create(e,t,s,r);if(null==u)return n("Failed to create motion from buffer in LoadMotion()"),null;if(a){let e=a.getMotionFadeInTimeValue(l,o);e>=0&&u.setFadeInTime(e);let t=a.getMotionFadeOutTimeValue(l,o);t>=0&&u.setFadeOutTime(t)}return u}loadExpression(e,t,i){return null==e||0==t?(n("Failed to loadExpression()."),null):e5.create(e,t)}loadPose(e,t){if(null==e||0==t){n("Failed to loadPose().");return}this._pose=eZ.create(e,t)}loadUserData(e,t){if(null==e||0==t){n("Failed to loadUserData().");return}this._modelUserData=iL.create(e,t)}loadPhysics(e,t){if(null==e||0==t){n("Failed to loadPhysics().");return}this._physics=t3.create(e,t)}isHit(e,t,i){let s=this._model.getDrawableIndex(e);if(s<0)return!1;let r=this._model.getDrawableVertexCount(s),a=this._model.getDrawableVertices(s),n=a[0],l=a[0],o=a[1],u=a[1];for(let e=1;e<r;++e){let t=a[eV.vertexOffset+e*eV.vertexStep],i=a[eV.vertexOffset+e*eV.vertexStep+1];t<n&&(n=t),t>l&&(l=t),i<o&&(o=i),i>u&&(u=i)}let h=this._modelMatrix.invertTransformX(t),g=this._modelMatrix.invertTransformY(i);return n<=h&&h<=l&&o<=g&&g<=u}getModel(){return this._model}getRenderer(){return this._renderer}createRenderer(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this._renderer&&this.deleteRenderer(),this._renderer=new iP,this._renderer.initialize(this._model,e)}deleteRenderer(){null!=this._renderer&&(this._renderer.release(),this._renderer=null)}motionEventFired(e){r("{0}",e.s)}static cubismDefaultMotionEventCallback(e,t,i){null!=i&&i.motionEventFired(t)}release(){null!=this._motionManager&&(this._motionManager.release(),this._motionManager=null),null!=this._expressionManager&&(this._expressionManager.release(),this._expressionManager=null),null!=this._moc&&(this._moc.deleteModel(this._model),this._moc.release(),this._moc=null),this._modelMatrix=null,eZ.delete(this._pose),eH.delete(this._eyeBlink),eX.delete(this._breath),this._dragManager=null,t3.delete(this._physics),iL.delete(this._modelUserData),this.deleteRenderer()}constructor(){this._moc=null,this._model=null,this._motionManager=null,this._expressionManager=null,this._eyeBlink=null,this._breath=null,this._modelMatrix=null,this._pose=null,this._dragManager=null,this._physics=null,this._modelUserData=null,this._initialized=!1,this._updating=!1,this._opacity=1,this._lipsync=!0,this._lastLipSyncValue=0,this._dragX=0,this._dragY=0,this._accelerationX=0,this._accelerationY=0,this._accelerationZ=0,this._mocConsistency=!1,this._debugMode=!1,this._renderer=null,this._motionManager=new tB,this._motionManager.setEventCallback(iD.cubismDefaultMotionEventCallback,this),this._expressionManager=new ti,this._dragManager=new e1}}(J||(J={})).CubismUserModel=iD;let ik=null;class iN{static getInstance(){return null==ik&&(ik=new iN),ik}static releaseInstance(){null!=ik&&(ik=void 0),ik=null}update(e){let t,i;if(null==this._pcmData||this._sampleOffset>=this._wavFileInfo._samplesPerChannel)return this._lastRms=0,!1;this._userTimeSeconds+=e,(t=Math.floor(this._userTimeSeconds*this._wavFileInfo._samplingRate))>this._wavFileInfo._samplesPerChannel&&(t=this._wavFileInfo._samplesPerChannel),i=0;for(let e=0;e<this._wavFileInfo._numberOfChannels;e++)for(let s=this._sampleOffset;s<t;s++){let t=this._pcmData[e][s];i+=t*t}return i=Math.sqrt(i/(this._wavFileInfo._numberOfChannels*(t-this._sampleOffset))),this._lastRms=i,this._sampleOffset=t,!0}start(e){this._sampleOffset=0,this._userTimeSeconds=0,this._lastRms=0,this.loadWavFile(e)}getRms(){return this._lastRms}loadWavFile(e){return new Promise(t=>{let i=!1;null!=this._pcmData&&this.releasePcmData();let s=async()=>e instanceof ArrayBuffer?e:(await fetch(e)).arrayBuffer();(async()=>{if(this._byteReader._fileByte=await s(),this._byteReader._fileDataView=new DataView(this._byteReader._fileByte),this._byteReader._fileSize=this._byteReader._fileByte.byteLength,this._byteReader._readOffset=0,null==this._byteReader._fileByte||this._byteReader._fileSize<4){t(!1);return}this._wavFileInfo._fileName=e instanceof ArrayBuffer?"":e;try{if(!this._byteReader.getCheckSignature("RIFF"))throw i=!1,Error('Cannot find Signeture "RIFF".');if(this._byteReader.get32LittleEndian(),!this._byteReader.getCheckSignature("WAVE"))throw i=!1,Error('Cannot find Signeture "WAVE".');if(!this._byteReader.getCheckSignature("fmt "))throw i=!1,Error('Cannot find Signeture "fmt".');let e=this._byteReader.get32LittleEndian();if(1!=this._byteReader.get16LittleEndian())throw i=!1,Error("File is not linear PCM.");for(this._wavFileInfo._numberOfChannels=this._byteReader.get16LittleEndian(),this._wavFileInfo._samplingRate=this._byteReader.get32LittleEndian(),this._byteReader.get32LittleEndian(),this._byteReader.get16LittleEndian(),this._wavFileInfo._bitsPerSample=this._byteReader.get16LittleEndian(),e>16&&(this._byteReader._readOffset+=e-16);!this._byteReader.getCheckSignature("data")&&this._byteReader._readOffset<this._byteReader._fileSize;)this._byteReader._readOffset+=this._byteReader.get32LittleEndian()+4;if(this._byteReader._readOffset>=this._byteReader._fileSize)throw i=!1,Error('Cannot find "data" Chunk.');{let e=this._byteReader.get32LittleEndian();this._wavFileInfo._samplesPerChannel=8*e/(this._wavFileInfo._bitsPerSample*this._wavFileInfo._numberOfChannels)}this._pcmData=Array(this._wavFileInfo._numberOfChannels);for(let e=0;e<this._wavFileInfo._numberOfChannels;e++)this._pcmData[e]=new Float32Array(this._wavFileInfo._samplesPerChannel);for(let e=0;e<this._wavFileInfo._samplesPerChannel;e++)for(let t=0;t<this._wavFileInfo._numberOfChannels;t++)this._pcmData[t][e]=this.getPcmSample();i=!0,t(i)}catch(e){console.log(e)}})().then(()=>{t(i)})})}getPcmSample(){let e;switch(this._wavFileInfo._bitsPerSample){case 8:e=this._byteReader.get8()-128<<24;break;case 16:e=this._byteReader.get16LittleEndian()<<16;break;case 24:e=this._byteReader.get24LittleEndian()<<8;break;default:e=0}return e/0x7fffffff}getPcmDataChannel(e){return this._pcmData&&e<this._pcmData.length?Float32Array.from(this._pcmData[e]):null}getWavSamplingRate(){return!this._wavFileInfo||this._wavFileInfo._samplingRate<1?null:this._wavFileInfo._samplingRate}releasePcmData(){for(let e=0;e<this._wavFileInfo._numberOfChannels;e++)this._pcmData[e]=null;delete this._pcmData,this._pcmData=null}constructor(){this.loadFiletoBytes=(e,t)=>{this._byteReader._fileByte=e,this._byteReader._fileDataView=new DataView(this._byteReader._fileByte),this._byteReader._fileSize=t},this._pcmData=null,this._userTimeSeconds=0,this._lastRms=0,this._sampleOffset=0,this._wavFileInfo=new iO,this._byteReader=new ij}}class iO{constructor(){this._fileName="",this._numberOfChannels=0,this._bitsPerSample=0,this._samplingRate=0,this._samplesPerChannel=0}}class ij{get8(){let e=this._fileDataView.getUint8(this._readOffset);return this._readOffset++,e}get16LittleEndian(){let e=this._fileDataView.getUint8(this._readOffset+1)<<8|this._fileDataView.getUint8(this._readOffset);return this._readOffset+=2,e}get24LittleEndian(){let e=this._fileDataView.getUint8(this._readOffset+2)<<16|this._fileDataView.getUint8(this._readOffset+1)<<8|this._fileDataView.getUint8(this._readOffset);return this._readOffset+=3,e}get32LittleEndian(){let e=this._fileDataView.getUint8(this._readOffset+3)<<24|this._fileDataView.getUint8(this._readOffset+2)<<16|this._fileDataView.getUint8(this._readOffset+1)<<8|this._fileDataView.getUint8(this._readOffset);return this._readOffset+=4,e}getCheckSignature(e){let t=new Uint8Array(4),i=new TextEncoder().encode(e);if(4!=e.length)return!1;for(let e=0;e<4;e++)t[e]=this.get8();return t[0]==i[0]&&t[1]==i[1]&&t[2]==i[2]&&t[3]==i[3]}constructor(){this._fileByte=null,this._fileDataView=null,this._fileSize=0,this._readOffset=0}}class iU{static getInstance(){return this._instance||(this._instance=new iU),this._instance}setReady(e){this._ready=e}isReady(){return this._ready}changeCharacter(e){this._ready=!1,i$.getInstance().changeCharacter(e)}setLipFactor(e){this._lipFactor=e}getLipFactor(){return this._lipFactor}pushAudioQueue(e){this._ttsQueue.push(e)}popAudioQueue(){return this._ttsQueue.length>0?this._ttsQueue.shift():null}clearAudioQueue(){this._ttsQueue=[]}playAudio(){if(this._audioIsPlaying)return null;let e=this.popAudioQueue();if(null==e)return null;this._audioIsPlaying=!0;let t=e=>{var t=this._audioContext.createBufferSource();t.buffer=e,t.connect(this._audioContext.destination),t.onended=()=>{this._audioIsPlaying=!1},t.start(),this._audioSource=t},i=e.slice(0);return this._audioContext.decodeAudioData(i).then(e=>{t(e)}),e}stopAudio(){this.clearAudioQueue(),this._audioSource&&(this._audioSource.stop(),this._audioSource=null),this._audioIsPlaying=!1}isAudioPlaying(){return this._audioIsPlaying}constructor(){this._ttsQueue=[],this._audioContext=new(window.AudioContext||window.webkitAudioContext),this._audioIsPlaying=!1,this._audioSource=null,this._lipFactor=1,this._ready=!1}}var iz=function(e){return e[e.LoadAssets=0]="LoadAssets",e[e.LoadModel=1]="LoadModel",e[e.WaitLoadModel=2]="WaitLoadModel",e[e.LoadExpression=3]="LoadExpression",e[e.WaitLoadExpression=4]="WaitLoadExpression",e[e.LoadPhysics=5]="LoadPhysics",e[e.WaitLoadPhysics=6]="WaitLoadPhysics",e[e.LoadPose=7]="LoadPose",e[e.WaitLoadPose=8]="WaitLoadPose",e[e.SetupEyeBlink=9]="SetupEyeBlink",e[e.SetupBreath=10]="SetupBreath",e[e.LoadUserData=11]="LoadUserData",e[e.WaitLoadUserData=12]="WaitLoadUserData",e[e.SetupEyeBlinkIds=13]="SetupEyeBlinkIds",e[e.SetupLipSyncIds=14]="SetupLipSyncIds",e[e.SetupLayout=15]="SetupLayout",e[e.LoadMotion=16]="LoadMotion",e[e.WaitLoadMotion=17]="WaitLoadMotion",e[e.CompleteInitialize=18]="CompleteInitialize",e[e.CompleteSetupModel=19]="CompleteSetupModel",e[e.LoadTexture=20]="LoadTexture",e[e.WaitLoadTexture=21]="WaitLoadTexture",e[e.CompleteSetup=22]="CompleteSetup",e}(iz||{});class iG extends iD{loadAssets(e,t){this._modelHomeDir=e,iU.getInstance().setReady(!1),fetch("".concat(this._modelHomeDir).concat(t)).then(e=>e.arrayBuffer()).then(e=>{let t=new eG(e,e.byteLength);this._state=1,this.setupModel(t)}).catch(e=>{n("Failed to load file ".concat(this._modelHomeDir).concat(t))})}setupModel(e){if(this._updating=!0,this._initialized=!1,this._modelSetting=e,""!=this._modelSetting.getModelFileName()){let e=this._modelSetting.getModelFileName();fetch("".concat(this._modelHomeDir).concat(e)).then(t=>t.ok?t.arrayBuffer():t.status>=400?(n("Failed to load file ".concat(this._modelHomeDir).concat(e)),new ArrayBuffer(0)):void 0).then(e=>{this.loadModel(e,this._mocConsistency),this._state=3,t()}),this._state=2}else eN.printMessage("Model data does not exist.");let t=()=>{if(this._modelSetting.getExpressionCount()>0){let e=this._modelSetting.getExpressionCount();for(let t=0;t<e;t++){let s=this._modelSetting.getExpressionName(t),r=this._modelSetting.getExpressionFileName(t);fetch("".concat(this._modelHomeDir).concat(r)).then(e=>e.ok?e.arrayBuffer():e.status>=400?(n("Failed to load file ".concat(this._modelHomeDir).concat(r)),new ArrayBuffer(0)):void 0).then(t=>{let r=this.loadExpression(t,t.byteLength,s);null!=this._expressions.getValue(s)&&(e2.delete(this._expressions.getValue(s)),this._expressions.setValue(s,null)),this._expressions.setValue(s,r),this._expressionCount++,this._expressionCount>=e&&(this._state=5,i())})}this._state=4}else this._state=5,i()},i=()=>{if(""!=this._modelSetting.getPhysicsFileName()){let e=this._modelSetting.getPhysicsFileName();fetch("".concat(this._modelHomeDir).concat(e)).then(t=>t.ok?t.arrayBuffer():t.status>=400?(n("Failed to load file ".concat(this._modelHomeDir).concat(e)),new ArrayBuffer(0)):void 0).then(e=>{this.loadPhysics(e,e.byteLength),this._state=7,s()}),this._state=6}else this._state=7,s()},s=()=>{if(""!=this._modelSetting.getPoseFileName()){let e=this._modelSetting.getPoseFileName();fetch("".concat(this._modelHomeDir).concat(e)).then(t=>t.ok?t.arrayBuffer():t.status>=400?(n("Failed to load file ".concat(this._modelHomeDir).concat(e)),new ArrayBuffer(0)):void 0).then(e=>{this.loadPose(e,e.byteLength),this._state=9,r()}),this._state=8}else this._state=9,r()},r=()=>{this._modelSetting.getEyeBlinkParameterCount()>0&&(this._eyeBlink=eH.create(this._modelSetting),this._state=10),a()},a=()=>{this._breath=eX.create();let e=new $;e.pushBack(new eY(this._idParamAngleX,0,15,6.5345,.5)),e.pushBack(new eY(this._idParamAngleY,0,8,3.5345,.5)),e.pushBack(new eY(this._idParamAngleZ,0,10,5.5345,.5)),e.pushBack(new eY(this._idParamBodyAngleX,0,4,15.5345,.5)),e.pushBack(new eY(eF.getIdManager().getId(ej.ParamBreath),.5,.5,3.2345,1)),this._breath.setParameters(e),this._state=11,l()},l=()=>{if(""!=this._modelSetting.getUserDataFile()){let e=this._modelSetting.getUserDataFile();fetch("".concat(this._modelHomeDir).concat(e)).then(t=>t.ok?t.arrayBuffer():t.status>=400?(n("Failed to load file ".concat(this._modelHomeDir).concat(e)),new ArrayBuffer(0)):void 0).then(e=>{this.loadUserData(e,e.byteLength),this._state=13,o()}),this._state=12}else this._state=13,o()},o=()=>{let e=this._modelSetting.getEyeBlinkParameterCount();for(let t=0;t<e;++t)this._eyeBlinkIds.pushBack(this._modelSetting.getEyeBlinkParameterId(t));this._state=14,u()},u=()=>{let e=this._modelSetting.getLipSyncParameterCount();for(let t=0;t<e;++t)this._lipSyncIds.pushBack(this._modelSetting.getLipSyncParameterId(t));this._state=15,h()},h=()=>{let e=new em;if(null==this._modelSetting||null==this._modelMatrix){n("Failed to setupLayout().");return}this._modelSetting.getLayoutMap(e),this._modelMatrix.setupFromLayout(e),this._state=16,g()},g=()=>{this._state=17,this._model.saveParameters(),this._allMotionCount=0,this._motionCount=0;let e=[],t=this._modelSetting.getMotionGroupCount();for(let i=0;i<t;i++)e[i]=this._modelSetting.getMotionGroupName(i),this._allMotionCount+=this._modelSetting.getMotionCount(e[i]);for(let i=0;i<t;i++)this.preLoadMotionGroup(e[i]);0==t&&(this._state=20,this._motionManager.stopAllMotions(),this._updating=!1,this._initialized=!0,this.createRenderer(),this.setupTextures(),this.getRenderer().startUp(this._subdelegate.getGlManager().getGl()))}}setupTextures(){if(20==this._state){let e=this._modelSetting.getTextureCount();for(let t=0;t<e;t++){if(""==this._modelSetting.getTextureFileName(t))continue;let i=this._modelSetting.getTextureFileName(t);i=this._modelHomeDir+i;let s=i=>{this.getRenderer().bindTexture(t,i.id),this._textureCount++,this._textureCount>=e&&(this._state=22)};this._subdelegate.getTextureManager().createTextureFromPngFile(i,!0,s),this.getRenderer().setIsPremultipliedAlpha(!0)}this._state=21}iU.getInstance().setReady(!0)}reloadRenderer(){this.deleteRenderer(),this.createRenderer(),this.setupTextures()}update(){if(22!=this._state)return;let e=eN.getDeltaTime();this._userTimeSeconds+=e,this._dragManager.update(e),this._dragX=this._dragManager.getX(),this._dragY=this._dragManager.getY();let t=!1;if(this._model.loadParameters(),this._motionManager.isFinished()?this.startRandomMotion("Idle",1):t=this._motionManager.updateMotion(this._model,e),this._model.saveParameters(),t||null==this._eyeBlink||this._eyeBlink.updateParameters(this._model,e),null!=this._expressionManager&&this._expressionManager.updateMotion(this._model,e),this._model.addParameterValueById(this._idParamAngleX,30*this._dragX),this._model.addParameterValueById(this._idParamAngleY,30*this._dragY),this._model.addParameterValueById(this._idParamAngleZ,-(this._dragX*this._dragY*30)),this._model.addParameterValueById(this._idParamBodyAngleX,10*this._dragX),this._model.addParameterValueById(this._idParamEyeBallX,this._dragX),this._model.addParameterValueById(this._idParamEyeBallY,this._dragY),null!=this._breath&&this._breath.updateParameters(this._model,e),null!=this._physics&&this._physics.evaluate(this._model,e),this._lipsync){let t=0;this._wavFileHandler.update(e),t=this._wavFileHandler.getRms()*iU.getInstance().getLipFactor();let i=iU.getInstance().playAudio();null!=i&&this._wavFileHandler.start(i),iU.getInstance().isAudioPlaying()||(t=0);for(let e=0;e<this._lipSyncIds.getSize();++e)this._model.addParameterValueById(this._lipSyncIds.at(e),t,.8)}null!=this._pose&&this._pose.updateParameters(this._model,e),this._model.update()}startMotion(e,t,i,s,r){if(3==i)this._motionManager.setReservePriority(i);else if(!this._motionManager.reserveMotion(i))return this._debugMode&&eN.printMessage("[APP]can't start motion."),te;let a=this._modelSetting.getMotionFileName(e,t),l="".concat(e,"_").concat(t),o=this._motions.getValue(l),u=!1;return null==o?fetch("".concat(this._modelHomeDir).concat(a)).then(e=>e.ok?e.arrayBuffer():e.status>=400?(n("Failed to load file ".concat(this._modelHomeDir).concat(a)),new ArrayBuffer(0)):void 0).then(i=>{null!=(o=this.loadMotion(i,i.byteLength,null,s,r,this._modelSetting,e,t))&&(o.setEffectIds(this._eyeBlinkIds,this._lipSyncIds),u=!0)}):(o.setBeganMotionHandler(r),o.setFinishedMotionHandler(s)),this._debugMode&&eN.printMessage("[APP]start motion: [".concat(e,"_").concat(t)),this._motionManager.startMotionPriority(o,u,i)}startRandomMotion(e,t,i,s){if(0==this._modelSetting.getMotionCount(e))return te;let r=Math.floor(Math.random()*this._modelSetting.getMotionCount(e));return this.startMotion(e,r,t,i,s)}setExpression(e){let t=this._expressions.getValue(e);this._debugMode&&eN.printMessage("[APP]expression: [".concat(e,"]")),null!=t?this._expressionManager.startMotion(t,!1):this._debugMode&&eN.printMessage("[APP]expression[".concat(e,"] is null"))}setRandomExpression(){if(0==this._expressions.getSize())return;let e=Math.floor(Math.random()*this._expressions.getSize());for(let t=0;t<this._expressions.getSize();t++)if(t==e){let e=this._expressions._keyValues[t].first;this.setExpression(e);return}}motionEventFired(e){r("{0} is fired on LAppModel!!",e.s)}hitTest(e,t,i){if(this._opacity<1)return!1;let s=this._modelSetting.getHitAreasCount();for(let r=0;r<s;r++)if(this._modelSetting.getHitAreaName(r)==e){let e=this._modelSetting.getHitAreaId(r);return this.isHit(e,t,i)}return!1}preLoadMotionGroup(e){for(let t=0;t<this._modelSetting.getMotionCount(e);t++){let i=this._modelSetting.getMotionFileName(e,t),s="".concat(e,"_").concat(t);this._debugMode&&eN.printMessage("[APP]load motion: ".concat(i," => [").concat(s,"]")),fetch("".concat(this._modelHomeDir).concat(i)).then(e=>e.ok?e.arrayBuffer():e.status>=400?(n("Failed to load file ".concat(this._modelHomeDir).concat(i)),new ArrayBuffer(0)):void 0).then(i=>{let r=this.loadMotion(i,i.byteLength,s,null,null,this._modelSetting,e,t);null!=r?(r.setEffectIds(this._eyeBlinkIds,this._lipSyncIds),null!=this._motions.getValue(s)&&e2.delete(this._motions.getValue(s)),this._motions.setValue(s,r),this._motionCount++,this._motionCount>=this._allMotionCount&&(this._state=20,this._motionManager.stopAllMotions(),this._updating=!1,this._initialized=!0,this.createRenderer(),this.setupTextures(),this.getRenderer().startUp(this._subdelegate.getGlManager().getGl()))):this._allMotionCount--})}}releaseMotions(){this._motions.clear()}releaseExpressions(){this._expressions.clear()}doDraw(){if(null==this._model)return;let e=this._subdelegate.getCanvas(),t=[0,0,e.width,e.height];this.getRenderer().setRenderState(this._subdelegate.getFrameBuffer(),t),this.getRenderer().drawModel()}draw(e){null!=this._model&&22==this._state&&(e.multiplyByMatrix(this._modelMatrix),this.getRenderer().setMvpMatrix(e),this.doDraw())}async hasMocConsistencyFromFile(){if(ed(this._modelSetting.getModelFileName().localeCompare("")),""!=this._modelSetting.getModelFileName()){let e=this._modelSetting.getModelFileName(),t=await fetch("".concat(this._modelHomeDir).concat(e)),i=await t.arrayBuffer();return this._consistency=iE.hasMocConsistency(i),this._consistency?r("Consistent MOC3."):r("Inconsistent MOC3."),this._consistency}eN.printMessage("Model data does not exist.")}setSubdelegate(e){this._subdelegate=e}constructor(){super(),this._modelSetting=null,this._modelHomeDir=null,this._userTimeSeconds=0,this._eyeBlinkIds=new $,this._lipSyncIds=new $,this._motions=new em,this._expressions=new em,this._hitArea=new $,this._userArea=new $,this._idParamAngleX=eF.getIdManager().getId(ej.ParamAngleX),this._idParamAngleY=eF.getIdManager().getId(ej.ParamAngleY),this._idParamAngleZ=eF.getIdManager().getId(ej.ParamAngleZ),this._idParamEyeBallX=eF.getIdManager().getId(ej.ParamEyeBallX),this._idParamEyeBallY=eF.getIdManager().getId(ej.ParamEyeBallY),this._idParamBodyAngleX=eF.getIdManager().getId(ej.ParamBodyAngleX),this._mocConsistency=!0,this._state=0,this._expressionCount=0,this._textureCount=0,this._motionCount=0,this._allMotionCount=0,this._wavFileHandler=new iN,this._consistency=!1}}var iX=i(23596);class iY{releaseAllModel(){this._models.clear()}onDrag(e,t){let i=this._models.at(0);i&&i.setDragging(e,t)}onTap(e,t){}onUpdate(){let{width:e,height:t}=this._subdelegate.getCanvas(),i=new er,s=this._models.at(0);s&&(s.getModel()&&(s.getModel().getCanvasWidth()>1&&e<t?(s.getModelMatrix().setWidth(2),i.scale(1,e/t)):i.scale(t/e,1),null!=this._viewMatrix&&i.multiplyByMatrix(this._viewMatrix)),s.update(),s.draw(i))}setViewMatrix(e){for(let t=0;t<16;t++)this._viewMatrix.getArray()[t]=e.getArray()[t]}release(){}initialize(e){this._subdelegate=e,this.changeCharacter(this._character)}changeCharacter(e){if(null==e){this.releaseAllModel();return}let t=iX.dirname(e.link)+"/",i="".concat(e.name,".model3.json");this.releaseAllModel();let s=new iG;s.setSubdelegate(this._subdelegate),s.loadAssets(t,i),this._models.pushBack(s),this._character=e}constructor(){this.beganMotion=e=>{eN.printMessage("Motion Began")},this.finishedMotion=e=>{eN.printMessage("Motion Finished")},this._subdelegate=null,this._viewMatrix=new er,this._models=new $,this._character=null}}class iH{release(){for(let e=this._textures.begin();e.notEqual(this._textures.end());e.preIncrement())this._glManager.getGl().deleteTexture(e.ptr().id);this._textures=null}createTextureFromPngFile(e,t,i){for(let s=this._textures.begin();s.notEqual(this._textures.end());s.preIncrement())if(s.ptr().fileName==e&&s.ptr().usePremultply==t){s.ptr().img=new Image,s.ptr().img.addEventListener("load",()=>i(s.ptr()),{passive:!0}),s.ptr().img.src=e;return}let s=new Image;s.addEventListener("load",()=>{let r=this._glManager.getGl().createTexture();this._glManager.getGl().bindTexture(this._glManager.getGl().TEXTURE_2D,r),this._glManager.getGl().texParameteri(this._glManager.getGl().TEXTURE_2D,this._glManager.getGl().TEXTURE_MIN_FILTER,this._glManager.getGl().LINEAR_MIPMAP_LINEAR),this._glManager.getGl().texParameteri(this._glManager.getGl().TEXTURE_2D,this._glManager.getGl().TEXTURE_MAG_FILTER,this._glManager.getGl().LINEAR),t&&this._glManager.getGl().pixelStorei(this._glManager.getGl().UNPACK_PREMULTIPLY_ALPHA_WEBGL,1),this._glManager.getGl().texImage2D(this._glManager.getGl().TEXTURE_2D,0,this._glManager.getGl().RGBA,this._glManager.getGl().RGBA,this._glManager.getGl().UNSIGNED_BYTE,s),this._glManager.getGl().generateMipmap(this._glManager.getGl().TEXTURE_2D),this._glManager.getGl().bindTexture(this._glManager.getGl().TEXTURE_2D,null);let a=new iW;null!=a&&(a.fileName=e,a.width=s.width,a.height=s.height,a.id=r,a.img=s,a.usePremultply=t,null!=this._textures&&this._textures.pushBack(a)),i(a)},{passive:!0}),s.crossOrigin="anonymous",s.src=e}releaseTextures(){for(let e=0;e<this._textures.getSize();e++)this._glManager.getGl().deleteTexture(this._textures.at(e).id),this._textures.set(e,null);this._textures.clear()}releaseTextureByTexture(e){for(let t=0;t<this._textures.getSize();t++)if(this._textures.at(t).id==e){this._glManager.getGl().deleteTexture(this._textures.at(t).id),this._textures.set(t,null),this._textures.remove(t);break}}releaseTextureByFilePath(e){for(let t=0;t<this._textures.getSize();t++)if(this._textures.at(t).fileName==e){this._glManager.getGl().deleteTexture(this._textures.at(t).id),this._textures.set(t,null),this._textures.remove(t);break}}setGlManager(e){this._glManager=e}constructor(){this._textures=new $}}class iW{constructor(){this.id=null,this.width=0,this.height=0}}class iq extends er{adjustTranslate(e,t){this._tr[0]*this._maxLeft+(this._tr[12]+e)>this._screenLeft&&(e=this._screenLeft-this._tr[0]*this._maxLeft-this._tr[12]),this._tr[0]*this._maxRight+(this._tr[12]+e)<this._screenRight&&(e=this._screenRight-this._tr[0]*this._maxRight-this._tr[12]),this._tr[5]*this._maxTop+(this._tr[13]+t)<this._screenTop&&(t=this._screenTop-this._tr[5]*this._maxTop-this._tr[13]),this._tr[5]*this._maxBottom+(this._tr[13]+t)>this._screenBottom&&(t=this._screenBottom-this._tr[5]*this._maxBottom-this._tr[13]);let i=new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,e,t,0,1]);er.multiply(i,this._tr,this._tr)}adjustScale(e,t,i){let s=this.getMaxScale(),r=this.getMinScale(),a=i*this._tr[0];a<r?this._tr[0]>0&&(i=r/this._tr[0]):a>s&&this._tr[0]>0&&(i=s/this._tr[0]);let n=new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,e,t,0,1]),l=new Float32Array([i,0,0,0,0,i,0,0,0,0,1,0,0,0,0,1]),o=new Float32Array([1,0,0,0,0,1,0,0,0,0,1,0,-e,-t,0,1]);er.multiply(o,this._tr,this._tr),er.multiply(l,this._tr,this._tr),er.multiply(n,this._tr,this._tr)}setScreenRect(e,t,i,s){this._screenLeft=e,this._screenRight=t,this._screenBottom=i,this._screenTop=s}setMaxScreenRect(e,t,i,s){this._maxLeft=e,this._maxRight=t,this._maxTop=s,this._maxBottom=i}setMaxScale(e){this._maxScale=e}setMinScale(e){this._minScale=e}getMaxScale(){return this._maxScale}getMinScale(){return this._minScale}isMaxScale(){return this.getScaleX()>=this._maxScale}isMinScale(){return this.getScaleX()<=this._minScale}getScreenLeft(){return this._screenLeft}getScreenRight(){return this._screenRight}getScreenBottom(){return this._screenBottom}getScreenTop(){return this._screenTop}getMaxLeft(){return this._maxLeft}getMaxRight(){return this._maxRight}getMaxBottom(){return this._maxBottom}getMaxTop(){return this._maxTop}constructor(){super(),this._screenLeft=0,this._screenRight=0,this._screenTop=0,this._screenBottom=0,this._maxLeft=0,this._maxRight=0,this._maxTop=0,this._maxBottom=0,this._maxScale=0,this._minScale=0}}(Z||(Z={})).CubismViewMatrix=iq;class iJ{getCenterX(){return this._lastX}getCenterY(){return this._lastY}getDeltaX(){return this._deltaX}getDeltaY(){return this._deltaY}getStartX(){return this._startX}getStartY(){return this._startY}getScale(){return this._scale}getX(){return this._lastX}getY(){return this._lastY}getX1(){return this._lastX1}getY1(){return this._lastY1}getX2(){return this._lastX2}getY2(){return this._lastY2}isSingleTouch(){return this._touchSingle}isFlickAvailable(){return this._flipAvailable}disableFlick(){this._flipAvailable=!1}touchesBegan(e,t){this._lastX=e,this._lastY=t,this._startX=e,this._startY=t,this._lastTouchDistance=-1,this._flipAvailable=!0,this._touchSingle=!0}touchesMoved(e,t){this._lastX=e,this._lastY=t,this._lastTouchDistance=-1,this._touchSingle=!0}getFlickDistance(){return this.calculateDistance(this._startX,this._startY,this._lastX,this._lastY)}calculateDistance(e,t,i,s){return Math.sqrt((e-i)*(e-i)+(t-s)*(t-s))}calculateMovingAmount(e,t){if(e>0!=t>0)return 0;let i=Math.abs(e),s=Math.abs(t);return(e>0?1:-1)*(i<s?i:s)}constructor(){this._startX=0,this._startY=0,this._lastX=0,this._lastY=0,this._lastX1=0,this._lastY1=0,this._lastX2=0,this._lastY2=0,this._lastTouchDistance=0,this._deltaX=0,this._deltaY=0,this._scale=1,this._touchSingle=!1,this._flipAvailable=!1}}class iZ{initialize(e){this._subdelegate=e;let{width:t,height:i}=e.getCanvas(),s=t/i,r=-s;if(this._viewMatrix.setScreenRect(r,s,-1,1),this._viewMatrix.scale(1,1),this._deviceToScreen.loadIdentity(),t>i){let e=Math.abs(s-r);this._deviceToScreen.scaleRelative(e/t,-e/t)}else{let e=Math.abs(2);this._deviceToScreen.scaleRelative(e/i,-e/i)}this._deviceToScreen.translateRelative(-(.5*t),-(.5*i)),this._viewMatrix.setMaxScale(2),this._viewMatrix.setMinScale(.8),this._viewMatrix.setMaxScreenRect(-2,2,-2,2)}release(){this._viewMatrix=null,this._touchManager=null,this._deviceToScreen=null,this._subdelegate.getGlManager().getGl().deleteProgram(this._programId),this._programId=null}render(){this._subdelegate.getGlManager().getGl().useProgram(this._programId),this._subdelegate.getGlManager().getGl().flush();let e=this._subdelegate.getLive2DManager();null!=e&&(e.setViewMatrix(this._viewMatrix),e.onUpdate())}initializeSprite(){this._subdelegate.getCanvas().width,this._subdelegate.getCanvas().height,this._subdelegate.getTextureManager(),null==this._programId&&(this._programId=this._subdelegate.createShader())}onTouchesBegan(e,t){this._touchManager.touchesBegan(e*window.devicePixelRatio,t*window.devicePixelRatio)}onTouchesMoved(e,t){let i=e*window.devicePixelRatio,s=t*window.devicePixelRatio,r=this._subdelegate.getLive2DManager(),a=this.transformViewX(this._touchManager.getX()),n=this.transformViewY(this._touchManager.getY());this._touchManager.touchesMoved(i,s),r.onDrag(a,n)}onTouchesEnded(e,t){let i=e*window.devicePixelRatio,s=t*window.devicePixelRatio,r=this._subdelegate.getLive2DManager();r.onDrag(0,0);let a=this.transformViewX(i),n=this.transformViewY(s);r.onTap(a,n)}transformViewX(e){let t=this._deviceToScreen.transformX(e);return this._viewMatrix.invertTransformX(t)}transformViewY(e){let t=this._deviceToScreen.transformY(e);return this._viewMatrix.invertTransformY(t)}transformScreenX(e){return this._deviceToScreen.transformX(e)}transformScreenY(e){return this._deviceToScreen.transformY(e)}constructor(){this._programId=null,this._touchManager=new iJ,this._deviceToScreen=new er,this._viewMatrix=new iq}}class iK{release(){this._resizeObserver.unobserve(this._canvas),this._resizeObserver.disconnect(),this._resizeObserver=null,this._live2dManager.release(),this._live2dManager=null,this._view.release(),this._view=null,this._textureManager.release(),this._textureManager=null,this._glManager.release(),this._glManager=null}initialize(e){if(!this._glManager.initialize(e))return!1;this._canvas=e,this.resizeCanvas(),this._textureManager.setGlManager(this._glManager);let t=this._glManager.getGl();return this._frameBuffer||(this._frameBuffer=t.getParameter(t.FRAMEBUFFER_BINDING)),t.enable(t.BLEND),t.blendFunc(t.SRC_ALPHA,t.ONE_MINUS_SRC_ALPHA),this._view.initialize(this),this._view.initializeSprite(),this._live2dManager.initialize(this),this._resizeObserver=new ResizeObserver((e,t)=>this.resizeObserverCallback.call(this,e,t)),this._resizeObserver.observe(this._canvas),!0}onResize(){this.resizeCanvas(),this._view.initialize(this),this._view.initializeSprite()}resizeObserverCallback(e,t){this._needResize=!0}update(){if(this._glManager.getGl().isContextLost())return;this._needResize&&(this.onResize(),this._needResize=!1);let e=this._glManager.getGl();e.clearColor(0,0,0,0),e.enable(e.DEPTH_TEST),e.depthFunc(e.LEQUAL),e.clear(e.COLOR_BUFFER_BIT|e.DEPTH_BUFFER_BIT),e.clearDepth(1),e.enable(e.BLEND),e.blendFunc(e.SRC_ALPHA,e.ONE_MINUS_SRC_ALPHA),this._view.render()}createShader(){let e=this._glManager.getGl(),t=e.createShader(e.VERTEX_SHADER);if(null==t)return eN.printMessage("failed to create vertexShader"),null;e.shaderSource(t,"precision mediump float;attribute vec3 position;attribute vec2 uv;varying vec2 vuv;void main(void){   gl_Position = vec4(position, 1.0);   vuv = uv;}"),e.compileShader(t);let i=e.createShader(e.FRAGMENT_SHADER);if(null==i)return eN.printMessage("failed to create fragmentShader"),null;e.shaderSource(i,"precision mediump float;varying vec2 vuv;uniform sampler2D texture;void main(void){   gl_FragColor = texture2D(texture, vuv);}"),e.compileShader(i);let s=e.createProgram();return e.attachShader(s,t),e.attachShader(s,i),e.deleteShader(t),e.deleteShader(i),e.linkProgram(s),e.useProgram(s),s}getTextureManager(){return this._textureManager}getFrameBuffer(){return this._frameBuffer}getCanvas(){return this._canvas}getGlManager(){return this._glManager}getLive2DManager(){return this._live2dManager}resizeCanvas(){this._canvas.width=this._canvas.clientWidth*window.devicePixelRatio,this._canvas.height=this._canvas.clientHeight*window.devicePixelRatio;let e=this._glManager.getGl();e.viewport(0,0,e.drawingBufferWidth,e.drawingBufferHeight)}onPointBegan(e,t){if(!this._view){eN.printMessage("view notfound");return}this._captured=!0;let i=e-this._canvas.offsetLeft,s=t-this._canvas.offsetTop;this._view.onTouchesBegan(i,s)}onPointMoved(e,t){if(!this._captured)return;let i=e-this._canvas.offsetLeft,s=t-this._canvas.offsetTop;this._view.onTouchesMoved(i,s)}onPointEnded(e,t){if(this._captured=!1,!this._view){eN.printMessage("view notfound");return}let i=e-this._canvas.offsetLeft,s=t-this._canvas.offsetTop;this._view.onTouchesEnded(i,s)}onTouchCancel(e,t){if(this._captured=!1,!this._view){eN.printMessage("view notfound");return}let i=e-this._canvas.offsetLeft,s=t-this._canvas.offsetTop;this._view.onTouchesEnded(i,s)}isContextLost(){return this._glManager.getGl().isContextLost()}changeCharacter(e){this._live2dManager.changeCharacter(e)}constructor(){this._canvas=null,this._glManager=new eO,this._textureManager=new iH,this._live2dManager=new iY,this._view=new iZ,this._frameBuffer=null,this._captured=!1}}let iQ=null;class i${static getInstance(){return null==iQ&&(iQ=new i$),iQ}static releaseInstance(){null!=iQ&&iQ.release(),iQ=null}onResize(){for(let e=0;e<this._subdelegates.getSize();e++)this._subdelegates.at(e).onResize()}run(){let e=()=>{if(null!=iQ){eN.updateTime();for(let e=0;e<this._subdelegates.getSize();e++)this._subdelegates.at(e).update();requestAnimationFrame(e)}};e()}release(){this.releaseEventListener(),this.releaseSubdelegates(),eF.dispose(),this._cubismOption=null}releaseEventListener(){document.removeEventListener("pointerup",i0),document.removeEventListener("pointermove",i1),document.removeEventListener("pointerdown",i2),document.removeEventListener("pointerdown",i3)}releaseSubdelegates(){for(let e=this._subdelegates.begin();e.notEqual(this._subdelegates.end());e.preIncrement())e.ptr().release();this._subdelegates.clear(),this._subdelegates=null}initialize(){return this.initializeCubism(),this.initializeSubdelegates(),this.initializeEventListener(),!0}initializeEventListener(){document.addEventListener("pointerdown",i0,{passive:!0}),document.addEventListener("pointermove",i1,{passive:!0}),document.addEventListener("pointerup",i2,{passive:!0}),document.addEventListener("pointercancel",i3,{passive:!0})}initializeCubism(){eN.updateTime(),this._cubismOption.logFunction=eN.printMessage,this._cubismOption.loggingLevel=ek,eF.startUp(this._cubismOption),eF.initialize()}initializeSubdelegates(){this._canvases.prepareCapacity(1),this._subdelegates.prepareCapacity(1);for(let e=0;e<1;e++){let e=document.getElementById("live2dCanvas");this._canvases.pushBack(e)}for(let e=0;e<this._canvases.getSize();e++){let t=new iK;t.initialize(this._canvases.at(e)),this._subdelegates.pushBack(t)}for(let e=0;e<1;e++)this._subdelegates.at(e).isContextLost()&&n("The context for Canvas at index ".concat(e," was lost, possibly because the acquisition limit for WebGLRenderingContext was reached."))}changeCharacter(e){this._subdelegates.at(0).changeCharacter(e)}getSubdelegate(){return this._subdelegates}constructor(){this._cubismOption=new eL,this._subdelegates=new $,this._canvases=new $}}function i0(e){for(let t=i$.getInstance().getSubdelegate().begin();t.notEqual(i$.getInstance().getSubdelegate().end());t.preIncrement())t.ptr().onPointBegan(e.pageX,e.pageY)}function i1(e){for(let t=i$.getInstance().getSubdelegate().begin();t.notEqual(i$.getInstance().getSubdelegate().end());t.preIncrement())t.ptr().onPointMoved(e.pageX,e.pageY)}function i2(e){for(let t=i$.getInstance().getSubdelegate().begin();t.notEqual(i$.getInstance().getSubdelegate().end());t.preIncrement())t.ptr().onPointEnded(e.pageX,e.pageY)}function i3(e){for(let t=i$.getInstance().getSubdelegate().begin();t.notEqual(i$.getInstance().getSubdelegate().end());t.preIncrement())t.ptr().onTouchCancel(e.pageX,e.pageY)}var i4=i(34583),i5=i(20412),i6=i(69175),i8=function(e){return e.ASR="ASR",e.TTS="TTS",e.LLM="LLM",e.AGENT="AGENT",e}({}),i9=function(e){return e.NORMAL="normal",e.STREAM="stream",e}({}),i7=function(e){return e.STRING="string",e.INT="int",e.FLOAT="float",e.BOOL="bool",e}({}),se=function(e){return e.STATIC="STATIC",e.DYNAMIC="DYNAMIC",e.CUSTOM="CUSTOM",e.ALL="ALL",e}({}),st=function(e){return e.IP="IP",e.FREE="FREE",e.CUSTOM="CUSTOM",e.ALL="ALL",e}({}),si=function(e){return e.FREEDOM="Freedom",e}({}),ss=function(e){return e.HUMAN="HUMAN",e.AI="AI",e}({}),sr=function(e){return e.DIALOGUE="DIALOGUE",e.IMMSERSIVE="IMMSERSIVE",e}({}),sa=function(e){return e.MP3="mp3",e.WAV="wav",e}({}),sn=function(e){return e.CONVERSATION_ID="CONVERSATION_ID",e.MESSAGE_ID="MESSAGE_ID",e.THINK="THINK",e.TEXT="TEXT",e.TASK="TASK",e.DONE="DONE",e.ERROR="ERROR",e}({}),sl=function(e){return e.BACKGROUND="background",e.CHARACTER="character",e.ICON="icon",e}({});let so=["夜晚街道.jpg","赛博朋克.jpg","火影忍者.jpg","插画.jpg","艺术.jpg","简约.jpg","抽象.jpg"],su=["太空站.mp4","赛博朋克.mp4","可爱城市.mp4","悟空.mp4","火影忍者.mp4","几何线条.mp4","公式.mp4"],sh="sentio/characters/free",sg=[],sd=["HaruGreeter","Haru","Kei","Chitose","Epsilon","Hibiki","Hiyori","Izumi","Mao","Rice","Shizuku","Tsumiki"],sc="HaruGreeter",s_="".concat(sh,"/").concat(sc,"/").concat(sc,".png"),sm=["；","！","？","。","?"],sp=["今天最浪漫的事就是遇见你。","你有百般模样，我也会百般喜欢。","这里什么都好，因为这就是你。"],sf=sr.DIALOGUE,sy=si.FREEDOM,sx=(0,i5.v)()((0,i6.Zr)(e=>({chatRecord:[],addChatRecord:t=>e(e=>({chatRecord:[...e.chatRecord,t]})),getLastRecord:()=>{let e=sx.getState().chatRecord;return e.length>0?e[e.length-1]:void 0},updateLastRecord:t=>e(e=>({chatRecord:[...e.chatRecord.slice(0,-1),t]})),deleteLastRecord:()=>e(e=>({chatRecord:[...e.chatRecord.slice(0,-1)]})),clearChatRecord:()=>e(e=>({chatRecord:[]}))}),{name:"sentio-chat-record-storage"})),sS=(0,i5.v)()((0,i6.Zr)(e=>({sound:!0,showThink:!0,lipFactor:5,setSound:t=>e(e=>({sound:t})),setShowThink:t=>e(e=>({showThink:t})),setLipFactor:t=>e(e=>({lipFactor:t}))}),{name:"sentio-basic-storage"})),sC=(0,i5.v)()((0,i6.Zr)(e=>({enable:!0,engine:"default",infer_type:i9.NORMAL,settings:{},setEnable:t=>e(e=>({enable:t})),setInferType:t=>e(e=>({infer_type:t})),setEngine:t=>e(e=>({engine:t})),setSettings:t=>e(e=>({settings:t}))}),{name:"sentio-asr-storage"})),sb=(0,i5.v)()((0,i6.Zr)(e=>({enable:!0,engine:"default",infer_type:i9.NORMAL,settings:{},setEnable:t=>e(e=>({enable:t})),setInferType:t=>e(e=>({infer_type:t})),setEngine:t=>e(e=>({engine:t})),setSettings:t=>e(e=>({settings:t}))}),{name:"sentio-tts-storage"})),sv=(0,i5.v)()((0,i6.Zr)(e=>({enable:!0,engine:"default",infer_type:i9.NORMAL,settings:{},setEnable:t=>e(e=>({})),setInferType:t=>e(e=>({infer_type:t})),setEngine:t=>e(e=>({engine:t})),setSettings:t=>e(e=>({settings:t}))}),{name:"sentio-agent-storage"})),sM=(0,i5.v)()((0,i6.Zr)(e=>({background:null,setBackground:t=>e(e=>({background:t}))}),{name:"sentio-background-storage"})),sP=(0,i5.v)()((0,i6.Zr)(e=>({character:null,setCharacter:t=>e(e=>({character:t}))}),{name:"sentio-character-storage"})),sw=(0,i5.v)()((0,i6.Zr)(e=>({chatMode:sf,setChatMode:t=>e(e=>({chatMode:t}))}),{name:"sentio-chat-mode-storage"})),sB=(0,i5.v)()((0,i6.Zr)(e=>({theme:sy,setTheme:t=>e(e=>({theme:t}))}),{name:"sentio-theme-storage"})),sI=(0,i5.v)()(e=>({ready:!1,setReady:t=>e(e=>({ready:t}))}));var sT=i(59471);let sE=()=>{let{ready:e,setReady:t}=sI(),i=()=>{iU.getInstance().isReady()?t(!0):setTimeout(i,1e3)};return{setLive2dCharacter:e=>{iU.getInstance().changeCharacter(e),null!=e&&(t(!1),i())},ready:e}};function sR(){let e=(0,sT.useTranslations)("Products.sentio"),{ready:t}=sE(),{background:i}=sM(),s=()=>{!1!=i$.getInstance().initialize()&&i$.getInstance().run()},r=()=>{i$.getInstance().onResize()},a=()=>{i$.releaseInstance()};return(0,Q.useEffect)(()=>(s(),window.addEventListener("resize",r),()=>{window.removeEventListener("resize",r),a()}),[]),(0,K.jsxs)("div",{className:"fixed bottom-4 right-4 w-80 h-80 z-0 rounded-lg overflow-hidden shadow-lg",children:[i&&(i.link.endsWith(".mp4")?(0,K.jsx)("video",{className:"absolute top-0 left-0 w-full h-full object-cover z-[-1]",autoPlay:!0,muted:!0,loop:!0,src:i.link,style:{pointerEvents:"none"}}):(0,K.jsx)("img",{src:i.link,alt:"Background Image",className:"absolute top-0 left-0 w-full h-full object-cover z-[-1]"})),!t&&(0,K.jsxs)("div",{className:"absolute top-0 left-0 w-full h-full flex flex-row gap-1 items-center justify-center z-50",children:[(0,K.jsx)("p",{className:"text-sm font-bold",children:e("loading")}),(0,K.jsx)(i4.o,{color:"warning",variant:"dots",size:"sm"})]}),(0,K.jsx)("canvas",{id:"live2dCanvas",className:"w-full h-full bg-center bg-cover"})]})}var sV=i(38611),sA=i(19932),sF=i(78763),sL=i(74980),sD=i(30413),sk=i(36238),sN=i(84269);let sO=(0,Q.memo)(e=>{let{message:t,thinking:i}=e,s=(0,sT.useTranslations)("Products.sentio"),{showThink:r}=sS(),a=(0,Q.useCallback)(()=>i?(0,K.jsxs)("div",{className:"flex flex-row gap-1 items-center overflow-hidden",children:[(0,K.jsx)("p",{className:"text-2xl",children:"\uD83E\uDD14"}),(0,K.jsx)("p",{children:s("thinking")}),(0,K.jsx)(i4.o,{color:"warning",variant:"dots"})]}):(0,K.jsx)(K.Fragment,{}),[i]);return r?(0,K.jsx)(()=>(0,K.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,K.jsx)("p",{className:"text-2xl",children:"\uD83E\uDD14"}),(0,K.jsx)(sD.o,{className:"text-gray-400 text-sm border-l-2 px-2 border-gray-400",remarkPlugins:[sk.A],children:t.replace(/\\n/g,"  \n")})]}),{}):(0,K.jsx)(a,{})}),sj=()=>{let e=(0,Q.useRef)(null),{chatRecord:t,clearChatRecord:i}=sx();return(0,Q.useEffect)(()=>{e.current.scrollTop=e.current.scrollHeight+100},[t]),(0,Q.useEffect)(()=>{i()},[]),(0,K.jsx)("div",{className:"flex flex-col w-full space-y-4 p-3 overflow-y-auto no-scrollbar z-10",ref:e,children:t.map((e,i)=>(0,K.jsx)("div",{children:(0,K.jsxs)("div",{className:(0,sN.A)("flex gap-2 items-start",e.role==ss.HUMAN?"justify-end":""),children:[(0,K.jsx)("div",{className:(0,sN.A)("min-w-8",e.role==ss.HUMAN?"text-gray-400 order-2":"text-yellow-400 order-1"),children:e.role==ss.HUMAN?(0,K.jsx)(sV.A,{className:"size-6"}):(0,K.jsx)(sA.A,{className:"size-6"})}),(0,K.jsx)(sF.Z,{className:(0,sN.A)("max-w-md opacity-80",e.role==ss.HUMAN?"order-1":"order-2"),children:(0,K.jsxs)(sL.U,{className:"flex flex-col gap-2",children:[e.role==ss.AI&&e.think&&(0,K.jsx)(sO,{message:e.think,thinking:0==e.content.length&&i==t.length-1}),(0,K.jsx)(sD.o,{remarkPlugins:[sk.A],children:e.content.replace(/\\n/g,"  \n")})]})})]})},i))})};var sU=i(98492),sz=i(48900),sG=i(84794),sX=i(25140),sY=i(19296),sH=i(53709),sW=i(59406);i(87679);var sq=i(5618),sJ=i(62585),sZ=i(26332);function sK(){var e;return"http://"+(sZ.env.NEXT_PUBLIC_SERVER_IP||(null===(e=globalThis.location)||void 0===e?void 0:e.hostname))+":8880"}function sQ(e){return e.includes("http")?e:sK()+e}function s$(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;t&&t.aborted||(0,sX.$U)({title:e.message,variant:"flat",color:"danger"})}async function s0(e){return e.json().then(e=>{if(!e.code||0==e.code)return e;throw Error(e.message)})}async function s1(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{"Content-Type":"application/json"},s=sQ(e);return i["Request-Id"]=(0,sJ.A)(),i["User-Id"]="",fetch(s,{method:"GET",headers:i,signal:t}).then(e=>s0(e)).catch(e=>(s$(e,t),Promise.reject(e.message)))}async function s2(e,t,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{"Content-Type":"application/json"},r="string"==typeof t?t:JSON.stringify(t),a=sQ(e);return s["Request-Id"]=(0,sJ.A)(),s["User-Id"]="",fetch(a,{method:"POST",body:r,headers:s,signal:i}).then(e=>s0(e)).catch(e=>(s$(e,i),Promise.reject(e.message)))}async function s3(e,t,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=sQ(e);return s["Request-Id"]=(0,sJ.A)(),s["User-Id"]="",fetch(r,{method:"POST",body:t,headers:s,signal:i}).then(e=>s0(e)).catch(e=>(s$(e,i),Promise.reject(e.message)))}let s4="/adh",s5=s4+"/asr/".concat("v0");async function s6(){return s1("".concat(s5,"/engine"),null).then(e=>e.data).catch(()=>[])}async function s8(){return s1("".concat(s5,"/engine/default"),null).then(e=>e.data).catch(()=>({}))}async function s9(e){return s1("".concat(s5,"/engine/").concat(e),null).then(e=>e.data).catch(()=>[])}async function s7(e,t,i){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:sa.MP3,r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:16e3,a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:2,n=new FormData,l=new File([i],"file.mp3",{type:"audio/mp3"});return n.append("file",l),n.append("engine",e),n.append("config",JSON.stringify(t)),n.append("type",s),n.append("sampleRate",String(r)),n.append("sampleWidth",String(a)),s3("".concat(s5,"/engine/file"),n,null).then(e=>e.data).catch(()=>"")}let re=s4+"/tts/".concat("v0");async function rt(){return s1("".concat(re,"/engine"),null).then(e=>e.data).catch(()=>[])}async function ri(e,t){return s1("".concat(re,"/engine/").concat(e,"/voice?config=").concat(encodeURIComponent(JSON.stringify(t))),null).then(e=>e.data).catch(()=>[])}async function rs(){return s1("".concat(re,"/engine/default"),null).then(e=>e.data).catch(()=>({}))}async function rr(e){return s1("".concat(re,"/engine/").concat(e),null).then(e=>e.data).catch(()=>[])}async function ra(e,t,i,s){let r=JSON.stringify({engine:e,config:t,data:i});return s2("".concat(re,"/engine"),r,s).then(e=>e.data).catch(()=>"")}let rn=s4+"/agent/".concat("v0");async function rl(){return s1("".concat(rn,"/engine"),null).then(e=>e.data).catch(()=>[])}async function ro(){return s1("".concat(rn,"/engine/default"),null).then(e=>e.data).catch(()=>({}))}async function ru(e){return s1("".concat(rn,"/engine/").concat(e),null).then(e=>e.data).catch(()=>[])}async function rh(e){switch(e){case i8.ASR:return s6();case i8.TTS:return rt();case i8.AGENT:return rl()}}async function rg(e){switch(e){case i8.ASR:return s8();case i8.TTS:return rs();case i8.AGENT:return ro()}}var rd=function(e){return e.PING="PING",e.ENGINE_START="ENGINE_START",e.ENGINE_PARTIAL_INPUT="PARTIAL_INPUT",e.ENGINE_FINAL_INPUT="FINAL_INPUT",e.ENGINE_STOP="ENGINE_STOP",e}({}),rc=function(e){return e.PONG="PONG",e.ENGINE_INITIALZING="ENGINE_INITIALZING",e.ENGINE_STARTED="ENGINE_STARTED",e.ENGINE_PARTIAL_OUTPUT="PARTIAL_OUTPUT",e.ENGINE_FINAL_OUTPUT="FINAL_OUTPUT",e.ENGINE_STOPPED="ENGINE_STOPPED",e.ERROR="ERROR",e}({});class r_{connect(){this._ws=new WebSocket(this._url),this._ws.binaryType="arraybuffer",this._ws.onopen=()=>{let e=JSON.stringify({engine:this._engine,config:this._config});this.sendMessage("ENGINE_START",e),this._onOpen&&this._onOpen()},this._ws.onmessage=e=>{if(this._onMessage){let{action:t,payload:i}=function(e){if(e.byteLength<22)throw Error("Message too short: ".concat(e.byteLength," bytes, expected at least ").concat(22));let t=new DataView(e),i=new Uint8Array(e,0,18),s=t.getUint32(18,!1),r=22+s;if(e.byteLength!==r)throw Error("Message size mismatch: got ".concat(e.byteLength," bytes, expected ").concat(r));let a=s>0?new Uint8Array(e,22,s):new Uint8Array(0);return{action:new TextDecoder().decode(i).trim(),payload:a}}(e.data);this._onMessage(t,i)}},this._ws.onclose=()=>{this.sendMessage("ENGINE_STOP"),this._onClose&&this._onClose()},this._ws.onerror=e=>{this._onError&&this._onError(Error("WebSocket error: ".concat(e.target)))}}disconnect(){this._ws&&(this._ws.close(),this._ws=null)}isConnected(){return this._ws&&this._ws.readyState===WebSocket.OPEN}sendMessage(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint8Array(0),i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Uint8Array(0);"string"==typeof t&&(t=new TextEncoder().encode(t));let i=function(e){if(e.length>18)throw Error("Action name '".concat(e,"' exceeds ").concat(18," bytes"));let t=e.padEnd(18," ");return new TextEncoder().encode(t)}(e);if(18!==i.length)throw Error("Action must be exactly ".concat(18," bytes, got ").concat(i.length));let s=t.length,r=new ArrayBuffer(22+s),a=new DataView(r);return new Uint8Array(r,0,18).set(i),a.setUint32(18,s,!1),s>0&&new Uint8Array(r,22,s).set(t),r}(e,t);this._ws&&this._ws.readyState===WebSocket.OPEN&&this._ws.send(i)}constructor(e,t,i,s,r,a,n){this._ws=null,this._url=e,this._engine=t,this._config=i,this._onMessage=s,this._onOpen=r,this._onClose=a,this._onError=n}}var rm=i(37222),rp=i(51936),rf=i.n(rp),ry=i(6623),rx=i.n(ry),rS=i(9333),rC=i.n(rS);globalThis&&(globalThis.MPEGMode=rf(),globalThis.Lame=rx(),globalThis.BitStream=rC());let rb=e=>{let{channels:t,sampleRate:i}=rm.E.readHeader(e.getWAV()),s=new rm.x(t,i,128),r=e.getChannelData(),a=[],n=r.left&&new Int16Array(r.left.buffer,0,r.left.byteLength/2),l=r.right&&new Int16Array(r.right.buffer,0,r.right.byteLength/2),o=n.length+(l?l.length:0);for(let e=0;e<o;e+=1152){let i=n.subarray(e,e+1152),r=null,o=null;2===t?(r=l.subarray(e,e+1152),o=s.encodeBuffer(i,r)):o=s.encodeBuffer(i),o.length>0&&a.push(o)}let u=s.flush();return u.length>0&&a.push(u),new Blob(a,{type:"audio/mp3"})},rv=e=>((e,t,i)=>{let s=[],r=new rm.x(e,t,128),a=i.length;for(let e=0;a>=1152;e+=1152){let t=i.subarray(e,e+1152),n=r.encodeBuffer(t);n.length>0&&s.push(n),a-=1152}let n=r.flush();return n.length>0&&s.push(n),new Blob(s,{type:"audio/mp3"})})(1,16e3,(e=>{let t=new Int16Array(e.length);for(let i=0,s=e.length;i<s;i++)e[i]<0?t[i]=32768*e[i]:t[i]=32767*e[i];return t})(e));function rM(e,t,i){for(let s=0;s<i.length;s++)e.setUint8(t+s,i.charCodeAt(s))}let rP=async e=>{let t=new Blob([e],{type:"audio/mp3"});return(await rw(t)).arrayBuffer()},rw=async e=>{let t=await e.arrayBuffer(),i=new(window.AudioContext||window.webkitAudioContext),s=await i.decodeAudioData(t),r=s.numberOfChannels,a=s.sampleRate,n=[];for(let e=0;e<r;e++){let t=s.getChannelData(e),i=new Int16Array(t.length);for(let e=0;e<t.length;e++)i[e]=Math.round(32767*t[e]);n.push(i)}let l=new ArrayBuffer(44),o=new DataView(l);rM(o,0,"RIFF"),o.setUint32(4,36+2*n[0].length*r,!0),rM(o,8,"WAVE"),rM(o,12,"fmt "),o.setUint32(16,16,!0),o.setUint16(20,1,!0),o.setUint16(22,r,!0),o.setUint32(24,a,!0),o.setUint32(28,2*a*r,!0),o.setUint16(32,2*r,!0),o.setUint16(34,16,!0),rM(o,36,"data"),o.setUint32(40,2*n[0].length*r,!0);let u=new Uint8Array(l.byteLength+2*n[0].length*r);new Uint8Array(l).forEach((e,t)=>{u[t]=e});let h=l.byteLength;for(let e=0;e<n[0].length;e++)for(let t=0;t<r;t++)u[h++]=255&n[t][e],u[h++]=n[t][e]>>8&255;return new Blob([u],{type:"audio/wav"})};class rB{async start(){this._mediaStream=await navigator.mediaDevices.getUserMedia({audio:{sampleRate:this._sampleRate,channelCount:this._channleCount,echoCancellation:!0,noiseSuppression:!0}}),this._audioContext=new(window.AudioContext||window.webkitAudioContext)({sampleRate:this._sampleRate}),await this._audioContext.audioWorklet.addModule(URL.createObjectURL(new Blob(["\n        class AudioProcessor extends AudioWorkletProcessor {\n            process(inputs, outputs, parameters) {\n                const input = inputs[0];\n                if (input && input[0]) {\n                    const float32Data = input[0];\n                    this.port.postMessage(float32Data);\n                }\n                return true;\n            }\n        }\n        registerProcessor('audio-processor', AudioProcessor);\n        "],{type:"application/javascript"})));let e=this._audioContext.createMediaStreamSource(this._mediaStream);this._audioWorkletNode=new AudioWorkletNode(this._audioContext,"audio-processor"),this._audioWorkletNode.port.onmessage=e=>{this.processAudioData(e.data)},e.connect(this._audioWorkletNode)}stop(){this._audioWorkletNode&&(this._audioWorkletNode.disconnect(),this._audioWorkletNode=null),this._mediaStream&&(this._mediaStream.getTracks().forEach(e=>e.stop()),this._mediaStream=null),this._audioContext&&(this._audioContext.close(),this._audioContext=null)}processAudioData(e){this._onFloat32AudioChunk&&this._onFloat32AudioChunk(e);let t=new Int16Array(e.length);for(let i=0;i<e.length;i++){let s=Math.max(-1,Math.min(1,e[i]));t[i]=s<0?32768*s:32767*s}let i=new Uint8Array(2*t.length);for(let e=0;e<t.length;e++){let s=t[e];i[2*e]=255&s,i[2*e+1]=s>>8&255}for(let e=0;e<i.length;e++)this._audioBuffer.push(i[e]);for(;this._audioBuffer.length>=this._chunkSize;){let e=new Uint8Array(this._audioBuffer.splice(0,this._chunkSize));this._onUint8AudioChunk&&this._onUint8AudioChunk(e)}}constructor(e=16e3,t=1,i=1920,s,r){this._audioContext=null,this._mediaStream=null,this._audioWorkletNode=null,this._audioBuffer=[],this._sampleRate=e,this._channleCount=t,this._chunkSize=i,this._onFloat32AudioChunk=r,this._onUint8AudioChunk=s}}var rI=i(17637),rT=i.n(rI),rE=i(57625);function rR(e){let t=window.atob(e),i=t.length,s=new Uint8Array(i);for(let e=0;e<i;e++)s[e]=t.charCodeAt(e);return s.buffer}function rV(){let e=(0,sT.useTranslations)("Products.sentio"),t=(0,Q.useRef)(new Date),i=e=>{(0,sX.$U)({title:e,color:"warning"})};return{startAudioTimer:()=>{t.current=new Date},stopAudioTimer:()=>{let s=new Date().getTime()-t.current.getTime();if(s<1e3)i("".concat(e("recordingTime")," < ").concat(1e3));else{if(!(s>3e4))return!0;i("".concat(e("recordingTime")," > ").concat(3e4))}return!1}}}let rA=(e,t)=>{let i=-1;for(let s=0;s<sm.length;s++){let r=e.indexOf(sm[s],t);r>t&&(i<0||r<i)&&(i=r)}return i};function rF(){let[e,t]=(0,Q.useState)(!1),{engine:i,settings:s}=sv(),{engine:r,settings:a}=sb(),{sound:n}=sS(),{addChatRecord:l,updateLastRecord:o}=sx(),u=(0,Q.useRef)(null),h=(0,Q.useRef)(""),g=(0,Q.useRef)(""),d=()=>{t(!1),iU.getInstance().stopAudio(),u.current&&(u.current.abort("abort"),u.current=null)},c=(e,d)=>{l({role:ss.HUMAN,think:"",content:e}),l({role:ss.AI,think:"",content:"..."}),u.current=new AbortController,t(!0);let c="",_="",m=0,p=!0,f=()=>{if(u.current){if(!p||c.length>m){let t="",i=e=>{""!=e&&rP(rR(e)).then(e=>{iU.getInstance().pushAudioQueue(e),t=""}),f()},s=m;for(;s>=m;){let e=rA(c,s);if(e>s){if(e-m>6){t=c.substring(m,e+1),m=e+1;break}s=e+1;continue}s=-1}if(0==t.length&&p&&(t=c.substring(m),m=c.length),""!=t){let s=function(e){let t=e.match(/([\u4e00-\u9fa5a-zA-Z0-9\u3002\uFF0C\uFF1F\uFF01\uFF1A\u002E\u002C\u003F\u0021\u003A\u005B\u005D\u0028\u0029\\s\\]+)/g);return(t?t.join(" "):"").replace(/\\n/g," ").trim()}(t);if(s){var e;ra(r,a,s,null===(e=u.current)||void 0===e?void 0:e.signal).then(e=>{i(e)})}else i("")}else setTimeout(()=>{f()},10)}else t(!1)}};!function(e,t,i,s,r,a){arguments.length>6&&void 0!==arguments[6]&&arguments[6];let n=sK()+"".concat(rn,"/engine");(0,sq.y)(n,{method:"POST",headers:{"Content-Type":"application/json","Request-Id":(0,sJ.A)(),"User-Id":""},body:JSON.stringify({engine:e,config:t,data:i,conversation_id:s}),signal:r,onmessage:e=>{let{event:t,data:i}=e;a({event:t,data:i})},onerror(e){throw Error(e)}}).catch(e=>{s$(e,r)})}(i,s,e,h.current,u.current.signal,e=>{let i=e.event,s=e.data;switch(i){case sn.CONVERSATION_ID:h.current=s;break;case sn.MESSAGE_ID:g.current=s;break;case sn.THINK:_+=s,o({role:ss.AI,think:_,content:c});break;case sn.TEXT:c+=s,o({role:ss.AI,think:_,content:c}),p&&(p=!1,n&&f());break;case sn.ERROR:(0,sX.$U)({title:s,color:"danger"});case sn.TASK:case sn.DONE:d&&d(h.current,g.current,_,c),p?t(!1):p=!0}},e=>{p=!0,t(!1)})};return(0,Q.useEffect)(()=>(h.current="",()=>{d()}),[i,s]),{chat:(e,t)=>{d(),c(e,t)},abort:d,chatting:e,conversationId:h}}function rL(e){return"".concat("/").concat(e)}let rD=null,rk=(0,Q.memo)(e=>{let{postProcess:t}=e,i=(0,sT.useTranslations)("Products.sentio"),[s,r]=(0,Q.useState)(""),[a,n]=(0,Q.useState)(!1),[l,o]=(0,Q.useState)(!1),{enable:u,engine:h,settings:g}=sC(),{chat:d,abort:c,chatting:_}=rF(),{startAudioTimer:m,stopAudioTimer:p}=rV(),f=()=>{c(),null==rD&&(rD=new(rT())({sampleBits:16,sampleRate:16e3,numChannels:1,compiling:!1})),rD.start().then(()=>{m(),n(!0)},()=>{(0,sX.$U)({title:i("micOpenError"),variant:"flat",color:"danger"})})},y=async()=>{if(rD.stop(),n(!1),!p())return;r(i("speech2text")),o(!0);let e=rb(rD),t="";(t=await s7(h,g,e)).length>0?r(t):r(""),o(!1)},x=()=>{""!=s&&(d(s,t),r(""))};return(0,Q.useEffect)(()=>{let e=e=>{"m"===e.key&&e.ctrlKey&&(a?y():f())};return window.addEventListener("keydown",e),()=>{window.removeEventListener("keydown",e)}}),(0,K.jsx)("div",{className:"flex flex-col w-4/5 md:w-2/3 2xl:w-1/2 items-start z-10 gap-2",children:(0,K.jsxs)("div",{className:"flex w-full items-center z-10",children:[(0,K.jsx)(sY.r,{className:"opacity-90",startContent:(0,K.jsx)("button",{type:"button",disabled:!u,"aria-label":"toggle password visibility",className:(0,sN.A)("focus:outline-none",a?"text-red-500":u?"hover:text-green-500":"hover:text-gray-500"),children:a?(0,K.jsx)(sU.A,{className:"size-6",onClick:y}):l?(0,K.jsx)(i4.o,{size:"sm"}):(0,K.jsx)(sH.I,{className:"opacity-90",content:"Ctrl + M",children:(0,K.jsx)(sz.A,{className:"size-6",onClick:f})})}),endContent:_?(0,K.jsx)("button",{type:"button",onClick:c,className:"focus:outline-none hover:text-red-500",children:(0,K.jsx)(sU.A,{className:"size-6"})}):(0,K.jsx)(K.Fragment,{}),type:"text",enterKeyHint:"send",value:s,onValueChange:r,onKeyDown:e=>{"Enter"===e.key&&x()},disabled:a||l}),(0,K.jsx)(sW.T,{className:"opacity-90",isIconOnly:!0,color:"primary",onPress:x,children:(0,K.jsx)(sG.A,{className:"size-6"})})]})})}),rN=e=>{let t=new Uint8Array(e.length),i=e.length;for(let s=0;s<i;s++){let i=Math.round((e[s]+1)*128);t[s]=Math.max(0,Math.min(255,i))}return t},rO=(0,Q.memo)(()=>{let e=(0,sT.useTranslations)("Products.sentio"),t=(0,Q.useRef)(null),i=(0,Q.useRef)(null),{engine:s,settings:r}=sC(),{chat:a,abort:n}=rF(),{startAudioTimer:l,stopAudioTimer:o}=rV(),u=(0,Q.useRef)(),h=(0,Q.useRef)(null),g=async e=>{let t=rv(e),i="";(i=await s7(s,r,t)).length>0&&a(i)},d=(0,rE.useMicVAD)({baseAssetPath:rL("vad/"),onnxWASMBasePath:rL("vad/"),onSpeechStart:()=>{n(),l()},onFrameProcessed:(e,t)=>{let i=rN(t);u.current=i},onSpeechEnd:e=>{o()&&g(e)}}),c=()=>{let e=window.devicePixelRatio||1,s=document.getElementById("voice-input");if(s){let{width:r,height:a}=s.getBoundingClientRect();s.width=e*r,s.height=e*a,t.current=s;let n=s.getContext("2d");n&&(n.scale(e,e),n.fillStyle="rgb(215, 183, 237)",i.current=n)}};function _(){let e=t.current,s=i.current;if(e&&s&&u.current){let t=[].slice.call(u.current),i=parseInt("".concat(e.width/3)),r=parseInt("".concat(t.length/i));s.clearRect(0,0,e.width,e.height),s.beginPath();let a=0;for(let n=0;n<i;n++){let i=(t.slice(n*r,n*r+r).reduce((e,t)=>e+t,0)/r-128)/128*e.height;s.moveTo(a,16),s.roundRect?s.roundRect(a,16-i,2,i,[1,1,0,0]):s.rect(a,16-i,2,i),s.fill(),a+=3}s.closePath()}h.current=requestAnimationFrame(_)}return(0,Q.useEffect)(()=>(c(),h.current=requestAnimationFrame(_),()=>{h.current&&cancelAnimationFrame(h.current)}),[]),(0,K.jsxs)("div",{className:"flex flex-col h-10 w-1/2 md:w-1/3 items-center",children:[d.loading&&(0,K.jsxs)("div",{className:"flex flex-row gap-1 items-center",children:[(0,K.jsx)("p",{className:"text-xl font-bold",children:e("loading")}),(0,K.jsx)(i4.o,{color:"warning",variant:"dots",size:"lg"})]}),(0,K.jsx)("canvas",{id:"voice-input",className:"h-full w-full"})]})}),rj=(0,Q.memo)(()=>{let e=(0,sT.useTranslations)("Products.sentio"),t=(0,Q.useRef)(null),i=(0,Q.useRef)(null),{chat:s,abort:r}=rF(),{engine:a,settings:n}=sC(),{getLastRecord:l,updateLastRecord:o,addChatRecord:u,deleteLastRecord:h}=sx(),g=(0,Q.useRef)(),d=(0,Q.useRef)(null),[c,_]=(0,Q.useState)(!0),m=(0,Q.useRef)(!1),p=()=>{let e=window.devicePixelRatio||1,s=document.getElementById("voice-input");if(s){let{width:r,height:a}=s.getBoundingClientRect();s.width=e*r,s.height=e*a,t.current=s;let n=s.getContext("2d");n&&(n.scale(e,e),n.fillStyle="rgb(215, 183, 237)",i.current=n)}};function f(){let e=t.current,s=i.current;if(e&&s&&g.current){let t=[].slice.call(g.current),i=parseInt("".concat(e.width/10)),r=parseInt("".concat(t.length/i));s.clearRect(0,0,e.width,e.height),s.beginPath();let a=0;for(let n=0;n<i;n++){let i=(t.slice(n*r,n*r+r).reduce((e,t)=>e+t,0)/r-128)/128*e.height;s.moveTo(a,16),s.roundRect?s.roundRect(a,16-i,2,i,[1,1,0,0]):s.rect(a,16-i,2,i),s.fill(),a+=10}s.closePath()}d.current=requestAnimationFrame(f)}return(0,Q.useEffect)(()=>{let e=function(e){var t;return new r_((t="/adh/asr/v0/engine/stream").includes("ws")?t:sK().replace("https","wss").replace("http","ws")+t,e.engine,e.config,e.onMessage,e.onOpen,e.onClose,e.onError)}({engine:a,config:n,onMessage:(e,t)=>{let i=new TextDecoder("utf-8").decode(t).trim();switch(e){case rc.ENGINE_INITIALZING:break;case rc.ENGINE_STARTED:_(!1),m.current=!0;break;case rc.ENGINE_PARTIAL_OUTPUT:let a=l();a&&a.role==ss.AI?(r(),u({role:ss.HUMAN,think:"",content:i})):o({role:ss.HUMAN,think:"",content:i});break;case rc.ENGINE_FINAL_OUTPUT:h(),s(i);break;case rc.ENGINE_STOPPED:_(!0),m.current=!1;break;case rc.ERROR:_(!0),m.current=!1,(0,sX.$U)({title:i,variant:"flat",color:"danger"})}},onError:e=>{(0,sX.$U)({title:e.message,variant:"flat",color:"danger"})}}),t=new rB(16e3,1,1920,t=>{try{e.isConnected&&m.current&&e.sendMessage(rd.ENGINE_PARTIAL_INPUT,t)}catch(e){(0,sX.$U)({title:e.message,variant:"flat",color:"danger"})}},e=>{m.current&&(g.current=rN(e))});return p(),d.current=requestAnimationFrame(f),e.connect(),t.start(),()=>{t.stop(),e.disconnect(),d.current&&cancelAnimationFrame(d.current)}},[]),(0,K.jsxs)("div",{className:"flex flex-col h-10 w-1/2 md:w-1/3 items-center",children:[c&&(0,K.jsxs)("div",{className:"flex flex-row gap-1 items-center",children:[(0,K.jsx)("p",{className:"text-xl font-bold",children:e("loading")}),(0,K.jsx)(i4.o,{color:"warning",variant:"dots",size:"lg"})]}),(0,K.jsx)("canvas",{id:"voice-input",className:"h-full w-full"})]})});function rU(){let{chatMode:e}=sw(),{infer_type:t}=sC();return(0,K.jsxs)("div",{className:"flex flex-col full-height-minus-64px pb-6 md:px-6 gap-6 justify-between items-center z-10",children:[(0,K.jsx)(sj,{}),e==sr.IMMSERSIVE?t==i9.NORMAL?(0,K.jsx)(rO,{}):(0,K.jsx)(rj,{}):(0,K.jsx)(rk,{})]})}let rz=(0,Q.memo)(function(){let{theme:e}=sB();return si.FREEDOM,(0,K.jsx)(rU,{})});var rG=i(45618),rX=i(86121),rY=i(71553),rH=i(84906),rW=i(81697),rq=i(16861),rJ=i(39612),rZ=i(33662),rK=i(69451),rQ=i(63665),r$=i(49076),r0=i(18834),r1=i(11315),r2=i(87486),r3=i(98319),r4=i(62907),r5=i(55113),r6=i(89007),r8=i(27810);function r9(){let e=(0,sT.useTranslations)("Products.sentio.settings.basic"),{sound:t,lipFactor:i,showThink:s,setSound:r,setLipFactor:a,setShowThink:n}=sS(),l=(e,t)=>(0,K.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,K.jsx)("p",{children:e}),t]});return(0,K.jsx)(sF.Z,{children:(0,K.jsx)(sL.U,{children:(0,K.jsx)("div",{className:"flex flex-col gap-2",children:(0,K.jsxs)("div",{className:"flex flex-col mt-2 gap-6",children:[l(e("soundSwitch"),(0,K.jsx)(r6.Z,{isSelected:t,color:"primary",onValueChange:e=>{r(e)}})),l(e("showThink"),(0,K.jsx)(r6.Z,{isSelected:s,color:"primary",onValueChange:e=>{n(e)}})),l(e("lipFactor"),(0,K.jsx)(r8.Q,{className:"max-w-md",defaultValue:i,minValue:0,maxValue:10,step:.1,label:" ",onChangeEnd:e=>{let t="number"==typeof e?e:e[0];a(t),iU.getInstance().setLipFactor(t)}}))]})})})})}var r7=i(93874),ae=i(6209),at=i(65994),ai=i(73430),as=i(4115),ar=i(22353);function aa(e){return(0,K.jsx)(sH.I,{className:"px-1.5 text-tiny text-default-600 rounded-small",content:e.content,placement:"right",children:(0,K.jsx)("span",{className:"transition-opacity opacity-80 hover:opacity-100",children:(0,K.jsx)(ar.A,{className:"size-5"})})})}var an=i(34349),al=i(5933);let ao=(0,Q.memo)(function(e){let[t,i]=(0,Q.useState)(!1),[s,r]=(0,Q.useState)(!1),{engine:a,settings:n,setSettings:l}=sb(),o=(0,Q.useRef)(null),u=new(window.AudioContext||window.webkitAudioContext),h=(0,Q.useRef)(null),g=e=>{let t=rR(e),s=e=>{var t=u.createBufferSource();t.buffer=e,t.connect(u.destination),t.onended=()=>{i(!1),h.current=null,o.current=null},t.start(),h.current=t};rP(t).then(e=>{let t=e.slice(0);r(!1),u.decodeAudioData(t).then(e=>{s(e)})})},d=()=>{i(!1),r(!1),o.current=null,h.current=null};return(0,K.jsxs)("div",{className:"flex flex-row items-center justify-center max-w-md gap-2",children:[(0,K.jsx)(ae.H,{name:e.name,label:e.name,required:!!e.required,placeholder:e.description,selectedKey:n[e.name],onSelectionChange:t=>{l({...n,[e.name]:t})},children:e.choices.map(e=>(0,K.jsx)(at.y,{children:e},e))}),(0,K.jsx)(sW.T,{isIconOnly:!0,isLoading:s,"aria-label":"play audio",variant:"light",children:t?(0,K.jsx)(an.A,{className:"size-6",onClick:()=>{o.current&&(o.current.abort(),o.current=null),h.current&&(h.current.stop(),h.current=null),i(!1)}}):(0,K.jsx)(al.A,{className:"size-6",onClick:()=>{i(!0),r(!0),o.current=new AbortController,ra(a,n,sp[Math.floor(Math.random()*sp.length)],o.current.signal).then(e=>{e?g(e):d()})}})})]})});function au(e){let[t,i]=(0,Q.useState)(e.defaultValue),[s,r]=(0,Q.useState)(e.defaultValue.toString()),a=t=>{isNaN(Number(t))||(i(t),r(t.toString()),e.onChange(t))},n=()=>(0,K.jsx)("span",{children:e.description});return(0,K.jsx)(r8.Q,{className:"max-w-md p-1",classNames:{base:"max-w-md",label:"text-medium"},name:e.label,label:e.label,maxValue:e.maxValue,minValue:e.minValue,renderLabel:e=>{let{children:t,...i}=e;return(0,K.jsxs)("label",{...i,className:"text-medium flex gap-2 items-center",children:[t,(0,K.jsx)(sH.I,{className:"px-1.5 text-tiny text-default-600 rounded-small",content:n(),placement:"right",children:(0,K.jsx)("span",{className:"transition-opacity opacity-80 hover:opacity-100",children:(0,K.jsx)(ar.A,{className:"size-5"})})})]})},renderValue:e=>{let{children:t,...i}=e;return(0,K.jsx)("output",{...i,children:(0,K.jsx)(sH.I,{className:"text-tiny text-default-500 rounded-md",content:"Press Enter to confirm",placement:"left",children:(0,K.jsx)("input",{"aria-label":"Input value",className:"px-1 py-0.5 w-12 text-right text-small text-default-700 font-medium bg-default-100 outline-none transition-colors rounded-small border-medium border-transparent hover:border-primary focus:border-primary",type:"text",value:s,onChange:e=>{r(e.target.value)},onKeyDown:e=>{"Enter"!==e.key||isNaN(Number(s))||a(Number(s))}})})})},step:e.step,value:t,onChange:a},e.label)}function ah(){return(0,K.jsxs)("div",{className:"space-y-3",children:[(0,K.jsx)(ai.m,{className:"max-w-xs rounded-lg",children:(0,K.jsx)("div",{className:"h-8 max-w-xs rounded-lg bg-default-200"})}),(0,K.jsx)(ai.m,{className:"max-w-xs rounded-lg",children:(0,K.jsx)("div",{className:"h-8 max-w-xs rounded-lg bg-default-200"})}),(0,K.jsx)(ai.m,{className:"max-w-xs rounded-lg",children:(0,K.jsx)("div",{className:"h-8 max-w-xs rounded-lg bg-default-300"})})]})}let ag=(0,Q.memo)(e=>{let{params:t,settings:i,setSettings:s}=e;return(0,K.jsx)(K.Fragment,{children:t.map(e=>{switch(e.type){case i7.STRING:if(e.choices.length>0)return"voice"==e.name?(0,K.jsx)(ao,{name:e.name,description:e.description,required:!!e.required,choices:e.choices,default:e.default},e.name):(0,K.jsx)(ae.H,{className:"max-w-md",isReadOnly:!0,name:e.name,label:e.name,required:!!e.required,placeholder:e.description,selectedKey:i[e.name],onSelectionChange:t=>{s({...i,[e.name]:t})},children:e.choices.map(e=>(0,K.jsx)(at.y,{children:e},e))},e.name);return(0,K.jsx)(sY.r,{className:"max-w-md",name:e.name,label:e.name,required:!!e.required,placeholder:e.description,value:i[e.name],onValueChange:t=>{s({...i,[e.name]:t})}},e.name);case i7.INT:case i7.FLOAT:if(e.range.length>0)return(0,K.jsx)(au,{label:e.name,description:e.description,minValue:e.range[0],maxValue:e.range[1],defaultValue:e.default,step:e.type==i7.INT?1:.01,onChange:t=>{s({...i,[e.name]:t})}},e.description);if(e.choices.length>0)return(0,K.jsx)(ae.H,{className:"max-w-xs",isReadOnly:!0,name:e.name,label:e.name,required:!!e.required,placeholder:e.description,selectedKey:i[e.name],onSelectionChange:t=>{s({...i,[e.name]:t})},children:e.choices.map(e=>(0,K.jsx)(at.y,{children:e},e))},e.name);case i7.BOOL:return}})})}),ad=(0,Q.memo)(e=>{let{engine:t,engineList:i,onEngineChange:s}=e;return(0,K.jsxs)("div",{className:"flex flex-row gap-2",children:[(0,K.jsx)(ae.H,{className:"max-w-xs",color:"warning","aria-label":"engineSelect",name:"engineSelect",selectedKey:t,onSelectionChange:e=>s(e),children:Object.values(i).map(e=>(0,K.jsx)(at.y,{children:e.name},e.name))},"engineSelect"),(0,K.jsx)(aa,{content:(()=>{var e,s,r,a;return(0,K.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,K.jsx)("p",{className:"font-bold",children:null===(e=i[t])||void 0===e?void 0:e.desc}),(null===(s=i[t])||void 0===s?void 0:s.meta.official)&&(0,K.jsx)(r7.h,{href:i[t].meta.official,isExternal:!0,className:"text-xs hover:underline",children:"\uD83D\uDC49 前往官网"}),(null===(r=i[t])||void 0===r?void 0:r.meta.configuration)&&(0,K.jsx)(r7.h,{href:i[t].meta.configuration,isExternal:!0,className:"text-xs hover:underline",children:"\uD83D\uDC49 如何配置"}),(null===(a=i[t])||void 0===a?void 0:a.meta.tips)&&(0,K.jsx)("p",{className:"text-xs text-yellow-500",children:"Tips: ".concat(i[t].meta.tips)})]})})()})]})}),ac=()=>(0,K.jsx)(ai.m,{className:"max-w-xs rounded-lg",children:(0,K.jsx)("div",{className:"h-8 max-w-xs rounded-lg bg-default-300"})}),a_=(0,Q.memo)(e=>{let{engineType:t}=e,i=(0,sT.useTranslations)("Products.sentio.settings"),{clearChatRecord:s}=sx(),{chatMode:r}=sw(),{enable:a,engine:n,settings:l,setEnable:o,setInferType:u,setEngine:h,setSettings:g}=(()=>{switch(t){case i8.ASR:return sC();case i8.TTS:return sb();case i8.AGENT:return sv()}})(),[d,c]=(0,Q.useState)(!0),[_,m]=(0,Q.useState)(!0),p=(0,Q.useRef)({}),f=(0,Q.useRef)([]),y=(e,t)=>{(function(e,t){switch(e){case i8.ASR:return s9(t);case i8.TTS:return rr(t);case i8.AGENT:return ru(t)}})(e,t).then(i=>{let s={};for(var r in i){let e=i[r];s[e.name]=e.default}if(Object.keys(l).length!=i.length&&g(s),Object.keys(s).length>0)for(var r in i){let e=i[r];e.name in l&&(e.default=l[e.name])}f.current=i,e==i8.TTS&&"voice"in s?(console.log("set voice",l),ri(t,l).then(e=>{for(var t in i){let s=i[t];if("voice"==s.name){s.choices=e.map(e=>e.name);break}}f.current=i,m(!1)})):m(!1)})},x=e=>{null!=e&&(m(!0),s(),f.current=[],h(e),u(p.current[e].infer_type),y(t,e))};(0,Q.useEffect)(()=>{rh(t).then(e=>{let i=e.filter(function(e){return r==sr.IMMSERSIVE||e.infer_type==i9.NORMAL});p.current=i.reduce((e,t)=>(e[t.name]=t,e),{}),c(!1),i.map(e=>e.name).includes(n)?(m(!0),f.current=[],h(n),u(p.current[n].infer_type),y(t,n)):rg(t).then(e=>{x(e.name)})})},[]);let S=(0,Q.memo)(e=>{let{show:t,onSelect:s}=e;return t&&(0,K.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,K.jsx)(r6.Z,{isSelected:a,color:"primary",onValueChange:s,children:i("switch")}),(0,K.jsx)(as.y,{})]})});return(0,K.jsx)(sF.Z,{children:(0,K.jsx)(sL.U,{className:"p-4",children:(0,K.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,K.jsx)(S,{show:t!=i8.AGENT,onSelect:e=>o(e)}),a&&(0,K.jsxs)(K.Fragment,{children:[(0,K.jsxs)("div",{className:"flex flex-col gap-1",children:[(0,K.jsx)("p",{className:"m-2 text-lg",children:i("selectEngine")}),d?(0,K.jsx)(ac,{}):(0,K.jsx)(ad,{engine:n,engineList:p.current,onEngineChange:x})]}),(0,K.jsxs)("div",{className:"flex flex-col gap-1 w-full",children:[(0,K.jsx)("p",{className:"m-2 text-lg",children:i("engineConfig")}),(0,K.jsx)("div",{className:"flex flex-col gap-1",children:_?(0,K.jsx)(ah,{}):(0,K.jsx)(ag,{params:f.current,settings:l,setSettings:g})})]})]})]})})})});function am(){return(0,K.jsx)(a_,{engineType:i8.ASR})}function ap(){return(0,K.jsx)(a_,{engineType:i8.TTS})}function af(){return(0,K.jsx)(a_,{engineType:i8.AGENT})}function ay(){let e=(0,sT.useTranslations)("Products.sentio.settings");return(0,K.jsxs)(r4.r,{"aria-label":"Settings",destroyInactiveTabPanel:!1,children:[(0,K.jsx)(r5.i,{title:e("basic.title"),children:(0,K.jsx)(r9,{})},"basic"),(0,K.jsx)(r5.i,{title:e("asr.title"),children:(0,K.jsx)(am,{})},"asr"),(0,K.jsx)(r5.i,{title:e("tts.title"),children:(0,K.jsx)(ap,{})},"tts"),(0,K.jsx)(r5.i,{title:e("agent.title"),children:(0,K.jsx)(af,{})},"agent")]})}function ax(){let{theme:e}=sB();return si.FREEDOM,(0,K.jsx)(ay,{})}function aS(e){let{isOpen:t,onClose:i}=e,s=(0,sT.useTranslations)("Common"),r=(0,sT.useTranslations)("Products.sentio.settings"),{isOpen:a,onOpen:n,onOpenChange:l}=(0,rK.j)({isOpen:t,onClose:i}),o=(0,Q.useRef)(null),{moveProps:u}=(0,rQ.P)({targetRef:o,isDisabled:!a});return(0,K.jsx)(r$.Y,{ref:o,isOpen:t,onOpenChange:l,size:"3xl",placement:"top",scrollBehavior:"inside",children:(0,K.jsxs)(r0.g,{children:[(0,K.jsx)(r1.c,{...u,children:r("title")}),(0,K.jsx)(r2.h,{className:"no-scrollbar",children:(0,K.jsx)(ax,{})}),(0,K.jsx)(r3.q,{children:(0,K.jsx)(sW.T,{color:"danger",variant:"light",onPress:i,children:s("close")})})]})})}var aC=i(74973),ab=i(50967),av=i(77454);function aM(e){let{current:t,descs:i,enable:s,showType:r,choiceFunc:a}=e,n=[se.ALL,st.ALL];return(0,K.jsx)("div",{className:"gap-6 grid grid-cols-2 sm:grid-cols-4 max-h-96",children:s&&i.map((e,i)=>(e.sub_type==r||n.includes(r))&&(0,K.jsxs)(sF.Z,{shadow:"md",isPressable:!0,onPress:()=>a(i),className:(0,sN.A)("text-small justify-between h-fit",{"text-blue-600 border-2 border-indigo-600":!!t&&e.resource_id==t.resource_id}),children:[(0,K.jsx)(sL.U,{className:"overflow-visible p-0",children:e.link.endsWith(".mp4")?(0,K.jsx)("video",{className:"w-full object-cover h-[120px]",autoPlay:!0,muted:!0,loop:!0,poster:rL("image/loading.png"),src:e.link,style:{pointerEvents:"none"}}):(0,K.jsx)(aC.W,{shadow:"sm",radius:"lg",width:"100%",alt:e.name,className:"w-full object-cover h-[120px]",src:e.link,isZoomed:!0,style:{objectFit:"cover"}})}),(0,K.jsx)(ab.Z,{className:"text-small justify-between",children:(0,K.jsx)("b",{children:e.name})})]},i))})}function aP(){let e=(0,sT.useTranslations)("Products.sentio.gallery.backgrounds"),{background:t,setBackground:i}=sM(),[s,r]=(0,Q.useState)(null!=t),[a,n]=(0,Q.useState)(e("all")),l={[e("all")]:se.ALL,[e("static")]:se.STATIC,[e("dynamic")]:se.DYNAMIC},o=e=>{var t=[];let i=e==se.STATIC?so:su,s=e==se.STATIC?"sentio/backgrounds/static":"sentio/backgrounds/dynamic";for(let r of i){let i=r.split(".")[0];t.push({resource_id:"".concat(e,"_").concat(r),type:sl.BACKGROUND,sub_type:e,name:i,link:rL("".concat(s,"/").concat(r))})}return t},u=[...(0,Q.useMemo)(()=>o(se.STATIC),[]),...(0,Q.useMemo)(()=>o(se.DYNAMIC),[])],h=e=>{null!=e?i(u[e]):i(null)};return(0,K.jsx)(sF.Z,{children:(0,K.jsx)(sL.U,{children:(0,K.jsxs)("div",{className:"flex flex-col gap-4 max-h-96 overflow-y-auto",children:[(0,K.jsx)(r6.Z,{defaultSelected:null!=t,color:"primary",onValueChange:e=>{r(e),e||h(null)},children:e("enable")}),(0,K.jsx)(as.y,{}),s&&(0,K.jsx)("div",{className:"flex flex-row items-center gap-2",children:(0,K.jsx)(av.d,{className:"max-w-md",name:"bgTypeSelect",label:e("select"),defaultSelectedKeys:[a],onSelectionChange:e=>n(e.currentKey),children:Object.keys(l).map(e=>(0,K.jsx)(at.y,{children:e},e))},e("select"))}),(0,K.jsx)(aM,{current:t,descs:u,enable:s,showType:l[a],choiceFunc:h})]})})})}function aw(){let e=(0,sT.useTranslations)("Products.sentio.gallery.characters"),{character:t,setCharacter:i}=sP(),[s,r]=(0,Q.useState)(e("all")),{setLive2dCharacter:a}=sE(),n={[e("all")]:st.ALL,[e("ip")]:st.IP,[e("free")]:st.FREE},l=e=>{var t=[];let i=e==st.FREE?sd:sg,s=e==st.FREE?sh:"sentio/characters/ip";for(let r of i)t.push({resource_id:"".concat(e,"_").concat(r),name:r,sub_type:e,link:rL("".concat(s,"/").concat(r,"/").concat(r,".png")),type:sl.CHARACTER});return t},o=[...(0,Q.useMemo)(()=>l(st.FREE),[]),...(0,Q.useMemo)(()=>l(st.IP),[])];return(0,K.jsx)(sF.Z,{children:(0,K.jsx)(sL.U,{children:(0,K.jsxs)("div",{className:"flex flex-col gap-4 max-h-96 overflow-y-auto",children:[(0,K.jsx)("div",{className:"flex flex-row items-center gap-2",children:(0,K.jsx)(av.d,{className:"max-w-md",name:"characterTypeSelect",label:e("select"),defaultSelectedKeys:[s],onSelectionChange:e=>r(e.currentKey),children:Object.keys(n).map(e=>(0,K.jsx)(at.y,{children:e},e))},e("select"))}),(0,K.jsx)(r7.h,{className:"hover:underline text-sm w-fit ml-2",href:"https://light4ai.feishu.cn/share/base/form/shrcnb0d1Au4dvMaswHNGDbUNTR",color:"warning",isExternal:!0,children:"\uD83D\uDC49 定制人物模型"}),(0,K.jsx)(aM,{current:t,descs:o,enable:!0,showType:n[s],choiceFunc:e=>{null!=e?(t.name!=o[e].name||t.resource_id!=o[e].resource_id)&&(i(o[e]),a(o[e])):i(null)}})]})})})}function aB(){let e=(0,sT.useTranslations)("Products.sentio.gallery");return(0,K.jsxs)(r4.r,{"aria-label":"Gallery",children:[(0,K.jsx)(r5.i,{title:e("characters.title"),children:(0,K.jsx)(aw,{})},"characters"),(0,K.jsx)(r5.i,{title:e("backgrounds.title"),children:(0,K.jsx)(aP,{})},"backgrounds")]})}function aI(e){let{isOpen:t,onClose:i}=e,s=(0,sT.useTranslations)("Common"),r=(0,sT.useTranslations)("Products.sentio.gallery"),{isOpen:a,onOpen:n,onOpenChange:l}=(0,rK.j)({isOpen:t,onClose:i}),o=(0,Q.useRef)(null),{moveProps:u}=(0,rQ.P)({targetRef:o,isDisabled:!a});return(0,K.jsx)(r$.Y,{ref:o,isOpen:t,onOpenChange:l,size:"5xl",placement:"center",scrollBehavior:"outside",children:(0,K.jsxs)(r0.g,{children:[(0,K.jsx)(r1.c,{...u,className:"flex flex-col gap-1",children:r("title")}),(0,K.jsx)(r2.h,{children:(0,K.jsx)(aB,{})}),(0,K.jsx)(r3.q,{children:(0,K.jsx)(sW.T,{color:"danger",variant:"light",onPress:i,children:s("close")})})]})})}function aT(){let e=(0,sT.useTranslations)("Products.sentio.items"),[t,i]=(0,Q.useState)(!1),[s,r]=(0,Q.useState)(!1),[a,n]=(0,Q.useState)(!1);return(0,K.jsxs)("div",{children:[(0,K.jsxs)(rG.A,{placement:"bottom-start",onOpenChange:e=>i(e),children:[(0,K.jsx)(rX.b,{children:(0,K.jsx)(sW.T,{isIconOnly:!0,variant:"light",children:t?(0,K.jsx)(rW.A,{className:"size-6"}):(0,K.jsx)(rq.A,{className:"size-6"})})}),(0,K.jsxs)(rY.y,{"aria-label":"Items Actions",variant:"flat",children:[(0,K.jsx)(rH.Y,{startContent:(0,K.jsx)(rJ.A,{className:"size-6"}),onPress:()=>r(!0),children:e("setting")},"setting"),(0,K.jsx)(rH.Y,{startContent:(0,K.jsx)(rZ.A,{className:"size-6"}),onPress:()=>n(!0),children:e("gallery")},"gallery")]})]}),(0,K.jsx)(aS,{isOpen:s,onClose:()=>r(!1)}),(0,K.jsx)(aI,{isOpen:a,onClose:()=>n(!1)})]})}var aE=i(71972),aR=i(80195),aV=i(46763),aA=i(13163),aF=i(86037),aL=i(75664);function aD(){let e=(0,sT.useTranslations)("Products.sentio"),{chatMode:t,setChatMode:i}=sw(),{enable:s}=sC(),{clearChatRecord:r}=sx();return(0,K.jsx)(r6.Z,{color:"secondary",startContent:(0,K.jsx)(aR.A,{}),endContent:(0,K.jsx)(aV.A,{}),isSelected:t==sr.IMMSERSIVE,onValueChange:t=>{s?(i(t?sr.IMMSERSIVE:sr.DIALOGUE),r()):(0,sX.$U)({title:e("asrEnableTip"),color:"warning"})}})}function ak(){var e;let{authState:t,logout:i}=(0,aF.J)(),s=(0,aL.useRouter)(),r=async()=>{try{await i(),s.push("/auth/login")}catch(e){console.error("Logout failed:",e),(0,sX.$U)({title:"登出失败",color:"danger"})}};return(0,K.jsxs)(rG.A,{placement:"bottom-end",children:[(0,K.jsx)(rX.b,{children:(0,K.jsx)(aE.Q,{as:"button",className:"transition-transform",color:"secondary",name:(null===(e=t.email)||void 0===e?void 0:e.charAt(0).toUpperCase())||"U",size:"sm",icon:(0,K.jsx)(sV.A,{})})}),(0,K.jsxs)(rY.y,{"aria-label":"用户菜单",variant:"flat",children:[(0,K.jsxs)(rH.Y,{className:"h-14 gap-2",children:[(0,K.jsx)("p",{className:"font-semibold",children:"登录为"}),(0,K.jsx)("p",{className:"font-semibold",children:t.email})]},"profile"),(0,K.jsx)(rH.Y,{color:"danger",startContent:(0,K.jsx)(aA.A,{className:"w-4 h-4"}),onClick:r,children:"登出"},"logout")]})]})}function aN(){return(0,K.jsx)("div",{className:"flex w-full h-[64px] p-6 justify-end z-10",children:(0,K.jsxs)("div",{className:"flex flex-row gap-4 items-center",children:[(0,K.jsx)(aD,{}),(0,K.jsx)(ak,{}),(0,K.jsx)(aT,{})]})})}function aO(){let{setAppConfig:e}=function(){let{enable:e,setEnable:t,engine:i,setEngine:s,settings:r,setSettings:a}=sC(),{enable:n,setEnable:l,engine:o,setEngine:u,setSettings:h,settings:g}=sb(),{engine:d,setEngine:c,setSettings:_,settings:m}=sv(),{background:p,setBackground:f}=sM(),{character:y,setCharacter:x}=sP(),{sound:S,setSound:C,showThink:b,setShowThink:v,lipFactor:M,setLipFactor:P}=sS(),{chatMode:w,setChatMode:B}=sw(),{theme:I,setTheme:T}=sB(),{clearChatRecord:E}=sx(),{setLive2dCharacter:R}=sE(),V=e=>{if(null==e){let e={resource_id:"FREE_HaruGreeter",name:sc,link:rL(s_),type:sl.CHARACTER};x(e),R(e)}else x(e),R(e)},A=e=>{T(e)},F=e=>{P(e),iU.getInstance().setLipFactor(e)},L=e=>{s(e||"default"),u(e||"default"),c(e||"default"),a({}),h({}),_({})},D=()=>{t(!0),l(!0),L(),E(),f(null),x(null),C(!0),v(!0),A(sy),F(5),B(sf)};return{getAppConfig:()=>({asr_enable:e,tts_enable:n,asr:{name:i,type:i8.ASR,config:r},tts:{name:o,type:i8.TTS,config:g},llm:{name:d,type:i8.LLM,config:m},agent:{name:d,type:i8.AGENT,config:m},background:p,character:y,type:I,ext:{sound:S,showThink:b,lip_factor:M,chat_mode:w}}),setAppConfig:e=>{e?(t(e.asr_enable),e.asr&&s(e.asr.name),e.asr&&a(e.asr.config),l(e.tts_enable),e.tts&&u(e.tts.name),e.tts&&h(e.tts.config),e.agent&&c(e.agent.name),e.agent&&_(e.agent.config),f(e.background),V(e.character),T(e.type),C(e.ext.sound),v(e.ext.showThink),F(e.ext.lip_factor),B(e.ext.chat_mode),iU.getInstance().setLipFactor(e.ext.lip_factor)):(D(),V(null))},resetAppConfig:D,resetAppEngine:L,setCurrentTheme:A}}(),[t,i]=(0,Q.useState)(!0);return(0,Q.useEffect)(()=>{e(null),i(!1)},[]),(0,K.jsxs)("div",{className:"w-full h-full",children:[t?(0,K.jsx)(i4.o,{className:"w-screen h-screen z-10",color:"secondary",size:"lg",variant:"wave"}):(0,K.jsxs)("div",{className:"flex flex-col w-full h-full",children:[(0,K.jsx)(aN,{}),(0,K.jsx)(rz,{})]}),(0,K.jsx)(sR,{})]})}},52599:(e,t,i)=>{"use strict";i.d(t,{ProtectedRoute:()=>o});var s=i(12389),r=i(41473),a=i(75664),n=i(86037),l=i(34583);let o=e=>{let{children:t}=e,i=(0,a.useRouter)(),{isAuthenticated:o}=(0,n.J)(),[u,h]=(0,r.useState)(!0);return((0,r.useEffect)(()=>{let e=setTimeout(()=>{o?h(!1):i.push("/auth/login")},1e3);return()=>clearTimeout(e)},[o,i]),(0,r.useEffect)(()=>{o&&h(!1)},[o]),u)?(0,s.jsxs)("div",{className:"min-h-screen bg-zinc-900 flex flex-col items-center justify-center",children:[(0,s.jsx)(l.o,{size:"lg",color:"secondary"}),(0,s.jsx)("p",{className:"mt-4 text-gray-400",children:"正在验证身份..."})]}):o?(0,s.jsx)(s.Fragment,{children:t}):(0,s.jsx)("div",{className:"min-h-screen bg-zinc-900 flex flex-col items-center justify-center",children:(0,s.jsx)("p",{className:"text-gray-400",children:"正在跳转到登录页面..."})})}},86037:(e,t,i)=>{"use strict";i.d(t,{J:()=>l,v:()=>o});var s=i(12389),r=i(41473),a=i(90959);let n=(0,r.createContext)({pipeline:null,setPipeline:()=>{},selectedModel:"null",setSelectedModel:()=>{},isAuthenticated:!1,login:async()=>({success:!1,userRole:"user"}),loginWithToken:async()=>({success:!1,userRole:"user"}),logout:async()=>{},unsetCredentials:async()=>{},register:async()=>{},authState:{isAuthenticated:!1,email:null,userRole:null,userId:null},getClient:()=>null,client:null,viewMode:"user",setViewMode:()=>{},isSuperUser:()=>!1,createUser:async()=>{throw Error("createUser is not implemented in the default context")}}),l=()=>{let e=(0,r.useContext)(n);if(!e)throw Error("useUser must be used within a UserProvider");return e},o=e=>{let{children:t}=e,[l,o]=(0,r.useState)(null),[u,h]=(0,r.useState)(null),[g,d]=(0,r.useState)("null"),[c,_]=(0,r.useState)("user"),[m,p]=(0,r.useState)({isAuthenticated:!1,email:null,userRole:null,userId:null}),f=(0,r.useCallback)(()=>"admin"===m.userRole&&"admin"===c,[m.userRole,c]),[y,x]=(0,r.useState)(null),S=(0,r.useCallback)(async(e,t,i)=>{let s=new a.r2rClient(i);try{var r,n,l,u;let i=await s.users.login({email:e,password:t}),a=(null===(r=i.results.accessToken)||void 0===r?void 0:r.token)||(null===(n=i.results.access_token)||void 0===n?void 0:n.token),h=(null===(l=i.results.refreshToken)||void 0===l?void 0:l.token)||(null===(u=i.results.refresh_token)||void 0===u?void 0:u.token);if(!a)throw Error("No access token received from server");localStorage.setItem("livechatAccessToken",a),h&&localStorage.setItem("livechatRefreshToken",h),s.setTokens(a,h||""),o(s);let g=await s.users.me();if(!g.results)throw Error("Failed to get user information");let d="user";try{await s.system.settings(),d="admin"}catch(e){e instanceof Error&&"status"in e&&403===e.status||console.error("Unexpected error when checking user role:",e)}return p({isAuthenticated:!0,email:g.results.email,userRole:d,userId:g.results.id}),x(Date.now()),{success:!0,userRole:d}}catch(e){throw console.error("Login error:",e),e}},[]),C=(0,r.useCallback)(async(e,t)=>{let i=new a.r2rClient(t);try{i.setTokens(e,"");let t=await i.users.me();if(!t.results)throw Error("Failed to get user information");localStorage.setItem("livechatAccessToken",e),o(i);let s="user";try{await i.system.settings(),s="admin"}catch(e){e instanceof Error&&"status"in e&&403===e.status||console.error("Unexpected error when checking user role:",e)}return p({isAuthenticated:!0,email:t.results.email,userRole:s,userId:t.results.id}),{success:!0,userRole:s}}catch(e){throw console.error("Token login error:",e),localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),e}},[]),b=(0,r.useCallback)(async()=>{try{l&&await l.users.logout()}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),o(null),p({isAuthenticated:!1,email:null,userRole:null,userId:null}),x(null)}},[l]),v=(0,r.useCallback)(async()=>{localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),o(null),p({isAuthenticated:!1,email:null,userRole:null,userId:null}),x(null)},[]),M=(0,r.useCallback)(async(e,t,i)=>{let s=new a.r2rClient(i);try{await s.users.create({email:e,password:t}),await S(e,t,i)}catch(e){throw console.error("Registration error:",e),e}},[S]),P=(0,r.useCallback)(()=>l,[l]),w=(0,r.useCallback)(async function(e,t){if(arguments.length>2&&void 0!==arguments[2]&&arguments[2],!l)throw Error("No authenticated client available");try{await l.users.create({email:e,password:t})}catch(e){throw console.error("Create user error:",e),e}},[l]);return(0,r.useEffect)(()=>{(async()=>{let e=localStorage.getItem("livechatAccessToken");if(e)try{let{loadChatConfig:t,getDeploymentUrl:s}=await i.e(111).then(i.bind(i,12492)),r=await t(),a=s(r);await C(e,a)}catch(e){console.error("Auto-login failed:",e),localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken")}})()},[C]),(0,s.jsx)(n.Provider,{value:{pipeline:u,setPipeline:h,selectedModel:g,setSelectedModel:d,isAuthenticated:m.isAuthenticated,login:S,loginWithToken:C,logout:b,unsetCredentials:v,register:M,authState:m,getClient:P,client:l,viewMode:c,setViewMode:_,isSuperUser:f,createUser:w},children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[671,78,965,431,316,809,161,432,663,61,358],()=>t(33961)),_N_E=e.O()}]);