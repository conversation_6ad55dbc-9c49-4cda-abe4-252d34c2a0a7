"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-stately+toggle@3.8.2_ef692d63749b87ee1217101ac176f9d9";
exports.ids = ["vendor-chunks/@react-stately+toggle@3.8.2_ef692d63749b87ee1217101ac176f9d9"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+toggle@3.8.2_ef692d63749b87ee1217101ac176f9d9/node_modules/@react-stately/toggle/dist/useToggleState.mjs":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+toggle@3.8.2_ef692d63749b87ee1217101ac176f9d9/node_modules/@react-stately/toggle/dist/useToggleState.mjs ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useToggleState: () => (/* binding */ $3017fa7ffdddec74$export$8042c6c013fd5226)\n/* harmony export */ });\n/* harmony import */ var _react_stately_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-stately/utils */ \"(ssr)/./node_modules/.pnpm/@react-stately+utils@3.10.5_643de144daf18c30f9f7e3eb5c877f70/node_modules/@react-stately/utils/dist/useControlledState.mjs\");\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \nfunction $3017fa7ffdddec74$export$8042c6c013fd5226(props = {}) {\n    let { isReadOnly: isReadOnly } = props;\n    // have to provide an empty function so useControlledState doesn't throw a fit\n    // can't use useControlledState's prop calling because we need the event object from the change\n    let [isSelected, setSelected] = (0, _react_stately_utils__WEBPACK_IMPORTED_MODULE_0__.useControlledState)(props.isSelected, props.defaultSelected || false, props.onChange);\n    function updateSelected(value) {\n        if (!isReadOnly) setSelected(value);\n    }\n    function toggleState() {\n        if (!isReadOnly) setSelected(!isSelected);\n    }\n    return {\n        isSelected: isSelected,\n        setSelected: updateSelected,\n        toggle: toggleState\n    };\n}\n\n\n\n//# sourceMappingURL=useToggleState.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+toggle@3.8.2_ef692d63749b87ee1217101ac176f9d9/node_modules/@react-stately/toggle/dist/useToggleState.mjs\n");

/***/ })

};
;