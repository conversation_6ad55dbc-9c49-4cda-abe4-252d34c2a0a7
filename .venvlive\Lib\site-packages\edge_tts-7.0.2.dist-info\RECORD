../../Scripts/edge-playback.exe,sha256=rD-5aEw5ehwCWmcltpMcNX6XS1qGSjW2LpIv2hECwaY,108426
../../Scripts/edge-tts.exe,sha256=7JtSe4AyvJmcp89I01I1uMk5EDzWn7dXfep96nACNo4,108419
edge_playback/__init__.py,sha256=2cLgicgIr1P6jjPb8qJR8WwaUjMqyC_2j1NxujgLIfw,226
edge_playback/__main__.py,sha256=AtWnSYXGTEYDwcaFco7Q5iuYaCrj01K8uodXSbd0ZfY,2833
edge_playback/__pycache__/__init__.cpython-311.pyc,,
edge_playback/__pycache__/__main__.cpython-311.pyc,,
edge_playback/__pycache__/util.cpython-311.pyc,,
edge_playback/__pycache__/win32_playback.cpython-311.pyc,,
edge_playback/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
edge_playback/util.py,sha256=S5tu-bdr0nJEyCSQD5rbWErwaKq3lX_oNjTyWlCydRg,145
edge_playback/win32_playback.py,sha256=WEo0KMyaA_9TejdXHG5nRowJHNYcTVKpq2_QWAyOUj8,1690
edge_tts-7.0.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
edge_tts-7.0.2.dist-info/METADATA,sha256=iguMQUFfW2emwCqFWT6SXXOcJp0yVUZkaXu48l6Adqg,5493
edge_tts-7.0.2.dist-info/RECORD,,
edge_tts-7.0.2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
edge_tts-7.0.2.dist-info/WHEEL,sha256=GHB6lJx2juba1wDgXDNlMTyM13ckjBMKf-OnwgKOCtA,91
edge_tts-7.0.2.dist-info/entry_points.txt,sha256=DV4aTWXXPdZgOPnMtRXII27IXunVnZfttsUYror6ddU,97
edge_tts-7.0.2.dist-info/licenses/LICENSE,sha256=46mU2C5kSwOnkqkw9XQAJlhBL2JAf1_uCD8lVcXyMRg,7652
edge_tts-7.0.2.dist-info/top_level.txt,sha256=lxYRVU1wLS0a2YHxkXYU1l6CNieX--fJn-HedzhwzPk,23
edge_tts/__init__.py,sha256=gViLeNei3s-GsesgY_eI9WhOjGzkWzBgSeqQ90v0GlY,468
edge_tts/__main__.py,sha256=hRZSXWoz2UAviLRG6APsmcpDdDshlZ9PUXcvV53id2Y,111
edge_tts/__pycache__/__init__.cpython-311.pyc,,
edge_tts/__pycache__/__main__.cpython-311.pyc,,
edge_tts/__pycache__/communicate.cpython-311.pyc,,
edge_tts/__pycache__/constants.cpython-311.pyc,,
edge_tts/__pycache__/data_classes.cpython-311.pyc,,
edge_tts/__pycache__/drm.cpython-311.pyc,,
edge_tts/__pycache__/exceptions.cpython-311.pyc,,
edge_tts/__pycache__/submaker.cpython-311.pyc,,
edge_tts/__pycache__/typing.cpython-311.pyc,,
edge_tts/__pycache__/util.cpython-311.pyc,,
edge_tts/__pycache__/version.cpython-311.pyc,,
edge_tts/__pycache__/voices.cpython-311.pyc,,
edge_tts/communicate.py,sha256=5ngCWtkQHFY_bMtvybk2eY3379ZcNlGiuMjdktay3zI,23572
edge_tts/constants.py,sha256=kFruEaCiqhUHVkHg90fei1JnlH1J3zNGBN9CO9BDHzI,1427
edge_tts/data_classes.py,sha256=qYXrPiGcrAsnAFNkvF6rywGLXNc1fImHJMpbHtyP0R4,2720
edge_tts/drm.py,sha256=Yv7ERiPQiqQ0ravC8B6UdW9fphMH64acKprJ6Jr3ebM,4546
edge_tts/exceptions.py,sha256=EE6rZ62AK1Xg3qc6BoI86noXLI8NchBv62GdsxwlxtA,798
edge_tts/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
edge_tts/submaker.py,sha256=z_L9KW_8G_cSnzulEpXpij4PrsqEpWrEjv9ktvYBNMg,2331
edge_tts/typing.py,sha256=AKJQ64Z6PvwNLty8iDTWGnn8u2XZCQq8JcGFodEji-U,2232
edge_tts/util.py,sha256=EGRrCWRtmJOn7WKFWDUjSg-8x0rwjUJ_V5KGG_19IZA,4790
edge_tts/version.py,sha256=cSXeR8Z9ayxgghnY5EzZR-0nNSW7uM8jf_WkAekh6s8,144
edge_tts/voices.py,sha256=p948jSVIvrY7RqGos2AYrqmf2votvrzyaFRYG61ayPA,4258
