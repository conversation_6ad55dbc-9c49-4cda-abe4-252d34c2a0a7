[INFO]2025-07-20 10:03:36,094 File 'main.py',line 11: [System] Welcome to Awesome digitalHuman System
[INFO]2025-07-20 10:03:36,094 File 'main.py',line 12: [System] Runing config:
COMMON:
  LOG_LEVEL: DEBUG
  NAME: Awesome-Digital-Human
  VERSION: v3.0.0
SERVER:
  AGENTS:
    DEFAULT: Repeater
    SUPPORT_LIST: [CfgNode({'NAME': 'Repeater', 'VERSION': 'v0.0.1', 'DESC': '复读机', 'META': CfgNode({'official': '', 'configuration': '', 'tips': '测试使用', 'fee': ''})}), CfgNode({'NAME': 'OpenAI', 'VERSION': 'v0.0.1', 'DESC': '接入Openai协议的服务', 'META': CfgNode({'official': '', 'configuration': '', 'tips': '兼容所有符合Openai协议的API', 'fee': ''}), 'PARAMETERS': [{'name': 'model', 'description': 'ID of the model to use.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'base_url', 'description': 'The base url for request.', 'type': 'string', 'required': False, 'choices': [], 'default': 'https://api.openai.com/v1'}, {'name': 'api_key', 'description': 'The api key for request.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用', 'fee': ''}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'FastGPT', 'VERSION': 'v0.0.1', 'DESC': '接入FastGPT应用', 'META': CfgNode({'official': 'https://fastgpt.cn', 'configuration': 'FastGPT云服务: https://cloud.fastgpt.cn', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'base_url', 'description': 'FastGPT base url.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'FastGPT API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'uid', 'description': 'FastGPT customUid.', 'type': 'string', 'required': False, 'choices': [], 'default': 'adh'}]}), CfgNode({'NAME': 'R2R', 'VERSION': 'v0.0.1', 'DESC': 'R2R RAG Agent - 检索增强生成智能体', 'META': CfgNode({'official': 'https://r2r-docs.sciphi.ai/', 'configuration': 'https://r2r-docs.sciphi.ai/documentation/configuration/overview', 'tips': '支持RAG检索、多种搜索策略、对话上下文管理', 'fee': ''}), 'PARAMETERS': [{'name': 'base_url', 'description': 'R2R服务器地址', 'type': 'string', 'required': True, 'choices': [], 'default': 'http://localhost:7272'}, {'name': 'api_key', 'description': 'R2R API密钥（如果需要认证）', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'search_limit', 'description': '搜索结果数量限制', 'type': 'int', 'required': False, 'range': [1, 50], 'choices': [], 'default': 10}, {'name': 'use_hybrid_search', 'description': '启用混合搜索', 'type': 'bool', 'required': False, 'range': [], 'choices': [], 'default': True}, {'name': 'use_vector_search', 'description': '启用向量搜索', 'type': 'bool', 'required': False, 'range': [], 'choices': [], 'default': True}, {'name': 'temperature', 'description': '生成温度', 'type': 'float', 'required': False, 'range': [0.0, 2.0], 'choices': [], 'default': 0.1}, {'name': 'max_tokens', 'description': '最大生成token数', 'type': 'int', 'required': False, 'range': [1, 4096], 'choices': [], 'default': 1024}, {'name': 'top_p', 'description': 'Top-p采样参数', 'type': 'float', 'required': False, 'range': [0.0, 1.0], 'choices': [], 'default': 1.0}, {'name': 'top_k', 'description': 'Top-k采样参数', 'type': 'int', 'required': False, 'range': [1, 200], 'choices': [], 'default': 100}]})]
  ENGINES:
    ASR:
      DEFAULT: Tencent-API
      SUPPORT_LIST: [CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用', 'fee': 'free'}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'Tencent-API', 'VERSION': 'v0.0.1', 'DESC': '接入腾讯服务', 'META': CfgNode({'official': '', 'configuration': 'https://console.cloud.tencent.com/asr', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'secret_id', 'description': 'tencent secret_id.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'secret_key', 'description': 'tencent secret_key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'funasrStreaming', 'VERSION': 'v0.0.1', 'DESC': '接入Stream ASR', 'META': CfgNode({'official': 'https://github.com/modelscope/FunASR', 'tips': '支持本地部署的FunAsrStream应用', 'fee': 'free', 'infer_type': 'stream'}), 'PARAMETERS': [{'name': 'api_url', 'description': 'Funasr Streaming API URL', 'type': 'string', 'required': False, 'choices': [], 'default': 'ws://adh-funasr:10095'}, {'name': 'mode', 'description': 'Funasr Streaming mode', 'type': 'string', 'required': False, 'choices': ['2pass'], 'default': '2pass'}]})]
    LLM:
      DEFAULT: None
      SUPPORT_LIST: []
    TTS:
      DEFAULT: Tencent-API
      SUPPORT_LIST: [CfgNode({'NAME': 'EdgeTTS', 'VERSION': 'v0.0.1', 'DESC': '适配EdgeTTS', 'META': CfgNode({'official': 'https://github.com/rany2/edge-tts', 'configuration': '', 'tips': '开源项目可能存在不稳定的情况', 'fee': 'free'}), 'PARAMETERS': [{'name': 'voice', 'description': 'Voice for TTS.', 'type': 'string', 'required': False, 'choices': ['Getting from voice api...'], 'default': 'zh-CN-XiaoxiaoNeural'}, {'name': 'rate', 'description': 'Set rate, default +0%.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}, {'name': 'volume', 'description': 'Set volume, default +0%.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}, {'name': 'pitch', 'description': 'Set pitch, default +0Hz.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}]}), CfgNode({'NAME': 'Tencent-API', 'VERSION': 'v0.0.1', 'DESC': '接入腾讯服务', 'META': CfgNode({'official': '', 'configuration': 'https://console.cloud.tencent.com/tts', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'secret_id', 'description': 'tencent secret_id.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'secret_key', 'description': 'tencent secret_key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'voice', 'description': 'Voice for TTS.', 'type': 'string', 'required': False, 'choices': [502001], 'default': '502001'}, {'name': 'volume', 'description': 'Set volume, default +0%.', 'type': 'float', 'required': False, 'range': [-10, 10], 'default': 5}, {'name': 'speed', 'description': 'Set speed, default +0%.', 'type': 'float', 'required': False, 'range': [-2, 6], 'default': 0.0}]}), CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用全', 'fee': ''}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]})]
  IP: 0.0.0.0
  PORT: 8880
  WORKSPACE_PATH: ./outputs
[INFO]2025-07-20 10:03:36,097 File 'asrFactory.py',line 23: [ASRFactory] Create engine: Dify
[INFO]2025-07-20 10:03:36,097 File 'enginePool.py',line 44: [EnginePool] ASR Engine Dify is created.
[INFO]2025-07-20 10:03:36,099 File 'asrFactory.py',line 23: [ASRFactory] Create engine: Tencent-API
[INFO]2025-07-20 10:03:36,099 File 'enginePool.py',line 44: [EnginePool] ASR Engine Tencent-API is created.
[INFO]2025-07-20 10:03:36,099 File 'asrFactory.py',line 23: [ASRFactory] Create engine: funasrStreaming
[INFO]2025-07-20 10:03:36,099 File 'enginePool.py',line 44: [EnginePool] ASR Engine funasrStreaming is created.
[INFO]2025-07-20 10:03:36,099 File 'enginePool.py',line 45: [EnginePool] ASR Engine default is Tencent-API.
[INFO]2025-07-20 10:03:36,099 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: EdgeTTS
[INFO]2025-07-20 10:03:36,099 File 'enginePool.py',line 49: [EnginePool] TTS Engine EdgeTTS is created.
[INFO]2025-07-20 10:03:36,101 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: Tencent-API
[INFO]2025-07-20 10:03:36,101 File 'enginePool.py',line 49: [EnginePool] TTS Engine Tencent-API is created.
[INFO]2025-07-20 10:03:36,101 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: Dify
[INFO]2025-07-20 10:03:36,101 File 'enginePool.py',line 49: [EnginePool] TTS Engine Dify is created.
[INFO]2025-07-20 10:03:36,102 File 'enginePool.py',line 50: [EnginePool] TTS Engine default is Tencent-API.
[INFO]2025-07-20 10:03:36,102 File 'enginePool.py',line 55: [EnginePool] LLM Engine default is None.
[INFO]2025-07-20 10:03:36,102 File 'agentFactory.py',line 21: [AgentFactory] Create instance: Repeater
[INFO]2025-07-20 10:03:36,102 File 'agentPool.py',line 39: [AgentPool] AGENT Engine Repeater is created.
[INFO]2025-07-20 10:03:36,102 File 'agentFactory.py',line 21: [AgentFactory] Create instance: OpenAI
[INFO]2025-07-20 10:03:36,102 File 'agentPool.py',line 39: [AgentPool] AGENT Engine OpenAI is created.
[INFO]2025-07-20 10:03:36,103 File 'agentFactory.py',line 21: [AgentFactory] Create instance: Dify
[INFO]2025-07-20 10:03:36,103 File 'agentPool.py',line 39: [AgentPool] AGENT Engine Dify is created.
[INFO]2025-07-20 10:03:36,103 File 'agentFactory.py',line 21: [AgentFactory] Create instance: FastGPT
[INFO]2025-07-20 10:03:36,103 File 'agentPool.py',line 39: [AgentPool] AGENT Engine FastGPT is created.
[INFO]2025-07-20 10:03:36,103 File 'agentFactory.py',line 21: [AgentFactory] Create instance: R2R
[INFO]2025-07-20 10:03:36,103 File 'agentPool.py',line 39: [AgentPool] AGENT Engine R2R is created.
[INFO]2025-07-20 10:03:36,103 File 'agentPool.py',line 40: [AgentPool] AGENT Engine default is Repeater.
[INFO]2025-07-20 10:04:07,680 File 'r2rAgent.py',line 73: [R2RAgent] Initializing with base_url: http://192.168.0.115:7272
[INFO]2025-07-20 10:04:09,187 File 'r2rAgent.py',line 91: [R2RAgent] Created new conversation: 087e5a6c-37d1-4b18-88cf-22616276454c
[INFO]2025-07-20 10:04:09,188 File 'r2rAgent.py',line 110: [R2RAgent] Processing message: Hello, can you tell me about artificial intelligence?...
[INFO]2025-07-20 10:04:09,189 File 'r2rAgent.py',line 120: [R2RAgent] Calling R2R agent with streaming enabled
[INFO]2025-07-20 10:04:09,191 File 'r2rAgent.py',line 136: [R2RAgent] Got stream response: <class 'generator'>
[INFO]2025-07-20 10:04:09,191 File 'r2rAgent.py',line 145: [R2RAgent] Starting to iterate through R2R stream
[DEBUG]2025-07-20 10:04:13,771 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_558dcc53', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='Artificial', annotations=[]))])))
[DEBUG]2025-07-20 10:04:13,772 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_558dcc53'...
[DEBUG]2025-07-20 10:04:14,404 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_c55e649e', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' intelligence', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,406 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_c55e649e'...
[DEBUG]2025-07-20 10:04:14,463 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_53abd0da', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' (', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,464 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_53abd0da'...
[DEBUG]2025-07-20 10:04:14,466 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_fd2e474b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='AI', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,467 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_fd2e474b'...
[DEBUG]2025-07-20 10:04:14,541 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_fd92428e', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=')', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,542 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_fd92428e'...
[DEBUG]2025-07-20 10:04:14,543 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_07eadbe5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' is', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,544 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_07eadbe5'...
[DEBUG]2025-07-20 10:04:14,590 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_3bedc2cc', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' a', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,592 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_3bedc2cc'...
[DEBUG]2025-07-20 10:04:14,593 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_de20f2d8', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' broad', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,595 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_de20f2d8'...
[DEBUG]2025-07-20 10:04:14,660 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f80d2e09', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' field', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,661 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f80d2e09'...
[DEBUG]2025-07-20 10:04:14,663 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_fe75927c', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' of', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,664 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_fe75927c'...
[DEBUG]2025-07-20 10:04:14,722 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_6a0cbf67', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' computer', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,724 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_6a0cbf67'...
[DEBUG]2025-07-20 10:04:14,725 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_58ae23b1', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' science', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,727 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_58ae23b1'...
[DEBUG]2025-07-20 10:04:14,787 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b2fd1327', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' focused', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,788 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b2fd1327'...
[DEBUG]2025-07-20 10:04:14,789 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d22ecfa5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' on', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,791 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d22ecfa5'...
[DEBUG]2025-07-20 10:04:14,850 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_cd160e48', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' creating', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,852 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_cd160e48'...
[DEBUG]2025-07-20 10:04:14,853 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b2f7e72f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' systems', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,855 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b2f7e72f'...
[DEBUG]2025-07-20 10:04:14,914 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d6689b4e', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' capable', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,915 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d6689b4e'...
[DEBUG]2025-07-20 10:04:14,917 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_63f0421c', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' of', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,918 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_63f0421c'...
[DEBUG]2025-07-20 10:04:14,970 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d3d1e10f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' performing', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,971 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d3d1e10f'...
[DEBUG]2025-07-20 10:04:14,972 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_1f118c25', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' tasks', annotations=[]))])))
[DEBUG]2025-07-20 10:04:14,974 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_1f118c25'...
[DEBUG]2025-07-20 10:04:15,048 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_26e62445', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' that', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,049 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_26e62445'...
[DEBUG]2025-07-20 10:04:15,051 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b31c67f8', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' typically', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,052 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b31c67f8'...
[DEBUG]2025-07-20 10:04:15,095 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_3de2c27c', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' require', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,097 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_3de2c27c'...
[DEBUG]2025-07-20 10:04:15,099 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b8158816', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' human', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,100 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b8158816'...
[DEBUG]2025-07-20 10:04:15,166 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_01a03600', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' intelligence', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,167 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_01a03600'...
[DEBUG]2025-07-20 10:04:15,169 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_46cf69bc', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,170 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_46cf69bc'...
[DEBUG]2025-07-20 10:04:15,221 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_48b1c447', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' These', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,222 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_48b1c447'...
[DEBUG]2025-07-20 10:04:15,224 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_fd04048e', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' tasks', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,224 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_fd04048e'...
[DEBUG]2025-07-20 10:04:15,289 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_037d5446', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' include', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,290 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_037d5446'...
[DEBUG]2025-07-20 10:04:15,292 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_a795b35b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' reasoning', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,292 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_a795b35b'...
[DEBUG]2025-07-20 10:04:15,358 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d9e700f1', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,358 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d9e700f1'...
[DEBUG]2025-07-20 10:04:15,360 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_56f64006', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' learning', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,361 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_56f64006'...
[DEBUG]2025-07-20 10:04:15,411 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5fb731f5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,412 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_5fb731f5'...
[DEBUG]2025-07-20 10:04:15,414 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_acb5538b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' problem', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,416 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_acb5538b'...
[DEBUG]2025-07-20 10:04:15,497 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_72b8e7f2', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='-solving', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,499 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_72b8e7f2'...
[DEBUG]2025-07-20 10:04:15,499 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_7e51726b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,502 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_7e51726b'...
[DEBUG]2025-07-20 10:04:15,563 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5deb1e6d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' perception', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,563 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_5deb1e6d'...
[DEBUG]2025-07-20 10:04:15,565 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f1bd5739', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,566 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f1bd5739'...
[DEBUG]2025-07-20 10:04:15,622 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_26de399d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' understanding', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,623 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_26de399d'...
[DEBUG]2025-07-20 10:04:15,739 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_2cd23d95', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' natural', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,740 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_2cd23d95'...
[DEBUG]2025-07-20 10:04:15,742 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_3b0bb080', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' language', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,743 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_3b0bb080'...
[DEBUG]2025-07-20 10:04:15,750 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_98d8915a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,751 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_98d8915a'...
[DEBUG]2025-07-20 10:04:15,752 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_9cb79267', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' and', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,753 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_9cb79267'...
[DEBUG]2025-07-20 10:04:15,817 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d743b653', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' decision', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,818 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d743b653'...
[DEBUG]2025-07-20 10:04:15,875 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8df508ba', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='-making', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,876 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_8df508ba'...
[DEBUG]2025-07-20 10:04:15,877 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_bfdbbe7f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,879 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_bfdbbe7f'...
[DEBUG]2025-07-20 10:04:15,942 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_af075e46', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' AI', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,943 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_af075e46'...
[DEBUG]2025-07-20 10:04:15,944 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_ae6da328', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' can', annotations=[]))])))
[DEBUG]2025-07-20 10:04:15,946 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_ae6da328'...
[DEBUG]2025-07-20 10:04:16,000 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_3fc1e4d9', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' be', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,001 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_3fc1e4d9'...
[DEBUG]2025-07-20 10:04:16,003 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_4b91eca3', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' categorized', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,005 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_4b91eca3'...
[DEBUG]2025-07-20 10:04:16,095 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_c0c5b1f4', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' into', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,096 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_c0c5b1f4'...
[DEBUG]2025-07-20 10:04:16,098 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f93cedb9', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' narrow', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,100 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f93cedb9'...
[DEBUG]2025-07-20 10:04:16,128 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_00f321df', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' AI', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,129 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_00f321df'...
[DEBUG]2025-07-20 10:04:16,131 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_6b33f2f5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' (', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,132 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_6b33f2f5'...
[DEBUG]2025-07-20 10:04:16,194 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_57f581ec', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='des', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,195 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_57f581ec'...
[DEBUG]2025-07-20 10:04:16,197 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_ca844e02', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='igned', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,198 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_ca844e02'...
[DEBUG]2025-07-20 10:04:16,265 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_eaf2b33b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' for', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,266 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_eaf2b33b'...
[DEBUG]2025-07-20 10:04:16,268 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_43bdaeb2', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' specific', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,268 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_43bdaeb2'...
[DEBUG]2025-07-20 10:04:16,322 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_77dd3f5a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' tasks', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,324 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_77dd3f5a'...
[DEBUG]2025-07-20 10:04:16,325 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_13bd3cef', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=')', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,327 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_13bd3cef'...
[DEBUG]2025-07-20 10:04:16,383 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_18193d34', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' and', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,383 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_18193d34'...
[DEBUG]2025-07-20 10:04:16,386 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d5c7cc31', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' general', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,387 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d5c7cc31'...
[DEBUG]2025-07-20 10:04:16,446 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_69b29ba1', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' AI', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,447 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_69b29ba1'...
[DEBUG]2025-07-20 10:04:16,449 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_ac0ee142', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' (', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,451 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_ac0ee142'...
[DEBUG]2025-07-20 10:04:16,510 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_7b770e5d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='hyp', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,511 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_7b770e5d'...
[DEBUG]2025-07-20 10:04:16,512 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_45bdbd9f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='ot', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,514 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_45bdbd9f'...
[DEBUG]2025-07-20 10:04:16,575 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_274ed761', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='hetical', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,577 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_274ed761'...
[DEBUG]2025-07-20 10:04:16,578 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_198aab6a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' systems', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,579 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_198aab6a'...
[DEBUG]2025-07-20 10:04:16,639 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_65b0e031', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' with', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,640 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_65b0e031'...
[DEBUG]2025-07-20 10:04:16,641 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b58a1dc3', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' human', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,643 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b58a1dc3'...
[DEBUG]2025-07-20 10:04:16,701 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8798044d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='-like', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,702 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_8798044d'...
[DEBUG]2025-07-20 10:04:16,704 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_ae65a8f7', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' cognitive', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,705 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_ae65a8f7'...
[DEBUG]2025-07-20 10:04:16,766 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_6655487f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' abilities', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,767 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_6655487f'...
[DEBUG]2025-07-20 10:04:16,833 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_12ca62b2', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=').\n\n', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,835 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_12ca62b2'...
[DEBUG]2025-07-20 10:04:16,836 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5886807f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='Here', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,837 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_5886807f'...
[DEBUG]2025-07-20 10:04:16,900 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_de57f4d1', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' are', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,902 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_de57f4d1'...
[DEBUG]2025-07-20 10:04:16,957 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_6d02b1bd', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' some', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,958 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_6d02b1bd'...
[DEBUG]2025-07-20 10:04:16,959 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_ee2309eb', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' key', annotations=[]))])))
[DEBUG]2025-07-20 10:04:16,960 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_ee2309eb'...
[DEBUG]2025-07-20 10:04:17,031 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_afee77e1', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' aspects', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,032 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_afee77e1'...
[DEBUG]2025-07-20 10:04:17,034 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_397c0648', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' of', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,035 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_397c0648'...
[DEBUG]2025-07-20 10:04:17,092 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_2a799782', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' AI', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,094 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_2a799782'...
[DEBUG]2025-07-20 10:04:17,154 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_113a5e7b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=':\n\n', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,156 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_113a5e7b'...
[DEBUG]2025-07-20 10:04:17,157 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d683299b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='1', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,158 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d683299b'...
[DEBUG]2025-07-20 10:04:17,221 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_cb3d77d9', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,222 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_cb3d77d9'...
[DEBUG]2025-07-20 10:04:17,223 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_03f8f73b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' **', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,224 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_03f8f73b'...
[DEBUG]2025-07-20 10:04:17,280 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_68215a9a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='Machine', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,282 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_68215a9a'...
[DEBUG]2025-07-20 10:04:17,284 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_1bedbd10', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' Learning', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,285 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_1bedbd10'...
[DEBUG]2025-07-20 10:04:17,341 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_3cb1ed7a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' (', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,342 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_3cb1ed7a'...
[DEBUG]2025-07-20 10:04:17,344 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_a080fdb5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='ML', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,344 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_a080fdb5'...
[DEBUG]2025-07-20 10:04:17,401 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d94e10a6', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=')', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,403 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d94e10a6'...
[DEBUG]2025-07-20 10:04:17,403 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_636360eb', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='**:', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,405 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_636360eb'...
[DEBUG]2025-07-20 10:04:17,468 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_06030e7b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' A', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,470 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_06030e7b'...
[DEBUG]2025-07-20 10:04:17,471 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_4ca0f1bd', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' subset', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,472 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_4ca0f1bd'...
[DEBUG]2025-07-20 10:04:17,533 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5ef97e6d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' of', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,534 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_5ef97e6d'...
[DEBUG]2025-07-20 10:04:17,536 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_47c196ac', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' AI', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,538 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_47c196ac'...
[DEBUG]2025-07-20 10:04:17,600 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0df67456', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' that', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,601 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0df67456'...
[DEBUG]2025-07-20 10:04:17,658 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_29f56a27', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' involves', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,660 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_29f56a27'...
[DEBUG]2025-07-20 10:04:17,662 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_480899f0', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' training', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,663 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_480899f0'...
[DEBUG]2025-07-20 10:04:17,720 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_96902e7f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' algorithms', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,721 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_96902e7f'...
[DEBUG]2025-07-20 10:04:17,723 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_2e636139', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' to', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,724 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_2e636139'...
[DEBUG]2025-07-20 10:04:17,779 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_de12b3b7', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' learn', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,781 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_de12b3b7'...
[DEBUG]2025-07-20 10:04:17,781 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8b048b27', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' patterns', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,783 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_8b048b27'...
[DEBUG]2025-07-20 10:04:17,848 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_34405255', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' from', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,849 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_34405255'...
[DEBUG]2025-07-20 10:04:17,850 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8cd82028', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' data', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,852 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_8cd82028'...
[DEBUG]2025-07-20 10:04:17,903 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f7dd09da', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' and', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,904 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f7dd09da'...
[DEBUG]2025-07-20 10:04:17,967 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0c32885d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' make', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,968 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0c32885d'...
[DEBUG]2025-07-20 10:04:17,970 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0b9ca25b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' predictions', annotations=[]))])))
[DEBUG]2025-07-20 10:04:17,972 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0b9ca25b'...
[DEBUG]2025-07-20 10:04:18,037 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_45ce86e5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' or', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,039 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_45ce86e5'...
[DEBUG]2025-07-20 10:04:18,040 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_70e8a7ce', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' decisions', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,041 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_70e8a7ce'...
[DEBUG]2025-07-20 10:04:18,098 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_c76508b0', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' without', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,100 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_c76508b0'...
[DEBUG]2025-07-20 10:04:18,102 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_ea9f8513', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' explicit', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,103 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_ea9f8513'...
[DEBUG]2025-07-20 10:04:18,163 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_84180eaf', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' programming', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,164 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_84180eaf'...
[DEBUG]2025-07-20 10:04:18,166 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b81cd431', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,167 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b81cd431'...
[DEBUG]2025-07-20 10:04:18,222 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_7ffd96a4', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' Examples', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,224 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_7ffd96a4'...
[DEBUG]2025-07-20 10:04:18,225 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_c1318f45', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' include', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,226 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_c1318f45'...
[DEBUG]2025-07-20 10:04:18,288 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_c1d76f80', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' image', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,288 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_c1d76f80'...
[DEBUG]2025-07-20 10:04:18,346 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5d38e550', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' recognition', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,348 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_5d38e550'...
[DEBUG]2025-07-20 10:04:18,349 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_977fc45d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,350 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_977fc45d'...
[DEBUG]2025-07-20 10:04:18,430 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0676e3fb', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' recommendation', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,430 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0676e3fb'...
[DEBUG]2025-07-20 10:04:18,434 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e02d0ae6', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' systems', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,435 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e02d0ae6'...
[DEBUG]2025-07-20 10:04:18,477 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8df0c308', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,479 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_8df0c308'...
[DEBUG]2025-07-20 10:04:18,480 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_3b401b28', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' and', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,480 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_3b401b28'...
[DEBUG]2025-07-20 10:04:18,536 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f068dcc7', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' fraud', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,537 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f068dcc7'...
[DEBUG]2025-07-20 10:04:18,538 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5b4b43c8', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' detection', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,539 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_5b4b43c8'...
[DEBUG]2025-07-20 10:04:18,605 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_9581e2c9', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.\n\n', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,607 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_9581e2c9'...
[DEBUG]2025-07-20 10:04:18,608 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f214b84c', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='2', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,609 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f214b84c'...
[DEBUG]2025-07-20 10:04:18,661 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_912896e8', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,662 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_912896e8'...
[DEBUG]2025-07-20 10:04:18,664 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d2db8664', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' **', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,664 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d2db8664'...
[DEBUG]2025-07-20 10:04:18,728 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_ebae6cbe', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='Deep', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,729 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_ebae6cbe'...
[DEBUG]2025-07-20 10:04:18,730 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_29773633', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' Learning', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,731 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_29773633'...
[DEBUG]2025-07-20 10:04:18,836 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_6b2b1ca4', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='**:', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,837 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_6b2b1ca4'...
[DEBUG]2025-07-20 10:04:18,838 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_80fdde21', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' A', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,839 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_80fdde21'...
[DEBUG]2025-07-20 10:04:18,855 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_db8bed29', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' specialized', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,856 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_db8bed29'...
[DEBUG]2025-07-20 10:04:18,857 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d09ab763', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' form', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,859 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d09ab763'...
[DEBUG]2025-07-20 10:04:18,919 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d856cfef', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' of', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,920 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d856cfef'...
[DEBUG]2025-07-20 10:04:18,922 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d67777d4', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' ML', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,923 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d67777d4'...
[DEBUG]2025-07-20 10:04:18,981 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d1c08513', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' using', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,983 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d1c08513'...
[DEBUG]2025-07-20 10:04:18,985 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b4a38246', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' neural', annotations=[]))])))
[DEBUG]2025-07-20 10:04:18,986 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b4a38246'...
[DEBUG]2025-07-20 10:04:19,047 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_9f4ad5ac', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' networks', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,048 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_9f4ad5ac'...
[DEBUG]2025-07-20 10:04:19,049 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d6989cb5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' with', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,051 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d6989cb5'...
[DEBUG]2025-07-20 10:04:19,105 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_db2b307f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' many', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,106 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_db2b307f'...
[DEBUG]2025-07-20 10:04:19,107 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b368ffae', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' layers', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,109 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b368ffae'...
[DEBUG]2025-07-20 10:04:19,167 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_cb3af896', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' (', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,168 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_cb3af896'...
[DEBUG]2025-07-20 10:04:19,170 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_2638fd3d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='deep', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,170 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_2638fd3d'...
[DEBUG]2025-07-20 10:04:19,230 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_237022ad', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' neural', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,231 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_237022ad'...
[DEBUG]2025-07-20 10:04:19,233 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_025c8df9', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' networks', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,234 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_025c8df9'...
[DEBUG]2025-07-20 10:04:19,351 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f8660de7', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=').', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,352 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f8660de7'...
[DEBUG]2025-07-20 10:04:19,357 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_712f7608', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' It', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,357 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_712f7608'...
[DEBUG]2025-07-20 10:04:19,359 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_eeac8c65', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' powers', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,363 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_eeac8c65'...
[DEBUG]2025-07-20 10:04:19,414 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_85d81377', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' advancements', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,414 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_85d81377'...
[DEBUG]2025-07-20 10:04:19,482 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_37f2322f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' in', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,484 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_37f2322f'...
[DEBUG]2025-07-20 10:04:19,485 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_86da8423', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' areas', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,486 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_86da8423'...
[DEBUG]2025-07-20 10:04:19,540 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_84633455', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' like', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,541 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_84633455'...
[DEBUG]2025-07-20 10:04:19,542 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_fc6414b9', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' computer', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,544 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_fc6414b9'...
[DEBUG]2025-07-20 10:04:19,611 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_375561fd', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' vision', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,612 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_375561fd'...
[DEBUG]2025-07-20 10:04:19,613 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_66e0f665', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,614 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_66e0f665'...
[DEBUG]2025-07-20 10:04:19,673 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_faef2e4b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' speech', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,674 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_faef2e4b'...
[DEBUG]2025-07-20 10:04:19,675 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_61d3935d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' recognition', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,677 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_61d3935d'...
[DEBUG]2025-07-20 10:04:19,741 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b481a3f0', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,742 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b481a3f0'...
[DEBUG]2025-07-20 10:04:19,745 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b92e376d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' and', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,747 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b92e376d'...
[DEBUG]2025-07-20 10:04:19,797 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e80446f8', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' natural', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,798 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e80446f8'...
[DEBUG]2025-07-20 10:04:19,800 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e9555834', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' language', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,801 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e9555834'...
[DEBUG]2025-07-20 10:04:19,855 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_dbcba290', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' processing', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,856 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_dbcba290'...
[DEBUG]2025-07-20 10:04:19,858 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_72835da1', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' (', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,858 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_72835da1'...
[DEBUG]2025-07-20 10:04:19,950 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_348e16d3', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='N', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,951 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_348e16d3'...
[DEBUG]2025-07-20 10:04:19,979 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_2e2c1061', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='LP', annotations=[]))])))
[DEBUG]2025-07-20 10:04:19,980 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_2e2c1061'...
[DEBUG]2025-07-20 10:04:20,041 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f26b8ff5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=').\n\n', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,043 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f26b8ff5'...
[DEBUG]2025-07-20 10:04:20,044 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_db15cb82', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='3', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,046 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_db15cb82'...
[DEBUG]2025-07-20 10:04:20,047 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_542833e0', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,049 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_542833e0'...
[DEBUG]2025-07-20 10:04:20,113 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_6588256f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' **', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,115 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_6588256f'...
[DEBUG]2025-07-20 10:04:20,115 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_21e93396', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='Natural', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,116 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_21e93396'...
[DEBUG]2025-07-20 10:04:20,172 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_93b690fc', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' Language', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,173 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_93b690fc'...
[DEBUG]2025-07-20 10:04:20,174 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_1a4ea935', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' Processing', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,174 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_1a4ea935'...
[DEBUG]2025-07-20 10:04:20,235 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e907ab82', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' (', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,235 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e907ab82'...
[DEBUG]2025-07-20 10:04:20,236 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_19bd136e', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='N', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,237 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_19bd136e'...
[DEBUG]2025-07-20 10:04:20,302 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b8686014', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='LP', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,303 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b8686014'...
[DEBUG]2025-07-20 10:04:20,304 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_aabf880a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=')', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,305 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_aabf880a'...
[DEBUG]2025-07-20 10:04:20,363 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_812f698c', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='**:', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,364 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_812f698c'...
[DEBUG]2025-07-20 10:04:20,365 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f8b456a0', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' En', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,366 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f8b456a0'...
[DEBUG]2025-07-20 10:04:20,420 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_267e8ad3', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='ables', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,421 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_267e8ad3'...
[DEBUG]2025-07-20 10:04:20,422 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d8d7242a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' machines', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,423 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d8d7242a'...
[DEBUG]2025-07-20 10:04:20,485 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_458201a8', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' to', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,486 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_458201a8'...
[DEBUG]2025-07-20 10:04:20,488 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8fdc8350', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' understand', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,488 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_8fdc8350'...
[DEBUG]2025-07-20 10:04:20,552 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_c39335f5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,552 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_c39335f5'...
[DEBUG]2025-07-20 10:04:20,619 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_22f4ef2b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' interpret', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,620 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_22f4ef2b'...
[DEBUG]2025-07-20 10:04:20,621 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_34a04cf0', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,622 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_34a04cf0'...
[DEBUG]2025-07-20 10:04:20,680 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e5a7e701', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' and', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,681 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e5a7e701'...
[DEBUG]2025-07-20 10:04:20,682 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_957baabf', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' generate', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,683 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_957baabf'...
[DEBUG]2025-07-20 10:04:20,738 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_291a01d6', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' human', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,739 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_291a01d6'...
[DEBUG]2025-07-20 10:04:20,741 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_70bacf24', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' language', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,742 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_70bacf24'...
[DEBUG]2025-07-20 10:04:20,799 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d37817d8', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,799 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d37817d8'...
[DEBUG]2025-07-20 10:04:20,802 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_2e11f70f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' Applications', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,804 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_2e11f70f'...
[DEBUG]2025-07-20 10:04:20,865 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_a46b46d3', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' include', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,866 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_a46b46d3'...
[DEBUG]2025-07-20 10:04:20,867 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_58a03611', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' chatbots', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,868 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_58a03611'...
[DEBUG]2025-07-20 10:04:20,923 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_2810097e', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,924 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_2810097e'...
[DEBUG]2025-07-20 10:04:20,925 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_dc299b14', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' translation', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,927 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_dc299b14'...
[DEBUG]2025-07-20 10:04:20,985 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_adc0e52e', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' services', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,986 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_adc0e52e'...
[DEBUG]2025-07-20 10:04:20,988 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8663a173', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:20,989 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_8663a173'...
[DEBUG]2025-07-20 10:04:21,049 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_62eeab71', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' and', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,050 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_62eeab71'...
[DEBUG]2025-07-20 10:04:21,051 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_a35ee356', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' sentiment', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,053 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_a35ee356'...
[DEBUG]2025-07-20 10:04:21,116 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5b08210a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' analysis', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,118 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_5b08210a'...
[DEBUG]2025-07-20 10:04:21,181 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_3da42d3d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.\n\n', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,182 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_3da42d3d'...
[DEBUG]2025-07-20 10:04:21,184 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d6f4e728', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='4', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,185 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d6f4e728'...
[DEBUG]2025-07-20 10:04:21,186 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0663b587', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,187 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0663b587'...
[DEBUG]2025-07-20 10:04:21,246 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e037bb2f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' **', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,247 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e037bb2f'...
[DEBUG]2025-07-20 10:04:21,247 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d8628039', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='Computer', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,249 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d8628039'...
[DEBUG]2025-07-20 10:04:21,307 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_4f769fe6', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' Vision', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,308 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_4f769fe6'...
[DEBUG]2025-07-20 10:04:21,310 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_1abe5c81', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='**:', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,311 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_1abe5c81'...
[DEBUG]2025-07-20 10:04:21,375 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0e37d12a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' Allows', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,376 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0e37d12a'...
[DEBUG]2025-07-20 10:04:21,378 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_a5da8bfd', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' machines', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,379 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_a5da8bfd'...
[DEBUG]2025-07-20 10:04:21,426 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_148cfe92', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' to', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,427 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_148cfe92'...
[DEBUG]2025-07-20 10:04:21,428 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d28eb7f5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' interpret', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,430 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d28eb7f5'...
[DEBUG]2025-07-20 10:04:21,490 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b41f1dc1', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' and', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,491 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b41f1dc1'...
[DEBUG]2025-07-20 10:04:21,557 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_fc3c40d0', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' process', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,558 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_fc3c40d0'...
[DEBUG]2025-07-20 10:04:21,559 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_68dc8a4c', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' visual', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,560 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_68dc8a4c'...
[DEBUG]2025-07-20 10:04:21,619 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5f700268', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' information', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,621 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_5f700268'...
[DEBUG]2025-07-20 10:04:21,685 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_fd52ef23', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' from', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,686 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_fd52ef23'...
[DEBUG]2025-07-20 10:04:21,688 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_cebc1214', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' the', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,689 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_cebc1214'...
[DEBUG]2025-07-20 10:04:21,742 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_9551f435', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' world', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,743 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_9551f435'...
[DEBUG]2025-07-20 10:04:21,744 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0699a6c5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,745 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0699a6c5'...
[DEBUG]2025-07-20 10:04:21,811 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_6187da22', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' such', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,813 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_6187da22'...
[DEBUG]2025-07-20 10:04:21,814 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_7adca892', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' as', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,815 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_7adca892'...
[DEBUG]2025-07-20 10:04:21,881 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0078048f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' facial', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,882 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0078048f'...
[DEBUG]2025-07-20 10:04:21,883 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_c5d3f956', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' recognition', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,884 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_c5d3f956'...
[DEBUG]2025-07-20 10:04:21,932 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_598d59a3', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,934 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_598d59a3'...
[DEBUG]2025-07-20 10:04:21,935 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_ad037bd9', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' autonomous', annotations=[]))])))
[DEBUG]2025-07-20 10:04:21,936 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_ad037bd9'...
[DEBUG]2025-07-20 10:04:21,999 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_651b1530', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' vehicles', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,000 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_651b1530'...
[DEBUG]2025-07-20 10:04:22,002 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_706facad', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,003 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_706facad'...
[DEBUG]2025-07-20 10:04:22,059 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_9767d636', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' and', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,060 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_9767d636'...
[DEBUG]2025-07-20 10:04:22,061 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0fbb1afd', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' medical', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,063 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0fbb1afd'...
[DEBUG]2025-07-20 10:04:22,127 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f6aeb9d4', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' image', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,129 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f6aeb9d4'...
[DEBUG]2025-07-20 10:04:22,191 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_aa5b5696', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' analysis', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,193 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_aa5b5696'...
[DEBUG]2025-07-20 10:04:22,250 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0a25d10f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.\n\n', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,251 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0a25d10f'...
[DEBUG]2025-07-20 10:04:22,253 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d31673dd', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='5', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,255 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d31673dd'...
[DEBUG]2025-07-20 10:04:22,257 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_1a0ad1a6', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,258 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_1a0ad1a6'...
[DEBUG]2025-07-20 10:04:22,314 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_762969e1', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' **', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,315 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_762969e1'...
[DEBUG]2025-07-20 10:04:22,317 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_a912fef8', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='Rob', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,318 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_a912fef8'...
[DEBUG]2025-07-20 10:04:22,386 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f22bfcb5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='otics', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,387 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f22bfcb5'...
[DEBUG]2025-07-20 10:04:22,389 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_6b76dbae', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='**:', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,390 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_6b76dbae'...
[DEBUG]2025-07-20 10:04:22,438 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_4b062f94', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' Comb', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,440 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_4b062f94'...
[DEBUG]2025-07-20 10:04:22,441 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_59b31946', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='ines', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,442 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_59b31946'...
[DEBUG]2025-07-20 10:04:22,511 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e4143ecb', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' AI', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,513 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e4143ecb'...
[DEBUG]2025-07-20 10:04:22,514 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f695ccc3', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' with', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,516 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f695ccc3'...
[DEBUG]2025-07-20 10:04:22,589 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_01fabed3', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' mechanical', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,590 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_01fabed3'...
[DEBUG]2025-07-20 10:04:22,591 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_9d64f0fc', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' systems', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,593 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_9d64f0fc'...
[DEBUG]2025-07-20 10:04:22,625 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_66bf9c16', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' to', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,627 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_66bf9c16'...
[DEBUG]2025-07-20 10:04:22,631 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_7cb4682c', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' create', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,633 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_7cb4682c'...
[DEBUG]2025-07-20 10:04:22,690 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_30485279', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' robots', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,691 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_30485279'...
[DEBUG]2025-07-20 10:04:22,691 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_6bfb0160', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' capable', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,692 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_6bfb0160'...
[DEBUG]2025-07-20 10:04:22,754 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_3b9cbf02', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' of', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,755 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_3b9cbf02'...
[DEBUG]2025-07-20 10:04:22,756 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0cb69d84', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' performing', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,757 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0cb69d84'...
[DEBUG]2025-07-20 10:04:22,827 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_584f1f06', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' tasks', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,828 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_584f1f06'...
[DEBUG]2025-07-20 10:04:22,829 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d1dfaab1', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' autonom', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,830 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d1dfaab1'...
[DEBUG]2025-07-20 10:04:22,878 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_7df2a508', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='ously', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,880 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_7df2a508'...
[DEBUG]2025-07-20 10:04:22,881 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_c7cd435b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' or', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,882 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_c7cd435b'...
[DEBUG]2025-07-20 10:04:22,942 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_fad21b96', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' semi', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,943 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_fad21b96'...
[DEBUG]2025-07-20 10:04:22,945 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8a46bd62', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='-aut', annotations=[]))])))
[DEBUG]2025-07-20 10:04:22,946 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_8a46bd62'...
[DEBUG]2025-07-20 10:04:23,009 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_acfb25ab', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='onom', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,011 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_acfb25ab'...
[DEBUG]2025-07-20 10:04:23,013 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_40be822a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='ously', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,014 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_40be822a'...
[DEBUG]2025-07-20 10:04:23,139 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_bb9785d3', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.\n\n', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,141 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_bb9785d3'...
[DEBUG]2025-07-20 10:04:23,142 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_2a905a47', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='6', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,143 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_2a905a47'...
[DEBUG]2025-07-20 10:04:23,145 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_dee210ae', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,146 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_dee210ae'...
[DEBUG]2025-07-20 10:04:23,195 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_6ad16d45', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' **', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,196 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_6ad16d45'...
[DEBUG]2025-07-20 10:04:23,197 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_9c142933', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='Eth', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,199 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_9c142933'...
[DEBUG]2025-07-20 10:04:23,263 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_4fef56f7', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='ics', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,265 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_4fef56f7'...
[DEBUG]2025-07-20 10:04:23,266 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_86a4a7ad', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' and', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,267 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_86a4a7ad'...
[DEBUG]2025-07-20 10:04:23,332 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_707d14ba', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' Bias', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,333 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_707d14ba'...
[DEBUG]2025-07-20 10:04:23,334 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_ba31dbb0', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='**:', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,336 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_ba31dbb0'...
[DEBUG]2025-07-20 10:04:23,392 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8dd8fcc2', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' AI', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,393 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_8dd8fcc2'...
[DEBUG]2025-07-20 10:04:23,395 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_ca2185e6', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' raises', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,396 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_ca2185e6'...
[DEBUG]2025-07-20 10:04:23,494 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_cc46cc25', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' ethical', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,495 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_cc46cc25'...
[DEBUG]2025-07-20 10:04:23,496 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_a7bbd8c8', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' concerns', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,497 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_a7bbd8c8'...
[DEBUG]2025-07-20 10:04:23,518 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_dbf22a95', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,519 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_dbf22a95'...
[DEBUG]2025-07-20 10:04:23,521 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_633a0e03', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' including', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,522 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_633a0e03'...
[DEBUG]2025-07-20 10:04:23,577 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_7ad631c7', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' bias', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,578 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_7ad631c7'...
[DEBUG]2025-07-20 10:04:23,579 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5412a9c4', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' in', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,580 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_5412a9c4'...
[DEBUG]2025-07-20 10:04:23,643 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d931c48c', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' algorithms', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,644 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d931c48c'...
[DEBUG]2025-07-20 10:04:23,647 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_81b753dc', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,648 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_81b753dc'...
[DEBUG]2025-07-20 10:04:23,704 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_ff8b26e7', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' privacy', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,705 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_ff8b26e7'...
[DEBUG]2025-07-20 10:04:23,707 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_7c0acd2d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' issues', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,708 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_7c0acd2d'...
[DEBUG]2025-07-20 10:04:23,771 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_a8936e47', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,772 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_a8936e47'...
[DEBUG]2025-07-20 10:04:23,774 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8e3c8bf5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' and', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,775 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_8e3c8bf5'...
[DEBUG]2025-07-20 10:04:23,832 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d8662f39', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' the', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,833 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d8662f39'...
[DEBUG]2025-07-20 10:04:23,834 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_1c7e69ae', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' potential', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,836 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_1c7e69ae'...
[DEBUG]2025-07-20 10:04:23,891 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_9a551f6f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' for', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,892 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_9a551f6f'...
[DEBUG]2025-07-20 10:04:23,893 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_2b39305e', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' job', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,895 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_2b39305e'...
[DEBUG]2025-07-20 10:04:23,955 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_6469e471', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' displacement', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,956 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_6469e471'...
[DEBUG]2025-07-20 10:04:23,958 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_a1fb9824', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.', annotations=[]))])))
[DEBUG]2025-07-20 10:04:23,959 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_a1fb9824'...
[DEBUG]2025-07-20 10:04:24,017 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f5719f3c', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' Efforts', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,019 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f5719f3c'...
[DEBUG]2025-07-20 10:04:24,083 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e4749f07', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' are', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,084 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e4749f07'...
[DEBUG]2025-07-20 10:04:24,086 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_c0d460af', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' underway', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,087 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_c0d460af'...
[DEBUG]2025-07-20 10:04:24,145 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e2dd3a39', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' to', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,146 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e2dd3a39'...
[DEBUG]2025-07-20 10:04:24,148 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_37991c22', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' develop', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,150 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_37991c22'...
[DEBUG]2025-07-20 10:04:24,213 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e9b2dfc9', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' fair', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,215 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e9b2dfc9'...
[DEBUG]2025-07-20 10:04:24,216 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_22782dac', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,218 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_22782dac'...
[DEBUG]2025-07-20 10:04:24,272 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_9dad3c6e', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' transparent', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,274 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_9dad3c6e'...
[DEBUG]2025-07-20 10:04:24,275 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_17ab3605', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,276 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_17ab3605'...
[DEBUG]2025-07-20 10:04:24,334 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_9aff4073', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' and', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,335 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_9aff4073'...
[DEBUG]2025-07-20 10:04:24,336 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5d7fec13', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' accountable', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,337 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_5d7fec13'...
[DEBUG]2025-07-20 10:04:24,395 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8f6c6260', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' AI', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,395 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_8f6c6260'...
[DEBUG]2025-07-20 10:04:24,396 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_05b42d23', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' systems', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,396 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_05b42d23'...
[DEBUG]2025-07-20 10:04:24,460 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_59361c62', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.\n\n', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,461 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_59361c62'...
[DEBUG]2025-07-20 10:04:24,462 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e6177d59', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='7', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,463 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e6177d59'...
[DEBUG]2025-07-20 10:04:24,525 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_acbfefa7', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='.', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,527 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_acbfefa7'...
[DEBUG]2025-07-20 10:04:24,529 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_92ae819a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' **', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,530 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_92ae819a'...
[DEBUG]2025-07-20 10:04:24,592 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_9e355109', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='Applications', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,593 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_9e355109'...
[DEBUG]2025-07-20 10:04:24,594 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_735227e8', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='**:', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,595 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_735227e8'...
[DEBUG]2025-07-20 10:04:24,657 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_80770034', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' AI', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,657 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_80770034'...
[DEBUG]2025-07-20 10:04:24,661 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_91c5bef5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' is', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,662 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_91c5bef5'...
[DEBUG]2025-07-20 10:04:24,715 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_396c3c5d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' used', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,716 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_396c3c5d'...
[DEBUG]2025-07-20 10:04:24,717 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_af730af9', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' in', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,719 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_af730af9'...
[DEBUG]2025-07-20 10:04:24,776 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_7c51e5d2', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' diverse', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,777 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_7c51e5d2'...
[DEBUG]2025-07-20 10:04:24,847 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e4709bd5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' fields', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,848 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e4709bd5'...
[DEBUG]2025-07-20 10:04:24,849 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0d19fa8f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' like', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,850 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0d19fa8f'...
[DEBUG]2025-07-20 10:04:24,906 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d2d5ff43', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' healthcare', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,908 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d2d5ff43'...
[DEBUG]2025-07-20 10:04:24,909 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0b6e50f9', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' (', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,909 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0b6e50f9'...
[DEBUG]2025-07-20 10:04:24,970 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_2174087e', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='diagn', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,971 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_2174087e'...
[DEBUG]2025-07-20 10:04:24,973 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_c9343275', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='osis', annotations=[]))])))
[DEBUG]2025-07-20 10:04:24,974 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_c9343275'...
[DEBUG]2025-07-20 10:04:25,027 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0fac0f10', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' and', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,028 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0fac0f10'...
[DEBUG]2025-07-20 10:04:25,094 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_c68a5fca', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' drug', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,095 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_c68a5fca'...
[DEBUG]2025-07-20 10:04:25,096 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_6eb1bb9d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' discovery', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,096 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_6eb1bb9d'...
[DEBUG]2025-07-20 10:04:25,187 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_7e7d27f7', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='),', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,187 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_7e7d27f7'...
[DEBUG]2025-07-20 10:04:25,188 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_daf336a7', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' finance', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,189 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_daf336a7'...
[DEBUG]2025-07-20 10:04:25,224 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5a006827', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' (', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,224 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_5a006827'...
[DEBUG]2025-07-20 10:04:25,225 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_ed251ec1', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='algorithm', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,225 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_ed251ec1'...
[DEBUG]2025-07-20 10:04:25,315 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_cc2714a7', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='ic', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,316 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_cc2714a7'...
[DEBUG]2025-07-20 10:04:25,319 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_3a6118db', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' trading', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,319 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_3a6118db'...
[DEBUG]2025-07-20 10:04:25,357 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_dc56b41c', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='),', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,358 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_dc56b41c'...
[DEBUG]2025-07-20 10:04:25,358 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5af420d6', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' education', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,359 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_5af420d6'...
[DEBUG]2025-07-20 10:04:25,413 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_77a28a93', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' (', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,414 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_77a28a93'...
[DEBUG]2025-07-20 10:04:25,416 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_72dab31a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='personal', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,416 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_72dab31a'...
[DEBUG]2025-07-20 10:04:25,478 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_2f20852a', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='ized', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,478 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_2f20852a'...
[DEBUG]2025-07-20 10:04:25,478 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_9c8fbfa7', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' learning', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,479 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_9c8fbfa7'...
[DEBUG]2025-07-20 10:04:25,536 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5cdcff1f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='),', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,536 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_5cdcff1f'...
[DEBUG]2025-07-20 10:04:25,537 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8895bf95', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' and', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,538 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_8895bf95'...
[DEBUG]2025-07-20 10:04:25,599 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e88e6738', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' entertainment', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,599 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e88e6738'...
[DEBUG]2025-07-20 10:04:25,600 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b61a8792', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' (', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,601 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b61a8792'...
[DEBUG]2025-07-20 10:04:25,671 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_7df2e1c1', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='content', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,671 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_7df2e1c1'...
[DEBUG]2025-07-20 10:04:25,672 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_63e41716', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' recommendation', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,672 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_63e41716'...
[DEBUG]2025-07-20 10:04:25,738 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e4f2ad35', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=').\n\n', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,739 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e4f2ad35'...
[DEBUG]2025-07-20 10:04:25,740 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_87264d31', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='Would', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,741 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_87264d31'...
[DEBUG]2025-07-20 10:04:25,799 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0911bbde', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' you', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,803 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0911bbde'...
[DEBUG]2025-07-20 10:04:25,804 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_2804d911', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' like', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,806 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_2804d911'...
[DEBUG]2025-07-20 10:04:25,859 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_6ec6258d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' more', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,861 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_6ec6258d'...
[DEBUG]2025-07-20 10:04:25,926 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_be8a6f7f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' details', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,927 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_be8a6f7f'...
[DEBUG]2025-07-20 10:04:25,927 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_e02b98ef', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' on', annotations=[]))])))
[DEBUG]2025-07-20 10:04:25,930 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_e02b98ef'...
[DEBUG]2025-07-20 10:04:26,014 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_492e16f6', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' a', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,015 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_492e16f6'...
[DEBUG]2025-07-20 10:04:26,051 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_62c5bb43', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' specific', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,052 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_62c5bb43'...
[DEBUG]2025-07-20 10:04:26,056 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_dcf617b8', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' aspect', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,057 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_dcf617b8'...
[DEBUG]2025-07-20 10:04:26,108 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_5a9ff8e5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' of', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,109 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_5a9ff8e5'...
[DEBUG]2025-07-20 10:04:26,109 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f0a90a16', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' AI', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,109 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f0a90a16'...
[DEBUG]2025-07-20 10:04:26,168 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b5ea2f8c', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,169 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b5ea2f8c'...
[DEBUG]2025-07-20 10:04:26,171 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d06c9bd7', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' such', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,171 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d06c9bd7'...
[DEBUG]2025-07-20 10:04:26,235 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_684116d3', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' as', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,236 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_684116d3'...
[DEBUG]2025-07-20 10:04:26,237 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_650702d9', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' its', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,237 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_650702d9'...
[DEBUG]2025-07-20 10:04:26,295 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_0852da22', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' history', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,295 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_0852da22'...
[DEBUG]2025-07-20 10:04:26,295 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_bfd2165f', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,296 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_bfd2165f'...
[DEBUG]2025-07-20 10:04:26,360 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_33c132da', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' current', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,360 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_33c132da'...
[DEBUG]2025-07-20 10:04:26,423 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_da5172ee', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' trends', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,424 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_da5172ee'...
[DEBUG]2025-07-20 10:04:26,425 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_2941fe45', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=',', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,425 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_2941fe45'...
[DEBUG]2025-07-20 10:04:26,487 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_ede84d3b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' or', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,487 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_ede84d3b'...
[DEBUG]2025-07-20 10:04:26,488 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_eba96886', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' ethical', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,488 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_eba96886'...
[DEBUG]2025-07-20 10:04:26,554 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_22134b02', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' considerations', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,555 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_22134b02'...
[DEBUG]2025-07-20 10:04:26,612 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_b37378ea', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='?', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,613 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_b37378ea'...
[DEBUG]2025-07-20 10:04:26,614 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d250466c', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' Or', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,615 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d250466c'...
[DEBUG]2025-07-20 10:04:26,854 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_7a206788', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' would', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,855 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_7a206788'...
[DEBUG]2025-07-20 10:04:26,855 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d28b92ab', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' you', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,856 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d28b92ab'...
[DEBUG]2025-07-20 10:04:26,858 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_d0e6253c', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' like', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,859 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_d0e6253c'...
[DEBUG]2025-07-20 10:04:26,860 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_fcad3833', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' examples', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,861 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_fcad3833'...
[DEBUG]2025-07-20 10:04:26,861 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_58abb35b', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' of', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,862 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_58abb35b'...
[DEBUG]2025-07-20 10:04:26,868 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_f3810805', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' AI', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,868 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_f3810805'...
[DEBUG]2025-07-20 10:04:26,868 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_a47a6a35', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' applications', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,869 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_a47a6a35'...
[DEBUG]2025-07-20 10:04:26,937 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_3debe1d6', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' in', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,938 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_3debe1d6'...
[DEBUG]2025-07-20 10:04:26,939 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_eef19031', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' a', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,939 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_eef19031'...
[DEBUG]2025-07-20 10:04:26,997 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_7b2fafe5', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' particular', annotations=[]))])))
[DEBUG]2025-07-20 10:04:26,998 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_7b2fafe5'...
[DEBUG]2025-07-20 10:04:26,999 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_df3e8681', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value=' industry', annotations=[]))])))
[DEBUG]2025-07-20 10:04:27,000 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_df3e8681'...
[DEBUG]2025-07-20 10:04:27,070 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: MessageEvent(event='message', data=MessageData(id='msg_8328ef3d', object='agent.message.delta', delta=Delta(content=[MessageDelta(type='text', payload=DeltaPayload(value='?', annotations=[]))])))
[DEBUG]2025-07-20 10:04:27,070 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='message' data=MessageData(id='msg_8328ef3d'...
[DEBUG]2025-07-20 10:04:27,071 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: FinalAnswerEvent(event='final_answer', data=FinalAnswerData(generated_answer='Artificial intelligence (AI) is a broad field of computer science focused on creating systems capable of performing tasks that typically require human intelligence. These tasks include reasoning, learning, problem-solving, perception, understanding natural language, and decision-making. AI can be categorized into narrow AI (designed for specific tasks) and general AI (hypothetical systems with human-like cognitive abilities).\n\nHere are some key aspects of AI:\n\n1. **Machine Learning (ML)**: A subset of AI that involves training algorithms to learn patterns from data and make predictions or decisions without explicit programming. Examples include image recognition, recommendation systems, and fraud detection.\n\n2. **Deep Learning**: A specialized form of ML using neural networks with many layers (deep neural networks). It powers advancements in areas like computer vision, speech recognition, and natural language processing (NLP).\n\n3. **Natural Language Processing (NLP)**: Enables machines to understand, interpret, and generate human language. Applications include chatbots, translation services, and sentiment analysis.\n\n4. **Computer Vision**: Allows machines to interpret and process visual information from the world, such as facial recognition, autonomous vehicles, and medical image analysis.\n\n5. **Robotics**: Combines AI with mechanical systems to create robots capable of performing tasks autonomously or semi-autonomously.\n\n6. **Ethics and Bias**: AI raises ethical concerns, including bias in algorithms, privacy issues, and the potential for job displacement. Efforts are underway to develop fair, transparent, and accountable AI systems.\n\n7. **Applications**: AI is used in diverse fields like healthcare (diagnosis and drug discovery), finance (algorithmic trading), education (personalized learning), and entertainment (content recommendation).\n\nWould you like more details on a specific aspect of AI, such as its history, current trends, or ethical considerations? Or would you like examples of AI applications in a particular industry?', citations=[]))
[DEBUG]2025-07-20 10:04:27,072 File 'r2rAgent.py',line 154: [R2RAgent] Converted chunk to string: event='final_answer' data=FinalAnswerData(generate...
[DEBUG]2025-07-20 10:04:27,072 File 'r2rAgent.py',line 147: [R2RAgent] Received chunk: None
[INFO]2025-07-20 10:04:30,299 File 'r2rAgent.py',line 155: [R2RAgent] Finished iterating, got 378 chunks
[DEBUG]2025-07-20 10:04:30,300 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_558dcc53'...
[DEBUG]2025-07-20 10:04:30,301 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_c55e649e'...
[DEBUG]2025-07-20 10:04:30,301 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_53abd0da'...
[DEBUG]2025-07-20 10:04:30,302 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_fd2e474b'...
[DEBUG]2025-07-20 10:04:30,302 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_fd92428e'...
[DEBUG]2025-07-20 10:04:30,302 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_07eadbe5'...
[DEBUG]2025-07-20 10:04:30,302 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_3bedc2cc'...
[DEBUG]2025-07-20 10:04:30,303 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_de20f2d8'...
[DEBUG]2025-07-20 10:04:30,303 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f80d2e09'...
[DEBUG]2025-07-20 10:04:30,304 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_fe75927c'...
[DEBUG]2025-07-20 10:04:30,304 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_6a0cbf67'...
[DEBUG]2025-07-20 10:04:30,305 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_58ae23b1'...
[DEBUG]2025-07-20 10:04:30,305 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b2fd1327'...
[DEBUG]2025-07-20 10:04:30,305 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d22ecfa5'...
[DEBUG]2025-07-20 10:04:30,306 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_cd160e48'...
[DEBUG]2025-07-20 10:04:30,306 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b2f7e72f'...
[DEBUG]2025-07-20 10:04:30,306 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d6689b4e'...
[DEBUG]2025-07-20 10:04:30,306 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_63f0421c'...
[DEBUG]2025-07-20 10:04:30,306 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d3d1e10f'...
[DEBUG]2025-07-20 10:04:30,307 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_1f118c25'...
[DEBUG]2025-07-20 10:04:30,307 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_26e62445'...
[DEBUG]2025-07-20 10:04:30,307 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b31c67f8'...
[DEBUG]2025-07-20 10:04:30,307 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_3de2c27c'...
[DEBUG]2025-07-20 10:04:30,308 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b8158816'...
[DEBUG]2025-07-20 10:04:30,308 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_01a03600'...
[DEBUG]2025-07-20 10:04:30,308 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_46cf69bc'...
[DEBUG]2025-07-20 10:04:30,308 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_48b1c447'...
[DEBUG]2025-07-20 10:04:30,309 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_fd04048e'...
[DEBUG]2025-07-20 10:04:30,309 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_037d5446'...
[DEBUG]2025-07-20 10:04:30,309 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_a795b35b'...
[DEBUG]2025-07-20 10:04:30,309 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d9e700f1'...
[DEBUG]2025-07-20 10:04:30,309 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_56f64006'...
[DEBUG]2025-07-20 10:04:30,310 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_5fb731f5'...
[DEBUG]2025-07-20 10:04:30,310 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_acb5538b'...
[DEBUG]2025-07-20 10:04:30,311 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_72b8e7f2'...
[DEBUG]2025-07-20 10:04:30,311 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_7e51726b'...
[DEBUG]2025-07-20 10:04:30,312 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_5deb1e6d'...
[DEBUG]2025-07-20 10:04:30,312 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f1bd5739'...
[DEBUG]2025-07-20 10:04:30,312 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_26de399d'...
[DEBUG]2025-07-20 10:04:30,312 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_2cd23d95'...
[DEBUG]2025-07-20 10:04:30,313 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_3b0bb080'...
[DEBUG]2025-07-20 10:04:30,313 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_98d8915a'...
[DEBUG]2025-07-20 10:04:30,313 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_9cb79267'...
[DEBUG]2025-07-20 10:04:30,313 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d743b653'...
[DEBUG]2025-07-20 10:04:30,313 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_8df508ba'...
[DEBUG]2025-07-20 10:04:30,314 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_bfdbbe7f'...
[DEBUG]2025-07-20 10:04:30,314 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_af075e46'...
[DEBUG]2025-07-20 10:04:30,314 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_ae6da328'...
[DEBUG]2025-07-20 10:04:30,314 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_3fc1e4d9'...
[DEBUG]2025-07-20 10:04:30,315 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_4b91eca3'...
[DEBUG]2025-07-20 10:04:30,315 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_c0c5b1f4'...
[DEBUG]2025-07-20 10:04:30,315 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f93cedb9'...
[DEBUG]2025-07-20 10:04:30,315 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_00f321df'...
[DEBUG]2025-07-20 10:04:30,315 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_6b33f2f5'...
[DEBUG]2025-07-20 10:04:30,316 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_57f581ec'...
[DEBUG]2025-07-20 10:04:30,316 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_ca844e02'...
[DEBUG]2025-07-20 10:04:30,316 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_eaf2b33b'...
[DEBUG]2025-07-20 10:04:30,316 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_43bdaeb2'...
[DEBUG]2025-07-20 10:04:30,316 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_77dd3f5a'...
[DEBUG]2025-07-20 10:04:30,317 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_13bd3cef'...
[DEBUG]2025-07-20 10:04:30,317 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_18193d34'...
[DEBUG]2025-07-20 10:04:30,317 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d5c7cc31'...
[DEBUG]2025-07-20 10:04:30,317 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_69b29ba1'...
[DEBUG]2025-07-20 10:04:30,317 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_ac0ee142'...
[DEBUG]2025-07-20 10:04:30,318 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_7b770e5d'...
[DEBUG]2025-07-20 10:04:30,318 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_45bdbd9f'...
[DEBUG]2025-07-20 10:04:30,318 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_274ed761'...
[DEBUG]2025-07-20 10:04:30,318 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_198aab6a'...
[DEBUG]2025-07-20 10:04:30,318 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_65b0e031'...
[DEBUG]2025-07-20 10:04:30,318 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b58a1dc3'...
[DEBUG]2025-07-20 10:04:30,319 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_8798044d'...
[DEBUG]2025-07-20 10:04:30,319 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_ae65a8f7'...
[DEBUG]2025-07-20 10:04:30,319 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_6655487f'...
[DEBUG]2025-07-20 10:04:30,319 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_12ca62b2'...
[DEBUG]2025-07-20 10:04:30,319 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_5886807f'...
[DEBUG]2025-07-20 10:04:30,319 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_de57f4d1'...
[DEBUG]2025-07-20 10:04:30,319 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_6d02b1bd'...
[DEBUG]2025-07-20 10:04:30,320 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_ee2309eb'...
[DEBUG]2025-07-20 10:04:30,320 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_afee77e1'...
[DEBUG]2025-07-20 10:04:30,320 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_397c0648'...
[DEBUG]2025-07-20 10:04:30,320 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_2a799782'...
[DEBUG]2025-07-20 10:04:30,320 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_113a5e7b'...
[DEBUG]2025-07-20 10:04:30,321 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d683299b'...
[DEBUG]2025-07-20 10:04:30,321 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_cb3d77d9'...
[DEBUG]2025-07-20 10:04:30,321 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_03f8f73b'...
[DEBUG]2025-07-20 10:04:30,322 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_68215a9a'...
[DEBUG]2025-07-20 10:04:30,322 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_1bedbd10'...
[DEBUG]2025-07-20 10:04:30,322 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_3cb1ed7a'...
[DEBUG]2025-07-20 10:04:30,322 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_a080fdb5'...
[DEBUG]2025-07-20 10:04:30,322 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d94e10a6'...
[DEBUG]2025-07-20 10:04:30,322 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_636360eb'...
[DEBUG]2025-07-20 10:04:30,323 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_06030e7b'...
[DEBUG]2025-07-20 10:04:30,323 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_4ca0f1bd'...
[DEBUG]2025-07-20 10:04:30,323 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_5ef97e6d'...
[DEBUG]2025-07-20 10:04:30,323 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_47c196ac'...
[DEBUG]2025-07-20 10:04:30,323 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0df67456'...
[DEBUG]2025-07-20 10:04:30,323 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_29f56a27'...
[DEBUG]2025-07-20 10:04:30,324 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_480899f0'...
[DEBUG]2025-07-20 10:04:30,324 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_96902e7f'...
[DEBUG]2025-07-20 10:04:30,324 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_2e636139'...
[DEBUG]2025-07-20 10:04:30,324 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_de12b3b7'...
[DEBUG]2025-07-20 10:04:30,325 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_8b048b27'...
[DEBUG]2025-07-20 10:04:30,325 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_34405255'...
[DEBUG]2025-07-20 10:04:30,325 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_8cd82028'...
[DEBUG]2025-07-20 10:04:30,325 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f7dd09da'...
[DEBUG]2025-07-20 10:04:30,325 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0c32885d'...
[DEBUG]2025-07-20 10:04:30,326 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0b9ca25b'...
[DEBUG]2025-07-20 10:04:30,326 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_45ce86e5'...
[DEBUG]2025-07-20 10:04:30,326 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_70e8a7ce'...
[DEBUG]2025-07-20 10:04:30,326 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_c76508b0'...
[DEBUG]2025-07-20 10:04:30,326 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_ea9f8513'...
[DEBUG]2025-07-20 10:04:30,327 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_84180eaf'...
[DEBUG]2025-07-20 10:04:30,327 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b81cd431'...
[DEBUG]2025-07-20 10:04:30,327 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_7ffd96a4'...
[DEBUG]2025-07-20 10:04:30,327 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_c1318f45'...
[DEBUG]2025-07-20 10:04:30,327 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_c1d76f80'...
[DEBUG]2025-07-20 10:04:30,328 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_5d38e550'...
[DEBUG]2025-07-20 10:04:30,328 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_977fc45d'...
[DEBUG]2025-07-20 10:04:30,328 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0676e3fb'...
[DEBUG]2025-07-20 10:04:30,328 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e02d0ae6'...
[DEBUG]2025-07-20 10:04:30,328 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_8df0c308'...
[DEBUG]2025-07-20 10:04:30,328 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_3b401b28'...
[DEBUG]2025-07-20 10:04:30,329 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f068dcc7'...
[DEBUG]2025-07-20 10:04:30,329 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_5b4b43c8'...
[DEBUG]2025-07-20 10:04:30,329 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_9581e2c9'...
[DEBUG]2025-07-20 10:04:30,329 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f214b84c'...
[DEBUG]2025-07-20 10:04:30,329 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_912896e8'...
[DEBUG]2025-07-20 10:04:30,330 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d2db8664'...
[DEBUG]2025-07-20 10:04:30,330 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_ebae6cbe'...
[DEBUG]2025-07-20 10:04:30,330 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_29773633'...
[DEBUG]2025-07-20 10:04:30,330 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_6b2b1ca4'...
[DEBUG]2025-07-20 10:04:30,330 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_80fdde21'...
[DEBUG]2025-07-20 10:04:30,331 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_db8bed29'...
[DEBUG]2025-07-20 10:04:30,331 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d09ab763'...
[DEBUG]2025-07-20 10:04:30,331 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d856cfef'...
[DEBUG]2025-07-20 10:04:30,331 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d67777d4'...
[DEBUG]2025-07-20 10:04:30,331 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d1c08513'...
[DEBUG]2025-07-20 10:04:30,331 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b4a38246'...
[DEBUG]2025-07-20 10:04:30,333 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_9f4ad5ac'...
[DEBUG]2025-07-20 10:04:30,333 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d6989cb5'...
[DEBUG]2025-07-20 10:04:30,333 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_db2b307f'...
[DEBUG]2025-07-20 10:04:30,333 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b368ffae'...
[DEBUG]2025-07-20 10:04:30,333 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_cb3af896'...
[DEBUG]2025-07-20 10:04:30,333 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_2638fd3d'...
[DEBUG]2025-07-20 10:04:30,334 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_237022ad'...
[DEBUG]2025-07-20 10:04:30,334 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_025c8df9'...
[DEBUG]2025-07-20 10:04:30,334 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f8660de7'...
[DEBUG]2025-07-20 10:04:30,334 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_712f7608'...
[DEBUG]2025-07-20 10:04:30,334 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_eeac8c65'...
[DEBUG]2025-07-20 10:04:30,335 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_85d81377'...
[DEBUG]2025-07-20 10:04:30,335 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_37f2322f'...
[DEBUG]2025-07-20 10:04:30,335 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_86da8423'...
[DEBUG]2025-07-20 10:04:30,335 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_84633455'...
[DEBUG]2025-07-20 10:04:30,335 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_fc6414b9'...
[DEBUG]2025-07-20 10:04:30,335 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_375561fd'...
[DEBUG]2025-07-20 10:04:30,336 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_66e0f665'...
[DEBUG]2025-07-20 10:04:30,336 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_faef2e4b'...
[DEBUG]2025-07-20 10:04:30,336 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_61d3935d'...
[DEBUG]2025-07-20 10:04:30,336 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b481a3f0'...
[DEBUG]2025-07-20 10:04:30,336 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b92e376d'...
[DEBUG]2025-07-20 10:04:30,337 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e80446f8'...
[DEBUG]2025-07-20 10:04:30,337 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e9555834'...
[DEBUG]2025-07-20 10:04:30,337 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_dbcba290'...
[DEBUG]2025-07-20 10:04:30,337 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_72835da1'...
[DEBUG]2025-07-20 10:04:30,337 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_348e16d3'...
[DEBUG]2025-07-20 10:04:30,337 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_2e2c1061'...
[DEBUG]2025-07-20 10:04:30,337 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f26b8ff5'...
[DEBUG]2025-07-20 10:04:30,337 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_db15cb82'...
[DEBUG]2025-07-20 10:04:30,337 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_542833e0'...
[DEBUG]2025-07-20 10:04:30,338 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_6588256f'...
[DEBUG]2025-07-20 10:04:30,338 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_21e93396'...
[DEBUG]2025-07-20 10:04:30,338 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_93b690fc'...
[DEBUG]2025-07-20 10:04:30,338 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_1a4ea935'...
[DEBUG]2025-07-20 10:04:30,338 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e907ab82'...
[DEBUG]2025-07-20 10:04:30,339 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_19bd136e'...
[DEBUG]2025-07-20 10:04:30,339 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b8686014'...
[DEBUG]2025-07-20 10:04:30,339 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_aabf880a'...
[DEBUG]2025-07-20 10:04:30,339 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_812f698c'...
[DEBUG]2025-07-20 10:04:30,340 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f8b456a0'...
[DEBUG]2025-07-20 10:04:30,340 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_267e8ad3'...
[DEBUG]2025-07-20 10:04:30,340 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d8d7242a'...
[DEBUG]2025-07-20 10:04:30,340 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_458201a8'...
[DEBUG]2025-07-20 10:04:30,341 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_8fdc8350'...
[DEBUG]2025-07-20 10:04:30,341 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_c39335f5'...
[DEBUG]2025-07-20 10:04:30,341 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_22f4ef2b'...
[DEBUG]2025-07-20 10:04:30,341 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_34a04cf0'...
[DEBUG]2025-07-20 10:04:30,342 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e5a7e701'...
[DEBUG]2025-07-20 10:04:30,342 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_957baabf'...
[DEBUG]2025-07-20 10:04:30,342 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_291a01d6'...
[DEBUG]2025-07-20 10:04:30,342 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_70bacf24'...
[DEBUG]2025-07-20 10:04:30,342 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d37817d8'...
[DEBUG]2025-07-20 10:04:30,343 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_2e11f70f'...
[DEBUG]2025-07-20 10:04:30,343 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_a46b46d3'...
[DEBUG]2025-07-20 10:04:30,343 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_58a03611'...
[DEBUG]2025-07-20 10:04:30,343 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_2810097e'...
[DEBUG]2025-07-20 10:04:30,343 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_dc299b14'...
[DEBUG]2025-07-20 10:04:30,344 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_adc0e52e'...
[DEBUG]2025-07-20 10:04:30,344 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_8663a173'...
[DEBUG]2025-07-20 10:04:30,344 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_62eeab71'...
[DEBUG]2025-07-20 10:04:30,344 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_a35ee356'...
[DEBUG]2025-07-20 10:04:30,344 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_5b08210a'...
[DEBUG]2025-07-20 10:04:30,344 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_3da42d3d'...
[DEBUG]2025-07-20 10:04:30,345 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d6f4e728'...
[DEBUG]2025-07-20 10:04:30,345 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0663b587'...
[DEBUG]2025-07-20 10:04:30,345 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e037bb2f'...
[DEBUG]2025-07-20 10:04:30,345 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d8628039'...
[DEBUG]2025-07-20 10:04:30,346 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_4f769fe6'...
[DEBUG]2025-07-20 10:04:30,346 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_1abe5c81'...
[DEBUG]2025-07-20 10:04:30,346 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0e37d12a'...
[DEBUG]2025-07-20 10:04:30,346 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_a5da8bfd'...
[DEBUG]2025-07-20 10:04:30,346 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_148cfe92'...
[DEBUG]2025-07-20 10:04:30,348 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d28eb7f5'...
[DEBUG]2025-07-20 10:04:30,348 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b41f1dc1'...
[DEBUG]2025-07-20 10:04:30,348 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_fc3c40d0'...
[DEBUG]2025-07-20 10:04:30,348 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_68dc8a4c'...
[DEBUG]2025-07-20 10:04:30,348 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_5f700268'...
[DEBUG]2025-07-20 10:04:30,348 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_fd52ef23'...
[DEBUG]2025-07-20 10:04:30,348 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_cebc1214'...
[DEBUG]2025-07-20 10:04:30,348 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_9551f435'...
[DEBUG]2025-07-20 10:04:30,348 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0699a6c5'...
[DEBUG]2025-07-20 10:04:30,348 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_6187da22'...
[DEBUG]2025-07-20 10:04:30,349 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_7adca892'...
[DEBUG]2025-07-20 10:04:30,349 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0078048f'...
[DEBUG]2025-07-20 10:04:30,349 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_c5d3f956'...
[DEBUG]2025-07-20 10:04:30,349 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_598d59a3'...
[DEBUG]2025-07-20 10:04:30,349 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_ad037bd9'...
[DEBUG]2025-07-20 10:04:30,349 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_651b1530'...
[DEBUG]2025-07-20 10:04:30,350 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_706facad'...
[DEBUG]2025-07-20 10:04:30,350 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_9767d636'...
[DEBUG]2025-07-20 10:04:30,350 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0fbb1afd'...
[DEBUG]2025-07-20 10:04:30,350 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f6aeb9d4'...
[DEBUG]2025-07-20 10:04:30,350 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_aa5b5696'...
[DEBUG]2025-07-20 10:04:30,351 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0a25d10f'...
[DEBUG]2025-07-20 10:04:30,351 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d31673dd'...
[DEBUG]2025-07-20 10:04:30,351 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_1a0ad1a6'...
[DEBUG]2025-07-20 10:04:30,351 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_762969e1'...
[DEBUG]2025-07-20 10:04:30,351 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_a912fef8'...
[DEBUG]2025-07-20 10:04:30,351 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f22bfcb5'...
[DEBUG]2025-07-20 10:04:30,352 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_6b76dbae'...
[DEBUG]2025-07-20 10:04:30,352 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_4b062f94'...
[DEBUG]2025-07-20 10:04:30,352 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_59b31946'...
[DEBUG]2025-07-20 10:04:30,352 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e4143ecb'...
[DEBUG]2025-07-20 10:04:30,352 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f695ccc3'...
[DEBUG]2025-07-20 10:04:30,352 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_01fabed3'...
[DEBUG]2025-07-20 10:04:30,352 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_9d64f0fc'...
[DEBUG]2025-07-20 10:04:30,353 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_66bf9c16'...
[DEBUG]2025-07-20 10:04:30,353 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_7cb4682c'...
[DEBUG]2025-07-20 10:04:30,353 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_30485279'...
[DEBUG]2025-07-20 10:04:30,353 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_6bfb0160'...
[DEBUG]2025-07-20 10:04:30,353 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_3b9cbf02'...
[DEBUG]2025-07-20 10:04:30,353 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0cb69d84'...
[DEBUG]2025-07-20 10:04:30,354 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_584f1f06'...
[DEBUG]2025-07-20 10:04:30,354 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d1dfaab1'...
[DEBUG]2025-07-20 10:04:30,354 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_7df2a508'...
[DEBUG]2025-07-20 10:04:30,354 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_c7cd435b'...
[DEBUG]2025-07-20 10:04:30,354 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_fad21b96'...
[DEBUG]2025-07-20 10:04:30,354 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_8a46bd62'...
[DEBUG]2025-07-20 10:04:30,354 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_acfb25ab'...
[DEBUG]2025-07-20 10:04:30,354 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_40be822a'...
[DEBUG]2025-07-20 10:04:30,356 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_bb9785d3'...
[DEBUG]2025-07-20 10:04:30,356 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_2a905a47'...
[DEBUG]2025-07-20 10:04:30,356 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_dee210ae'...
[DEBUG]2025-07-20 10:04:30,356 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_6ad16d45'...
[DEBUG]2025-07-20 10:04:30,356 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_9c142933'...
[DEBUG]2025-07-20 10:04:30,356 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_4fef56f7'...
[DEBUG]2025-07-20 10:04:30,356 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_86a4a7ad'...
[DEBUG]2025-07-20 10:04:30,356 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_707d14ba'...
[DEBUG]2025-07-20 10:04:30,357 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_ba31dbb0'...
[DEBUG]2025-07-20 10:04:30,357 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_8dd8fcc2'...
[DEBUG]2025-07-20 10:04:30,357 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_ca2185e6'...
[DEBUG]2025-07-20 10:04:30,357 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_cc46cc25'...
[DEBUG]2025-07-20 10:04:30,358 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_a7bbd8c8'...
[DEBUG]2025-07-20 10:04:30,358 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_dbf22a95'...
[DEBUG]2025-07-20 10:04:30,358 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_633a0e03'...
[DEBUG]2025-07-20 10:04:30,358 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_7ad631c7'...
[DEBUG]2025-07-20 10:04:30,359 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_5412a9c4'...
[DEBUG]2025-07-20 10:04:30,359 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d931c48c'...
[DEBUG]2025-07-20 10:04:30,359 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_81b753dc'...
[DEBUG]2025-07-20 10:04:30,359 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_ff8b26e7'...
[DEBUG]2025-07-20 10:04:30,359 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_7c0acd2d'...
[DEBUG]2025-07-20 10:04:30,359 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_a8936e47'...
[DEBUG]2025-07-20 10:04:30,360 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_8e3c8bf5'...
[DEBUG]2025-07-20 10:04:30,360 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d8662f39'...
[DEBUG]2025-07-20 10:04:30,360 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_1c7e69ae'...
[DEBUG]2025-07-20 10:04:30,360 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_9a551f6f'...
[DEBUG]2025-07-20 10:04:30,361 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_2b39305e'...
[DEBUG]2025-07-20 10:04:30,361 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_6469e471'...
[DEBUG]2025-07-20 10:04:30,361 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_a1fb9824'...
[DEBUG]2025-07-20 10:04:30,361 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f5719f3c'...
[DEBUG]2025-07-20 10:04:30,361 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e4749f07'...
[DEBUG]2025-07-20 10:04:30,362 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_c0d460af'...
[DEBUG]2025-07-20 10:04:30,362 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e2dd3a39'...
[DEBUG]2025-07-20 10:04:30,362 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_37991c22'...
[DEBUG]2025-07-20 10:04:30,362 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e9b2dfc9'...
[DEBUG]2025-07-20 10:04:30,362 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_22782dac'...
[DEBUG]2025-07-20 10:04:30,363 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_9dad3c6e'...
[DEBUG]2025-07-20 10:04:30,363 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_17ab3605'...
[DEBUG]2025-07-20 10:04:30,363 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_9aff4073'...
[DEBUG]2025-07-20 10:04:30,363 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_5d7fec13'...
[DEBUG]2025-07-20 10:04:30,363 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_8f6c6260'...
[DEBUG]2025-07-20 10:04:30,363 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_05b42d23'...
[DEBUG]2025-07-20 10:04:30,365 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_59361c62'...
[DEBUG]2025-07-20 10:04:30,365 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e6177d59'...
[DEBUG]2025-07-20 10:04:30,365 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_acbfefa7'...
[DEBUG]2025-07-20 10:04:30,365 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_92ae819a'...
[DEBUG]2025-07-20 10:04:30,365 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_9e355109'...
[DEBUG]2025-07-20 10:04:30,365 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_735227e8'...
[DEBUG]2025-07-20 10:04:30,365 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_80770034'...
[DEBUG]2025-07-20 10:04:30,365 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_91c5bef5'...
[DEBUG]2025-07-20 10:04:30,365 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_396c3c5d'...
[DEBUG]2025-07-20 10:04:30,365 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_af730af9'...
[DEBUG]2025-07-20 10:04:30,365 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_7c51e5d2'...
[DEBUG]2025-07-20 10:04:30,365 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e4709bd5'...
[DEBUG]2025-07-20 10:04:30,367 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0d19fa8f'...
[DEBUG]2025-07-20 10:04:30,367 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d2d5ff43'...
[DEBUG]2025-07-20 10:04:30,367 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0b6e50f9'...
[DEBUG]2025-07-20 10:04:30,368 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_2174087e'...
[DEBUG]2025-07-20 10:04:30,368 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_c9343275'...
[DEBUG]2025-07-20 10:04:30,368 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0fac0f10'...
[DEBUG]2025-07-20 10:04:30,368 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_c68a5fca'...
[DEBUG]2025-07-20 10:04:30,369 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_6eb1bb9d'...
[DEBUG]2025-07-20 10:04:30,369 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_7e7d27f7'...
[DEBUG]2025-07-20 10:04:30,369 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_daf336a7'...
[DEBUG]2025-07-20 10:04:30,369 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_5a006827'...
[DEBUG]2025-07-20 10:04:30,369 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_ed251ec1'...
[DEBUG]2025-07-20 10:04:30,370 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_cc2714a7'...
[DEBUG]2025-07-20 10:04:30,370 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_3a6118db'...
[DEBUG]2025-07-20 10:04:30,370 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_dc56b41c'...
[DEBUG]2025-07-20 10:04:30,370 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_5af420d6'...
[DEBUG]2025-07-20 10:04:30,370 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_77a28a93'...
[DEBUG]2025-07-20 10:04:30,370 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_72dab31a'...
[DEBUG]2025-07-20 10:04:30,370 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_2f20852a'...
[DEBUG]2025-07-20 10:04:30,370 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_9c8fbfa7'...
[DEBUG]2025-07-20 10:04:30,370 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_5cdcff1f'...
[DEBUG]2025-07-20 10:04:30,371 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_8895bf95'...
[DEBUG]2025-07-20 10:04:30,371 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e88e6738'...
[DEBUG]2025-07-20 10:04:30,371 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b61a8792'...
[DEBUG]2025-07-20 10:04:30,371 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_7df2e1c1'...
[DEBUG]2025-07-20 10:04:30,371 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_63e41716'...
[DEBUG]2025-07-20 10:04:30,372 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e4f2ad35'...
[DEBUG]2025-07-20 10:04:30,372 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_87264d31'...
[DEBUG]2025-07-20 10:04:30,372 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0911bbde'...
[DEBUG]2025-07-20 10:04:30,372 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_2804d911'...
[DEBUG]2025-07-20 10:04:30,373 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_6ec6258d'...
[DEBUG]2025-07-20 10:04:30,373 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_be8a6f7f'...
[DEBUG]2025-07-20 10:04:30,373 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_e02b98ef'...
[DEBUG]2025-07-20 10:04:30,373 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_492e16f6'...
[DEBUG]2025-07-20 10:04:30,374 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_62c5bb43'...
[DEBUG]2025-07-20 10:04:30,374 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_dcf617b8'...
[DEBUG]2025-07-20 10:04:30,375 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_5a9ff8e5'...
[DEBUG]2025-07-20 10:04:30,375 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f0a90a16'...
[DEBUG]2025-07-20 10:04:30,375 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b5ea2f8c'...
[DEBUG]2025-07-20 10:04:30,375 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d06c9bd7'...
[DEBUG]2025-07-20 10:04:30,375 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_684116d3'...
[DEBUG]2025-07-20 10:04:30,375 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_650702d9'...
[DEBUG]2025-07-20 10:04:30,377 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_0852da22'...
[DEBUG]2025-07-20 10:04:30,377 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_bfd2165f'...
[DEBUG]2025-07-20 10:04:30,377 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_33c132da'...
[DEBUG]2025-07-20 10:04:30,377 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_da5172ee'...
[DEBUG]2025-07-20 10:04:30,378 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_2941fe45'...
[DEBUG]2025-07-20 10:04:30,378 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_ede84d3b'...
[DEBUG]2025-07-20 10:04:30,378 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_eba96886'...
[DEBUG]2025-07-20 10:04:30,378 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_22134b02'...
[DEBUG]2025-07-20 10:04:30,379 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_b37378ea'...
[DEBUG]2025-07-20 10:04:30,379 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d250466c'...
[DEBUG]2025-07-20 10:04:30,379 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_7a206788'...
[DEBUG]2025-07-20 10:04:30,379 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d28b92ab'...
[DEBUG]2025-07-20 10:04:30,380 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_d0e6253c'...
[DEBUG]2025-07-20 10:04:30,380 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_fcad3833'...
[DEBUG]2025-07-20 10:04:30,381 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_58abb35b'...
[DEBUG]2025-07-20 10:04:30,381 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_f3810805'...
[DEBUG]2025-07-20 10:04:30,381 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_a47a6a35'...
[DEBUG]2025-07-20 10:04:30,381 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_3debe1d6'...
[DEBUG]2025-07-20 10:04:30,381 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_eef19031'...
[DEBUG]2025-07-20 10:04:30,381 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_7b2fafe5'...
[DEBUG]2025-07-20 10:04:30,382 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_df3e8681'...
[DEBUG]2025-07-20 10:04:30,382 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='message' data=MessageData(id='msg_8328ef3d'...
[DEBUG]2025-07-20 10:04:30,382 File 'r2rAgent.py',line 169: [R2RAgent] Yielded chunk: event='final_answer' data=FinalAnswerData(generate...
[INFO]2025-07-20 10:04:30,382 File 'r2rAgent.py',line 171: [R2RAgent] Completed response, total length: 71723
