"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-stately+overlays@3.6_8ca4e08d61b6a94891b76d2efe1c2a09";
exports.ids = ["vendor-chunks/@react-stately+overlays@3.6_8ca4e08d61b6a94891b76d2efe1c2a09"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+overlays@3.6_8ca4e08d61b6a94891b76d2efe1c2a09/node_modules/@react-stately/overlays/dist/useOverlayTriggerState.mjs":
/*!**************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+overlays@3.6_8ca4e08d61b6a94891b76d2efe1c2a09/node_modules/@react-stately/overlays/dist/useOverlayTriggerState.mjs ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useOverlayTriggerState: () => (/* binding */ $fc909762b330b746$export$61c6a8c84e605fb6)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _react_stately_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-stately/utils */ \"(ssr)/./node_modules/.pnpm/@react-stately+utils@3.10.5_643de144daf18c30f9f7e3eb5c877f70/node_modules/@react-stately/utils/dist/useControlledState.mjs\");\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\nfunction $fc909762b330b746$export$61c6a8c84e605fb6(props) {\n    let [isOpen, setOpen] = (0, _react_stately_utils__WEBPACK_IMPORTED_MODULE_1__.useControlledState)(props.isOpen, props.defaultOpen || false, props.onOpenChange);\n    const open = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setOpen(true);\n    }, [\n        setOpen\n    ]);\n    const close = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setOpen(false);\n    }, [\n        setOpen\n    ]);\n    const toggle = (0, react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setOpen(!isOpen);\n    }, [\n        setOpen,\n        isOpen\n    ]);\n    return {\n        isOpen: isOpen,\n        setOpen: setOpen,\n        open: open,\n        close: close,\n        toggle: toggle\n    };\n}\n\n\n\n//# sourceMappingURL=useOverlayTriggerState.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+overlays@3.6_8ca4e08d61b6a94891b76d2efe1c2a09/node_modules/@react-stately/overlays/dist/useOverlayTriggerState.mjs\n");

/***/ })

};
;