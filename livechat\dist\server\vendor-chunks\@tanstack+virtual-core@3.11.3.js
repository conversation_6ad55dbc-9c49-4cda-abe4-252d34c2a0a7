"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack+virtual-core@3.11.3";
exports.ids = ["vendor-chunks/@tanstack+virtual-core@3.11.3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+virtual-core@3.11.3/node_modules/@tanstack/virtual-core/dist/esm/index.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+virtual-core@3.11.3/node_modules/@tanstack/virtual-core/dist/esm/index.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Virtualizer: () => (/* binding */ Virtualizer),\n/* harmony export */   approxEqual: () => (/* reexport safe */ _utils_js__WEBPACK_IMPORTED_MODULE_0__.approxEqual),\n/* harmony export */   debounce: () => (/* reexport safe */ _utils_js__WEBPACK_IMPORTED_MODULE_0__.debounce),\n/* harmony export */   defaultKeyExtractor: () => (/* binding */ defaultKeyExtractor),\n/* harmony export */   defaultRangeExtractor: () => (/* binding */ defaultRangeExtractor),\n/* harmony export */   elementScroll: () => (/* binding */ elementScroll),\n/* harmony export */   measureElement: () => (/* binding */ measureElement),\n/* harmony export */   memo: () => (/* reexport safe */ _utils_js__WEBPACK_IMPORTED_MODULE_0__.memo),\n/* harmony export */   notUndefined: () => (/* reexport safe */ _utils_js__WEBPACK_IMPORTED_MODULE_0__.notUndefined),\n/* harmony export */   observeElementOffset: () => (/* binding */ observeElementOffset),\n/* harmony export */   observeElementRect: () => (/* binding */ observeElementRect),\n/* harmony export */   observeWindowOffset: () => (/* binding */ observeWindowOffset),\n/* harmony export */   observeWindowRect: () => (/* binding */ observeWindowRect),\n/* harmony export */   windowScroll: () => (/* binding */ windowScroll)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+virtual-core@3.11.3/node_modules/@tanstack/virtual-core/dist/esm/utils.js\");\n\nconst defaultKeyExtractor = (index) => index;\nconst defaultRangeExtractor = (range) => {\n  const start = Math.max(range.startIndex - range.overscan, 0);\n  const end = Math.min(range.endIndex + range.overscan, range.count - 1);\n  const arr = [];\n  for (let i = start; i <= end; i++) {\n    arr.push(i);\n  }\n  return arr;\n};\nconst observeElementRect = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const targetWindow = instance.targetWindow;\n  if (!targetWindow) {\n    return;\n  }\n  const handler = (rect) => {\n    const { width, height } = rect;\n    cb({ width: Math.round(width), height: Math.round(height) });\n  };\n  handler(element.getBoundingClientRect());\n  if (!targetWindow.ResizeObserver) {\n    return () => {\n    };\n  }\n  const observer = new targetWindow.ResizeObserver((entries) => {\n    const entry = entries[0];\n    if (entry == null ? void 0 : entry.borderBoxSize) {\n      const box = entry.borderBoxSize[0];\n      if (box) {\n        handler({ width: box.inlineSize, height: box.blockSize });\n        return;\n      }\n    }\n    handler(element.getBoundingClientRect());\n  });\n  observer.observe(element, { box: \"border-box\" });\n  return () => {\n    observer.unobserve(element);\n  };\n};\nconst addEventListenerOptions = {\n  passive: true\n};\nconst observeWindowRect = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const handler = () => {\n    cb({ width: element.innerWidth, height: element.innerHeight });\n  };\n  handler();\n  element.addEventListener(\"resize\", handler, addEventListenerOptions);\n  return () => {\n    element.removeEventListener(\"resize\", handler);\n  };\n};\nconst supportsScrollend = typeof window == \"undefined\" ? true : \"onscrollend\" in window;\nconst observeElementOffset = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const targetWindow = instance.targetWindow;\n  if (!targetWindow) {\n    return;\n  }\n  let offset = 0;\n  const fallback = instance.options.useScrollendEvent && supportsScrollend ? () => void 0 : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.debounce)(\n    targetWindow,\n    () => {\n      cb(offset, false);\n    },\n    instance.options.isScrollingResetDelay\n  );\n  const createHandler = (isScrolling) => () => {\n    const { horizontal, isRtl } = instance.options;\n    offset = horizontal ? element[\"scrollLeft\"] * (isRtl && -1 || 1) : element[\"scrollTop\"];\n    fallback();\n    cb(offset, isScrolling);\n  };\n  const handler = createHandler(true);\n  const endHandler = createHandler(false);\n  endHandler();\n  element.addEventListener(\"scroll\", handler, addEventListenerOptions);\n  element.addEventListener(\"scrollend\", endHandler, addEventListenerOptions);\n  return () => {\n    element.removeEventListener(\"scroll\", handler);\n    element.removeEventListener(\"scrollend\", endHandler);\n  };\n};\nconst observeWindowOffset = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const targetWindow = instance.targetWindow;\n  if (!targetWindow) {\n    return;\n  }\n  let offset = 0;\n  const fallback = instance.options.useScrollendEvent && supportsScrollend ? () => void 0 : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.debounce)(\n    targetWindow,\n    () => {\n      cb(offset, false);\n    },\n    instance.options.isScrollingResetDelay\n  );\n  const createHandler = (isScrolling) => () => {\n    offset = element[instance.options.horizontal ? \"scrollX\" : \"scrollY\"];\n    fallback();\n    cb(offset, isScrolling);\n  };\n  const handler = createHandler(true);\n  const endHandler = createHandler(false);\n  endHandler();\n  element.addEventListener(\"scroll\", handler, addEventListenerOptions);\n  element.addEventListener(\"scrollend\", endHandler, addEventListenerOptions);\n  return () => {\n    element.removeEventListener(\"scroll\", handler);\n    element.removeEventListener(\"scrollend\", endHandler);\n  };\n};\nconst measureElement = (element, entry, instance) => {\n  if (entry == null ? void 0 : entry.borderBoxSize) {\n    const box = entry.borderBoxSize[0];\n    if (box) {\n      const size = Math.round(\n        box[instance.options.horizontal ? \"inlineSize\" : \"blockSize\"]\n      );\n      return size;\n    }\n  }\n  return Math.round(\n    element.getBoundingClientRect()[instance.options.horizontal ? \"width\" : \"height\"]\n  );\n};\nconst windowScroll = (offset, {\n  adjustments = 0,\n  behavior\n}, instance) => {\n  var _a, _b;\n  const toOffset = offset + adjustments;\n  (_b = (_a = instance.scrollElement) == null ? void 0 : _a.scrollTo) == null ? void 0 : _b.call(_a, {\n    [instance.options.horizontal ? \"left\" : \"top\"]: toOffset,\n    behavior\n  });\n};\nconst elementScroll = (offset, {\n  adjustments = 0,\n  behavior\n}, instance) => {\n  var _a, _b;\n  const toOffset = offset + adjustments;\n  (_b = (_a = instance.scrollElement) == null ? void 0 : _a.scrollTo) == null ? void 0 : _b.call(_a, {\n    [instance.options.horizontal ? \"left\" : \"top\"]: toOffset,\n    behavior\n  });\n};\nclass Virtualizer {\n  constructor(opts) {\n    this.unsubs = [];\n    this.scrollElement = null;\n    this.targetWindow = null;\n    this.isScrolling = false;\n    this.scrollToIndexTimeoutId = null;\n    this.measurementsCache = [];\n    this.itemSizeCache = /* @__PURE__ */ new Map();\n    this.pendingMeasuredCacheIndexes = [];\n    this.scrollRect = null;\n    this.scrollOffset = null;\n    this.scrollDirection = null;\n    this.scrollAdjustments = 0;\n    this.elementsCache = /* @__PURE__ */ new Map();\n    this.observer = /* @__PURE__ */ (() => {\n      let _ro = null;\n      const get = () => {\n        if (_ro) {\n          return _ro;\n        }\n        if (!this.targetWindow || !this.targetWindow.ResizeObserver) {\n          return null;\n        }\n        return _ro = new this.targetWindow.ResizeObserver((entries) => {\n          entries.forEach((entry) => {\n            this._measureElement(entry.target, entry);\n          });\n        });\n      };\n      return {\n        disconnect: () => {\n          var _a;\n          (_a = get()) == null ? void 0 : _a.disconnect();\n          _ro = null;\n        },\n        observe: (target) => {\n          var _a;\n          return (_a = get()) == null ? void 0 : _a.observe(target, { box: \"border-box\" });\n        },\n        unobserve: (target) => {\n          var _a;\n          return (_a = get()) == null ? void 0 : _a.unobserve(target);\n        }\n      };\n    })();\n    this.range = null;\n    this.setOptions = (opts2) => {\n      Object.entries(opts2).forEach(([key, value]) => {\n        if (typeof value === \"undefined\") delete opts2[key];\n      });\n      this.options = {\n        debug: false,\n        initialOffset: 0,\n        overscan: 1,\n        paddingStart: 0,\n        paddingEnd: 0,\n        scrollPaddingStart: 0,\n        scrollPaddingEnd: 0,\n        horizontal: false,\n        getItemKey: defaultKeyExtractor,\n        rangeExtractor: defaultRangeExtractor,\n        onChange: () => {\n        },\n        measureElement,\n        initialRect: { width: 0, height: 0 },\n        scrollMargin: 0,\n        gap: 0,\n        indexAttribute: \"data-index\",\n        initialMeasurementsCache: [],\n        lanes: 1,\n        isScrollingResetDelay: 150,\n        enabled: true,\n        isRtl: false,\n        useScrollendEvent: true,\n        ...opts2\n      };\n    };\n    this.notify = (sync) => {\n      var _a, _b;\n      (_b = (_a = this.options).onChange) == null ? void 0 : _b.call(_a, this, sync);\n    };\n    this.maybeNotify = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memo)(\n      () => {\n        this.calculateRange();\n        return [\n          this.isScrolling,\n          this.range ? this.range.startIndex : null,\n          this.range ? this.range.endIndex : null\n        ];\n      },\n      (isScrolling) => {\n        this.notify(isScrolling);\n      },\n      {\n        key:  true && \"maybeNotify\",\n        debug: () => this.options.debug,\n        initialDeps: [\n          this.isScrolling,\n          this.range ? this.range.startIndex : null,\n          this.range ? this.range.endIndex : null\n        ]\n      }\n    );\n    this.cleanup = () => {\n      this.unsubs.filter(Boolean).forEach((d) => d());\n      this.unsubs = [];\n      this.observer.disconnect();\n      this.scrollElement = null;\n      this.targetWindow = null;\n    };\n    this._didMount = () => {\n      return () => {\n        this.cleanup();\n      };\n    };\n    this._willUpdate = () => {\n      var _a;\n      const scrollElement = this.options.enabled ? this.options.getScrollElement() : null;\n      if (this.scrollElement !== scrollElement) {\n        this.cleanup();\n        if (!scrollElement) {\n          this.maybeNotify();\n          return;\n        }\n        this.scrollElement = scrollElement;\n        if (this.scrollElement && \"ownerDocument\" in this.scrollElement) {\n          this.targetWindow = this.scrollElement.ownerDocument.defaultView;\n        } else {\n          this.targetWindow = ((_a = this.scrollElement) == null ? void 0 : _a.window) ?? null;\n        }\n        this.elementsCache.forEach((cached) => {\n          this.observer.observe(cached);\n        });\n        this._scrollToOffset(this.getScrollOffset(), {\n          adjustments: void 0,\n          behavior: void 0\n        });\n        this.unsubs.push(\n          this.options.observeElementRect(this, (rect) => {\n            this.scrollRect = rect;\n            this.maybeNotify();\n          })\n        );\n        this.unsubs.push(\n          this.options.observeElementOffset(this, (offset, isScrolling) => {\n            this.scrollAdjustments = 0;\n            this.scrollDirection = isScrolling ? this.getScrollOffset() < offset ? \"forward\" : \"backward\" : null;\n            this.scrollOffset = offset;\n            this.isScrolling = isScrolling;\n            this.maybeNotify();\n          })\n        );\n      }\n    };\n    this.getSize = () => {\n      if (!this.options.enabled) {\n        this.scrollRect = null;\n        return 0;\n      }\n      this.scrollRect = this.scrollRect ?? this.options.initialRect;\n      return this.scrollRect[this.options.horizontal ? \"width\" : \"height\"];\n    };\n    this.getScrollOffset = () => {\n      if (!this.options.enabled) {\n        this.scrollOffset = null;\n        return 0;\n      }\n      this.scrollOffset = this.scrollOffset ?? (typeof this.options.initialOffset === \"function\" ? this.options.initialOffset() : this.options.initialOffset);\n      return this.scrollOffset;\n    };\n    this.getFurthestMeasurement = (measurements, index) => {\n      const furthestMeasurementsFound = /* @__PURE__ */ new Map();\n      const furthestMeasurements = /* @__PURE__ */ new Map();\n      for (let m = index - 1; m >= 0; m--) {\n        const measurement = measurements[m];\n        if (furthestMeasurementsFound.has(measurement.lane)) {\n          continue;\n        }\n        const previousFurthestMeasurement = furthestMeasurements.get(\n          measurement.lane\n        );\n        if (previousFurthestMeasurement == null || measurement.end > previousFurthestMeasurement.end) {\n          furthestMeasurements.set(measurement.lane, measurement);\n        } else if (measurement.end < previousFurthestMeasurement.end) {\n          furthestMeasurementsFound.set(measurement.lane, true);\n        }\n        if (furthestMeasurementsFound.size === this.options.lanes) {\n          break;\n        }\n      }\n      return furthestMeasurements.size === this.options.lanes ? Array.from(furthestMeasurements.values()).sort((a, b) => {\n        if (a.end === b.end) {\n          return a.index - b.index;\n        }\n        return a.end - b.end;\n      })[0] : void 0;\n    };\n    this.getMeasurementOptions = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memo)(\n      () => [\n        this.options.count,\n        this.options.paddingStart,\n        this.options.scrollMargin,\n        this.options.getItemKey,\n        this.options.enabled\n      ],\n      (count, paddingStart, scrollMargin, getItemKey, enabled) => {\n        this.pendingMeasuredCacheIndexes = [];\n        return {\n          count,\n          paddingStart,\n          scrollMargin,\n          getItemKey,\n          enabled\n        };\n      },\n      {\n        key: false\n      }\n    );\n    this.getMeasurements = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memo)(\n      () => [this.getMeasurementOptions(), this.itemSizeCache],\n      ({ count, paddingStart, scrollMargin, getItemKey, enabled }, itemSizeCache) => {\n        if (!enabled) {\n          this.measurementsCache = [];\n          this.itemSizeCache.clear();\n          return [];\n        }\n        if (this.measurementsCache.length === 0) {\n          this.measurementsCache = this.options.initialMeasurementsCache;\n          this.measurementsCache.forEach((item) => {\n            this.itemSizeCache.set(item.key, item.size);\n          });\n        }\n        const min = this.pendingMeasuredCacheIndexes.length > 0 ? Math.min(...this.pendingMeasuredCacheIndexes) : 0;\n        this.pendingMeasuredCacheIndexes = [];\n        const measurements = this.measurementsCache.slice(0, min);\n        for (let i = min; i < count; i++) {\n          const key = getItemKey(i);\n          const furthestMeasurement = this.options.lanes === 1 ? measurements[i - 1] : this.getFurthestMeasurement(measurements, i);\n          const start = furthestMeasurement ? furthestMeasurement.end + this.options.gap : paddingStart + scrollMargin;\n          const measuredSize = itemSizeCache.get(key);\n          const size = typeof measuredSize === \"number\" ? measuredSize : this.options.estimateSize(i);\n          const end = start + size;\n          const lane = furthestMeasurement ? furthestMeasurement.lane : i % this.options.lanes;\n          measurements[i] = {\n            index: i,\n            start,\n            size,\n            end,\n            key,\n            lane\n          };\n        }\n        this.measurementsCache = measurements;\n        return measurements;\n      },\n      {\n        key:  true && \"getMeasurements\",\n        debug: () => this.options.debug\n      }\n    );\n    this.calculateRange = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memo)(\n      () => [this.getMeasurements(), this.getSize(), this.getScrollOffset()],\n      (measurements, outerSize, scrollOffset) => {\n        return this.range = measurements.length > 0 && outerSize > 0 ? calculateRange({\n          measurements,\n          outerSize,\n          scrollOffset\n        }) : null;\n      },\n      {\n        key:  true && \"calculateRange\",\n        debug: () => this.options.debug\n      }\n    );\n    this.getIndexes = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memo)(\n      () => {\n        let startIndex = null;\n        let endIndex = null;\n        const range = this.calculateRange();\n        if (range) {\n          startIndex = range.startIndex;\n          endIndex = range.endIndex;\n        }\n        return [\n          this.options.rangeExtractor,\n          this.options.overscan,\n          this.options.count,\n          startIndex,\n          endIndex\n        ];\n      },\n      (rangeExtractor, overscan, count, startIndex, endIndex) => {\n        return startIndex === null || endIndex === null ? [] : rangeExtractor({\n          startIndex,\n          endIndex,\n          overscan,\n          count\n        });\n      },\n      {\n        key:  true && \"getIndexes\",\n        debug: () => this.options.debug\n      }\n    );\n    this.indexFromElement = (node) => {\n      const attributeName = this.options.indexAttribute;\n      const indexStr = node.getAttribute(attributeName);\n      if (!indexStr) {\n        console.warn(\n          `Missing attribute name '${attributeName}={index}' on measured element.`\n        );\n        return -1;\n      }\n      return parseInt(indexStr, 10);\n    };\n    this._measureElement = (node, entry) => {\n      const index = this.indexFromElement(node);\n      const item = this.measurementsCache[index];\n      if (!item) {\n        return;\n      }\n      const key = item.key;\n      const prevNode = this.elementsCache.get(key);\n      if (prevNode !== node) {\n        if (prevNode) {\n          this.observer.unobserve(prevNode);\n        }\n        this.observer.observe(node);\n        this.elementsCache.set(key, node);\n      }\n      if (node.isConnected) {\n        this.resizeItem(index, this.options.measureElement(node, entry, this));\n      }\n    };\n    this.resizeItem = (index, size) => {\n      const item = this.measurementsCache[index];\n      if (!item) {\n        return;\n      }\n      const itemSize = this.itemSizeCache.get(item.key) ?? item.size;\n      const delta = size - itemSize;\n      if (delta !== 0) {\n        if (this.shouldAdjustScrollPositionOnItemSizeChange !== void 0 ? this.shouldAdjustScrollPositionOnItemSizeChange(item, delta, this) : item.start < this.getScrollOffset() + this.scrollAdjustments) {\n          if ( true && this.options.debug) {\n            console.info(\"correction\", delta);\n          }\n          this._scrollToOffset(this.getScrollOffset(), {\n            adjustments: this.scrollAdjustments += delta,\n            behavior: void 0\n          });\n        }\n        this.pendingMeasuredCacheIndexes.push(item.index);\n        this.itemSizeCache = new Map(this.itemSizeCache.set(item.key, size));\n        this.notify(false);\n      }\n    };\n    this.measureElement = (node) => {\n      if (!node) {\n        this.elementsCache.forEach((cached, key) => {\n          if (!cached.isConnected) {\n            this.observer.unobserve(cached);\n            this.elementsCache.delete(key);\n          }\n        });\n        return;\n      }\n      this._measureElement(node, void 0);\n    };\n    this.getVirtualItems = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memo)(\n      () => [this.getIndexes(), this.getMeasurements()],\n      (indexes, measurements) => {\n        const virtualItems = [];\n        for (let k = 0, len = indexes.length; k < len; k++) {\n          const i = indexes[k];\n          const measurement = measurements[i];\n          virtualItems.push(measurement);\n        }\n        return virtualItems;\n      },\n      {\n        key:  true && \"getVirtualItems\",\n        debug: () => this.options.debug\n      }\n    );\n    this.getVirtualItemForOffset = (offset) => {\n      const measurements = this.getMeasurements();\n      if (measurements.length === 0) {\n        return void 0;\n      }\n      return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.notUndefined)(\n        measurements[findNearestBinarySearch(\n          0,\n          measurements.length - 1,\n          (index) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.notUndefined)(measurements[index]).start,\n          offset\n        )]\n      );\n    };\n    this.getOffsetForAlignment = (toOffset, align) => {\n      const size = this.getSize();\n      const scrollOffset = this.getScrollOffset();\n      if (align === \"auto\") {\n        if (toOffset >= scrollOffset + size) {\n          align = \"end\";\n        }\n      }\n      if (align === \"end\") {\n        toOffset -= size;\n      }\n      const scrollSizeProp = this.options.horizontal ? \"scrollWidth\" : \"scrollHeight\";\n      const scrollSize = this.scrollElement ? \"document\" in this.scrollElement ? this.scrollElement.document.documentElement[scrollSizeProp] : this.scrollElement[scrollSizeProp] : 0;\n      const maxOffset = scrollSize - size;\n      return Math.max(Math.min(maxOffset, toOffset), 0);\n    };\n    this.getOffsetForIndex = (index, align = \"auto\") => {\n      index = Math.max(0, Math.min(index, this.options.count - 1));\n      const item = this.measurementsCache[index];\n      if (!item) {\n        return void 0;\n      }\n      const size = this.getSize();\n      const scrollOffset = this.getScrollOffset();\n      if (align === \"auto\") {\n        if (item.end >= scrollOffset + size - this.options.scrollPaddingEnd) {\n          align = \"end\";\n        } else if (item.start <= scrollOffset + this.options.scrollPaddingStart) {\n          align = \"start\";\n        } else {\n          return [scrollOffset, align];\n        }\n      }\n      const centerOffset = item.start - this.options.scrollPaddingStart + (item.size - size) / 2;\n      switch (align) {\n        case \"center\":\n          return [this.getOffsetForAlignment(centerOffset, align), align];\n        case \"end\":\n          return [\n            this.getOffsetForAlignment(\n              item.end + this.options.scrollPaddingEnd,\n              align\n            ),\n            align\n          ];\n        default:\n          return [\n            this.getOffsetForAlignment(\n              item.start - this.options.scrollPaddingStart,\n              align\n            ),\n            align\n          ];\n      }\n    };\n    this.isDynamicMode = () => this.elementsCache.size > 0;\n    this.cancelScrollToIndex = () => {\n      if (this.scrollToIndexTimeoutId !== null && this.targetWindow) {\n        this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId);\n        this.scrollToIndexTimeoutId = null;\n      }\n    };\n    this.scrollToOffset = (toOffset, { align = \"start\", behavior } = {}) => {\n      this.cancelScrollToIndex();\n      if (behavior === \"smooth\" && this.isDynamicMode()) {\n        console.warn(\n          \"The `smooth` scroll behavior is not fully supported with dynamic size.\"\n        );\n      }\n      this._scrollToOffset(this.getOffsetForAlignment(toOffset, align), {\n        adjustments: void 0,\n        behavior\n      });\n    };\n    this.scrollToIndex = (index, { align: initialAlign = \"auto\", behavior } = {}) => {\n      index = Math.max(0, Math.min(index, this.options.count - 1));\n      this.cancelScrollToIndex();\n      if (behavior === \"smooth\" && this.isDynamicMode()) {\n        console.warn(\n          \"The `smooth` scroll behavior is not fully supported with dynamic size.\"\n        );\n      }\n      const offsetAndAlign = this.getOffsetForIndex(index, initialAlign);\n      if (!offsetAndAlign) return;\n      const [offset, align] = offsetAndAlign;\n      this._scrollToOffset(offset, { adjustments: void 0, behavior });\n      if (behavior !== \"smooth\" && this.isDynamicMode() && this.targetWindow) {\n        this.scrollToIndexTimeoutId = this.targetWindow.setTimeout(() => {\n          this.scrollToIndexTimeoutId = null;\n          const elementInDOM = this.elementsCache.has(\n            this.options.getItemKey(index)\n          );\n          if (elementInDOM) {\n            const [latestOffset] = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.notUndefined)(\n              this.getOffsetForIndex(index, align)\n            );\n            if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.approxEqual)(latestOffset, this.getScrollOffset())) {\n              this.scrollToIndex(index, { align, behavior });\n            }\n          } else {\n            this.scrollToIndex(index, { align, behavior });\n          }\n        });\n      }\n    };\n    this.scrollBy = (delta, { behavior } = {}) => {\n      this.cancelScrollToIndex();\n      if (behavior === \"smooth\" && this.isDynamicMode()) {\n        console.warn(\n          \"The `smooth` scroll behavior is not fully supported with dynamic size.\"\n        );\n      }\n      this._scrollToOffset(this.getScrollOffset() + delta, {\n        adjustments: void 0,\n        behavior\n      });\n    };\n    this.getTotalSize = () => {\n      var _a;\n      const measurements = this.getMeasurements();\n      let end;\n      if (measurements.length === 0) {\n        end = this.options.paddingStart;\n      } else {\n        end = this.options.lanes === 1 ? ((_a = measurements[measurements.length - 1]) == null ? void 0 : _a.end) ?? 0 : Math.max(\n          ...measurements.slice(-this.options.lanes).map((m) => m.end)\n        );\n      }\n      return Math.max(\n        end - this.options.scrollMargin + this.options.paddingEnd,\n        0\n      );\n    };\n    this._scrollToOffset = (offset, {\n      adjustments,\n      behavior\n    }) => {\n      this.options.scrollToFn(offset, { behavior, adjustments }, this);\n    };\n    this.measure = () => {\n      this.itemSizeCache = /* @__PURE__ */ new Map();\n      this.notify(false);\n    };\n    this.setOptions(opts);\n  }\n}\nconst findNearestBinarySearch = (low, high, getCurrentValue, value) => {\n  while (low <= high) {\n    const middle = (low + high) / 2 | 0;\n    const currentValue = getCurrentValue(middle);\n    if (currentValue < value) {\n      low = middle + 1;\n    } else if (currentValue > value) {\n      high = middle - 1;\n    } else {\n      return middle;\n    }\n  }\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\nfunction calculateRange({\n  measurements,\n  outerSize,\n  scrollOffset\n}) {\n  const count = measurements.length - 1;\n  const getOffset = (index) => measurements[index].start;\n  const startIndex = findNearestBinarySearch(0, count, getOffset, scrollOffset);\n  let endIndex = startIndex;\n  while (endIndex < count && measurements[endIndex].end < scrollOffset + outerSize) {\n    endIndex++;\n  }\n  return { startIndex, endIndex };\n}\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+virtual-core@3.11.3/node_modules/@tanstack/virtual-core/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+virtual-core@3.11.3/node_modules/@tanstack/virtual-core/dist/esm/utils.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+virtual-core@3.11.3/node_modules/@tanstack/virtual-core/dist/esm/utils.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   approxEqual: () => (/* binding */ approxEqual),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   memo: () => (/* binding */ memo),\n/* harmony export */   notUndefined: () => (/* binding */ notUndefined)\n/* harmony export */ });\nfunction memo(getDeps, fn, opts) {\n  let deps = opts.initialDeps ?? [];\n  let result;\n  return () => {\n    var _a, _b, _c, _d;\n    let depTime;\n    if (opts.key && ((_a = opts.debug) == null ? void 0 : _a.call(opts))) depTime = Date.now();\n    const newDeps = getDeps();\n    const depsChanged = newDeps.length !== deps.length || newDeps.some((dep, index) => deps[index] !== dep);\n    if (!depsChanged) {\n      return result;\n    }\n    deps = newDeps;\n    let resultTime;\n    if (opts.key && ((_b = opts.debug) == null ? void 0 : _b.call(opts))) resultTime = Date.now();\n    result = fn(...newDeps);\n    if (opts.key && ((_c = opts.debug) == null ? void 0 : _c.call(opts))) {\n      const depEndTime = Math.round((Date.now() - depTime) * 100) / 100;\n      const resultEndTime = Math.round((Date.now() - resultTime) * 100) / 100;\n      const resultFpsPercentage = resultEndTime / 16;\n      const pad = (str, num) => {\n        str = String(str);\n        while (str.length < num) {\n          str = \" \" + str;\n        }\n        return str;\n      };\n      console.info(\n        `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n        `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n          0,\n          Math.min(120 - 120 * resultFpsPercentage, 120)\n        )}deg 100% 31%);`,\n        opts == null ? void 0 : opts.key\n      );\n    }\n    (_d = opts == null ? void 0 : opts.onChange) == null ? void 0 : _d.call(opts, result);\n    return result;\n  };\n}\nfunction notUndefined(value, msg) {\n  if (value === void 0) {\n    throw new Error(`Unexpected undefined${msg ? `: ${msg}` : \"\"}`);\n  } else {\n    return value;\n  }\n}\nconst approxEqual = (a, b) => Math.abs(a - b) < 1;\nconst debounce = (targetWindow, fn, ms) => {\n  let timeoutId;\n  return function(...args) {\n    targetWindow.clearTimeout(timeoutId);\n    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms);\n  };\n};\n\n//# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+virtual-core@3.11.3/node_modules/@tanstack/virtual-core/dist/esm/utils.js\n");

/***/ })

};
;