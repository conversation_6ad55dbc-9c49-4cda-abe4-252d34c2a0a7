"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-stately+menu@3.9.2_react@19.0.0-rc-69d4b800-20241021";
exports.ids = ["vendor-chunks/@react-stately+menu@3.9.2_react@19.0.0-rc-69d4b800-20241021"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+menu@3.9.2_react@19.0.0-rc-69d4b800-20241021/node_modules/@react-stately/menu/dist/useMenuTriggerState.mjs":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+menu@3.9.2_react@19.0.0-rc-69d4b800-20241021/node_modules/@react-stately/menu/dist/useMenuTriggerState.mjs ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMenuTriggerState: () => (/* binding */ $a28c903ee9ad8dc5$export$79fefeb1c2091ac3)\n/* harmony export */ });\n/* harmony import */ var _react_stately_overlays__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-stately/overlays */ \"(ssr)/./node_modules/.pnpm/@react-stately+overlays@3.6_8ca4e08d61b6a94891b76d2efe1c2a09/node_modules/@react-stately/overlays/dist/useOverlayTriggerState.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\nfunction $a28c903ee9ad8dc5$export$79fefeb1c2091ac3(props) {\n    let overlayTriggerState = (0, _react_stately_overlays__WEBPACK_IMPORTED_MODULE_1__.useOverlayTriggerState)(props);\n    let [focusStrategy, setFocusStrategy] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    let [expandedKeysStack, setExpandedKeysStack] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    let closeAll = ()=>{\n        setExpandedKeysStack([]);\n        overlayTriggerState.close();\n    };\n    let openSubmenu = (triggerKey, level)=>{\n        setExpandedKeysStack((oldStack)=>{\n            if (level > oldStack.length) return oldStack;\n            return [\n                ...oldStack.slice(0, level),\n                triggerKey\n            ];\n        });\n    };\n    let closeSubmenu = (triggerKey, level)=>{\n        setExpandedKeysStack((oldStack)=>{\n            let key = oldStack[level];\n            if (key === triggerKey) return oldStack.slice(0, level);\n            else return oldStack;\n        });\n    };\n    return {\n        focusStrategy: focusStrategy,\n        ...overlayTriggerState,\n        open (focusStrategy = null) {\n            setFocusStrategy(focusStrategy);\n            overlayTriggerState.open();\n        },\n        toggle (focusStrategy = null) {\n            setFocusStrategy(focusStrategy);\n            overlayTriggerState.toggle();\n        },\n        close () {\n            closeAll();\n        },\n        expandedKeysStack: expandedKeysStack,\n        openSubmenu: openSubmenu,\n        closeSubmenu: closeSubmenu\n    };\n}\n\n\n\n//# sourceMappingURL=useMenuTriggerState.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+menu@3.9.2_react@19.0.0-rc-69d4b800-20241021/node_modules/@react-stately/menu/dist/useMenuTriggerState.mjs\n");

/***/ })

};
;