"use strict";exports.id=188,exports.ids=[188],exports.modules={13188:(e,t,n)=>{let r,i,o,a;n.r(t),n.d(t,{AcceleratedAnimation:()=>t7.m,AnimatePresence:()=>v.N,AnimateSharedLayout:()=>nb,DeprecatedLayoutGroupContext:()=>nh.Q,DragControls:()=>tz,FlatTree:()=>nm.Y,LayoutGroup:()=>x.o,LayoutGroupContext:()=>nf.L,LazyMotion:()=>M.F,MotionConfig:()=>w.x,MotionConfigContext:()=>V.Q,MotionContext:()=>nu.A,MotionGlobalConfig:()=>t5.W,MotionValue:()=>C.KG,PresenceContext:()=>nc.t,Reorder:()=>s,SwitchLayoutGroupContext:()=>nd.N,VisualElement:()=>to.B,addPointerEvent:()=>n1.h,addPointerInfo:()=>n9.F,addScaleCorrector:()=>tY.$,animate:()=>td,animateMini:()=>tO,animateValue:()=>N.L,animateVisualElement:()=>eD._,animationControls:()=>eQ,animations:()=>u.W,anticipate:()=>nQ.b,backIn:()=>n$.dg,backInOut:()=>n$.ZZ,backOut:()=>n$.Sz,buildTransform:()=>t2.d,calcLength:()=>nK.CQ,cancelFrame:()=>P.WG,cancelSync:()=>nN,circIn:()=>nj.po,circInOut:()=>nj.tn,circOut:()=>nj.yT,clamp:()=>nF.q,color:()=>t8.y,complex:()=>t3.f,createBox:()=>ti.ge,createRendererMotionComponent:()=>t$.Z,createScopedAnimate:()=>tf,cubicBezier:()=>nU.A,delay:()=>nC.c,disableInstantTransitions:()=>t1,distance:()=>nZ.I,distance2D:()=>nZ.w,domAnimation:()=>S.l,domMax:()=>A.z,domMin:()=>E,easeIn:()=>nX.a6,easeInOut:()=>nX.am,easeOut:()=>nX.vT,filterProps:()=>n_.J,findSpring:()=>nl.pX,frame:()=>P.Gt,frameData:()=>P.uv,frameSteps:()=>P.PP,inView:()=>tF,inertia:()=>nW.B,interpolate:()=>k.G,invariant:()=>U.V,isBrowser:()=>n2.B,isDragActive:()=>n0.D3,isMotionComponent:()=>tX,isMotionValue:()=>W.S,isValidMotionProp:()=>tL.S,keyframes:()=>nL.i,m:()=>y.m,makeUseVisualState:()=>np.T,mirrorEasing:()=>nq.V,mix:()=>nB.j,motion:()=>g,motionValue:()=>C.OQ,optimizedAppearDataAttribute:()=>ne.n,pipe:()=>nz.F,progress:()=>_.q,px:()=>t4.px,resolveMotionValue:()=>R.u,reverseEasing:()=>nJ.G,scroll:()=>eE,scrollInfo:()=>ep,spring:()=>eX.o,stagger:()=>nk,startOptimizedAppearAnimation:()=>ns,steps:()=>nY,sync:()=>nD,transform:()=>z,unwrapMotionComponent:()=>tU,useAnimate:()=>tm,useAnimateMini:()=>tT,useAnimation:()=>tG,useAnimationControls:()=>tP,useAnimationFrame:()=>eI,useCycle:()=>tW,useDeprecatedAnimatedState:()=>nw,useDeprecatedInvertedScale:()=>nE,useDomEvent:()=>tQ,useDragControls:()=>tD,useElementScroll:()=>eC,useForceUpdate:()=>tK.C,useInView:()=>tB,useInstantLayoutTransition:()=>tJ,useInstantTransition:()=>t0,useIsPresent:()=>tR.tF,useIsomorphicLayoutEffect:()=>T.E,useMotionTemplate:()=>L,useMotionValue:()=>O,useMotionValueEvent:()=>j,usePresence:()=>tR.xQ,useReducedMotion:()=>eB,useReducedMotionConfig:()=>ez,useResetProjection:()=>t9,useScroll:()=>eb,useSpring:()=>$,useTime:()=>eO,useTransform:()=>H,useUnmountEffect:()=>e$,useVelocity:()=>X,useViewportScroll:()=>eV,useWillChange:()=>eR,visualElementStore:()=>e6.C,warning:()=>U.$,wrap:()=>e1});var s={};n.r(s),n.d(s,{Group:()=>nI,Item:()=>nG});var l=n(92890),u=n(38070),c=n(37016),f=n(29086),d=n(34414),m=n(92628),h=n(51569);let p=(0,m.C)({...u.W,...f.n,...c.$,...d.Z},h.J),g=(0,l.I)(p);var y=n(22451),v=n(40280),w=n(71587),M=n(79575),x=n(64538);let E={renderer:h.J,...u.W};var S=n(76393),A=n(76021),b=n(423),C=n(91607),V=n(5520),I=n(1359);function O(e){let t=(0,I.M)(()=>(0,C.OQ)(e)),{isStatic:n}=(0,b.useContext)(V.Q);if(n){let[,n]=(0,b.useState)(e);(0,b.useEffect)(()=>t.on("change",n),[])}return t}var T=n(43430),P=n(39846);function G(e,t){let n=O(t()),r=()=>n.set(t());return r(),(0,T.E)(()=>{let t=()=>P.Gt.preRender(r,!1,!0),n=e.map(e=>e.on("change",t));return()=>{n.forEach(e=>e()),(0,P.WG)(r)}}),n}var W=n(94222);function L(e,...t){let n=e.length;return G(t.filter(W.S),function(){let r="";for(let i=0;i<n;i++){r+=e[i];let n=t[i];n&&(r+=(0,W.S)(n)?n.get():n)}return r})}var R=n(41596),k=n(81650);let F=e=>e&&"object"==typeof e&&e.mix,B=e=>F(e)?e.mix:void 0;function z(...e){let t=!Array.isArray(e[0]),n=t?0:-1,r=e[0+n],i=e[1+n],o=e[2+n],a=e[3+n],s=(0,k.G)(i,o,{mixer:B(o[0]),...a});return t?s(r):s}function H(e,t,n,r){if("function"==typeof e)return function(e){C.bt.current=[],e();let t=G(C.bt.current,e);return C.bt.current=void 0,t}(e);let i="function"==typeof t?t:z(t,n,r);return Array.isArray(e)?D(e,i):D([e],([e])=>i(e))}function D(e,t){let n=(0,I.M)(()=>[]);return G(e,()=>{n.length=0;let r=e.length;for(let t=0;t<r;t++)n[t]=e[t].get();return t(n)})}var N=n(97432);function Q(e){return"number"==typeof e?e:parseFloat(e)}function $(e,t={}){let{isStatic:n}=(0,b.useContext)(V.Q),r=(0,b.useRef)(null),i=O((0,W.S)(e)?Q(e.get()):e),o=(0,b.useRef)(i.get()),a=(0,b.useRef)(()=>{}),s=()=>{let e=r.current;e&&0===e.time&&e.sample(P.uv.delta),l(),r.current=(0,N.L)({keyframes:[i.get(),o.current],velocity:i.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...t,onUpdate:a.current})},l=()=>{r.current&&r.current.stop()};return(0,b.useInsertionEffect)(()=>i.attach((e,t)=>n?t(e):(o.current=e,a.current=t,P.Gt.update(s),i.get()),l),[JSON.stringify(t)]),(0,T.E)(()=>{if((0,W.S)(e))return e.on("change",e=>i.set(Q(e)))},[i]),i}function j(e,t,n){(0,b.useInsertionEffect)(()=>e.on(t,n),[e,t,n])}function X(e){let t=O(e.getVelocity()),n=()=>{let r=e.getVelocity();t.set(r),r&&P.Gt.update(n)};return j(e,"change",()=>{P.Gt.update(n,!1,!0)}),t}var U=n(48148);function Y(e,t,n){var r;if("string"==typeof e){let i=document;t&&((0,U.V)(!!t.current,"Scope provided, but no element detected."),i=t.current),n?(null!==(r=n[e])&&void 0!==r||(n[e]=i.querySelectorAll(e)),e=n[e]):e=i.querySelectorAll(e)}else e instanceof Element&&(e=[e]);return Array.from(e||[])}let q=new WeakMap;function J({target:e,contentRect:t,borderBoxSize:n}){var r;null===(r=q.get(e))||void 0===r||r.forEach(r=>{r({target:e,contentSize:t,get size(){return function(e,t){if(t){let{inlineSize:e,blockSize:n}=t[0];return{width:e,height:n}}return e instanceof SVGElement&&"getBBox"in e?e.getBBox():{width:e.offsetWidth,height:e.offsetHeight}}(e,n)}})})}function Z(e){e.forEach(J)}let K=new Set;var _=n(92948),ee=n(67531);let et=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),en=()=>({time:0,x:et(),y:et()}),er={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function ei(e,t,n,r){let i=n[t],{length:o,position:a}=er[t],s=i.current,l=n.time;i.current=e[`scroll${a}`],i.scrollLength=e[`scroll${o}`]-e[`client${o}`],i.offset.length=0,i.offset[0]=0,i.offset[1]=i.scrollLength,i.progress=(0,_.q)(0,i.scrollLength,i.current);let u=r-l;i.velocity=u>50?0:(0,ee.f)(i.current-s,u)}let eo={All:[[0,0],[1,1]]},ea={start:0,center:.5,end:1};function es(e,t,n=0){let r=0;if(e in ea&&(e=ea[e]),"string"==typeof e){let t=parseFloat(e);e.endsWith("px")?r=t:e.endsWith("%")?e=t/100:e.endsWith("vw")?r=t/100*document.documentElement.clientWidth:e.endsWith("vh")?r=t/100*document.documentElement.clientHeight:e=t}return"number"==typeof e&&(r=t*e),n+r}let el=[0,0];var eu=n(71605);let ec={x:0,y:0},ef=new WeakMap,ed=new WeakMap,em=new WeakMap,eh=e=>e===document.documentElement?window:e;function ep(e,{container:t=document.documentElement,...n}={}){let o=em.get(t);o||(o=new Set,em.set(t,o));let a=function(e,t,n,r={}){return{measure:()=>(function(e,t=e,n){if(n.x.targetOffset=0,n.y.targetOffset=0,t!==e){let r=t;for(;r&&r!==e;)n.x.targetOffset+=r.offsetLeft,n.y.targetOffset+=r.offsetTop,r=r.offsetParent}n.x.targetLength=t===e?t.scrollWidth:t.clientWidth,n.y.targetLength=t===e?t.scrollHeight:t.clientHeight,n.x.containerLength=e.clientWidth,n.y.containerLength=e.clientHeight})(e,r.target,n),update:t=>{ei(e,"x",n,t),ei(e,"y",n,t),n.time=t,(r.offset||r.target)&&function(e,t,n){let{offset:r=eo.All}=n,{target:i=e,axis:o="y"}=n,a="y"===o?"height":"width",s=i!==e?function(e,t){let n={x:0,y:0},r=e;for(;r&&r!==t;)if(r instanceof HTMLElement)n.x+=r.offsetLeft,n.y+=r.offsetTop,r=r.offsetParent;else if("svg"===r.tagName){let e=r.getBoundingClientRect(),t=(r=r.parentElement).getBoundingClientRect();n.x+=e.left-t.left,n.y+=e.top-t.top}else if(r instanceof SVGGraphicsElement){let{x:e,y:t}=r.getBBox();n.x+=e,n.y+=t;let i=null,o=r.parentNode;for(;!i;)"svg"===o.tagName&&(i=o),o=r.parentNode;r=i}else break;return n}(i,e):ec,l=i===e?{width:e.scrollWidth,height:e.scrollHeight}:"getBBox"in i&&"svg"!==i.tagName?i.getBBox():{width:i.clientWidth,height:i.clientHeight},u={width:e.clientWidth,height:e.clientHeight};t[o].offset.length=0;let c=!t[o].interpolate,f=r.length;for(let e=0;e<f;e++){let n=function(e,t,n,r){let i=Array.isArray(e)?e:el,o=0;return"number"==typeof e?i=[e,e]:"string"==typeof e&&(i=(e=e.trim()).includes(" ")?e.split(" "):[e,ea[e]?e:"0"]),es(i[0],n,r)-es(i[1],t)}(r[e],u[a],l[a],s[o]);c||n===t[o].interpolatorOffsets[e]||(c=!0),t[o].offset[e]=n}c&&(t[o].interpolate=(0,k.G)(t[o].offset,(0,eu.Z)(r)),t[o].interpolatorOffsets=[...t[o].offset]),t[o].progress=t[o].interpolate(t[o].current)}(e,n,r)},notify:()=>t(n)}}(t,e,en(),n);if(o.add(a),!ef.has(t)){let e=()=>{for(let e of o)e.measure()},n=()=>{for(let e of o)e.update(P.uv.timestamp)},a=()=>{for(let e of o)e.notify()},s=()=>{P.Gt.read(e,!1,!0),P.Gt.read(n,!1,!0),P.Gt.update(a,!1,!0)};ef.set(t,s);let l=eh(t);window.addEventListener("resize",s,{passive:!0}),t!==document.documentElement&&ed.set(t,"function"==typeof t?(K.add(t),i||(i=()=>{let e={width:window.innerWidth,height:window.innerHeight},t={target:window,size:e,contentSize:e};K.forEach(e=>e(t))},window.addEventListener("resize",i)),()=>{K.delete(t),!K.size&&i&&(i=void 0)}):function(e,t){r||"undefined"==typeof ResizeObserver||(r=new ResizeObserver(Z));let n=Y(e);return n.forEach(e=>{let n=q.get(e);n||(n=new Set,q.set(e,n)),n.add(t),null==r||r.observe(e)}),()=>{n.forEach(e=>{let n=q.get(e);null==n||n.delete(t),(null==n?void 0:n.size)||null==r||r.unobserve(e)})}}(t,s)),l.addEventListener("scroll",s,{passive:!0})}let s=ef.get(t);return P.Gt.read(s,!1,!0),()=>{var e;(0,P.WG)(s);let n=em.get(t);if(!n||(n.delete(a),n.size))return;let r=ef.get(t);ef.delete(t),r&&(eh(t).removeEventListener("scroll",r),null===(e=ed.get(t))||void 0===e||e(),window.removeEventListener("resize",r))}}function eg(e,t){let n;let r=()=>{let{currentTime:r}=t,i=(null===r?0:r.value)/100;n!==i&&e(i),n=i};return P.Gt.update(r,!0),()=>(0,P.WG)(r)}var ey=n(8649),ev=n(34929);let ew=new Map;function eM({source:e,container:t=document.documentElement,axis:n="y"}={}){e&&(t=e),ew.has(t)||ew.set(t,{});let r=ew.get(t);return r[n]||(r[n]=(0,ey.J)()?new ScrollTimeline({source:t,axis:n}):function({source:e,container:t,axis:n="y"}){e&&(t=e);let r={value:0},i=ep(e=>{r.value=100*e[n].progress},{container:t,axis:n});return{currentTime:r,cancel:i}}({source:t,axis:n})),r[n]}function ex(e){return e&&(e.target||e.offset)}function eE(e,{axis:t="y",...n}={}){let r={axis:t,...n};return"function"==typeof e?2===e.length||ex(r)?ep(t=>{e(t[r.axis].progress,t)},r):eg(e,eM(r)):function(e,t){if(e.flatten(),ex(t))return e.pause(),ep(n=>{e.time=e.duration*n[t.axis].progress},t);{let n=eM(t);return e.attachTimeline?e.attachTimeline(n,e=>(e.pause(),eg(t=>{e.time=e.duration*t},n))):ev.l}}(e,r)}function eS(e,t){(0,U.$)(!!(!t||t.current),`You have defined a ${e} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \`layoutEffect: false\` option.`)}let eA=()=>({scrollX:(0,C.OQ)(0),scrollY:(0,C.OQ)(0),scrollXProgress:(0,C.OQ)(0),scrollYProgress:(0,C.OQ)(0)});function eb({container:e,target:t,layoutEffect:n=!0,...r}={}){let i=(0,I.M)(eA);return(n?T.E:b.useEffect)(()=>(eS("target",t),eS("container",e),eE((e,{x:t,y:n})=>{i.scrollX.set(t.current),i.scrollXProgress.set(t.progress),i.scrollY.set(n.current),i.scrollYProgress.set(n.progress)},{...r,container:(null==e?void 0:e.current)||void 0,target:(null==t?void 0:t.current)||void 0})),[e,t,JSON.stringify(r.offset)]),i}function eC(e){return eb({container:e})}function eV(){return eb()}function eI(e){let t=(0,b.useRef)(0),{isStatic:n}=(0,b.useContext)(V.Q);(0,b.useEffect)(()=>{if(n)return;let r=({timestamp:n,delta:r})=>{t.current||(t.current=n),e(n-t.current,r)};return P.Gt.update(r,!0),()=>(0,P.WG)(r)},[e])}function eO(){let e=O(0);return eI(t=>e.set(t)),e}var eT=n(27805),eP=n(42260),eG=n(92086),eW=n(80212);class eL extends C.KG{constructor(){super(...arguments),this.values=[]}add(e){let t=eG.f.has(e)?"transform":eT.M.has(e)?(0,eP.I)(e):void 0;t&&((0,eW.Kq)(this.values,t),this.update())}update(){this.set(this.values.length?this.values.join(", "):"auto")}}function eR(){return(0,I.M)(()=>new eL("auto"))}var ek=n(92021),eF=n(98126);function eB(){eF.r.current||(0,ek.U)();let[e]=(0,b.useState)(eF.O.current);return e}function ez(){let e=eB(),{reducedMotion:t}=(0,b.useContext)(V.Q);return"never"!==t&&("always"===t||e)}var eH=n(35400),eD=n(4296);function eN(e,t){[...t].reverse().forEach(n=>{let r=e.getVariant(n);r&&(0,eH.U)(e,r),e.variantChildren&&e.variantChildren.forEach(e=>{eN(e,t)})})}function eQ(){let e=!1,t=new Set,n={subscribe:e=>(t.add(e),()=>void t.delete(e)),start(n,r){(0,U.V)(e,"controls.start() should only be called after a component has mounted. Consider calling within a useEffect hook.");let i=[];return t.forEach(e=>{i.push((0,eD._)(e,n,{transitionOverride:r}))}),Promise.all(i)},set:n=>((0,U.V)(e,"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook."),t.forEach(e=>{Array.isArray(n)?eN(e,n):"string"==typeof n?eN(e,[n]):(0,eH.U)(e,n)})),stop(){t.forEach(e=>{!function(e){e.values.forEach(e=>e.stop())}(e)})},mount:()=>(e=!0,()=>{e=!1,n.stop()})};return n}function e$(e){return(0,b.useEffect)(()=>()=>e(),[])}var ej=n(98690),eX=n(36221),eU=n(13575),eY=n(23851);function eq(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min((0,eU.t)(r),eU.Y);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:(0,eY.X)(i)}}var eJ=n(10585);function eZ(e){return"object"==typeof e&&!Array.isArray(e)}function eK(e,t,n,r){return"string"==typeof e&&eZ(t)?Y(e,n,r):e instanceof NodeList?Array.from(e):Array.isArray(e)?e:[e]}var e_=n(36433);function e0(e,t,n,r){var i;return"number"==typeof t?t:t.startsWith("-")||t.startsWith("+")?Math.max(0,e+parseFloat(t)):"<"===t?n:null!==(i=r.get(t))&&void 0!==i?i:e}let e1=(e,t,n)=>{let r=t-e;return((n-e)%r+r)%r+e};var e9=n(94533),e2=n(77031);function e8(e,t){return e.at!==t.at?e.at-t.at:null===e.value?1:null===t.value?-1:0}function e3(e,t){return t.has(e)||t.set(e,{}),t.get(e)}function e4(e,t){return t[e]||(t[e]=[]),t[e]}let e5=e=>"number"==typeof e,e7=e=>e.every(e5);var e6=n(83273),te=n(81484),tt=n(43857),tn=n(71061),tr=n(69217),ti=n(34848),to=n(19781);class ta extends to.B{constructor(){super(...arguments),this.type="object"}readValueFromInstance(e,t){if(t in e){let n=e[t];if("string"==typeof n||"number"==typeof n)return n}}getBaseTargetFromProps(){}removeValueFromRenderState(e,t){delete t.output[e]}measureInstanceViewportBox(){return(0,ti.ge)()}build(e,t){Object.assign(e.output,t)}renderInstance(e,{output:t}){Object.assign(e,t)}sortInstanceNodePosition(){return 0}}function ts(e){let t={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=(0,tt.x)(e)?new tn.l(t):new tr.M(t);n.mount(e),e6.C.set(e,n)}function tl(e){let t=new ta({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});t.mount(e),e6.C.set(e,t)}var tu=n(38802);function tc(e,t,n,r){let i=[];if((0,W.S)(e)||"number"==typeof e||"string"==typeof e&&!eZ(t))i.push((0,tu.z)(e,eZ(t)&&t.default||t,n&&n.default||n));else{let o=eK(e,t,r),a=o.length;(0,U.V)(!!a,"No valid elements provided.");for(let e=0;e<a;e++){let r=o[e],s=r instanceof Element?ts:tl;e6.C.has(r)||s(r);let l=e6.C.get(r),u={...n};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(e,a)),i.push(...(0,te.$)(l,{...t,transition:u},{}))}}return i}function tf(e){return function(t,n,r){let i=[];i=Array.isArray(t)&&Array.isArray(t[0])?function(e,t,n){let r=[];return(function(e,{defaultTransition:t={},...n}={},r,i){let o=t.duration||.3,a=new Map,s=new Map,l={},u=new Map,c=0,f=0,d=0;for(let n=0;n<e.length;n++){let a=e[n];if("string"==typeof a){u.set(a,f);continue}if(!Array.isArray(a)){u.set(a.name,e0(f,a.at,c,u));continue}let[m,h,p={}]=a;void 0!==p.at&&(f=e0(f,p.at,c,u));let g=0,y=(e,n,r,a=0,s=0)=>{let l=Array.isArray(e)?e:[e],{delay:u=0,times:c=(0,eu.Z)(l),type:m="keyframes",...h}=n,{ease:p=t.ease||"easeOut",duration:y}=n,v="function"==typeof u?u(a,s):u,w=l.length,M=(0,e_.W)(m)?m:null==i?void 0:i[m];if(w<=2&&M){let e=100;2===w&&e7(l)&&(e=Math.abs(l[1]-l[0]));let t={...h};void 0!==y&&(t.duration=(0,eY.f)(y));let n=eq(t,e,M);p=n.ease,y=n.duration}null!=y||(y=o);let x=f+v,E=x+y;1===c.length&&0===c[0]&&(c[1]=1);let S=c.length-l.length;S>0&&(0,eJ.f)(c,S),1===l.length&&l.unshift(null),function(e,t,n,r,i,o){!function(e,t,n){for(let r=0;r<e.length;r++){let i=e[r];i.at>t&&i.at<n&&((0,eW.Ai)(e,i),r--)}}(e,i,o);for(let s=0;s<t.length;s++){var a;e.push({value:t[s],at:(0,e2.k)(i,o,r[s]),easing:(a=s,(0,e9.h)(n)?n[e1(0,n.length,a)]:n)})}}(r,l,p,c,x,E),g=Math.max(v+y,g),d=Math.max(E,d)};if((0,W.S)(m))y(h,p,e4("default",e3(m,s)));else{let e=eK(m,h,r,l),t=e.length;for(let n=0;n<t;n++){let r=e3(e[n],s);for(let e in h)y(h[e],p&&p[e]?{...p,...p[e]}:{...p},e4(e,r),n,t)}}c=f,f+=g}return s.forEach((e,r)=>{for(let i in e){let o=e[i];o.sort(e8);let s=[],l=[],u=[];for(let e=0;e<o.length;e++){let{at:t,value:n,easing:r}=o[e];s.push(n),l.push((0,_.q)(0,d,t)),u.push(r||"easeOut")}0!==l[0]&&(l.unshift(0),s.unshift(s[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),s.push(null)),a.has(r)||a.set(r,{keyframes:{},transition:{}});let c=a.get(r);c.keyframes[i]=s,c.transition[i]={...t,duration:d,ease:u,times:l,...n}}}),a})(e,t,n,{spring:eX.o}).forEach(({keyframes:e,transition:t},n)=>{r.push(...tc(n,e,t))}),r}(t,n,e):tc(t,n,r,e);let o=new ej.P(i);return e&&e.animations.push(o),o}}let td=tf();function tm(){let e=(0,I.M)(()=>({current:null,animations:[]})),t=(0,I.M)(()=>tf(e));return e$(()=>{e.animations.forEach(e=>e.stop())}),[e,t]}var th=n(50368),tp=n(50692),tg=n(99579),ty=n(59291),tv=n(19266);function tw(e,t,n){e.style.setProperty(`--${t}`,n)}function tM(e,t,n){e.style[t]=n}var tx=n(6391);let tE=(0,n(49907).p)(()=>{try{document.createElement("div").animate({opacity:[1]})}catch(e){return!1}return!0});var tS=n(98497);let tA=new WeakMap,tb="easeOut";function tC(e){let t=tA.get(e)||new Map;return tA.set(e,t),tA.get(e)}class tV{constructor(e,t,n,r){let i=t.startsWith("--");this.setValue=i?tw:tM,this.options=r,this.updateFinishedPromise(),(0,U.V)("string"!=typeof r.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "framer-motion"?');let o=tC(e).get(t);if(o&&o.stop(),Array.isArray(n)||(n=[n]),function(e,t,n){for(let r=0;r<t.length;r++)null===t[r]&&(t[r]=0===r?n():t[r-1]),"number"==typeof t[r]&&tg.o[e]&&(t[r]=tg.o[e].transform(t[r]));!tE()&&t.length<2&&t.unshift(n())}(t,n,()=>t.startsWith("--")?e.style.getPropertyValue(t):window.getComputedStyle(e)[t]),(0,e_.W)(r.type)){let e=eq(r,100,r.type);r.ease=(0,tx.n)()?e.ease:tb,r.duration=(0,eY.f)(e.duration),r.type="keyframes"}else r.ease=r.ease||tb;this.removeAnimation=()=>{var n;return null===(n=tA.get(e))||void 0===n?void 0:n.delete(t)};let a=()=>{this.setValue(e,t,(0,tv.X)(n,this.options)),this.cancel(),this.resolveFinishedPromise()};(0,tS.B)()?(this.animation=(0,tp.R)(e,t,n,r),!1===r.autoplay&&this.animation.pause(),this.animation.onfinish=a,this.pendingTimeline&&(0,ty.v)(this.animation,this.pendingTimeline),tC(e).set(t,this)):a()}get duration(){return(0,eY.X)(this.options.duration||300)}get time(){var e;return this.animation?(0,eY.X)((null===(e=this.animation)||void 0===e?void 0:e.currentTime)||0):0}set time(e){this.animation&&(this.animation.currentTime=(0,eY.f)(e))}get speed(){return this.animation?this.animation.playbackRate:1}set speed(e){this.animation&&(this.animation.playbackRate=e)}get state(){return this.animation?this.animation.playState:"finished"}get startTime(){return this.animation?this.animation.startTime:null}flatten(){var e;this.animation&&(null===(e=this.animation.effect)||void 0===e||e.updateTiming({easing:"linear"}))}play(){"finished"===this.state&&this.updateFinishedPromise(),this.animation&&this.animation.play()}pause(){this.animation&&this.animation.pause()}stop(){this.animation&&"idle"!==this.state&&"finished"!==this.state&&(this.animation.commitStyles&&this.animation.commitStyles(),this.cancel())}complete(){this.animation&&this.animation.finish()}cancel(){this.removeAnimation();try{this.animation&&this.animation.cancel()}catch(e){}}then(e,t){return this.currentFinishedPromise.then(e,t)}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}attachTimeline(e){return this.animation?(0,ty.v)(this.animation,e):this.pendingTimeline=e,ev.l}}let tI=e=>function(t,n,r){return new ej.P(function(e,t,n,r){let i=Y(e,r),o=i.length;(0,U.V)(!!o,"No valid element provided.");let a=[];for(let e=0;e<o;e++){let r=i[e],s={...n};for(let n in"function"==typeof s.delay&&(s.delay=s.delay(e,o)),t){let e=t[n],i={...(0,th.r)(s,n)};i.duration=i.duration?(0,eY.f)(i.duration):i.duration,i.delay=(0,eY.f)(i.delay||0),a.push(new tV(r,n,e,i))}}return a}(t,n,r,e))},tO=tI();function tT(){let e=(0,I.M)(()=>({current:null,animations:[]})),t=(0,I.M)(()=>tI(e));return e$(()=>{e.animations.forEach(e=>e.stop())}),[e,t]}function tP(){let e=(0,I.M)(eQ);return(0,T.E)(e.mount,[]),e}let tG=tP;function tW(...e){let t=(0,b.useRef)(0),[n,r]=(0,b.useState)(e[t.current]);return[n,(0,b.useCallback)(n=>{t.current="number"!=typeof n?e1(0,e.length,t.current+1):n,r(e[t.current])},[e.length,...e])]}var tL=n(20230),tR=n(93106);let tk={some:0,all:1};function tF(e,t,{root:n,margin:r,amount:i="some"}={}){let o=Y(e),a=new WeakMap,s=new IntersectionObserver(e=>{e.forEach(e=>{let n=a.get(e.target);if(!!n!==e.isIntersecting){if(e.isIntersecting){let n=t(e);"function"==typeof n?a.set(e.target,n):s.unobserve(e.target)}else n&&(n(e),a.delete(e.target))}})},{root:n,rootMargin:r,threshold:"number"==typeof i?i:tk[i]});return o.forEach(e=>s.observe(e)),()=>s.disconnect()}function tB(e,{root:t,margin:n,amount:r,once:i=!1}={}){let[o,a]=(0,b.useState)(!1);return(0,b.useEffect)(()=>{if(!e.current||i&&o)return;let s={root:t&&t.current||void 0,margin:n,amount:r};return tF(e.current,()=>(a(!0),i?void 0:()=>a(!1)),s)},[t,e,n,i,r]),o}class tz{constructor(){this.componentControls=new Set}subscribe(e){return this.componentControls.add(e),()=>this.componentControls.delete(e)}start(e,t){this.componentControls.forEach(n=>{n.start(e.nativeEvent||e,t)})}}let tH=()=>new tz;function tD(){return(0,I.M)(tH)}var tN=n(68124);function tQ(e,t,n,r){(0,b.useEffect)(()=>{let i=e.current;if(n&&i)return(0,tN.k)(i,t,n,r)},[e,t,n,r])}var t$=n(7594),tj=n(21850);function tX(e){return null!==e&&"object"==typeof e&&tj.o in e}function tU(e){if(tX(e))return e[tj.o]}var tY=n(94575),tq=n(20357);function tJ(){return tZ}function tZ(e){tq.i.current&&(tq.i.current.isUpdating=!1,tq.i.current.blockUpdate(),e&&e())}var tK=n(88554),t_=n(41210);function t0(){let[e,t]=(0,tK.C)(),n=(0,b.useRef)();return(0,b.useEffect)(()=>{P.Gt.postRender(()=>P.Gt.postRender(()=>{t===n.current&&(t_.d.current=!1)}))},[t]),r=>{tZ(()=>{t_.d.current=!0,e(),r(),n.current=t+1})}}function t1(){t_.d.current=!1}function t9(){return(0,b.useCallback)(()=>{let e=tq.i.current;e&&e.resetTree()},[])}var t2=n(61013),t8=n(40737),t3=n(9915),t4=n(82075),t5=n(60682),t7=n(88798);let t6=(e,t)=>{let n=eG.f.has(t)?"transform":t;return`${e}: ${n}`};var ne=n(67494);let nt=new Map,nn=new Map;function nr(e,t,n){var r;let i=t6(e,t),o=nt.get(i);if(!o)return null;let{animation:a,startTime:s}=o;function l(){var r;null===(r=window.MotionCancelOptimisedAnimation)||void 0===r||r.call(window,e,t,n)}return(a.onfinish=l,null===s||(null===(r=window.MotionHandoffIsComplete)||void 0===r?void 0:r.call(window,e)))?(l(),null):s}var ni=n(49388);let no=new Set;function na(){no.forEach(e=>{e.animation.play(),e.animation.startTime=e.startTime}),no.clear()}function ns(e,t,n,r,i){if(window.MotionIsMounted)return;let s=e.dataset[ne.c];if(!s)return;window.MotionHandoffAnimation=nr;let l=t6(s,t);a||(a=(0,tp.R)(e,t,[n[0],n[0]],{duration:1e4,ease:"linear"}),nt.set(l,{animation:a,startTime:null}),window.MotionHandoffAnimation=nr,window.MotionHasOptimisedAnimation=(e,t)=>{if(!e)return!1;if(!t)return nn.has(e);let n=t6(e,t);return!!nt.get(n)},window.MotionHandoffMarkAsComplete=e=>{nn.has(e)&&nn.set(e,!0)},window.MotionHandoffIsComplete=e=>!0===nn.get(e),window.MotionCancelOptimisedAnimation=(e,t,n,r)=>{let i=t6(e,t),o=nt.get(i);o&&(n&&void 0===r?n.postRender(()=>{n.postRender(()=>{o.animation.cancel()})}):o.animation.cancel(),n&&r?(no.add(o),n.render(na)):(nt.delete(i),nt.size||(window.MotionCancelOptimisedAnimation=void 0)))},window.MotionCheckAppearSync=(e,t,n)=>{var r,i;let o=(0,ni.P)(e);if(!o)return;let a=null===(r=window.MotionHasOptimisedAnimation)||void 0===r?void 0:r.call(window,o,t),s=null===(i=e.props.values)||void 0===i?void 0:i[t];if(!a||!s)return;let l=n.on("change",e=>{var n;s.get()!==e&&(null===(n=window.MotionCancelOptimisedAnimation)||void 0===n||n.call(window,o,t),l())});return l});let u=()=>{a.cancel();let s=(0,tp.R)(e,t,n,r);void 0===o&&(o=performance.now()),s.startTime=o,nt.set(l,{animation:s,startTime:o}),i&&i(s)};nn.set(s,!1),a.ready?a.ready.then(u).catch(ev.l):u()}var nl=n(19346),nu=n(57875),nc=n(28905),nf=n(44091),nd=n(41931),nm=n(74955),nh=n(92708),np=n(19196);let ng=()=>({});class ny extends to.B{constructor(){super(...arguments),this.measureInstanceViewportBox=ti.ge}build(){}resetTransform(){}restoreTransform(){}removeValueFromRenderState(){}renderInstance(){}scrapeMotionValuesFromProps(){return ng()}getBaseTargetFromProps(){}readValueFromInstance(e,t,n){return n.initialState[t]||0}sortInstanceNodePosition(){return 0}}let nv=(0,np.T)({scrapeMotionValuesFromProps:ng,createRenderState:ng});function nw(e){let[t,n]=(0,b.useState)(e),r=nv({},!1),i=(0,I.M)(()=>new ny({props:{onUpdate:e=>{n({...e})}},visualState:r,presenceContext:null},{initialState:e}));return(0,b.useLayoutEffect)(()=>(i.mount({}),()=>i.unmount()),[i]),[t,(0,I.M)(()=>e=>(0,eD._)(i,e))]}let nM=e=>e>.001?1/e:1e5,nx=!1;function nE(e){let t=O(1),n=O(1),{visualElement:r}=(0,b.useContext)(nu.A);return(0,U.V)(!!(e||r),"If no scale values are provided, useInvertedScale must be used within a child of another motion component."),(0,U.$)(nx,"useInvertedScale is deprecated and will be removed in 3.0. Use the layout prop instead."),nx=!0,e?(t=e.scaleX||t,n=e.scaleY||n):r&&(t=r.getValue("scaleX",1),n=r.getValue("scaleY",1)),{scaleX:H(t,nM),scaleY:H(n,nM)}}var nS=n(15238);let nA=0,nb=({children:e})=>(b.useEffect(()=>{(0,U.V)(!1,"AnimateSharedLayout is deprecated: https://www.framer.com/docs/guide-upgrade/##shared-layout-animations")},[]),(0,nS.jsx)(x.o,{id:(0,I.M)(()=>`asl-${nA++}`),children:e}));var nC=n(94954);let nV=(0,b.createContext)(null),nI=(0,b.forwardRef)(function({children:e,as:t="ul",axis:n="y",onReorder:r,values:i,...o},a){let s=(0,I.M)(()=>g[t]),l=[],u=(0,b.useRef)(!1);return(0,U.V)(!!i,"Reorder.Group must be provided a values prop"),(0,nS.jsx)(s,{...o,ref:a,ignoreStrict:!0,children:(0,nS.jsx)(nV.Provider,{value:{axis:n,registerItem:(e,t)=>{let r=l.findIndex(t=>e===t.value);-1!==r?l[r].layout=t[n]:l.push({value:e,layout:t[n]}),l.sort(nT)},updateOrder:(e,t,n)=>{if(u.current)return;let o=function(e,t,n,r){if(!r)return e;let i=e.findIndex(e=>e.value===t);if(-1===i)return e;let o=r>0?1:-1,a=e[i+o];if(!a)return e;let s=e[i],l=a.layout,u=(0,e2.k)(l.min,l.max,.5);return 1===o&&s.layout.max+n>u||-1===o&&s.layout.min+n<u?(0,eW.Pe)(e,i,i+o):e}(l,e,t,n);l!==o&&(u.current=!0,r(o.map(nO).filter(e=>-1!==i.indexOf(e))))}},children:e})})});function nO(e){return e.value}function nT(e,t){return e.layout.min-t.layout.min}function nP(e,t=0){return(0,W.S)(e)?e:O(t)}let nG=(0,b.forwardRef)(function({children:e,style:t={},value:n,as:r="li",onDrag:i,layout:o=!0,...a},s){let l=(0,I.M)(()=>g[r]),u=(0,b.useContext)(nV),c={x:nP(t.x),y:nP(t.y)},f=H([c.x,c.y],([e,t])=>e||t?1:"unset");(0,U.V)(!!u,"Reorder.Item must be a child of Reorder.Group");let{axis:d,registerItem:m,updateOrder:h}=u;return(0,nS.jsx)(l,{drag:d,...a,dragSnapToOrigin:!0,style:{...t,x:c.x,y:c.y,zIndex:f},layout:o,onDrag:(e,t)=>{let{velocity:r}=t;r[d]&&h(n,c[d].get(),r[d]),i&&i(e,t)},onLayoutMeasure:e=>m(n,e),ref:s,ignoreStrict:!0,children:e})});var nW=n(8935),nL=n(93790),nR=n(86081);function nk(e=.1,{startDelay:t=0,from:n=0,ease:r}={}){return(i,o)=>{let a=e*Math.abs(("number"==typeof n?n:function(e,t){if("first"===e)return 0;{let n=t-1;return"last"===e?n:n/2}}(n,o))-i);if(r){let t=o*e;a=(0,nR.K)(r)(a/t)*t}return t+a}}var nF=n(80590),nB=n(47349),nz=n(95021),nH=n(70552);let nD=P.Gt,nN=nH.q.reduce((e,t)=>(e[t]=e=>(0,P.WG)(e),e),{});var nQ=n(45399),n$=n(31526),nj=n(18338),nX=n(27789),nU=n(95009);function nY(e,t="end"){return n=>{let r=(n="end"===t?Math.min(n,.999):Math.max(n,.001))*e,i="end"===t?Math.floor(r):Math.ceil(r);return(0,nF.q)(0,1,i/e)}}var nq=n(32447),nJ=n(29224),nZ=n(62112),nK=n(96082),n_=n(23132),n0=n(18064),n1=n(16927),n9=n(28110),n2=n(72274)}};