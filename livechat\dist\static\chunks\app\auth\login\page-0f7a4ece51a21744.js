(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[111,859],{3767:()=>{},2208:()=>{},39583:(e,t,r)=>{Promise.resolve().then(r.bind(r,51483))},51483:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w});var a=r(12389),s=r(41473),l=r(75664);let o=s.forwardRef(function(e,t){let{title:r,titleId:a,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))}),n=s.forwardRef(function(e,t){let{title:r,titleId:a,...l}=e;return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":a},l),r?s.createElement("title",{id:a},r):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))});var i=r(86037),c=r(78763),u=r(74980),d=r(68117),h=r(78790),m=r(74029),p=r(38579),g=(0,h.Rf)((e,t)=>{var r;let{as:s,className:l,children:o,...n}=e,i=(0,m.zD)(t),{slots:c,classNames:u}=(0,d.f)(),h=(0,p.$z)(null==u?void 0:u.header,l);return(0,a.jsx)(s||"div",{ref:i,className:null==(r=c.header)?void 0:r.call(c,{class:h}),...n,children:o})});g.displayName="HeroUI.CardHeader";var v=r(19296),x=r(59406),f=r(34583),k=r(12492);let w=()=>{let e=(0,l.useRouter)(),{login:t,isAuthenticated:r}=(0,i.J)(),[d,h]=(0,s.useState)(""),[m,p]=(0,s.useState)(""),[w,y]=(0,s.useState)(""),[b,S]=(0,s.useState)(!1),[j,C]=(0,s.useState)(!1),[N,T]=(0,s.useState)(null),[E,R]=(0,s.useState)(!1),[A,L]=(0,s.useState)(null);(0,s.useEffect)(()=>{(async()=>{try{let e=await (0,k.loadChatConfig)(),t=(0,k.getDeploymentUrl)(e);y(t)}catch(e){console.error("Failed to load configuration:",e),y("http://localhost:7272")}})()},[]),(0,s.useEffect)(()=>{r&&e.push("/sentio")},[r,e]),(0,s.useEffect)(()=>{if(E){let t=setTimeout(()=>{e.push("/sentio")},1e3);return()=>clearTimeout(t)}},[E,e]);let I=async()=>{try{let e=(await fetch("".concat(w,"/v3/health"))).ok;return L(e),e}catch(e){return console.error("Health check failed:",e),L(!1),!1}},U=async e=>{e.preventDefault(),C(!0),T(null);try{await t(d,m,w),R(!0)}catch(r){console.error("Login failed:",r);let e=await I(),t="An unknown error occurred";r instanceof Error?t=r.message:"string"==typeof r&&(t=r),T("".concat(t," ").concat(e?"服务器运行正常，请检查您的凭据后重试。":"无法与服务器通信，请检查配置文件中的API地址是否正确。"))}finally{C(!1)}};return E?(0,a.jsx)("div",{className:"min-h-screen bg-zinc-900 flex flex-col items-center justify-center p-8",children:(0,a.jsx)(c.Z,{className:"w-full max-w-md",children:(0,a.jsx)(u.U,{className:"pt-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-green-600 text-lg font-semibold mb-2",children:"登录成功！"}),(0,a.jsx)("div",{className:"text-gray-600 text-sm",children:"正在跳转到聊天页面..."})]})})})}):(0,a.jsx)("div",{className:"min-h-screen bg-zinc-900 flex flex-col items-center justify-center p-8",children:(0,a.jsxs)(c.Z,{className:"w-full max-w-md shadow-2xl",children:[(0,a.jsxs)(g,{className:"text-center pb-2",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent",children:"LiveChat"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"欢迎登录"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"请输入您的邮箱和密码"})]}),!1===A&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md",children:(0,a.jsx)("span",{className:"text-red-600 dark:text-red-400 text-sm font-medium",children:"无法连接到服务器，请检查网络连接或联系管理员。"})})]}),(0,a.jsx)(u.U,{children:(0,a.jsxs)("form",{onSubmit:U,className:"space-y-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)(v.r,{id:"email",name:"email",type:"email",label:"邮箱",placeholder:"请输入邮箱",value:d,onChange:e=>h(e.target.value),autoComplete:"email",required:!0,variant:"bordered",classNames:{label:"text-gray-700 dark:text-gray-300",input:"text-gray-900 dark:text-white"}})}),(0,a.jsx)("div",{children:(0,a.jsx)(v.r,{id:"password",name:"password",type:b?"text":"password",label:"密码",placeholder:"请输入密码",value:m,onChange:e=>p(e.target.value),autoComplete:"current-password",required:!0,variant:"bordered",classNames:{label:"text-gray-700 dark:text-gray-300",input:"text-gray-900 dark:text-white"},endContent:(0,a.jsx)("button",{type:"button",onClick:()=>{S(!b)},className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none","aria-label":b?"隐藏密码":"显示密码",children:b?(0,a.jsx)(o,{className:"h-5 w-5"}):(0,a.jsx)(n,{className:"h-5 w-5"})})})}),(0,a.jsx)(x.T,{type:"submit",className:"w-full py-3 text-base font-medium bg-gradient-to-r from-blue-500 to-purple-600 text-white",disabled:j,size:"lg",children:j?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.o,{size:"sm",color:"white"}),(0,a.jsx)("span",{children:"登录中..."})]}):"登录"}),N&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md",children:(0,a.jsx)("span",{className:"text-red-600 dark:text-red-400 text-sm",children:N})})]})})]})})}},12492:(e,t,r)=>{"use strict";r.d(t,{getDeploymentUrl:()=>l,loadChatConfig:()=>s});let a={server:{apiUrl:"http://localhost:7272",useHttps:!1,apiVersion:"v3",timeout:3e4},app:{appName:"LiveChat",appDescription:"Live chat application with R2R integration",version:"1.0.0",defaultMode:"rag_agent",conversationHistoryLimit:10},vectorSearch:{enabled:!0,searchLimit:10,searchFilters:"{}",indexMeasure:"cosine_distance",includeMetadatas:!1,probes:void 0,efSearch:void 0},hybridSearch:{enabled:!1,fullTextWeight:void 0,semanticWeight:void 0,fullTextLimit:void 0,rrfK:void 0},graphSearch:{enabled:!0,kgSearchLevel:null,maxCommunityDescriptionLength:100,localSearchLimits:{},maxLlmQueries:void 0},ragGeneration:{temperature:.1,topP:1,topK:100,maxTokensToSample:1024,kgTemperature:.1,kgTopP:1,kgTopK:100,kgMaxTokensToSample:1024}},s=async()=>{try{let e=await fetch("/config.json");if(!e.ok)return console.warn("Failed to load config.json, using default configuration"),a;let t=await e.json();return{server:{...a.server,...t.server},app:{...a.app,...t.app},vectorSearch:{...a.vectorSearch,...t.vectorSearch},hybridSearch:{...a.hybridSearch,...t.hybridSearch},graphSearch:{...a.graphSearch,...t.graphSearch},ragGeneration:{...a.ragGeneration,...t.ragGeneration}}}catch(e){return console.error("Error loading configuration:",e),a}},l=e=>{let t=e||a;if(t.server.apiUrl.startsWith("http://")||t.server.apiUrl.startsWith("https://"))return t.server.apiUrl;let r=t.server.useHttps?"https":"http";return"".concat(r,"://").concat(t.server.apiUrl)}},86037:(e,t,r)=>{"use strict";r.d(t,{J:()=>n,v:()=>i});var a=r(12389),s=r(41473),l=r(90959);let o=(0,s.createContext)({pipeline:null,setPipeline:()=>{},selectedModel:"null",setSelectedModel:()=>{},isAuthenticated:!1,login:async()=>({success:!1,userRole:"user"}),loginWithToken:async()=>({success:!1,userRole:"user"}),logout:async()=>{},unsetCredentials:async()=>{},register:async()=>{},authState:{isAuthenticated:!1,email:null,userRole:null,userId:null},getClient:()=>null,client:null,viewMode:"user",setViewMode:()=>{},isSuperUser:()=>!1,createUser:async()=>{throw Error("createUser is not implemented in the default context")}}),n=()=>{let e=(0,s.useContext)(o);if(!e)throw Error("useUser must be used within a UserProvider");return e},i=e=>{let{children:t}=e,[n,i]=(0,s.useState)(null),[c,u]=(0,s.useState)(null),[d,h]=(0,s.useState)("null"),[m,p]=(0,s.useState)("user"),[g,v]=(0,s.useState)({isAuthenticated:!1,email:null,userRole:null,userId:null}),x=(0,s.useCallback)(()=>"admin"===g.userRole&&"admin"===m,[g.userRole,m]),[f,k]=(0,s.useState)(null),w=(0,s.useCallback)(async(e,t,r)=>{let a=new l.r2rClient(r);try{var s,o,n,c;let r=await a.users.login({email:e,password:t}),l=(null===(s=r.results.accessToken)||void 0===s?void 0:s.token)||(null===(o=r.results.access_token)||void 0===o?void 0:o.token),u=(null===(n=r.results.refreshToken)||void 0===n?void 0:n.token)||(null===(c=r.results.refresh_token)||void 0===c?void 0:c.token);if(!l)throw Error("No access token received from server");localStorage.setItem("livechatAccessToken",l),u&&localStorage.setItem("livechatRefreshToken",u),a.setTokens(l,u||""),i(a);let d=await a.users.me();if(!d.results)throw Error("Failed to get user information");let h="user";try{await a.system.settings(),h="admin"}catch(e){e instanceof Error&&"status"in e&&403===e.status||console.error("Unexpected error when checking user role:",e)}return v({isAuthenticated:!0,email:d.results.email,userRole:h,userId:d.results.id}),k(Date.now()),{success:!0,userRole:h}}catch(e){throw console.error("Login error:",e),e}},[]),y=(0,s.useCallback)(async(e,t)=>{let r=new l.r2rClient(t);try{r.setTokens(e,"");let t=await r.users.me();if(!t.results)throw Error("Failed to get user information");localStorage.setItem("livechatAccessToken",e),i(r);let a="user";try{await r.system.settings(),a="admin"}catch(e){e instanceof Error&&"status"in e&&403===e.status||console.error("Unexpected error when checking user role:",e)}return v({isAuthenticated:!0,email:t.results.email,userRole:a,userId:t.results.id}),{success:!0,userRole:a}}catch(e){throw console.error("Token login error:",e),localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),e}},[]),b=(0,s.useCallback)(async()=>{try{n&&await n.users.logout()}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),i(null),v({isAuthenticated:!1,email:null,userRole:null,userId:null}),k(null)}},[n]),S=(0,s.useCallback)(async()=>{localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),i(null),v({isAuthenticated:!1,email:null,userRole:null,userId:null}),k(null)},[]),j=(0,s.useCallback)(async(e,t,r)=>{let a=new l.r2rClient(r);try{await a.users.create({email:e,password:t}),await w(e,t,r)}catch(e){throw console.error("Registration error:",e),e}},[w]),C=(0,s.useCallback)(()=>n,[n]),N=(0,s.useCallback)(async function(e,t){if(arguments.length>2&&void 0!==arguments[2]&&arguments[2],!n)throw Error("No authenticated client available");try{await n.users.create({email:e,password:t})}catch(e){throw console.error("Create user error:",e),e}},[n]);return(0,s.useEffect)(()=>{(async()=>{let e=localStorage.getItem("livechatAccessToken");if(e)try{let{loadChatConfig:t,getDeploymentUrl:a}=await r.e(111).then(r.bind(r,12492)),s=await t(),l=a(s);await y(e,l)}catch(e){console.error("Auto-login failed:",e),localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken")}})()},[y]),(0,a.jsx)(o.Provider,{value:{pipeline:c,setPipeline:u,selectedModel:d,setSelectedModel:h,isAuthenticated:g.isAuthenticated,login:w,loginWithToken:y,logout:b,unsetCredentials:S,register:j,authState:g,getClient:C,client:n,viewMode:m,setViewMode:p,isSuperUser:x,createUser:N},children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[78,431,316,663,61,358],()=>t(39583)),_N_E=e.O()}]);