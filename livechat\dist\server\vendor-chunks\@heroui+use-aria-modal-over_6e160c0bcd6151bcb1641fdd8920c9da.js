"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@heroui+use-aria-modal-over_6e160c0bcd6151bcb1641fdd8920c9da";
exports.ids = ["vendor-chunks/@heroui+use-aria-modal-over_6e160c0bcd6151bcb1641fdd8920c9da"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@heroui+use-aria-modal-over_6e160c0bcd6151bcb1641fdd8920c9da/node_modules/@heroui/use-aria-modal-overlay/dist/index.mjs":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@heroui+use-aria-modal-over_6e160c0bcd6151bcb1641fdd8920c9da/node_modules/@heroui/use-aria-modal-overlay/dist/index.mjs ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAriaModalOverlay: () => (/* binding */ useAriaModalOverlay)\n/* harmony export */ });\n/* harmony import */ var _react_aria_overlays__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/overlays */ \"(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.26.0_f683e2f5d8a049041921877a4d68e6f4/node_modules/@react-aria/overlays/dist/useOverlay.mjs\");\n/* harmony import */ var _react_aria_overlays__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/overlays */ \"(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.26.0_f683e2f5d8a049041921877a4d68e6f4/node_modules/@react-aria/overlays/dist/usePreventScroll.mjs\");\n/* harmony import */ var _react_aria_overlays__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/overlays */ \"(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.26.0_f683e2f5d8a049041921877a4d68e6f4/node_modules/@react-aria/overlays/dist/Overlay.mjs\");\n/* harmony import */ var _react_aria_overlays__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-aria/overlays */ \"(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.26.0_f683e2f5d8a049041921877a4d68e6f4/node_modules/@react-aria/overlays/dist/ariaHideOutside.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/mergeProps.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/index.ts\n\n\n\nfunction useAriaModalOverlay(props = {\n  shouldBlockScroll: true\n}, state, ref) {\n  let { overlayProps, underlayProps } = (0,_react_aria_overlays__WEBPACK_IMPORTED_MODULE_1__.useOverlay)(\n    {\n      ...props,\n      isOpen: state.isOpen,\n      onClose: state.close\n    },\n    ref\n  );\n  (0,_react_aria_overlays__WEBPACK_IMPORTED_MODULE_2__.usePreventScroll)({\n    isDisabled: !state.isOpen || !props.shouldBlockScroll\n  });\n  (0,_react_aria_overlays__WEBPACK_IMPORTED_MODULE_3__.useOverlayFocusContain)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (state.isOpen && ref.current) {\n      return (0,_react_aria_overlays__WEBPACK_IMPORTED_MODULE_4__.ariaHideOutside)([ref.current]);\n    }\n  }, [state.isOpen, ref]);\n  return {\n    modalProps: (0,_react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.mergeProps)(overlayProps),\n    underlayProps\n  };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@heroui+use-aria-modal-over_6e160c0bcd6151bcb1641fdd8920c9da/node_modules/@heroui/use-aria-modal-overlay/dist/index.mjs\n");

/***/ })

};
;