"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/onnxruntime-common@1.14.0";
exports.ids = ["vendor-chunks/onnxruntime-common@1.14.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/backend-impl.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/backend-impl.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerBackend: () => (/* binding */ registerBackend),\n/* harmony export */   resolveBackend: () => (/* binding */ resolveBackend)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nconst backends = {};\nconst backendsSortedByPriority = [];\n/**\n * Register a backend.\n *\n * @param name - the name as a key to lookup as an execution provider.\n * @param backend - the backend object.\n * @param priority - an integer indicating the priority of the backend. Higher number means higher priority. if priority\n * < 0, it will be considered as a 'beta' version and will not be used as a fallback backend by default.\n *\n * @internal\n */\nconst registerBackend = (name, backend, priority) => {\n    if (backend && typeof backend.init === 'function' && typeof backend.createSessionHandler === 'function') {\n        const currentBackend = backends[name];\n        if (currentBackend === undefined) {\n            backends[name] = { backend, priority };\n        }\n        else if (currentBackend.priority > priority) {\n            // same name is already registered with a higher priority. skip registeration.\n            return;\n        }\n        else if (currentBackend.priority === priority) {\n            if (currentBackend.backend !== backend) {\n                throw new Error(`cannot register backend \"${name}\" using priority ${priority}`);\n            }\n        }\n        if (priority >= 0) {\n            const i = backendsSortedByPriority.indexOf(name);\n            if (i !== -1) {\n                backendsSortedByPriority.splice(i, 1);\n            }\n            for (let i = 0; i < backendsSortedByPriority.length; i++) {\n                if (backends[backendsSortedByPriority[i]].priority <= priority) {\n                    backendsSortedByPriority.splice(i, 0, name);\n                    return;\n                }\n            }\n            backendsSortedByPriority.push(name);\n        }\n        return;\n    }\n    throw new TypeError('not a valid backend');\n};\n/**\n * Resolve backend by specified hints.\n *\n * @param backendHints - a list of execution provider names to lookup. If omitted use registered backends as list.\n * @returns a promise that resolves to the backend.\n *\n * @internal\n */\nconst resolveBackend = async (backendHints) => {\n    const backendNames = backendHints.length === 0 ? backendsSortedByPriority : backendHints;\n    const errors = [];\n    for (const backendName of backendNames) {\n        const backendInfo = backends[backendName];\n        if (backendInfo) {\n            if (backendInfo.initialized) {\n                return backendInfo.backend;\n            }\n            else if (backendInfo.aborted) {\n                continue; // current backend is unavailable; try next\n            }\n            const isInitializing = !!backendInfo.initPromise;\n            try {\n                if (!isInitializing) {\n                    backendInfo.initPromise = backendInfo.backend.init();\n                }\n                await backendInfo.initPromise;\n                backendInfo.initialized = true;\n                return backendInfo.backend;\n            }\n            catch (e) {\n                if (!isInitializing) {\n                    errors.push({ name: backendName, err: e });\n                }\n                backendInfo.aborted = true;\n            }\n            finally {\n                delete backendInfo.initPromise;\n            }\n        }\n    }\n    throw new Error(`no available backend found. ERR: ${errors.map(e => `[${e.name}] ${e.err}`).join(', ')}`);\n};\n//# sourceMappingURL=backend-impl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/backend-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/backend.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/backend.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerBackend: () => (/* reexport safe */ _backend_impl__WEBPACK_IMPORTED_MODULE_0__.registerBackend)\n/* harmony export */ });\n/* harmony import */ var _backend_impl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./backend-impl */ \"(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/backend-impl.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n//# sourceMappingURL=backend.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vb25ueHJ1bnRpbWUtY29tbW9uQDEuMTQuMC9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvbGliL2JhY2tlbmQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ2lEO0FBQ2pEIiwic291cmNlcyI6WyJEOlxcTExNXFxMZWFybmluZ1xcYWlfY29kaW5nXFxhbnl0aGluZ2NoYXRcXGxpdmVjaGF0XFxub2RlX21vZHVsZXNcXC5wbnBtXFxvbm54cnVudGltZS1jb21tb25AMS4xNC4wXFxub2RlX21vZHVsZXNcXG9ubnhydW50aW1lLWNvbW1vblxcZGlzdFxcbGliXFxiYWNrZW5kLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuLy8gTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlLlxuZXhwb3J0IHsgcmVnaXN0ZXJCYWNrZW5kIH0gZnJvbSAnLi9iYWNrZW5kLWltcGwnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YmFja2VuZC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/backend.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/env-impl.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/env-impl.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EnvImpl: () => (/* binding */ EnvImpl)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nclass EnvImpl {\n    constructor() {\n        this.wasm = {};\n        this.webgl = {};\n        this.logLevelInternal = 'warning';\n    }\n    // TODO standadize the getter and setter convention in env for other fields.\n    set logLevel(value) {\n        if (value === undefined) {\n            return;\n        }\n        if (typeof value !== 'string' || ['verbose', 'info', 'warning', 'error', 'fatal'].indexOf(value) === -1) {\n            throw new Error(`Unsupported logging level: ${value}`);\n        }\n        this.logLevelInternal = value;\n    }\n    get logLevel() {\n        return this.logLevelInternal;\n    }\n}\n//# sourceMappingURL=env-impl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vb25ueHJ1bnRpbWUtY29tbW9uQDEuMTQuMC9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvbGliL2Vudi1pbXBsLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMERBQTBELE1BQU07QUFDaEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXExMTVxcTGVhcm5pbmdcXGFpX2NvZGluZ1xcYW55dGhpbmdjaGF0XFxsaXZlY2hhdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcb25ueHJ1bnRpbWUtY29tbW9uQDEuMTQuMFxcbm9kZV9tb2R1bGVzXFxvbm54cnVudGltZS1jb21tb25cXGRpc3RcXGxpYlxcZW52LWltcGwuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIExpY2Vuc2UuXG5leHBvcnQgY2xhc3MgRW52SW1wbCB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHRoaXMud2FzbSA9IHt9O1xuICAgICAgICB0aGlzLndlYmdsID0ge307XG4gICAgICAgIHRoaXMubG9nTGV2ZWxJbnRlcm5hbCA9ICd3YXJuaW5nJztcbiAgICB9XG4gICAgLy8gVE9ETyBzdGFuZGFkaXplIHRoZSBnZXR0ZXIgYW5kIHNldHRlciBjb252ZW50aW9uIGluIGVudiBmb3Igb3RoZXIgZmllbGRzLlxuICAgIHNldCBsb2dMZXZlbCh2YWx1ZSkge1xuICAgICAgICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgdmFsdWUgIT09ICdzdHJpbmcnIHx8IFsndmVyYm9zZScsICdpbmZvJywgJ3dhcm5pbmcnLCAnZXJyb3InLCAnZmF0YWwnXS5pbmRleE9mKHZhbHVlKSA9PT0gLTEpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgVW5zdXBwb3J0ZWQgbG9nZ2luZyBsZXZlbDogJHt2YWx1ZX1gKTtcbiAgICAgICAgfVxuICAgICAgICB0aGlzLmxvZ0xldmVsSW50ZXJuYWwgPSB2YWx1ZTtcbiAgICB9XG4gICAgZ2V0IGxvZ0xldmVsKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5sb2dMZXZlbEludGVybmFsO1xuICAgIH1cbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWVudi1pbXBsLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/env-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/env.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/env.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   env: () => (/* binding */ env)\n/* harmony export */ });\n/* harmony import */ var _env_impl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./env-impl */ \"(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/env-impl.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n/**\n * Represent a set of flags as a global singleton.\n */\nconst env = new _env_impl__WEBPACK_IMPORTED_MODULE_0__.EnvImpl();\n//# sourceMappingURL=env.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vb25ueHJ1bnRpbWUtY29tbW9uQDEuMTQuMC9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvbGliL2Vudi5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDcUM7QUFDckM7QUFDQTtBQUNBO0FBQ08sZ0JBQWdCLDhDQUFPO0FBQzlCIiwic291cmNlcyI6WyJEOlxcTExNXFxMZWFybmluZ1xcYWlfY29kaW5nXFxhbnl0aGluZ2NoYXRcXGxpdmVjaGF0XFxub2RlX21vZHVsZXNcXC5wbnBtXFxvbm54cnVudGltZS1jb21tb25AMS4xNC4wXFxub2RlX21vZHVsZXNcXG9ubnhydW50aW1lLWNvbW1vblxcZGlzdFxcbGliXFxlbnYuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIExpY2Vuc2UuXG5pbXBvcnQgeyBFbnZJbXBsIH0gZnJvbSAnLi9lbnYtaW1wbCc7XG4vKipcbiAqIFJlcHJlc2VudCBhIHNldCBvZiBmbGFncyBhcyBhIGdsb2JhbCBzaW5nbGV0b24uXG4gKi9cbmV4cG9ydCBjb25zdCBlbnYgPSBuZXcgRW52SW1wbCgpO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZW52LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/env.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/index.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/index.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InferenceSession: () => (/* reexport safe */ _inference_session__WEBPACK_IMPORTED_MODULE_2__.InferenceSession),\n/* harmony export */   Tensor: () => (/* reexport safe */ _tensor__WEBPACK_IMPORTED_MODULE_3__.Tensor),\n/* harmony export */   env: () => (/* reexport safe */ _env__WEBPACK_IMPORTED_MODULE_1__.env),\n/* harmony export */   registerBackend: () => (/* reexport safe */ _backend__WEBPACK_IMPORTED_MODULE_0__.registerBackend)\n/* harmony export */ });\n/* harmony import */ var _backend__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./backend */ \"(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/backend.js\");\n/* harmony import */ var _env__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./env */ \"(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/env.js\");\n/* harmony import */ var _inference_session__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./inference-session */ \"(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/inference-session.js\");\n/* harmony import */ var _tensor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tensor */ \"(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/tensor.js\");\n/* harmony import */ var _onnx_value__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./onnx-value */ \"(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/onnx-value.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n/**\n * # ONNX Runtime JavaScript API\n *\n * ONNX Runtime JavaScript API is a unified API for all JavaScript usages, including the following NPM packages:\n *\n * - [onnxruntime-node](https://www.npmjs.com/package/onnxruntime-node)\n * - [onnxruntime-web](https://www.npmjs.com/package/onnxruntime-web)\n * - [onnxruntime-react-native](https://www.npmjs.com/package/onnxruntime-react-native)\n *\n * See also:\n * - [Get Started](https://onnxruntime.ai/docs/get-started/with-javascript.html)\n * - [Inference examples](https://github.com/microsoft/onnxruntime-inference-examples/tree/main/js)\n *\n * @packageDocumentation\n */\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vb25ueHJ1bnRpbWUtY29tbW9uQDEuMTQuMC9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDMEI7QUFDSjtBQUNjO0FBQ1g7QUFDSTtBQUM3QiIsInNvdXJjZXMiOlsiRDpcXExMTVxcTGVhcm5pbmdcXGFpX2NvZGluZ1xcYW55dGhpbmdjaGF0XFxsaXZlY2hhdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcb25ueHJ1bnRpbWUtY29tbW9uQDEuMTQuMFxcbm9kZV9tb2R1bGVzXFxvbm54cnVudGltZS1jb21tb25cXGRpc3RcXGxpYlxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIExpY2Vuc2UuXG4vKipcbiAqICMgT05OWCBSdW50aW1lIEphdmFTY3JpcHQgQVBJXG4gKlxuICogT05OWCBSdW50aW1lIEphdmFTY3JpcHQgQVBJIGlzIGEgdW5pZmllZCBBUEkgZm9yIGFsbCBKYXZhU2NyaXB0IHVzYWdlcywgaW5jbHVkaW5nIHRoZSBmb2xsb3dpbmcgTlBNIHBhY2thZ2VzOlxuICpcbiAqIC0gW29ubnhydW50aW1lLW5vZGVdKGh0dHBzOi8vd3d3Lm5wbWpzLmNvbS9wYWNrYWdlL29ubnhydW50aW1lLW5vZGUpXG4gKiAtIFtvbm54cnVudGltZS13ZWJdKGh0dHBzOi8vd3d3Lm5wbWpzLmNvbS9wYWNrYWdlL29ubnhydW50aW1lLXdlYilcbiAqIC0gW29ubnhydW50aW1lLXJlYWN0LW5hdGl2ZV0oaHR0cHM6Ly93d3cubnBtanMuY29tL3BhY2thZ2Uvb25ueHJ1bnRpbWUtcmVhY3QtbmF0aXZlKVxuICpcbiAqIFNlZSBhbHNvOlxuICogLSBbR2V0IFN0YXJ0ZWRdKGh0dHBzOi8vb25ueHJ1bnRpbWUuYWkvZG9jcy9nZXQtc3RhcnRlZC93aXRoLWphdmFzY3JpcHQuaHRtbClcbiAqIC0gW0luZmVyZW5jZSBleGFtcGxlc10oaHR0cHM6Ly9naXRodWIuY29tL21pY3Jvc29mdC9vbm54cnVudGltZS1pbmZlcmVuY2UtZXhhbXBsZXMvdHJlZS9tYWluL2pzKVxuICpcbiAqIEBwYWNrYWdlRG9jdW1lbnRhdGlvblxuICovXG5leHBvcnQgKiBmcm9tICcuL2JhY2tlbmQnO1xuZXhwb3J0ICogZnJvbSAnLi9lbnYnO1xuZXhwb3J0ICogZnJvbSAnLi9pbmZlcmVuY2Utc2Vzc2lvbic7XG5leHBvcnQgKiBmcm9tICcuL3RlbnNvcic7XG5leHBvcnQgKiBmcm9tICcuL29ubngtdmFsdWUnO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/inference-session-impl.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/inference-session-impl.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InferenceSession: () => (/* binding */ InferenceSession)\n/* harmony export */ });\n/* harmony import */ var _backend_impl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./backend-impl */ \"(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/backend-impl.js\");\n/* harmony import */ var _tensor__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tensor */ \"(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/tensor.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n\nclass InferenceSession {\n    constructor(handler) {\n        this.handler = handler;\n    }\n    async run(feeds, arg1, arg2) {\n        const fetches = {};\n        let options = {};\n        // check inputs\n        if (typeof feeds !== 'object' || feeds === null || feeds instanceof _tensor__WEBPACK_IMPORTED_MODULE_1__.Tensor || Array.isArray(feeds)) {\n            throw new TypeError('\\'feeds\\' must be an object that use input names as keys and OnnxValue as corresponding values.');\n        }\n        let isFetchesEmpty = true;\n        // determine which override is being used\n        if (typeof arg1 === 'object') {\n            if (arg1 === null) {\n                throw new TypeError('Unexpected argument[1]: cannot be null.');\n            }\n            if (arg1 instanceof _tensor__WEBPACK_IMPORTED_MODULE_1__.Tensor) {\n                throw new TypeError('\\'fetches\\' cannot be a Tensor');\n            }\n            if (Array.isArray(arg1)) {\n                if (arg1.length === 0) {\n                    throw new TypeError('\\'fetches\\' cannot be an empty array.');\n                }\n                isFetchesEmpty = false;\n                // output names\n                for (const name of arg1) {\n                    if (typeof name !== 'string') {\n                        throw new TypeError('\\'fetches\\' must be a string array or an object.');\n                    }\n                    if (this.outputNames.indexOf(name) === -1) {\n                        throw new RangeError(`'fetches' contains invalid output name: ${name}.`);\n                    }\n                    fetches[name] = null;\n                }\n                if (typeof arg2 === 'object' && arg2 !== null) {\n                    options = arg2;\n                }\n                else if (typeof arg2 !== 'undefined') {\n                    throw new TypeError('\\'options\\' must be an object.');\n                }\n            }\n            else {\n                // decide whether arg1 is fetches or options\n                // if any output name is present and its value is valid OnnxValue, we consider it fetches\n                let isFetches = false;\n                const arg1Keys = Object.getOwnPropertyNames(arg1);\n                for (const name of this.outputNames) {\n                    if (arg1Keys.indexOf(name) !== -1) {\n                        const v = arg1[name];\n                        if (v === null || v instanceof _tensor__WEBPACK_IMPORTED_MODULE_1__.Tensor) {\n                            isFetches = true;\n                            isFetchesEmpty = false;\n                            fetches[name] = v;\n                        }\n                    }\n                }\n                if (isFetches) {\n                    if (typeof arg2 === 'object' && arg2 !== null) {\n                        options = arg2;\n                    }\n                    else if (typeof arg2 !== 'undefined') {\n                        throw new TypeError('\\'options\\' must be an object.');\n                    }\n                }\n                else {\n                    options = arg1;\n                }\n            }\n        }\n        else if (typeof arg1 !== 'undefined') {\n            throw new TypeError('Unexpected argument[1]: must be \\'fetches\\' or \\'options\\'.');\n        }\n        // check if all inputs are in feed\n        for (const name of this.inputNames) {\n            if (typeof feeds[name] === 'undefined') {\n                throw new Error(`input '${name}' is missing in 'feeds'.`);\n            }\n        }\n        // if no fetches is specified, we use the full output names list\n        if (isFetchesEmpty) {\n            for (const name of this.outputNames) {\n                fetches[name] = null;\n            }\n        }\n        // feeds, fetches and options are prepared\n        const results = await this.handler.run(feeds, fetches, options);\n        const returnValue = {};\n        for (const key in results) {\n            if (Object.hasOwnProperty.call(results, key)) {\n                returnValue[key] = new _tensor__WEBPACK_IMPORTED_MODULE_1__.Tensor(results[key].type, results[key].data, results[key].dims);\n            }\n        }\n        return returnValue;\n    }\n    static async create(arg0, arg1, arg2, arg3) {\n        // either load from a file or buffer\n        let filePathOrUint8Array;\n        let options = {};\n        if (typeof arg0 === 'string') {\n            filePathOrUint8Array = arg0;\n            if (typeof arg1 === 'object' && arg1 !== null) {\n                options = arg1;\n            }\n            else if (typeof arg1 !== 'undefined') {\n                throw new TypeError('\\'options\\' must be an object.');\n            }\n        }\n        else if (arg0 instanceof Uint8Array) {\n            filePathOrUint8Array = arg0;\n            if (typeof arg1 === 'object' && arg1 !== null) {\n                options = arg1;\n            }\n            else if (typeof arg1 !== 'undefined') {\n                throw new TypeError('\\'options\\' must be an object.');\n            }\n        }\n        else if (arg0 instanceof ArrayBuffer ||\n            (typeof SharedArrayBuffer !== 'undefined' && arg0 instanceof SharedArrayBuffer)) {\n            const buffer = arg0;\n            let byteOffset = 0;\n            let byteLength = arg0.byteLength;\n            if (typeof arg1 === 'object' && arg1 !== null) {\n                options = arg1;\n            }\n            else if (typeof arg1 === 'number') {\n                byteOffset = arg1;\n                if (!Number.isSafeInteger(byteOffset)) {\n                    throw new RangeError('\\'byteOffset\\' must be an integer.');\n                }\n                if (byteOffset < 0 || byteOffset >= buffer.byteLength) {\n                    throw new RangeError(`'byteOffset' is out of range [0, ${buffer.byteLength}).`);\n                }\n                byteLength = arg0.byteLength - byteOffset;\n                if (typeof arg2 === 'number') {\n                    byteLength = arg2;\n                    if (!Number.isSafeInteger(byteLength)) {\n                        throw new RangeError('\\'byteLength\\' must be an integer.');\n                    }\n                    if (byteLength <= 0 || byteOffset + byteLength > buffer.byteLength) {\n                        throw new RangeError(`'byteLength' is out of range (0, ${buffer.byteLength - byteOffset}].`);\n                    }\n                    if (typeof arg3 === 'object' && arg3 !== null) {\n                        options = arg3;\n                    }\n                    else if (typeof arg3 !== 'undefined') {\n                        throw new TypeError('\\'options\\' must be an object.');\n                    }\n                }\n                else if (typeof arg2 !== 'undefined') {\n                    throw new TypeError('\\'byteLength\\' must be a number.');\n                }\n            }\n            else if (typeof arg1 !== 'undefined') {\n                throw new TypeError('\\'options\\' must be an object.');\n            }\n            filePathOrUint8Array = new Uint8Array(buffer, byteOffset, byteLength);\n        }\n        else {\n            throw new TypeError('Unexpected argument[0]: must be \\'path\\' or \\'buffer\\'.');\n        }\n        // get backend hints\n        const eps = options.executionProviders || [];\n        const backendHints = eps.map(i => typeof i === 'string' ? i : i.name);\n        const backend = await (0,_backend_impl__WEBPACK_IMPORTED_MODULE_0__.resolveBackend)(backendHints);\n        const handler = await backend.createSessionHandler(filePathOrUint8Array, options);\n        return new InferenceSession(handler);\n    }\n    startProfiling() {\n        this.handler.startProfiling();\n    }\n    endProfiling() {\n        this.handler.endProfiling();\n    }\n    get inputNames() {\n        return this.handler.inputNames;\n    }\n    get outputNames() {\n        return this.handler.outputNames;\n    }\n}\n//# sourceMappingURL=inference-session-impl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/inference-session-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/inference-session.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/inference-session.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InferenceSession: () => (/* binding */ InferenceSession)\n/* harmony export */ });\n/* harmony import */ var _inference_session_impl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./inference-session-impl */ \"(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/inference-session-impl.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nconst InferenceSession = _inference_session_impl__WEBPACK_IMPORTED_MODULE_0__.InferenceSession;\n//# sourceMappingURL=inference-session.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vb25ueHJ1bnRpbWUtY29tbW9uQDEuMTQuMC9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvbGliL2luZmVyZW5jZS1zZXNzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNvRjtBQUNwRjtBQUNPLHlCQUF5QixxRUFBb0I7QUFDcEQiLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXG9ubnhydW50aW1lLWNvbW1vbkAxLjE0LjBcXG5vZGVfbW9kdWxlc1xcb25ueHJ1bnRpbWUtY29tbW9uXFxkaXN0XFxsaWJcXGluZmVyZW5jZS1zZXNzaW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuLy8gTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlLlxuaW1wb3J0IHsgSW5mZXJlbmNlU2Vzc2lvbiBhcyBJbmZlcmVuY2VTZXNzaW9uSW1wbCB9IGZyb20gJy4vaW5mZXJlbmNlLXNlc3Npb24taW1wbCc7XG4vLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L25hbWluZy1jb252ZW50aW9uXG5leHBvcnQgY29uc3QgSW5mZXJlbmNlU2Vzc2lvbiA9IEluZmVyZW5jZVNlc3Npb25JbXBsO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5mZXJlbmNlLXNlc3Npb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/inference-session.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/onnx-value.js":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/onnx-value.js ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n//# sourceMappingURL=onnx-value.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vb25ueHJ1bnRpbWUtY29tbW9uQDEuMTQuMC9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvbGliL29ubngtdmFsdWUuanMiLCJtYXBwaW5ncyI6IjtBQUFBO0FBQ0E7QUFDVTtBQUNWIiwic291cmNlcyI6WyJEOlxcTExNXFxMZWFybmluZ1xcYWlfY29kaW5nXFxhbnl0aGluZ2NoYXRcXGxpdmVjaGF0XFxub2RlX21vZHVsZXNcXC5wbnBtXFxvbm54cnVudGltZS1jb21tb25AMS4xNC4wXFxub2RlX21vZHVsZXNcXG9ubnhydW50aW1lLWNvbW1vblxcZGlzdFxcbGliXFxvbm54LXZhbHVlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIENvcHlyaWdodCAoYykgTWljcm9zb2Z0IENvcnBvcmF0aW9uLiBBbGwgcmlnaHRzIHJlc2VydmVkLlxuLy8gTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlLlxuZXhwb3J0IHt9O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9b25ueC12YWx1ZS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/onnx-value.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/tensor-impl.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/tensor-impl.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tensor: () => (/* binding */ Tensor)\n/* harmony export */ });\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\nconst isBigInt64ArrayAvailable = typeof BigInt64Array !== 'undefined' && typeof BigInt64Array.from === 'function';\nconst isBigUint64ArrayAvailable = typeof BigUint64Array !== 'undefined' && typeof BigUint64Array.from === 'function';\n// a runtime map that maps type string to TypedArray constructor. Should match Tensor.DataTypeMap.\nconst NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP = new Map([\n    ['float32', Float32Array],\n    ['uint8', Uint8Array],\n    ['int8', Int8Array],\n    ['uint16', Uint16Array],\n    ['int16', Int16Array],\n    ['int32', Int32Array],\n    ['bool', Uint8Array],\n    ['float64', Float64Array],\n    ['uint32', Uint32Array],\n]);\n// a runtime map that maps type string to TypedArray constructor. Should match Tensor.DataTypeMap.\nconst NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP = new Map([\n    [Float32Array, 'float32'],\n    [Uint8Array, 'uint8'],\n    [Int8Array, 'int8'],\n    [Uint16Array, 'uint16'],\n    [Int16Array, 'int16'],\n    [Int32Array, 'int32'],\n    [Float64Array, 'float64'],\n    [Uint32Array, 'uint32'],\n]);\nif (isBigInt64ArrayAvailable) {\n    NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.set('int64', BigInt64Array);\n    NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.set(BigInt64Array, 'int64');\n}\nif (isBigUint64ArrayAvailable) {\n    NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.set('uint64', BigUint64Array);\n    NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.set(BigUint64Array, 'uint64');\n}\n/**\n * calculate size from dims.\n *\n * @param dims the dims array. May be an illegal input.\n */\nconst calculateSize = (dims) => {\n    let size = 1;\n    for (let i = 0; i < dims.length; i++) {\n        const dim = dims[i];\n        if (typeof dim !== 'number' || !Number.isSafeInteger(dim)) {\n            throw new TypeError(`dims[${i}] must be an integer, got: ${dim}`);\n        }\n        if (dim < 0) {\n            throw new RangeError(`dims[${i}] must be a non-negative integer, got: ${dim}`);\n        }\n        size *= dim;\n    }\n    return size;\n};\nclass Tensor {\n    constructor(arg0, arg1, arg2) {\n        let type;\n        let data;\n        let dims;\n        // check whether arg0 is type or data\n        if (typeof arg0 === 'string') {\n            //\n            // Override: constructor(type, data, ...)\n            //\n            type = arg0;\n            dims = arg2;\n            if (arg0 === 'string') {\n                // string tensor\n                if (!Array.isArray(arg1)) {\n                    throw new TypeError('A string tensor\\'s data must be a string array.');\n                }\n                // we don't check whether every element in the array is string; this is too slow. we assume it's correct and\n                // error will be populated at inference\n                data = arg1;\n            }\n            else {\n                // numeric tensor\n                const typedArrayConstructor = NUMERIC_TENSOR_TYPE_TO_TYPEDARRAY_MAP.get(arg0);\n                if (typedArrayConstructor === undefined) {\n                    throw new TypeError(`Unsupported tensor type: ${arg0}.`);\n                }\n                if (Array.isArray(arg1)) {\n                    // use 'as any' here because TypeScript's check on type of 'SupportedTypedArrayConstructors.from()' produces\n                    // incorrect results.\n                    // 'typedArrayConstructor' should be one of the typed array prototype objects.\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    data = typedArrayConstructor.from(arg1);\n                }\n                else if (arg1 instanceof typedArrayConstructor) {\n                    data = arg1;\n                }\n                else {\n                    throw new TypeError(`A ${type} tensor's data must be type of ${typedArrayConstructor}`);\n                }\n            }\n        }\n        else {\n            //\n            // Override: constructor(data, ...)\n            //\n            dims = arg1;\n            if (Array.isArray(arg0)) {\n                // only boolean[] and string[] is supported\n                if (arg0.length === 0) {\n                    throw new TypeError('Tensor type cannot be inferred from an empty array.');\n                }\n                const firstElementType = typeof arg0[0];\n                if (firstElementType === 'string') {\n                    type = 'string';\n                    data = arg0;\n                }\n                else if (firstElementType === 'boolean') {\n                    type = 'bool';\n                    // 'arg0' is of type 'boolean[]'. Uint8Array.from(boolean[]) actually works, but typescript thinks this is\n                    // wrong type. We use 'as any' to make it happy.\n                    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n                    data = Uint8Array.from(arg0);\n                }\n                else {\n                    throw new TypeError(`Invalid element type of data array: ${firstElementType}.`);\n                }\n            }\n            else {\n                // get tensor type from TypedArray\n                const mappedType = NUMERIC_TENSOR_TYPEDARRAY_TO_TYPE_MAP.get(arg0.constructor);\n                if (mappedType === undefined) {\n                    throw new TypeError(`Unsupported type for tensor data: ${arg0.constructor}.`);\n                }\n                type = mappedType;\n                data = arg0;\n            }\n        }\n        // type and data is processed, now processing dims\n        if (dims === undefined) {\n            // assume 1-D tensor if dims omitted\n            dims = [data.length];\n        }\n        else if (!Array.isArray(dims)) {\n            throw new TypeError('A tensor\\'s dims must be a number array');\n        }\n        // perform check\n        const size = calculateSize(dims);\n        if (size !== data.length) {\n            throw new Error(`Tensor's size(${size}) does not match data length(${data.length}).`);\n        }\n        this.dims = dims;\n        this.type = type;\n        this.data = data;\n        this.size = size;\n    }\n    // #endregion\n    /**\n     * Create a new tensor object from image object\n     *\n     * @param buffer - Extracted image buffer data - assuming RGBA format\n     * @param imageFormat - input image configuration - required configurations height, width, format\n     * @param tensorFormat - output tensor configuration - Default is RGB format\n     */\n    static bufferToTensor(buffer, options) {\n        if (buffer === undefined) {\n            throw new Error('Image buffer must be defined');\n        }\n        if (options.height === undefined || options.width === undefined) {\n            throw new Error('Image height and width must be defined');\n        }\n        const { height, width } = options;\n        const norm = options.norm;\n        let normMean;\n        let normBias;\n        if (norm === undefined || norm.mean === undefined) {\n            normMean = 255;\n        }\n        else {\n            normMean = norm.mean;\n        }\n        if (norm === undefined || norm.bias === undefined) {\n            normBias = 0;\n        }\n        else {\n            normBias = norm.bias;\n        }\n        const inputformat = options.bitmapFormat !== undefined ? options.bitmapFormat : 'RGBA';\n        // default value is RGBA since imagedata and HTMLImageElement uses it\n        const outputformat = options.tensorFormat !== undefined ?\n            (options.tensorFormat !== undefined ? options.tensorFormat : 'RGB') :\n            'RGB';\n        const offset = height * width;\n        const float32Data = outputformat === 'RGBA' ? new Float32Array(offset * 4) : new Float32Array(offset * 3);\n        // Default pointer assignments\n        let step = 4, rImagePointer = 0, gImagePointer = 1, bImagePointer = 2, aImagePointer = 3;\n        let rTensorPointer = 0, gTensorPointer = offset, bTensorPointer = offset * 2, aTensorPointer = -1;\n        // Updating the pointer assignments based on the input image format\n        if (inputformat === 'RGB') {\n            step = 3;\n            rImagePointer = 0;\n            gImagePointer = 1;\n            bImagePointer = 2;\n            aImagePointer = -1;\n        }\n        // Updating the pointer assignments based on the output tensor format\n        if (outputformat === 'RGBA') {\n            aTensorPointer = offset * 3;\n        }\n        else if (outputformat === 'RBG') {\n            rTensorPointer = 0;\n            bTensorPointer = offset;\n            gTensorPointer = offset * 2;\n        }\n        else if (outputformat === 'BGR') {\n            bTensorPointer = 0;\n            gTensorPointer = offset;\n            rTensorPointer = offset * 2;\n        }\n        for (let i = 0; i < offset; i++, rImagePointer += step, bImagePointer += step, gImagePointer += step, aImagePointer += step) {\n            float32Data[rTensorPointer++] = (buffer[rImagePointer] + normBias) / normMean;\n            float32Data[gTensorPointer++] = (buffer[gImagePointer] + normBias) / normMean;\n            float32Data[bTensorPointer++] = (buffer[bImagePointer] + normBias) / normMean;\n            if (aTensorPointer !== -1 && aImagePointer !== -1) {\n                float32Data[aTensorPointer++] = (buffer[aImagePointer] + normBias) / normMean;\n            }\n        }\n        // Float32Array -> ort.Tensor\n        const outputTensor = outputformat === 'RGBA' ? new Tensor('float32', float32Data, [1, 4, height, width]) :\n            new Tensor('float32', float32Data, [1, 3, height, width]);\n        return outputTensor;\n    }\n    static async fromImage(image, options) {\n        // checking the type of image object\n        const isHTMLImageEle = typeof (HTMLImageElement) !== 'undefined' && image instanceof HTMLImageElement;\n        const isImageDataEle = typeof (ImageData) !== 'undefined' && image instanceof ImageData;\n        const isImageBitmap = typeof (ImageBitmap) !== 'undefined' && image instanceof ImageBitmap;\n        const isURL = typeof (String) !== 'undefined' && (image instanceof String || typeof image === 'string');\n        let data;\n        let tensorConfig = {};\n        // filling and checking image configuration options\n        if (isHTMLImageEle) {\n            // HTMLImageElement - image object - format is RGBA by default\n            const canvas = document.createElement('canvas');\n            const pixels2DContext = canvas.getContext('2d');\n            if (pixels2DContext != null) {\n                let height = image.naturalHeight;\n                let width = image.naturalWidth;\n                if (options !== undefined && options.resizedHeight !== undefined && options.resizedWidth !== undefined) {\n                    height = options.resizedHeight;\n                    width = options.resizedWidth;\n                }\n                if (options !== undefined) {\n                    tensorConfig = options;\n                    if (options.tensorFormat !== undefined) {\n                        throw new Error('Image input config format must be RGBA for HTMLImageElement');\n                    }\n                    else {\n                        tensorConfig.tensorFormat = 'RGBA';\n                    }\n                    if (options.height !== undefined && options.height !== height) {\n                        throw new Error('Image input config height doesn\\'t match HTMLImageElement height');\n                    }\n                    else {\n                        tensorConfig.height = height;\n                    }\n                    if (options.width !== undefined && options.width !== width) {\n                        throw new Error('Image input config width doesn\\'t match HTMLImageElement width');\n                    }\n                    else {\n                        tensorConfig.width = width;\n                    }\n                }\n                else {\n                    tensorConfig.tensorFormat = 'RGBA';\n                    tensorConfig.height = height;\n                    tensorConfig.width = width;\n                }\n                canvas.width = width;\n                canvas.height = height;\n                pixels2DContext.drawImage(image, 0, 0, width, height);\n                data = pixels2DContext.getImageData(0, 0, width, height).data;\n            }\n            else {\n                throw new Error('Can not access image data');\n            }\n        }\n        else if (isImageDataEle) {\n            // ImageData - image object - format is RGBA by default\n            const format = 'RGBA';\n            let height;\n            let width;\n            if (options !== undefined && options.resizedWidth !== undefined && options.resizedHeight !== undefined) {\n                height = options.resizedHeight;\n                width = options.resizedWidth;\n            }\n            else {\n                height = image.height;\n                width = image.width;\n            }\n            if (options !== undefined) {\n                tensorConfig = options;\n                if (options.bitmapFormat !== undefined && options.bitmapFormat !== format) {\n                    throw new Error('Image input config format must be RGBA for ImageData');\n                }\n                else {\n                    tensorConfig.bitmapFormat = 'RGBA';\n                }\n            }\n            else {\n                tensorConfig.bitmapFormat = 'RGBA';\n            }\n            tensorConfig.height = height;\n            tensorConfig.width = width;\n            if (options !== undefined) {\n                const tempCanvas = document.createElement('canvas');\n                tempCanvas.width = width;\n                tempCanvas.height = height;\n                const pixels2DContext = tempCanvas.getContext('2d');\n                if (pixels2DContext != null) {\n                    pixels2DContext.putImageData(image, 0, 0);\n                    data = pixels2DContext.getImageData(0, 0, width, height).data;\n                }\n                else {\n                    throw new Error('Can not access image data');\n                }\n            }\n            else {\n                data = image.data;\n            }\n        }\n        else if (isImageBitmap) {\n            // ImageBitmap - image object - format must be provided by user\n            if (options === undefined) {\n                throw new Error('Please provide image config with format for Imagebitmap');\n            }\n            if (options.bitmapFormat !== undefined) {\n                throw new Error('Image input config format must be defined for ImageBitmap');\n            }\n            const pixels2DContext = document.createElement('canvas').getContext('2d');\n            if (pixels2DContext != null) {\n                const height = image.height;\n                const width = image.width;\n                pixels2DContext.drawImage(image, 0, 0, width, height);\n                data = pixels2DContext.getImageData(0, 0, width, height).data;\n                if (options !== undefined) {\n                    // using square brackets to avoid TS error - type 'never'\n                    if (options.height !== undefined && options.height !== height) {\n                        throw new Error('Image input config height doesn\\'t match ImageBitmap height');\n                    }\n                    else {\n                        tensorConfig.height = height;\n                    }\n                    // using square brackets to avoid TS error - type 'never'\n                    if (options.width !== undefined && options.width !== width) {\n                        throw new Error('Image input config width doesn\\'t match ImageBitmap width');\n                    }\n                    else {\n                        tensorConfig.width = width;\n                    }\n                }\n                else {\n                    tensorConfig.height = height;\n                    tensorConfig.width = width;\n                }\n                return Tensor.bufferToTensor(data, tensorConfig);\n            }\n            else {\n                throw new Error('Can not access image data');\n            }\n        }\n        else if (isURL) {\n            return new Promise((resolve, reject) => {\n                const canvas = document.createElement('canvas');\n                const context = canvas.getContext('2d');\n                if (!image || !context) {\n                    return reject();\n                }\n                const newImage = new Image();\n                newImage.crossOrigin = 'Anonymous';\n                newImage.src = image;\n                newImage.onload = () => {\n                    canvas.width = newImage.width;\n                    canvas.height = newImage.height;\n                    context.drawImage(newImage, 0, 0, canvas.width, canvas.height);\n                    const img = context.getImageData(0, 0, canvas.width, canvas.height);\n                    if (options !== undefined) {\n                        // using square brackets to avoid TS error - type 'never'\n                        if (options.height !== undefined && options.height !== canvas.height) {\n                            throw new Error('Image input config height doesn\\'t match ImageBitmap height');\n                        }\n                        else {\n                            tensorConfig.height = canvas.height;\n                        }\n                        // using square brackets to avoid TS error - type 'never'\n                        if (options.width !== undefined && options.width !== canvas.width) {\n                            throw new Error('Image input config width doesn\\'t match ImageBitmap width');\n                        }\n                        else {\n                            tensorConfig.width = canvas.width;\n                        }\n                    }\n                    else {\n                        tensorConfig.height = canvas.height;\n                        tensorConfig.width = canvas.width;\n                    }\n                    resolve(Tensor.bufferToTensor(img.data, tensorConfig));\n                };\n            });\n        }\n        else {\n            throw new Error('Input data provided is not supported - aborted tensor creation');\n        }\n        if (data !== undefined) {\n            return Tensor.bufferToTensor(data, tensorConfig);\n        }\n        else {\n            throw new Error('Input data provided is not supported - aborted tensor creation');\n        }\n    }\n    toImageData(options) {\n        var _a, _b;\n        const pixels2DContext = document.createElement('canvas').getContext('2d');\n        let image;\n        if (pixels2DContext != null) {\n            // Default values for height and width & format\n            const width = this.dims[3];\n            const height = this.dims[2];\n            const channels = this.dims[1];\n            const inputformat = options !== undefined ? (options.format !== undefined ? options.format : 'RGB') : 'RGB';\n            const normMean = options !== undefined ? (((_a = options.norm) === null || _a === void 0 ? void 0 : _a.mean) !== undefined ? options.norm.mean : 255) : 255;\n            const normBias = options !== undefined ? (((_b = options.norm) === null || _b === void 0 ? void 0 : _b.bias) !== undefined ? options.norm.bias : 0) : 0;\n            const offset = height * width;\n            if (options !== undefined) {\n                if (options.height !== undefined && options.height !== height) {\n                    throw new Error('Image output config height doesn\\'t match tensor height');\n                }\n                if (options.width !== undefined && options.width !== width) {\n                    throw new Error('Image output config width doesn\\'t match tensor width');\n                }\n                if (options.format !== undefined && (channels === 4 && options.format !== 'RGBA') ||\n                    (channels === 3 && (options.format !== 'RGB' && options.format !== 'BGR'))) {\n                    throw new Error('Tensor format doesn\\'t match input tensor dims');\n                }\n            }\n            // Default pointer assignments\n            const step = 4;\n            let rImagePointer = 0, gImagePointer = 1, bImagePointer = 2, aImagePointer = 3;\n            let rTensorPointer = 0, gTensorPointer = offset, bTensorPointer = offset * 2, aTensorPointer = -1;\n            // Updating the pointer assignments based on the input image format\n            if (inputformat === 'RGBA') {\n                rTensorPointer = 0;\n                gTensorPointer = offset;\n                bTensorPointer = offset * 2;\n                aTensorPointer = offset * 3;\n            }\n            else if (inputformat === 'RGB') {\n                rTensorPointer = 0;\n                gTensorPointer = offset;\n                bTensorPointer = offset * 2;\n            }\n            else if (inputformat === 'RBG') {\n                rTensorPointer = 0;\n                bTensorPointer = offset;\n                gTensorPointer = offset * 2;\n            }\n            image = pixels2DContext.createImageData(width, height);\n            for (let i = 0; i < height * width; rImagePointer += step, gImagePointer += step, bImagePointer += step, aImagePointer += step, i++) {\n                image.data[rImagePointer] = (this.data[rTensorPointer++] - normBias) * normMean; // R value\n                image.data[gImagePointer] = (this.data[gTensorPointer++] - normBias) * normMean; // G value\n                image.data[bImagePointer] = (this.data[bTensorPointer++] - normBias) * normMean; // B value\n                image.data[aImagePointer] =\n                    aTensorPointer === -1 ? 255 : (this.data[aTensorPointer++] - normBias) * normMean; // A value\n            }\n        }\n        else {\n            throw new Error('Can not access image data');\n        }\n        return image;\n    }\n    // #endregion\n    // #region tensor utilities\n    reshape(dims) {\n        return new Tensor(this.type, this.data, dims);\n    }\n}\n//# sourceMappingURL=tensor-impl.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/tensor-impl.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/tensor.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/tensor.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tensor: () => (/* binding */ Tensor)\n/* harmony export */ });\n/* harmony import */ var _tensor_impl__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tensor-impl */ \"(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/tensor-impl.js\");\n// Copyright (c) Microsoft Corporation. All rights reserved.\n// Licensed under the MIT License.\n\n// eslint-disable-next-line @typescript-eslint/naming-convention\nconst Tensor = _tensor_impl__WEBPACK_IMPORTED_MODULE_0__.Tensor;\n//# sourceMappingURL=tensor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vb25ueHJ1bnRpbWUtY29tbW9uQDEuMTQuMC9ub2RlX21vZHVsZXMvb25ueHJ1bnRpbWUtY29tbW9uL2Rpc3QvbGliL3RlbnNvci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDcUQ7QUFDckQ7QUFDTyxlQUFlLGdEQUFVO0FBQ2hDIiwic291cmNlcyI6WyJEOlxcTExNXFxMZWFybmluZ1xcYWlfY29kaW5nXFxhbnl0aGluZ2NoYXRcXGxpdmVjaGF0XFxub2RlX21vZHVsZXNcXC5wbnBtXFxvbm54cnVudGltZS1jb21tb25AMS4xNC4wXFxub2RlX21vZHVsZXNcXG9ubnhydW50aW1lLWNvbW1vblxcZGlzdFxcbGliXFx0ZW5zb3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uIEFsbCByaWdodHMgcmVzZXJ2ZWQuXG4vLyBMaWNlbnNlZCB1bmRlciB0aGUgTUlUIExpY2Vuc2UuXG5pbXBvcnQgeyBUZW5zb3IgYXMgVGVuc29ySW1wbCB9IGZyb20gJy4vdGVuc29yLWltcGwnO1xuLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uYW1pbmctY29udmVudGlvblxuZXhwb3J0IGNvbnN0IFRlbnNvciA9IFRlbnNvckltcGw7XG4vLyMgc291cmNlTWFwcGluZ1VSTD10ZW5zb3IuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/onnxruntime-common@1.14.0/node_modules/onnxruntime-common/dist/lib/tensor.js\n");

/***/ })

};
;