"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@formatjs+icu-skeleton-parser@1.8.5";
exports.ids = ["vendor-chunks/@formatjs+icu-skeleton-parser@1.8.5"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* binding */ parseDateTimeSkeleton)\n/* harmony export */ });\n/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nfunction parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/index.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/index.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* reexport safe */ _date_time__WEBPACK_IMPORTED_MODULE_0__.parseDateTimeSkeleton),\n/* harmony export */   parseNumberSkeleton: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var _date_time__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./date-time */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\");\n/* harmony import */ var _number__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./number */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/number.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1za2VsZXRvbi1wYXJzZXJAMS44LjUvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3Utc2tlbGV0b24tcGFyc2VyL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE0QjtBQUNIIiwic291cmNlcyI6WyJEOlxcTExNXFxMZWFybmluZ1xcYWlfY29kaW5nXFxhbnl0aGluZ2NoYXRcXGxpdmVjaGF0XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAZm9ybWF0anMraWN1LXNrZWxldG9uLXBhcnNlckAxLjguNVxcbm9kZV9tb2R1bGVzXFxAZm9ybWF0anNcXGljdS1za2VsZXRvbi1wYXJzZXJcXGxpYlxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9kYXRlLXRpbWUnO1xuZXhwb3J0ICogZnJvbSAnLi9udW1iZXInO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/number.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/number.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseNumberSkeleton: () => (/* binding */ parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* binding */ parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(ssr)/./node_modules/.pnpm/tslib@2.8.0/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.generated */ \"(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\");\n\n\nfunction parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(_regex_generated__WEBPACK_IMPORTED_MODULE_0__.WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nfunction parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1za2VsZXRvbi1wYXJzZXJAMS44LjUvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3Utc2tlbGV0b24tcGFyc2VyL2xpYi9udW1iZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFpQztBQUNxQjtBQUMvQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLCtEQUFpQjtBQUNoQywrQkFBK0Isc0JBQXNCO0FBQ3JEO0FBQ0Esb0RBQW9ELDRCQUE0QjtBQUNoRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4Q0FBOEMsdUJBQXVCO0FBQ3JFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsOEJBQThCO0FBQ3BEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSx3Q0FBd0Msc0JBQXNCO0FBQzlEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUIsK0NBQVEsQ0FBQywrQ0FBUSxDQUFDLCtDQUFRLEdBQUcsYUFBYSx3QkFBd0IsOENBQThDLFFBQVEsK0NBQVEsQ0FBQywrQ0FBUSxHQUFHLHNDQUFzQyxJQUFJO0FBQy9NO0FBQ0E7QUFDQSx5QkFBeUIsK0NBQVEsQ0FBQywrQ0FBUSxDQUFDLCtDQUFRLEdBQUcsYUFBYSx5QkFBeUIsOENBQThDLFFBQVEsK0NBQVEsQ0FBQywrQ0FBUSxHQUFHLHNDQUFzQyxJQUFJO0FBQ2hOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLCtDQUFRLENBQUMsK0NBQVEsR0FBRyxhQUFhLHVDQUF1QztBQUNqRztBQUNBO0FBQ0EseUJBQXlCLCtDQUFRLENBQUMsK0NBQVEsR0FBRztBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLCtDQUFRLENBQUMsK0NBQVEsR0FBRztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQiwrQ0FBUSxDQUFDLCtDQUFRLEdBQUc7QUFDekM7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLCtDQUFRLENBQUMsK0NBQVEsR0FBRztBQUN6QztBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiRDpcXExMTVxcTGVhcm5pbmdcXGFpX2NvZGluZ1xcYW55dGhpbmdjaGF0XFxsaXZlY2hhdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGZvcm1hdGpzK2ljdS1za2VsZXRvbi1wYXJzZXJAMS44LjVcXG5vZGVfbW9kdWxlc1xcQGZvcm1hdGpzXFxpY3Utc2tlbGV0b24tcGFyc2VyXFxsaWJcXG51bWJlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBfX2Fzc2lnbiB9IGZyb20gXCJ0c2xpYlwiO1xuaW1wb3J0IHsgV0hJVEVfU1BBQ0VfUkVHRVggfSBmcm9tICcuL3JlZ2V4LmdlbmVyYXRlZCc7XG5leHBvcnQgZnVuY3Rpb24gcGFyc2VOdW1iZXJTa2VsZXRvbkZyb21TdHJpbmcoc2tlbGV0b24pIHtcbiAgICBpZiAoc2tlbGV0b24ubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignTnVtYmVyIHNrZWxldG9uIGNhbm5vdCBiZSBlbXB0eScpO1xuICAgIH1cbiAgICAvLyBQYXJzZSB0aGUgc2tlbGV0b25cbiAgICB2YXIgc3RyaW5nVG9rZW5zID0gc2tlbGV0b25cbiAgICAgICAgLnNwbGl0KFdISVRFX1NQQUNFX1JFR0VYKVxuICAgICAgICAuZmlsdGVyKGZ1bmN0aW9uICh4KSB7IHJldHVybiB4Lmxlbmd0aCA+IDA7IH0pO1xuICAgIHZhciB0b2tlbnMgPSBbXTtcbiAgICBmb3IgKHZhciBfaSA9IDAsIHN0cmluZ1Rva2Vuc18xID0gc3RyaW5nVG9rZW5zOyBfaSA8IHN0cmluZ1Rva2Vuc18xLmxlbmd0aDsgX2krKykge1xuICAgICAgICB2YXIgc3RyaW5nVG9rZW4gPSBzdHJpbmdUb2tlbnNfMVtfaV07XG4gICAgICAgIHZhciBzdGVtQW5kT3B0aW9ucyA9IHN0cmluZ1Rva2VuLnNwbGl0KCcvJyk7XG4gICAgICAgIGlmIChzdGVtQW5kT3B0aW9ucy5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignSW52YWxpZCBudW1iZXIgc2tlbGV0b24nKTtcbiAgICAgICAgfVxuICAgICAgICB2YXIgc3RlbSA9IHN0ZW1BbmRPcHRpb25zWzBdLCBvcHRpb25zID0gc3RlbUFuZE9wdGlvbnMuc2xpY2UoMSk7XG4gICAgICAgIGZvciAodmFyIF9hID0gMCwgb3B0aW9uc18xID0gb3B0aW9uczsgX2EgPCBvcHRpb25zXzEubGVuZ3RoOyBfYSsrKSB7XG4gICAgICAgICAgICB2YXIgb3B0aW9uID0gb3B0aW9uc18xW19hXTtcbiAgICAgICAgICAgIGlmIChvcHRpb24ubGVuZ3RoID09PSAwKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIG51bWJlciBza2VsZXRvbicpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIHRva2Vucy5wdXNoKHsgc3RlbTogc3RlbSwgb3B0aW9uczogb3B0aW9ucyB9KTtcbiAgICB9XG4gICAgcmV0dXJuIHRva2Vucztcbn1cbmZ1bmN0aW9uIGljdVVuaXRUb0VjbWEodW5pdCkge1xuICAgIHJldHVybiB1bml0LnJlcGxhY2UoL14oLio/KS0vLCAnJyk7XG59XG52YXIgRlJBQ1RJT05fUFJFQ0lTSU9OX1JFR0VYID0gL15cXC4oPzooMCspKFxcKik/fCgjKyl8KDArKSgjKykpJC9nO1xudmFyIFNJR05JRklDQU5UX1BSRUNJU0lPTl9SRUdFWCA9IC9eKEArKT8oXFwrfCMrKT9bcnNdPyQvZztcbnZhciBJTlRFR0VSX1dJRFRIX1JFR0VYID0gLyhcXCopKDArKXwoIyspKDArKXwoMCspL2c7XG52YXIgQ09OQ0lTRV9JTlRFR0VSX1dJRFRIX1JFR0VYID0gL14oMCspJC87XG5mdW5jdGlvbiBwYXJzZVNpZ25pZmljYW50UHJlY2lzaW9uKHN0cikge1xuICAgIHZhciByZXN1bHQgPSB7fTtcbiAgICBpZiAoc3RyW3N0ci5sZW5ndGggLSAxXSA9PT0gJ3InKSB7XG4gICAgICAgIHJlc3VsdC5yb3VuZGluZ1ByaW9yaXR5ID0gJ21vcmVQcmVjaXNpb24nO1xuICAgIH1cbiAgICBlbHNlIGlmIChzdHJbc3RyLmxlbmd0aCAtIDFdID09PSAncycpIHtcbiAgICAgICAgcmVzdWx0LnJvdW5kaW5nUHJpb3JpdHkgPSAnbGVzc1ByZWNpc2lvbic7XG4gICAgfVxuICAgIHN0ci5yZXBsYWNlKFNJR05JRklDQU5UX1BSRUNJU0lPTl9SRUdFWCwgZnVuY3Rpb24gKF8sIGcxLCBnMikge1xuICAgICAgICAvLyBAQEAgY2FzZVxuICAgICAgICBpZiAodHlwZW9mIGcyICE9PSAnc3RyaW5nJykge1xuICAgICAgICAgICAgcmVzdWx0Lm1pbmltdW1TaWduaWZpY2FudERpZ2l0cyA9IGcxLmxlbmd0aDtcbiAgICAgICAgICAgIHJlc3VsdC5tYXhpbXVtU2lnbmlmaWNhbnREaWdpdHMgPSBnMS5sZW5ndGg7XG4gICAgICAgIH1cbiAgICAgICAgLy8gQEBAKyBjYXNlXG4gICAgICAgIGVsc2UgaWYgKGcyID09PSAnKycpIHtcbiAgICAgICAgICAgIHJlc3VsdC5taW5pbXVtU2lnbmlmaWNhbnREaWdpdHMgPSBnMS5sZW5ndGg7XG4gICAgICAgIH1cbiAgICAgICAgLy8gLiMjIyBjYXNlXG4gICAgICAgIGVsc2UgaWYgKGcxWzBdID09PSAnIycpIHtcbiAgICAgICAgICAgIHJlc3VsdC5tYXhpbXVtU2lnbmlmaWNhbnREaWdpdHMgPSBnMS5sZW5ndGg7XG4gICAgICAgIH1cbiAgICAgICAgLy8gLkBAIyMgb3IgLkBAQCBjYXNlXG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmVzdWx0Lm1pbmltdW1TaWduaWZpY2FudERpZ2l0cyA9IGcxLmxlbmd0aDtcbiAgICAgICAgICAgIHJlc3VsdC5tYXhpbXVtU2lnbmlmaWNhbnREaWdpdHMgPVxuICAgICAgICAgICAgICAgIGcxLmxlbmd0aCArICh0eXBlb2YgZzIgPT09ICdzdHJpbmcnID8gZzIubGVuZ3RoIDogMCk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuICcnO1xuICAgIH0pO1xuICAgIHJldHVybiByZXN1bHQ7XG59XG5mdW5jdGlvbiBwYXJzZVNpZ24oc3RyKSB7XG4gICAgc3dpdGNoIChzdHIpIHtcbiAgICAgICAgY2FzZSAnc2lnbi1hdXRvJzpcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc2lnbkRpc3BsYXk6ICdhdXRvJyxcbiAgICAgICAgICAgIH07XG4gICAgICAgIGNhc2UgJ3NpZ24tYWNjb3VudGluZyc6XG4gICAgICAgIGNhc2UgJygpJzpcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgY3VycmVuY3lTaWduOiAnYWNjb3VudGluZycsXG4gICAgICAgICAgICB9O1xuICAgICAgICBjYXNlICdzaWduLWFsd2F5cyc6XG4gICAgICAgIGNhc2UgJyshJzpcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc2lnbkRpc3BsYXk6ICdhbHdheXMnLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgY2FzZSAnc2lnbi1hY2NvdW50aW5nLWFsd2F5cyc6XG4gICAgICAgIGNhc2UgJygpISc6XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHNpZ25EaXNwbGF5OiAnYWx3YXlzJyxcbiAgICAgICAgICAgICAgICBjdXJyZW5jeVNpZ246ICdhY2NvdW50aW5nJyxcbiAgICAgICAgICAgIH07XG4gICAgICAgIGNhc2UgJ3NpZ24tZXhjZXB0LXplcm8nOlxuICAgICAgICBjYXNlICcrPyc6XG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIHNpZ25EaXNwbGF5OiAnZXhjZXB0WmVybycsXG4gICAgICAgICAgICB9O1xuICAgICAgICBjYXNlICdzaWduLWFjY291bnRpbmctZXhjZXB0LXplcm8nOlxuICAgICAgICBjYXNlICcoKT8nOlxuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICBzaWduRGlzcGxheTogJ2V4Y2VwdFplcm8nLFxuICAgICAgICAgICAgICAgIGN1cnJlbmN5U2lnbjogJ2FjY291bnRpbmcnLFxuICAgICAgICAgICAgfTtcbiAgICAgICAgY2FzZSAnc2lnbi1uZXZlcic6XG4gICAgICAgIGNhc2UgJytfJzpcbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgc2lnbkRpc3BsYXk6ICduZXZlcicsXG4gICAgICAgICAgICB9O1xuICAgIH1cbn1cbmZ1bmN0aW9uIHBhcnNlQ29uY2lzZVNjaWVudGlmaWNBbmRFbmdpbmVlcmluZ1N0ZW0oc3RlbSkge1xuICAgIC8vIEVuZ2luZWVyaW5nXG4gICAgdmFyIHJlc3VsdDtcbiAgICBpZiAoc3RlbVswXSA9PT0gJ0UnICYmIHN0ZW1bMV0gPT09ICdFJykge1xuICAgICAgICByZXN1bHQgPSB7XG4gICAgICAgICAgICBub3RhdGlvbjogJ2VuZ2luZWVyaW5nJyxcbiAgICAgICAgfTtcbiAgICAgICAgc3RlbSA9IHN0ZW0uc2xpY2UoMik7XG4gICAgfVxuICAgIGVsc2UgaWYgKHN0ZW1bMF0gPT09ICdFJykge1xuICAgICAgICByZXN1bHQgPSB7XG4gICAgICAgICAgICBub3RhdGlvbjogJ3NjaWVudGlmaWMnLFxuICAgICAgICB9O1xuICAgICAgICBzdGVtID0gc3RlbS5zbGljZSgxKTtcbiAgICB9XG4gICAgaWYgKHJlc3VsdCkge1xuICAgICAgICB2YXIgc2lnbkRpc3BsYXkgPSBzdGVtLnNsaWNlKDAsIDIpO1xuICAgICAgICBpZiAoc2lnbkRpc3BsYXkgPT09ICcrIScpIHtcbiAgICAgICAgICAgIHJlc3VsdC5zaWduRGlzcGxheSA9ICdhbHdheXMnO1xuICAgICAgICAgICAgc3RlbSA9IHN0ZW0uc2xpY2UoMik7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoc2lnbkRpc3BsYXkgPT09ICcrPycpIHtcbiAgICAgICAgICAgIHJlc3VsdC5zaWduRGlzcGxheSA9ICdleGNlcHRaZXJvJztcbiAgICAgICAgICAgIHN0ZW0gPSBzdGVtLnNsaWNlKDIpO1xuICAgICAgICB9XG4gICAgICAgIGlmICghQ09OQ0lTRV9JTlRFR0VSX1dJRFRIX1JFR0VYLnRlc3Qoc3RlbSkpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignTWFsZm9ybWVkIGNvbmNpc2UgZW5nL3NjaWVudGlmaWMgbm90YXRpb24nKTtcbiAgICAgICAgfVxuICAgICAgICByZXN1bHQubWluaW11bUludGVnZXJEaWdpdHMgPSBzdGVtLmxlbmd0aDtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbn1cbmZ1bmN0aW9uIHBhcnNlTm90YXRpb25PcHRpb25zKG9wdCkge1xuICAgIHZhciByZXN1bHQgPSB7fTtcbiAgICB2YXIgc2lnbk9wdHMgPSBwYXJzZVNpZ24ob3B0KTtcbiAgICBpZiAoc2lnbk9wdHMpIHtcbiAgICAgICAgcmV0dXJuIHNpZ25PcHRzO1xuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xufVxuLyoqXG4gKiBodHRwczovL2dpdGh1Yi5jb20vdW5pY29kZS1vcmcvaWN1L2Jsb2IvbWFzdGVyL2RvY3MvdXNlcmd1aWRlL2Zvcm1hdF9wYXJzZS9udW1iZXJzL3NrZWxldG9ucy5tZCNza2VsZXRvbi1zdGVtcy1hbmQtb3B0aW9uc1xuICovXG5leHBvcnQgZnVuY3Rpb24gcGFyc2VOdW1iZXJTa2VsZXRvbih0b2tlbnMpIHtcbiAgICB2YXIgcmVzdWx0ID0ge307XG4gICAgZm9yICh2YXIgX2kgPSAwLCB0b2tlbnNfMSA9IHRva2VuczsgX2kgPCB0b2tlbnNfMS5sZW5ndGg7IF9pKyspIHtcbiAgICAgICAgdmFyIHRva2VuID0gdG9rZW5zXzFbX2ldO1xuICAgICAgICBzd2l0Y2ggKHRva2VuLnN0ZW0pIHtcbiAgICAgICAgICAgIGNhc2UgJ3BlcmNlbnQnOlxuICAgICAgICAgICAgY2FzZSAnJSc6XG4gICAgICAgICAgICAgICAgcmVzdWx0LnN0eWxlID0gJ3BlcmNlbnQnO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY2FzZSAnJXgxMDAnOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5zdHlsZSA9ICdwZXJjZW50JztcbiAgICAgICAgICAgICAgICByZXN1bHQuc2NhbGUgPSAxMDA7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjYXNlICdjdXJyZW5jeSc6XG4gICAgICAgICAgICAgICAgcmVzdWx0LnN0eWxlID0gJ2N1cnJlbmN5JztcbiAgICAgICAgICAgICAgICByZXN1bHQuY3VycmVuY3kgPSB0b2tlbi5vcHRpb25zWzBdO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY2FzZSAnZ3JvdXAtb2ZmJzpcbiAgICAgICAgICAgIGNhc2UgJyxfJzpcbiAgICAgICAgICAgICAgICByZXN1bHQudXNlR3JvdXBpbmcgPSBmYWxzZTtcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIGNhc2UgJ3ByZWNpc2lvbi1pbnRlZ2VyJzpcbiAgICAgICAgICAgIGNhc2UgJy4nOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5tYXhpbXVtRnJhY3Rpb25EaWdpdHMgPSAwO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY2FzZSAnbWVhc3VyZS11bml0JzpcbiAgICAgICAgICAgIGNhc2UgJ3VuaXQnOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5zdHlsZSA9ICd1bml0JztcbiAgICAgICAgICAgICAgICByZXN1bHQudW5pdCA9IGljdVVuaXRUb0VjbWEodG9rZW4ub3B0aW9uc1swXSk7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjYXNlICdjb21wYWN0LXNob3J0JzpcbiAgICAgICAgICAgIGNhc2UgJ0snOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5ub3RhdGlvbiA9ICdjb21wYWN0JztcbiAgICAgICAgICAgICAgICByZXN1bHQuY29tcGFjdERpc3BsYXkgPSAnc2hvcnQnO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY2FzZSAnY29tcGFjdC1sb25nJzpcbiAgICAgICAgICAgIGNhc2UgJ0tLJzpcbiAgICAgICAgICAgICAgICByZXN1bHQubm90YXRpb24gPSAnY29tcGFjdCc7XG4gICAgICAgICAgICAgICAgcmVzdWx0LmNvbXBhY3REaXNwbGF5ID0gJ2xvbmcnO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY2FzZSAnc2NpZW50aWZpYyc6XG4gICAgICAgICAgICAgICAgcmVzdWx0ID0gX19hc3NpZ24oX19hc3NpZ24oX19hc3NpZ24oe30sIHJlc3VsdCksIHsgbm90YXRpb246ICdzY2llbnRpZmljJyB9KSwgdG9rZW4ub3B0aW9ucy5yZWR1Y2UoZnVuY3Rpb24gKGFsbCwgb3B0KSB7IHJldHVybiAoX19hc3NpZ24oX19hc3NpZ24oe30sIGFsbCksIHBhcnNlTm90YXRpb25PcHRpb25zKG9wdCkpKTsgfSwge30pKTtcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIGNhc2UgJ2VuZ2luZWVyaW5nJzpcbiAgICAgICAgICAgICAgICByZXN1bHQgPSBfX2Fzc2lnbihfX2Fzc2lnbihfX2Fzc2lnbih7fSwgcmVzdWx0KSwgeyBub3RhdGlvbjogJ2VuZ2luZWVyaW5nJyB9KSwgdG9rZW4ub3B0aW9ucy5yZWR1Y2UoZnVuY3Rpb24gKGFsbCwgb3B0KSB7IHJldHVybiAoX19hc3NpZ24oX19hc3NpZ24oe30sIGFsbCksIHBhcnNlTm90YXRpb25PcHRpb25zKG9wdCkpKTsgfSwge30pKTtcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIGNhc2UgJ25vdGF0aW9uLXNpbXBsZSc6XG4gICAgICAgICAgICAgICAgcmVzdWx0Lm5vdGF0aW9uID0gJ3N0YW5kYXJkJztcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS91bmljb2RlLW9yZy9pY3UvYmxvYi9tYXN0ZXIvaWN1NGMvc291cmNlL2kxOG4vdW5pY29kZS91bnVtYmVyZm9ybWF0dGVyLmhcbiAgICAgICAgICAgIGNhc2UgJ3VuaXQtd2lkdGgtbmFycm93JzpcbiAgICAgICAgICAgICAgICByZXN1bHQuY3VycmVuY3lEaXNwbGF5ID0gJ25hcnJvd1N5bWJvbCc7XG4gICAgICAgICAgICAgICAgcmVzdWx0LnVuaXREaXNwbGF5ID0gJ25hcnJvdyc7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjYXNlICd1bml0LXdpZHRoLXNob3J0JzpcbiAgICAgICAgICAgICAgICByZXN1bHQuY3VycmVuY3lEaXNwbGF5ID0gJ2NvZGUnO1xuICAgICAgICAgICAgICAgIHJlc3VsdC51bml0RGlzcGxheSA9ICdzaG9ydCc7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjYXNlICd1bml0LXdpZHRoLWZ1bGwtbmFtZSc6XG4gICAgICAgICAgICAgICAgcmVzdWx0LmN1cnJlbmN5RGlzcGxheSA9ICduYW1lJztcbiAgICAgICAgICAgICAgICByZXN1bHQudW5pdERpc3BsYXkgPSAnbG9uZyc7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjYXNlICd1bml0LXdpZHRoLWlzby1jb2RlJzpcbiAgICAgICAgICAgICAgICByZXN1bHQuY3VycmVuY3lEaXNwbGF5ID0gJ3N5bWJvbCc7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjYXNlICdzY2FsZSc6XG4gICAgICAgICAgICAgICAgcmVzdWx0LnNjYWxlID0gcGFyc2VGbG9hdCh0b2tlbi5vcHRpb25zWzBdKTtcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIGNhc2UgJ3JvdW5kaW5nLW1vZGUtZmxvb3InOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5yb3VuZGluZ01vZGUgPSAnZmxvb3InO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY2FzZSAncm91bmRpbmctbW9kZS1jZWlsaW5nJzpcbiAgICAgICAgICAgICAgICByZXN1bHQucm91bmRpbmdNb2RlID0gJ2NlaWwnO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgY2FzZSAncm91bmRpbmctbW9kZS1kb3duJzpcbiAgICAgICAgICAgICAgICByZXN1bHQucm91bmRpbmdNb2RlID0gJ3RydW5jJztcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIGNhc2UgJ3JvdW5kaW5nLW1vZGUtdXAnOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5yb3VuZGluZ01vZGUgPSAnZXhwYW5kJztcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIGNhc2UgJ3JvdW5kaW5nLW1vZGUtaGFsZi1ldmVuJzpcbiAgICAgICAgICAgICAgICByZXN1bHQucm91bmRpbmdNb2RlID0gJ2hhbGZFdmVuJztcbiAgICAgICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgICAgIGNhc2UgJ3JvdW5kaW5nLW1vZGUtaGFsZi1kb3duJzpcbiAgICAgICAgICAgICAgICByZXN1bHQucm91bmRpbmdNb2RlID0gJ2hhbGZUcnVuYyc7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICBjYXNlICdyb3VuZGluZy1tb2RlLWhhbGYtdXAnOlxuICAgICAgICAgICAgICAgIHJlc3VsdC5yb3VuZGluZ01vZGUgPSAnaGFsZkV4cGFuZCc7XG4gICAgICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgICAgICAvLyBodHRwczovL3VuaWNvZGUtb3JnLmdpdGh1Yi5pby9pY3UvdXNlcmd1aWRlL2Zvcm1hdF9wYXJzZS9udW1iZXJzL3NrZWxldG9ucy5odG1sI2ludGVnZXItd2lkdGhcbiAgICAgICAgICAgIGNhc2UgJ2ludGVnZXItd2lkdGgnOlxuICAgICAgICAgICAgICAgIGlmICh0b2tlbi5vcHRpb25zLmxlbmd0aCA+IDEpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoJ2ludGVnZXItd2lkdGggc3RlbXMgb25seSBhY2NlcHQgYSBzaW5nbGUgb3B0aW9uYWwgb3B0aW9uJyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIHRva2VuLm9wdGlvbnNbMF0ucmVwbGFjZShJTlRFR0VSX1dJRFRIX1JFR0VYLCBmdW5jdGlvbiAoXywgZzEsIGcyLCBnMywgZzQsIGc1KSB7XG4gICAgICAgICAgICAgICAgICAgIGlmIChnMSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmVzdWx0Lm1pbmltdW1JbnRlZ2VyRGlnaXRzID0gZzIubGVuZ3RoO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGVsc2UgaWYgKGczICYmIGc0KSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1dlIGN1cnJlbnRseSBkbyBub3Qgc3VwcG9ydCBtYXhpbXVtIGludGVnZXIgZGlnaXRzJyk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAoZzUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignV2UgY3VycmVudGx5IGRvIG5vdCBzdXBwb3J0IGV4YWN0IGludGVnZXIgZGlnaXRzJyk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuICcnO1xuICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIC8vIGh0dHBzOi8vdW5pY29kZS1vcmcuZ2l0aHViLmlvL2ljdS91c2VyZ3VpZGUvZm9ybWF0X3BhcnNlL251bWJlcnMvc2tlbGV0b25zLmh0bWwjaW50ZWdlci13aWR0aFxuICAgICAgICBpZiAoQ09OQ0lTRV9JTlRFR0VSX1dJRFRIX1JFR0VYLnRlc3QodG9rZW4uc3RlbSkpIHtcbiAgICAgICAgICAgIHJlc3VsdC5taW5pbXVtSW50ZWdlckRpZ2l0cyA9IHRva2VuLnN0ZW0ubGVuZ3RoO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKEZSQUNUSU9OX1BSRUNJU0lPTl9SRUdFWC50ZXN0KHRva2VuLnN0ZW0pKSB7XG4gICAgICAgICAgICAvLyBQcmVjaXNpb25cbiAgICAgICAgICAgIC8vIGh0dHBzOi8vdW5pY29kZS1vcmcuZ2l0aHViLmlvL2ljdS91c2VyZ3VpZGUvZm9ybWF0X3BhcnNlL251bWJlcnMvc2tlbGV0b25zLmh0bWwjZnJhY3Rpb24tcHJlY2lzaW9uXG4gICAgICAgICAgICAvLyBwcmVjaXNpb24taW50ZWdlciBjYXNlXG4gICAgICAgICAgICBpZiAodG9rZW4ub3B0aW9ucy5sZW5ndGggPiAxKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IFJhbmdlRXJyb3IoJ0ZyYWN0aW9uLXByZWNpc2lvbiBzdGVtcyBvbmx5IGFjY2VwdCBhIHNpbmdsZSBvcHRpb25hbCBvcHRpb24nKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRva2VuLnN0ZW0ucmVwbGFjZShGUkFDVElPTl9QUkVDSVNJT05fUkVHRVgsIGZ1bmN0aW9uIChfLCBnMSwgZzIsIGczLCBnNCwgZzUpIHtcbiAgICAgICAgICAgICAgICAvLyAuMDAwKiBjYXNlIChiZWZvcmUgSUNVNjcgaXQgd2FzIC4wMDArKVxuICAgICAgICAgICAgICAgIGlmIChnMiA9PT0gJyonKSB7XG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdC5taW5pbXVtRnJhY3Rpb25EaWdpdHMgPSBnMS5sZW5ndGg7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIC8vIC4jIyMgY2FzZVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGczICYmIGczWzBdID09PSAnIycpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0Lm1heGltdW1GcmFjdGlvbkRpZ2l0cyA9IGczLmxlbmd0aDtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgLy8gLjAwIyMgY2FzZVxuICAgICAgICAgICAgICAgIGVsc2UgaWYgKGc0ICYmIGc1KSB7XG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdC5taW5pbXVtRnJhY3Rpb25EaWdpdHMgPSBnNC5sZW5ndGg7XG4gICAgICAgICAgICAgICAgICAgIHJlc3VsdC5tYXhpbXVtRnJhY3Rpb25EaWdpdHMgPSBnNC5sZW5ndGggKyBnNS5sZW5ndGg7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICByZXN1bHQubWluaW11bUZyYWN0aW9uRGlnaXRzID0gZzEubGVuZ3RoO1xuICAgICAgICAgICAgICAgICAgICByZXN1bHQubWF4aW11bUZyYWN0aW9uRGlnaXRzID0gZzEubGVuZ3RoO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICByZXR1cm4gJyc7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHZhciBvcHQgPSB0b2tlbi5vcHRpb25zWzBdO1xuICAgICAgICAgICAgLy8gaHR0cHM6Ly91bmljb2RlLW9yZy5naXRodWIuaW8vaWN1L3VzZXJndWlkZS9mb3JtYXRfcGFyc2UvbnVtYmVycy9za2VsZXRvbnMuaHRtbCN0cmFpbGluZy16ZXJvLWRpc3BsYXlcbiAgICAgICAgICAgIGlmIChvcHQgPT09ICd3Jykge1xuICAgICAgICAgICAgICAgIHJlc3VsdCA9IF9fYXNzaWduKF9fYXNzaWduKHt9LCByZXN1bHQpLCB7IHRyYWlsaW5nWmVyb0Rpc3BsYXk6ICdzdHJpcElmSW50ZWdlcicgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChvcHQpIHtcbiAgICAgICAgICAgICAgICByZXN1bHQgPSBfX2Fzc2lnbihfX2Fzc2lnbih7fSwgcmVzdWx0KSwgcGFyc2VTaWduaWZpY2FudFByZWNpc2lvbihvcHQpKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIC8vIGh0dHBzOi8vdW5pY29kZS1vcmcuZ2l0aHViLmlvL2ljdS91c2VyZ3VpZGUvZm9ybWF0X3BhcnNlL251bWJlcnMvc2tlbGV0b25zLmh0bWwjc2lnbmlmaWNhbnQtZGlnaXRzLXByZWNpc2lvblxuICAgICAgICBpZiAoU0lHTklGSUNBTlRfUFJFQ0lTSU9OX1JFR0VYLnRlc3QodG9rZW4uc3RlbSkpIHtcbiAgICAgICAgICAgIHJlc3VsdCA9IF9fYXNzaWduKF9fYXNzaWduKHt9LCByZXN1bHQpLCBwYXJzZVNpZ25pZmljYW50UHJlY2lzaW9uKHRva2VuLnN0ZW0pKTtcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIHZhciBzaWduT3B0cyA9IHBhcnNlU2lnbih0b2tlbi5zdGVtKTtcbiAgICAgICAgaWYgKHNpZ25PcHRzKSB7XG4gICAgICAgICAgICByZXN1bHQgPSBfX2Fzc2lnbihfX2Fzc2lnbih7fSwgcmVzdWx0KSwgc2lnbk9wdHMpO1xuICAgICAgICB9XG4gICAgICAgIHZhciBjb25jaXNlU2NpZW50aWZpY0FuZEVuZ2luZWVyaW5nT3B0cyA9IHBhcnNlQ29uY2lzZVNjaWVudGlmaWNBbmRFbmdpbmVlcmluZ1N0ZW0odG9rZW4uc3RlbSk7XG4gICAgICAgIGlmIChjb25jaXNlU2NpZW50aWZpY0FuZEVuZ2luZWVyaW5nT3B0cykge1xuICAgICAgICAgICAgcmVzdWx0ID0gX19hc3NpZ24oX19hc3NpZ24oe30sIHJlc3VsdCksIGNvbmNpc2VTY2llbnRpZmljQW5kRW5naW5lZXJpbmdPcHRzKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1za2VsZXRvbi1wYXJzZXJAMS44LjUvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3Utc2tlbGV0b24tcGFyc2VyL2xpYi9yZWdleC5nZW5lcmF0ZWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ08iLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBmb3JtYXRqcytpY3Utc2tlbGV0b24tcGFyc2VyQDEuOC41XFxub2RlX21vZHVsZXNcXEBmb3JtYXRqc1xcaWN1LXNrZWxldG9uLXBhcnNlclxcbGliXFxyZWdleC5nZW5lcmF0ZWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQGdlbmVyYXRlZCBmcm9tIHJlZ2V4LWdlbi50c1xuZXhwb3J0IHZhciBXSElURV9TUEFDRV9SRUdFWCA9IC9bXFx0LVxcciBcXHg4NVxcdTIwMEVcXHUyMDBGXFx1MjAyOFxcdTIwMjldL2k7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js":
/*!****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js ***!
  \****************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* binding */ parseDateTimeSkeleton)\n/* harmony export */ });\n/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nfunction parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/index.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/index.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseDateTimeSkeleton: () => (/* reexport safe */ _date_time__WEBPACK_IMPORTED_MODULE_0__.parseDateTimeSkeleton),\n/* harmony export */   parseNumberSkeleton: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* reexport safe */ _number__WEBPACK_IMPORTED_MODULE_1__.parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var _date_time__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./date-time */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js\");\n/* harmony import */ var _number__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./number */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/number.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1za2VsZXRvbi1wYXJzZXJAMS44LjUvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3Utc2tlbGV0b24tcGFyc2VyL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUE0QjtBQUNIIiwic291cmNlcyI6WyJEOlxcTExNXFxMZWFybmluZ1xcYWlfY29kaW5nXFxhbnl0aGluZ2NoYXRcXGxpdmVjaGF0XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAZm9ybWF0anMraWN1LXNrZWxldG9uLXBhcnNlckAxLjguNVxcbm9kZV9tb2R1bGVzXFxAZm9ybWF0anNcXGljdS1za2VsZXRvbi1wYXJzZXJcXGxpYlxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9kYXRlLXRpbWUnO1xuZXhwb3J0ICogZnJvbSAnLi9udW1iZXInO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/number.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/number.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseNumberSkeleton: () => (/* binding */ parseNumberSkeleton),\n/* harmony export */   parseNumberSkeletonFromString: () => (/* binding */ parseNumberSkeletonFromString)\n/* harmony export */ });\n/* harmony import */ var tslib__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tslib */ \"(rsc)/./node_modules/.pnpm/tslib@2.8.0/node_modules/tslib/tslib.es6.mjs\");\n/* harmony import */ var _regex_generated__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.generated */ \"(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\");\n\n\nfunction parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(_regex_generated__WEBPACK_IMPORTED_MODULE_0__.WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nfunction parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return ((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = (0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)((0,tslib__WEBPACK_IMPORTED_MODULE_1__.__assign)({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/number.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WHITE_SPACE_REGEX: () => (/* binding */ WHITE_SPACE_REGEX)\n/* harmony export */ });\n// @generated from regex-gen.ts\nvar WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGZvcm1hdGpzK2ljdS1za2VsZXRvbi1wYXJzZXJAMS44LjUvbm9kZV9tb2R1bGVzL0Bmb3JtYXRqcy9pY3Utc2tlbGV0b24tcGFyc2VyL2xpYi9yZWdleC5nZW5lcmF0ZWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ08iLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBmb3JtYXRqcytpY3Utc2tlbGV0b24tcGFyc2VyQDEuOC41XFxub2RlX21vZHVsZXNcXEBmb3JtYXRqc1xcaWN1LXNrZWxldG9uLXBhcnNlclxcbGliXFxyZWdleC5nZW5lcmF0ZWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQGdlbmVyYXRlZCBmcm9tIHJlZ2V4LWdlbi50c1xuZXhwb3J0IHZhciBXSElURV9TUEFDRV9SRUdFWCA9IC9bXFx0LVxcciBcXHg4NVxcdTIwMEVcXHUyMDBGXFx1MjAyOFxcdTIwMjldL2k7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@formatjs+icu-skeleton-parser@1.8.5/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js\n");

/***/ })

};
;