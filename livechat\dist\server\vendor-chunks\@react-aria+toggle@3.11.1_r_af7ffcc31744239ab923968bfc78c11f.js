"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+toggle@3.11.1_r_af7ffcc31744239ab923968bfc78c11f";
exports.ids = ["vendor-chunks/@react-aria+toggle@3.11.1_r_af7ffcc31744239ab923968bfc78c11f"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+toggle@3.11.1_r_af7ffcc31744239ab923968bfc78c11f/node_modules/@react-aria/toggle/dist/useToggle.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+toggle@3.11.1_r_af7ffcc31744239ab923968bfc78c11f/node_modules/@react-aria/toggle/dist/useToggle.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useToggle: () => (/* binding */ $d2c8e2b0480f3f34$export$cbe85ee05b554577)\n/* harmony export */ });\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/mergeProps.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/filterDOMProps.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.1_re_6fb8411884eed619bfc87b0d76ebb26f/node_modules/@react-aria/utils/dist/useFormReset.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f2d9528c866cda2b9c509f40adab71db/node_modules/@react-aria/interactions/dist/usePress.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f2d9528c866cda2b9c509f40adab71db/node_modules/@react-aria/interactions/dist/useFocusable.mjs\");\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\nfunction $d2c8e2b0480f3f34$export$cbe85ee05b554577(props, state, ref) {\n    let { isDisabled: isDisabled = false, isReadOnly: isReadOnly = false, value: value, name: name, children: children, 'aria-label': ariaLabel, 'aria-labelledby': ariaLabelledby, validationState: validationState = 'valid', isInvalid: isInvalid } = props;\n    let onChange = (e)=>{\n        // since we spread props on label, onChange will end up there as well as in here.\n        // so we have to stop propagation at the lowest level that we care about\n        e.stopPropagation();\n        state.setSelected(e.target.checked);\n    };\n    let hasChildren = children != null;\n    let hasAriaLabel = ariaLabel != null || ariaLabelledby != null;\n    if (!hasChildren && !hasAriaLabel) console.warn('If you do not provide children, you must specify an aria-label for accessibility');\n    // Handle press state for keyboard interactions and cases where labelProps is not used.\n    let { pressProps: pressProps, isPressed: isPressed } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_0__.usePress)({\n        isDisabled: isDisabled\n    });\n    // Handle press state on the label.\n    let { pressProps: labelProps, isPressed: isLabelPressed } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_0__.usePress)({\n        onPress () {\n            var _ref_current;\n            state.toggle();\n            (_ref_current = ref.current) === null || _ref_current === void 0 ? void 0 : _ref_current.focus();\n        },\n        isDisabled: isDisabled || isReadOnly\n    });\n    let { focusableProps: focusableProps } = (0, _react_aria_interactions__WEBPACK_IMPORTED_MODULE_1__.useFocusable)(props, ref);\n    let interactions = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.mergeProps)(pressProps, focusableProps);\n    let domProps = (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_3__.filterDOMProps)(props, {\n        labelable: true\n    });\n    (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__.useFormReset)(ref, state.isSelected, state.setSelected);\n    return {\n        labelProps: (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.mergeProps)(labelProps, {\n            onClick: (e)=>e.preventDefault()\n        }),\n        inputProps: (0, _react_aria_utils__WEBPACK_IMPORTED_MODULE_2__.mergeProps)(domProps, {\n            'aria-invalid': isInvalid || validationState === 'invalid' || undefined,\n            'aria-errormessage': props['aria-errormessage'],\n            'aria-controls': props['aria-controls'],\n            'aria-readonly': isReadOnly || undefined,\n            onChange: onChange,\n            disabled: isDisabled,\n            ...value == null ? {} : {\n                value: value\n            },\n            name: name,\n            type: 'checkbox',\n            ...interactions\n        }),\n        isSelected: state.isSelected,\n        isPressed: isPressed || isLabelPressed,\n        isDisabled: isDisabled,\n        isReadOnly: isReadOnly,\n        isInvalid: isInvalid || validationState === 'invalid'\n    };\n}\n\n\n\n//# sourceMappingURL=useToggle.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+toggle@3.11.1_r_af7ffcc31744239ab923968bfc78c11f/node_modules/@react-aria/toggle/dist/useToggle.mjs\n");

/***/ })

};
;