"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@heroui+use-aria-button@2.2_7880f9c74c624088c4e8354ca917f6e3";
exports.ids = ["vendor-chunks/@heroui+use-aria-button@2.2_7880f9c74c624088c4e8354ca917f6e3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@heroui+use-aria-button@2.2_7880f9c74c624088c4e8354ca917f6e3/node_modules/@heroui/use-aria-button/dist/index.mjs":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@heroui+use-aria-button@2.2_7880f9c74c624088c4e8354ca917f6e3/node_modules/@heroui/use-aria-button/dist/index.mjs ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAriaButton: () => (/* binding */ useAriaButton)\n/* harmony export */ });\n/* harmony import */ var _heroui_shared_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @heroui/shared-utils */ \"(ssr)/./node_modules/.pnpm/@heroui+shared-utils@2.1.7/node_modules/@heroui/shared-utils/dist/index.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/platform.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/mergeProps.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/filterDOMProps.mjs\");\n/* harmony import */ var _react_aria_focus__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/focus */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f4c4e47fdd38dbbc2317f9013c56c4ca/node_modules/@react-aria/interactions/dist/useFocusable.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f4c4e47fdd38dbbc2317f9013c56c4ca/node_modules/@react-aria/interactions/dist/usePress.mjs\");\n// src/index.ts\n\n\n\n\nfunction useAriaButton(props, ref) {\n  let {\n    elementType = \"button\",\n    isDisabled,\n    onPress,\n    onPressStart,\n    onPressEnd,\n    onPressChange,\n    // @ts-ignore - undocumented\n    preventFocusOnPress,\n    // @ts-ignore - undocumented\n    allowFocusWhenDisabled,\n    // @ts-ignore\n    onClick: deprecatedOnClick,\n    href,\n    target,\n    rel,\n    type = \"button\",\n    allowTextSelectionOnPress,\n    role\n  } = props;\n  let additionalProps;\n  if (elementType === \"button\") {\n    additionalProps = {\n      type,\n      disabled: isDisabled\n    };\n  } else {\n    additionalProps = {\n      role: \"button\",\n      href: elementType === \"a\" && !isDisabled ? href : void 0,\n      target: elementType === \"a\" ? target : void 0,\n      type: elementType === \"input\" ? type : void 0,\n      disabled: elementType === \"input\" ? isDisabled : void 0,\n      \"aria-disabled\": !isDisabled || elementType === \"input\" ? void 0 : isDisabled,\n      rel: elementType === \"a\" ? rel : void 0\n    };\n  }\n  let isMobile = (0,_react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.isIOS)() || (0,_react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.isAndroid)();\n  if (deprecatedOnClick && typeof deprecatedOnClick === \"function\" && // bypass since onClick is passed from <Link as={Button} /> internally\n  role !== \"link\" && // bypass since onClick is passed from useDisclosure's `getButtonProps` internally\n  !(props.hasOwnProperty(\"aria-expanded\") && props.hasOwnProperty(\"aria-controls\"))) {\n    (0,_heroui_shared_utils__WEBPACK_IMPORTED_MODULE_1__.warn)(\n      \"onClick is deprecated, please use onPress instead. See: https://github.com/heroui-inc/heroui/issues/4292\",\n      \"useButton\"\n    );\n  }\n  const handlePress = (e) => {\n    if (isMobile) {\n      deprecatedOnClick == null ? void 0 : deprecatedOnClick(e);\n    }\n    onPress == null ? void 0 : onPress(e);\n  };\n  let { pressProps, isPressed } = (0,_react_aria_interactions__WEBPACK_IMPORTED_MODULE_2__.usePress)({\n    onPressStart,\n    onPressEnd,\n    onPressChange,\n    onPress: handlePress,\n    isDisabled,\n    preventFocusOnPress,\n    allowTextSelectionOnPress,\n    ref\n  });\n  let { focusableProps } = (0,_react_aria_focus__WEBPACK_IMPORTED_MODULE_3__.useFocusable)(props, ref);\n  if (allowFocusWhenDisabled) {\n    focusableProps.tabIndex = isDisabled ? -1 : focusableProps.tabIndex;\n  }\n  let buttonProps = (0,_react_aria_utils__WEBPACK_IMPORTED_MODULE_4__.mergeProps)(\n    focusableProps,\n    pressProps,\n    (0,_react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.filterDOMProps)(props, { labelable: true })\n  );\n  return {\n    isPressed,\n    // Used to indicate press state for visual\n    buttonProps: (0,_react_aria_utils__WEBPACK_IMPORTED_MODULE_4__.mergeProps)(additionalProps, buttonProps, {\n      \"aria-haspopup\": props[\"aria-haspopup\"],\n      \"aria-expanded\": props[\"aria-expanded\"],\n      \"aria-controls\": props[\"aria-controls\"],\n      \"aria-pressed\": props[\"aria-pressed\"],\n      \"aria-current\": props[\"aria-current\"],\n      onClick: (e) => {\n        if (type === \"button\" && isMobile) {\n          return;\n        }\n        deprecatedOnClick == null ? void 0 : deprecatedOnClick(e);\n      }\n    })\n  };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@heroui+use-aria-button@2.2_7880f9c74c624088c4e8354ca917f6e3/node_modules/@heroui/use-aria-button/dist/index.mjs\n");

/***/ })

};
;