"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-util-subtokenize@2.0.4";
exports.ids = ["vendor-chunks/micromark-util-subtokenize@2.0.4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/micromark-util-subtokenize@2.0.4/node_modules/micromark-util-subtokenize/dev/index.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-util-subtokenize@2.0.4/node_modules/micromark-util-subtokenize/dev/index.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpliceBuffer: () => (/* reexport safe */ _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__.SpliceBuffer),\n/* harmony export */   subtokenize: () => (/* binding */ subtokenize)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/.pnpm/devlop@1.1.0/node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_util_chunked__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-chunked */ \"(ssr)/./node_modules/.pnpm/micromark-util-chunked@2.0.1/node_modules/micromark-util-chunked/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/splice-buffer.js */ \"(ssr)/./node_modules/.pnpm/micromark-util-subtokenize@2.0.4/node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js\");\n/**\n * @import {Chunk, Event, Token} from 'micromark-util-types'\n */\n\n\n\n\n\n\n// Hidden API exposed for testing.\n\n\n/**\n * Tokenize subcontent.\n *\n * @param {Array<Event>} eventsArray\n *   List of events.\n * @returns {boolean}\n *   Whether subtokens were found.\n */\n// eslint-disable-next-line complexity\nfunction subtokenize(eventsArray) {\n  /** @type {Record<string, number>} */\n  const jumps = {}\n  let index = -1\n  /** @type {Event} */\n  let event\n  /** @type {number | undefined} */\n  let lineIndex\n  /** @type {number} */\n  let otherIndex\n  /** @type {Event} */\n  let otherEvent\n  /** @type {Array<Event>} */\n  let parameters\n  /** @type {Array<Event>} */\n  let subevents\n  /** @type {boolean | undefined} */\n  let more\n  const events = new _lib_splice_buffer_js__WEBPACK_IMPORTED_MODULE_0__.SpliceBuffer(eventsArray)\n\n  while (++index < events.length) {\n    while (index in jumps) {\n      index = jumps[index]\n    }\n\n    event = events.get(index)\n\n    // Add a hook for the GFM tasklist extension, which needs to know if text\n    // is in the first content of a list item.\n    if (\n      index &&\n      event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkFlow &&\n      events.get(index - 1)[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.listItemPrefix\n    ) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(event[1]._tokenizer, 'expected `_tokenizer` on subtokens')\n      subevents = event[1]._tokenizer.events\n      otherIndex = 0\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank\n      ) {\n        otherIndex += 2\n      }\n\n      if (\n        otherIndex < subevents.length &&\n        subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.content\n      ) {\n        while (++otherIndex < subevents.length) {\n          if (subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.content) {\n            break\n          }\n\n          if (subevents[otherIndex][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.chunkText) {\n            subevents[otherIndex][1]._isInFirstContentOfListItem = true\n            otherIndex++\n          }\n        }\n      }\n    }\n\n    // Enter.\n    if (event[0] === 'enter') {\n      if (event[1].contentType) {\n        Object.assign(jumps, subcontent(events, index))\n        index = jumps[index]\n        more = true\n      }\n    }\n    // Exit.\n    else if (event[1]._container) {\n      otherIndex = index\n      lineIndex = undefined\n\n      while (otherIndex--) {\n        otherEvent = events.get(otherIndex)\n\n        if (\n          otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding ||\n          otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank\n        ) {\n          if (otherEvent[0] === 'enter') {\n            if (lineIndex) {\n              events.get(lineIndex)[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEndingBlank\n            }\n\n            otherEvent[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding\n            lineIndex = otherIndex\n          }\n        } else if (otherEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix) {\n          // Move past.\n        } else {\n          break\n        }\n      }\n\n      if (lineIndex) {\n        // Fix position.\n        event[1].end = {...events.get(lineIndex)[1].start}\n\n        // Switch container exit w/ line endings.\n        parameters = events.slice(lineIndex, index)\n        parameters.unshift(event)\n        events.splice(lineIndex, index - lineIndex + 1, parameters)\n      }\n    }\n  }\n\n  // The changes to the `events` buffer must be copied back into the eventsArray\n  (0,micromark_util_chunked__WEBPACK_IMPORTED_MODULE_3__.splice)(eventsArray, 0, Number.POSITIVE_INFINITY, events.slice(0))\n  return !more\n}\n\n/**\n * Tokenize embedded tokens.\n *\n * @param {SpliceBuffer<Event>} events\n *   Events.\n * @param {number} eventIndex\n *   Index.\n * @returns {Record<string, number>}\n *   Gaps.\n */\nfunction subcontent(events, eventIndex) {\n  const token = events.get(eventIndex)[1]\n  const context = events.get(eventIndex)[2]\n  let startPosition = eventIndex - 1\n  /** @type {Array<number>} */\n  const startPositions = []\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(token.contentType, 'expected `contentType` on subtokens')\n  const tokenizer =\n    token._tokenizer || context.parser[token.contentType](token.start)\n  const childEvents = tokenizer.events\n  /** @type {Array<[number, number]>} */\n  const jumps = []\n  /** @type {Record<string, number>} */\n  const gaps = {}\n  /** @type {Array<Chunk>} */\n  let stream\n  /** @type {Token | undefined} */\n  let previous\n  let index = -1\n  /** @type {Token | undefined} */\n  let current = token\n  let adjust = 0\n  let start = 0\n  const breaks = [start]\n\n  // Loop forward through the linked tokens to pass them in order to the\n  // subtokenizer.\n  while (current) {\n    // Find the position of the event for this token.\n    while (events.get(++startPosition)[1] !== current) {\n      // Empty.\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(\n      !previous || current.previous === previous,\n      'expected previous to match'\n    )\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!previous || previous.next === current, 'expected next to match')\n\n    startPositions.push(startPosition)\n\n    if (!current._tokenizer) {\n      stream = context.sliceStream(current)\n\n      if (!current.next) {\n        stream.push(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_4__.codes.eof)\n      }\n\n      if (previous) {\n        tokenizer.defineSkip(current.start)\n      }\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = true\n      }\n\n      tokenizer.write(stream)\n\n      if (current._isInFirstContentOfListItem) {\n        tokenizer._gfmTasklistFirstContentOfListItem = undefined\n      }\n    }\n\n    // Unravel the next token.\n    previous = current\n    current = current.next\n  }\n\n  // Now, loop back through all events (and linked tokens), to figure out which\n  // parts belong where.\n  current = token\n\n  while (++index < childEvents.length) {\n    if (\n      // Find a void token that includes a break.\n      childEvents[index][0] === 'exit' &&\n      childEvents[index - 1][0] === 'enter' &&\n      childEvents[index][1].type === childEvents[index - 1][1].type &&\n      childEvents[index][1].start.line !== childEvents[index][1].end.line\n    ) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(current, 'expected a current token')\n      start = index + 1\n      breaks.push(start)\n      // Help GC.\n      current._tokenizer = undefined\n      current.previous = undefined\n      current = current.next\n    }\n  }\n\n  // Help GC.\n  tokenizer.events = []\n\n  // If there’s one more token (which is the cases for lines that end in an\n  // EOF), that’s perfect: the last point we found starts it.\n  // If there isn’t then make sure any remaining content is added to it.\n  if (current) {\n    // Help GC.\n    current._tokenizer = undefined\n    current.previous = undefined\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(!current.next, 'expected no next token')\n  } else {\n    breaks.pop()\n  }\n\n  // Now splice the events from the subtokenizer into the current events,\n  // moving back to front so that splice indices aren’t affected.\n  index = breaks.length\n\n  while (index--) {\n    const slice = childEvents.slice(breaks[index], breaks[index + 1])\n    const start = startPositions.pop()\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(start !== undefined, 'expected a start position when splicing')\n    jumps.push([start, start + slice.length - 1])\n    events.splice(start, 2, slice)\n  }\n\n  jumps.reverse()\n  index = -1\n\n  while (++index < jumps.length) {\n    gaps[adjust + jumps[index][0]] = adjust + jumps[index][1]\n    adjust += jumps[index][1] - jumps[index][0] - 1\n  }\n\n  return gaps\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-util-subtokenize@2.0.4/node_modules/micromark-util-subtokenize/dev/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/micromark-util-subtokenize@2.0.4/node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/micromark-util-subtokenize@2.0.4/node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpliceBuffer: () => (/* binding */ SpliceBuffer)\n/* harmony export */ });\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/constants.js\");\n\n\n/**\n * Some of the internal operations of micromark do lots of editing\n * operations on very large arrays. This runs into problems with two\n * properties of most circa-2020 JavaScript interpreters:\n *\n *  - Array-length modifications at the high end of an array (push/pop) are\n *    expected to be common and are implemented in (amortized) time\n *    proportional to the number of elements added or removed, whereas\n *    other operations (shift/unshift and splice) are much less efficient.\n *  - Function arguments are passed on the stack, so adding tens of thousands\n *    of elements to an array with `arr.push(...newElements)` will frequently\n *    cause stack overflows. (see <https://stackoverflow.com/questions/22123769/rangeerror-maximum-call-stack-size-exceeded-why>)\n *\n * SpliceBuffers are an implementation of gap buffers, which are a\n * generalization of the \"queue made of two stacks\" idea. The splice buffer\n * maintains a cursor, and moving the cursor has cost proportional to the\n * distance the cursor moves, but inserting, deleting, or splicing in\n * new information at the cursor is as efficient as the push/pop operation.\n * This allows for an efficient sequence of splices (or pushes, pops, shifts,\n * or unshifts) as long such edits happen at the same part of the array or\n * generally sweep through the array from the beginning to the end.\n *\n * The interface for splice buffers also supports large numbers of inputs by\n * passing a single array argument rather passing multiple arguments on the\n * function call stack.\n *\n * @template T\n *   Item type.\n */\nclass SpliceBuffer {\n  /**\n   * @param {ReadonlyArray<T> | null | undefined} [initial]\n   *   Initial items (optional).\n   * @returns\n   *   Splice buffer.\n   */\n  constructor(initial) {\n    /** @type {Array<T>} */\n    this.left = initial ? [...initial] : []\n    /** @type {Array<T>} */\n    this.right = []\n  }\n\n  /**\n   * Array access;\n   * does not move the cursor.\n   *\n   * @param {number} index\n   *   Index.\n   * @return {T}\n   *   Item.\n   */\n  get(index) {\n    if (index < 0 || index >= this.left.length + this.right.length) {\n      throw new RangeError(\n        'Cannot access index `' +\n          index +\n          '` in a splice buffer of size `' +\n          (this.left.length + this.right.length) +\n          '`'\n      )\n    }\n\n    if (index < this.left.length) return this.left[index]\n    return this.right[this.right.length - index + this.left.length - 1]\n  }\n\n  /**\n   * The length of the splice buffer, one greater than the largest index in the\n   * array.\n   */\n  get length() {\n    return this.left.length + this.right.length\n  }\n\n  /**\n   * Remove and return `list[0]`;\n   * moves the cursor to `0`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */\n  shift() {\n    this.setCursor(0)\n    return this.right.pop()\n  }\n\n  /**\n   * Slice the buffer to get an array;\n   * does not move the cursor.\n   *\n   * @param {number} start\n   *   Start.\n   * @param {number | null | undefined} [end]\n   *   End (optional).\n   * @returns {Array<T>}\n   *   Array of items.\n   */\n  slice(start, end) {\n    /** @type {number} */\n    const stop =\n      end === null || end === undefined ? Number.POSITIVE_INFINITY : end\n\n    if (stop < this.left.length) {\n      return this.left.slice(start, stop)\n    }\n\n    if (start > this.left.length) {\n      return this.right\n        .slice(\n          this.right.length - stop + this.left.length,\n          this.right.length - start + this.left.length\n        )\n        .reverse()\n    }\n\n    return this.left\n      .slice(start)\n      .concat(\n        this.right.slice(this.right.length - stop + this.left.length).reverse()\n      )\n  }\n\n  /**\n   * Mimics the behavior of Array.prototype.splice() except for the change of\n   * interface necessary to avoid segfaults when patching in very large arrays.\n   *\n   * This operation moves cursor is moved to `start` and results in the cursor\n   * placed after any inserted items.\n   *\n   * @param {number} start\n   *   Start;\n   *   zero-based index at which to start changing the array;\n   *   negative numbers count backwards from the end of the array and values\n   *   that are out-of bounds are clamped to the appropriate end of the array.\n   * @param {number | null | undefined} [deleteCount=0]\n   *   Delete count (default: `0`);\n   *   maximum number of elements to delete, starting from start.\n   * @param {Array<T> | null | undefined} [items=[]]\n   *   Items to include in place of the deleted items (default: `[]`).\n   * @return {Array<T>}\n   *   Any removed items.\n   */\n  splice(start, deleteCount, items) {\n    /** @type {number} */\n    const count = deleteCount || 0\n\n    this.setCursor(Math.trunc(start))\n    const removed = this.right.splice(\n      this.right.length - count,\n      Number.POSITIVE_INFINITY\n    )\n    if (items) chunkedPush(this.left, items)\n    return removed.reverse()\n  }\n\n  /**\n   * Remove and return the highest-numbered item in the array, so\n   * `list[list.length - 1]`;\n   * Moves the cursor to `length`.\n   *\n   * @returns {T | undefined}\n   *   Item, optional.\n   */\n  pop() {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    return this.left.pop()\n  }\n\n  /**\n   * Inserts a single item to the high-numbered side of the array;\n   * moves the cursor to `length`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  push(item) {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    this.left.push(item)\n  }\n\n  /**\n   * Inserts many items to the high-numbered side of the array.\n   * Moves the cursor to `length`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  pushMany(items) {\n    this.setCursor(Number.POSITIVE_INFINITY)\n    chunkedPush(this.left, items)\n  }\n\n  /**\n   * Inserts a single item to the low-numbered side of the array;\n   * Moves the cursor to `0`.\n   *\n   * @param {T} item\n   *   Item.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  unshift(item) {\n    this.setCursor(0)\n    this.right.push(item)\n  }\n\n  /**\n   * Inserts many items to the low-numbered side of the array;\n   * moves the cursor to `0`.\n   *\n   * @param {Array<T>} items\n   *   Items.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  unshiftMany(items) {\n    this.setCursor(0)\n    chunkedPush(this.right, items.reverse())\n  }\n\n  /**\n   * Move the cursor to a specific position in the array. Requires\n   * time proportional to the distance moved.\n   *\n   * If `n < 0`, the cursor will end up at the beginning.\n   * If `n > length`, the cursor will end up at the end.\n   *\n   * @param {number} n\n   *   Position.\n   * @return {undefined}\n   *   Nothing.\n   */\n  setCursor(n) {\n    if (\n      n === this.left.length ||\n      (n > this.left.length && this.right.length === 0) ||\n      (n < 0 && this.left.length === 0)\n    )\n      return\n    if (n < this.left.length) {\n      // Move cursor to the this.left\n      const removed = this.left.splice(n, Number.POSITIVE_INFINITY)\n      chunkedPush(this.right, removed.reverse())\n    } else {\n      // Move cursor to the this.right\n      const removed = this.right.splice(\n        this.left.length + this.right.length - n,\n        Number.POSITIVE_INFINITY\n      )\n      chunkedPush(this.left, removed.reverse())\n    }\n  }\n}\n\n/**\n * Avoid stack overflow by pushing items onto the stack in segments\n *\n * @template T\n *   Item type.\n * @param {Array<T>} list\n *   List to inject into.\n * @param {ReadonlyArray<T>} right\n *   Items to inject.\n * @return {undefined}\n *   Nothing.\n */\nfunction chunkedPush(list, right) {\n  /** @type {number} */\n  let chunkStart = 0\n\n  if (right.length < micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize) {\n    list.push(...right)\n  } else {\n    while (chunkStart < right.length) {\n      list.push(\n        ...right.slice(chunkStart, chunkStart + micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize)\n      )\n      chunkStart += micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.constants.v8MaxSafeChunkSize\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/micromark-util-subtokenize@2.0.4/node_modules/micromark-util-subtokenize/dev/lib/splice-buffer.js\n");

/***/ })

};
;