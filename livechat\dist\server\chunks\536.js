"use strict";exports.id=536,exports.ids=[536],exports.modules={59536:(e,r,t)=>{t.d(r,{getDeploymentUrl:()=>o,loadChatConfig:()=>i});let a={server:{apiUrl:"http://localhost:7272",useHttps:!1,apiVersion:"v3",timeout:3e4},app:{appName:"LiveChat",appDescription:"Live chat application with R2R integration",version:"1.0.0",defaultMode:"rag_agent",conversationHistoryLimit:10},vectorSearch:{enabled:!0,searchLimit:10,searchFilters:"{}",indexMeasure:"cosine_distance",includeMetadatas:!1,probes:void 0,efSearch:void 0},hybridSearch:{enabled:!1,fullTextWeight:void 0,semanticWeight:void 0,fullTextLimit:void 0,rrfK:void 0},graphSearch:{enabled:!0,kgSearchLevel:null,maxCommunityDescriptionLength:100,localSearchLimits:{},maxLlmQueries:void 0},ragGeneration:{temperature:.1,topP:1,topK:100,maxTokensToSample:1024,kgTemperature:.1,kgTopP:1,kgTopK:100,kgMaxTokensToSample:1024}},i=async()=>{try{let e=await fetch("/config.json");if(!e.ok)return console.warn("Failed to load config.json, using default configuration"),a;let r=await e.json();return{server:{...a.server,...r.server},app:{...a.app,...r.app},vectorSearch:{...a.vectorSearch,...r.vectorSearch},hybridSearch:{...a.hybridSearch,...r.hybridSearch},graphSearch:{...a.graphSearch,...r.graphSearch},ragGeneration:{...a.ragGeneration,...r.ragGeneration}}}catch(e){return console.error("Error loading configuration:",e),a}},o=e=>{let r=e||a;if(r.server.apiUrl.startsWith("http://")||r.server.apiUrl.startsWith("https://"))return r.server.apiUrl;let t=r.server.useHttps?"https":"http";return`${t}://${r.server.apiUrl}`}}};