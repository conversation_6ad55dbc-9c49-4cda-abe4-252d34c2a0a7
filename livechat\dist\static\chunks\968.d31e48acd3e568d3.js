"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[968],{17567:(e,t,n)=>{n.d(t,{x:()=>l});var r=n(12389),i=n(41473),o=n(51432),a=n(43228),s=n(58209);function l(e){let{children:t,isValidProp:n,...l}=e;n&&(0,a.D)(n),(l={...(0,i.useContext)(o.Q),...l}).isStatic=(0,s.M)(()=>l.isStatic);let u=(0,i.useMemo)(()=>l,[JSON.stringify(l.transition),l.transformPagePoint,l.reducedMotion]);return(0,r.jsx)(o.Q.Provider,{value:u,children:t})}},42968:(e,t,n)=>{let r,i,o,a;n.r(t),n.d(t,{AcceleratedAnimation:()=>t6.m,AnimatePresence:()=>y.N,AnimateSharedLayout:()=>nC,DeprecatedLayoutGroupContext:()=>nh.Q,DragControls:()=>tz,FlatTree:()=>nm.Y,LayoutGroup:()=>x.o,LayoutGroupContext:()=>nf.L,LazyMotion:()=>M.F,MotionConfig:()=>w.x,MotionConfigContext:()=>V.Q,MotionContext:()=>nu.A,MotionGlobalConfig:()=>t4.W,MotionValue:()=>A.KG,PresenceContext:()=>nc.t,Reorder:()=>s,SwitchLayoutGroupContext:()=>nd.N,VisualElement:()=>to.B,addPointerEvent:()=>n1.h,addPointerInfo:()=>n9.F,addScaleCorrector:()=>tY.$,animate:()=>td,animateMini:()=>tO,animateValue:()=>Q.L,animateVisualElement:()=>eN._,animationControls:()=>eD,animations:()=>u.W,anticipate:()=>nD.b,backIn:()=>nj.dg,backInOut:()=>nj.ZZ,backOut:()=>nj.Sz,buildTransform:()=>t2.d,calcLength:()=>nZ.CQ,cancelFrame:()=>T.WG,cancelSync:()=>nQ,circIn:()=>n$.po,circInOut:()=>n$.tn,circOut:()=>n$.yT,clamp:()=>nF.q,color:()=>t3.y,complex:()=>t5.f,createBox:()=>ti.ge,createRendererMotionComponent:()=>tj.Z,createScopedAnimate:()=>tf,cubicBezier:()=>nU.A,delay:()=>nA.c,disableInstantTransitions:()=>t1,distance:()=>n_.I,distance2D:()=>n_.w,domAnimation:()=>S.l,domMax:()=>b.z,domMin:()=>E,easeIn:()=>nX.a6,easeInOut:()=>nX.am,easeOut:()=>nX.vT,filterProps:()=>nK.J,findSpring:()=>nl.pX,frame:()=>T.Gt,frameData:()=>T.uv,frameSteps:()=>T.PP,inView:()=>tF,inertia:()=>nW.B,interpolate:()=>R.G,invariant:()=>U.V,isBrowser:()=>n2.B,isDragActive:()=>n0.D3,isMotionComponent:()=>tX,isMotionValue:()=>W.S,isValidMotionProp:()=>tk.S,keyframes:()=>nk.i,m:()=>v.m,makeUseVisualState:()=>np.T,mirrorEasing:()=>nq.V,mix:()=>nB.j,motion:()=>g,motionValue:()=>A.OQ,optimizedAppearDataAttribute:()=>ne.n,pipe:()=>nz.F,progress:()=>K.q,px:()=>t7.px,resolveMotionValue:()=>L.u,reverseEasing:()=>nJ.G,scroll:()=>eE,scrollInfo:()=>ep,spring:()=>eX.o,stagger:()=>nR,startOptimizedAppearAnimation:()=>ns,steps:()=>nY,sync:()=>nN,transform:()=>z,unwrapMotionComponent:()=>tU,useAnimate:()=>tm,useAnimateMini:()=>tP,useAnimation:()=>tG,useAnimationControls:()=>tT,useAnimationFrame:()=>eI,useCycle:()=>tW,useDeprecatedAnimatedState:()=>nw,useDeprecatedInvertedScale:()=>nE,useDomEvent:()=>tD,useDragControls:()=>tN,useElementScroll:()=>eA,useForceUpdate:()=>tZ.C,useInView:()=>tB,useInstantLayoutTransition:()=>tJ,useInstantTransition:()=>t0,useIsPresent:()=>tL.tF,useIsomorphicLayoutEffect:()=>P.E,useMotionTemplate:()=>k,useMotionValue:()=>O,useMotionValueEvent:()=>$,usePresence:()=>tL.xQ,useReducedMotion:()=>eB,useReducedMotionConfig:()=>ez,useResetProjection:()=>t9,useScroll:()=>eC,useSpring:()=>j,useTime:()=>eO,useTransform:()=>H,useUnmountEffect:()=>ej,useVelocity:()=>X,useViewportScroll:()=>eV,useWillChange:()=>eL,visualElementStore:()=>e8.C,warning:()=>U.$,wrap:()=>e1});var s={};n.r(s),n.d(s,{Group:()=>nI,Item:()=>nG});var l=n(52408),u=n(75786),c=n(15295),f=n(97072),d=n(99832),m=n(16676),h=n(39047);let p=(0,m.C)({...u.W,...f.n,...c.$,...d.Z},h.J),g=(0,l.I)(p);var v=n(46211),y=n(35277),w=n(17567),M=n(97783),x=n(84405);let E={renderer:h.J,...u.W};var S=n(46965),b=n(73589),C=n(41473),A=n(81991),V=n(51432),I=n(58209);function O(e){let t=(0,I.M)(()=>(0,A.OQ)(e)),{isStatic:n}=(0,C.useContext)(V.Q);if(n){let[,n]=(0,C.useState)(e);(0,C.useEffect)(()=>t.on("change",n),[])}return t}var P=n(24250),T=n(20638);function G(e,t){let n=O(t()),r=()=>n.set(t());return r(),(0,P.E)(()=>{let t=()=>T.Gt.preRender(r,!1,!0),n=e.map(e=>e.on("change",t));return()=>{n.forEach(e=>e()),(0,T.WG)(r)}}),n}var W=n(99630);function k(e,...t){let n=e.length;return G(t.filter(W.S),function(){let r="";for(let i=0;i<n;i++){r+=e[i];let n=t[i];n&&(r+=(0,W.S)(n)?n.get():n)}return r})}var L=n(43218),R=n(86002);let F=e=>e&&"object"==typeof e&&e.mix,B=e=>F(e)?e.mix:void 0;function z(...e){let t=!Array.isArray(e[0]),n=t?0:-1,r=e[0+n],i=e[1+n],o=e[2+n],a=e[3+n],s=(0,R.G)(i,o,{mixer:B(o[0]),...a});return t?s(r):s}function H(e,t,n,r){if("function"==typeof e)return function(e){A.bt.current=[],e();let t=G(A.bt.current,e);return A.bt.current=void 0,t}(e);let i="function"==typeof t?t:z(t,n,r);return Array.isArray(e)?N(e,i):N([e],([e])=>i(e))}function N(e,t){let n=(0,I.M)(()=>[]);return G(e,()=>{n.length=0;let r=e.length;for(let t=0;t<r;t++)n[t]=e[t].get();return t(n)})}var Q=n(61640);function D(e){return"number"==typeof e?e:parseFloat(e)}function j(e,t={}){let{isStatic:n}=(0,C.useContext)(V.Q),r=(0,C.useRef)(null),i=O((0,W.S)(e)?D(e.get()):e),o=(0,C.useRef)(i.get()),a=(0,C.useRef)(()=>{}),s=()=>{let e=r.current;e&&0===e.time&&e.sample(T.uv.delta),l(),r.current=(0,Q.L)({keyframes:[i.get(),o.current],velocity:i.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...t,onUpdate:a.current})},l=()=>{r.current&&r.current.stop()};return(0,C.useInsertionEffect)(()=>i.attach((e,t)=>n?t(e):(o.current=e,a.current=t,T.Gt.update(s),i.get()),l),[JSON.stringify(t)]),(0,P.E)(()=>{if((0,W.S)(e))return e.on("change",e=>i.set(D(e)))},[i]),i}function $(e,t,n){(0,C.useInsertionEffect)(()=>e.on(t,n),[e,t,n])}function X(e){let t=O(e.getVelocity()),n=()=>{let r=e.getVelocity();t.set(r),r&&T.Gt.update(n)};return $(e,"change",()=>{T.Gt.update(n,!1,!0)}),t}var U=n(35042);function Y(e,t,n){var r;if("string"==typeof e){let i=document;t&&((0,U.V)(!!t.current,"Scope provided, but no element detected."),i=t.current),n?(null!==(r=n[e])&&void 0!==r||(n[e]=i.querySelectorAll(e)),e=n[e]):e=i.querySelectorAll(e)}else e instanceof Element&&(e=[e]);return Array.from(e||[])}let q=new WeakMap;function J({target:e,contentRect:t,borderBoxSize:n}){var r;null===(r=q.get(e))||void 0===r||r.forEach(r=>{r({target:e,contentSize:t,get size(){return function(e,t){if(t){let{inlineSize:e,blockSize:n}=t[0];return{width:e,height:n}}return e instanceof SVGElement&&"getBBox"in e?e.getBBox():{width:e.offsetWidth,height:e.offsetHeight}}(e,n)}})})}function _(e){e.forEach(J)}let Z=new Set;var K=n(29606),ee=n(90359);let et=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),en=()=>({time:0,x:et(),y:et()}),er={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function ei(e,t,n,r){let i=n[t],{length:o,position:a}=er[t],s=i.current,l=n.time;i.current=e[`scroll${a}`],i.scrollLength=e[`scroll${o}`]-e[`client${o}`],i.offset.length=0,i.offset[0]=0,i.offset[1]=i.scrollLength,i.progress=(0,K.q)(0,i.scrollLength,i.current);let u=r-l;i.velocity=u>50?0:(0,ee.f)(i.current-s,u)}let eo={All:[[0,0],[1,1]]},ea={start:0,center:.5,end:1};function es(e,t,n=0){let r=0;if(e in ea&&(e=ea[e]),"string"==typeof e){let t=parseFloat(e);e.endsWith("px")?r=t:e.endsWith("%")?e=t/100:e.endsWith("vw")?r=t/100*document.documentElement.clientWidth:e.endsWith("vh")?r=t/100*document.documentElement.clientHeight:e=t}return"number"==typeof e&&(r=t*e),n+r}let el=[0,0];var eu=n(47805);let ec={x:0,y:0},ef=new WeakMap,ed=new WeakMap,em=new WeakMap,eh=e=>e===document.documentElement?window:e;function ep(e,{container:t=document.documentElement,...n}={}){let o=em.get(t);o||(o=new Set,em.set(t,o));let a=function(e,t,n,r={}){return{measure:()=>(function(e,t=e,n){if(n.x.targetOffset=0,n.y.targetOffset=0,t!==e){let r=t;for(;r&&r!==e;)n.x.targetOffset+=r.offsetLeft,n.y.targetOffset+=r.offsetTop,r=r.offsetParent}n.x.targetLength=t===e?t.scrollWidth:t.clientWidth,n.y.targetLength=t===e?t.scrollHeight:t.clientHeight,n.x.containerLength=e.clientWidth,n.y.containerLength=e.clientHeight})(e,r.target,n),update:t=>{ei(e,"x",n,t),ei(e,"y",n,t),n.time=t,(r.offset||r.target)&&function(e,t,n){let{offset:r=eo.All}=n,{target:i=e,axis:o="y"}=n,a="y"===o?"height":"width",s=i!==e?function(e,t){let n={x:0,y:0},r=e;for(;r&&r!==t;)if(r instanceof HTMLElement)n.x+=r.offsetLeft,n.y+=r.offsetTop,r=r.offsetParent;else if("svg"===r.tagName){let e=r.getBoundingClientRect(),t=(r=r.parentElement).getBoundingClientRect();n.x+=e.left-t.left,n.y+=e.top-t.top}else if(r instanceof SVGGraphicsElement){let{x:e,y:t}=r.getBBox();n.x+=e,n.y+=t;let i=null,o=r.parentNode;for(;!i;)"svg"===o.tagName&&(i=o),o=r.parentNode;r=i}else break;return n}(i,e):ec,l=i===e?{width:e.scrollWidth,height:e.scrollHeight}:"getBBox"in i&&"svg"!==i.tagName?i.getBBox():{width:i.clientWidth,height:i.clientHeight},u={width:e.clientWidth,height:e.clientHeight};t[o].offset.length=0;let c=!t[o].interpolate,f=r.length;for(let e=0;e<f;e++){let n=function(e,t,n,r){let i=Array.isArray(e)?e:el,o=0;return"number"==typeof e?i=[e,e]:"string"==typeof e&&(i=(e=e.trim()).includes(" ")?e.split(" "):[e,ea[e]?e:"0"]),es(i[0],n,r)-es(i[1],t)}(r[e],u[a],l[a],s[o]);c||n===t[o].interpolatorOffsets[e]||(c=!0),t[o].offset[e]=n}c&&(t[o].interpolate=(0,R.G)(t[o].offset,(0,eu.Z)(r)),t[o].interpolatorOffsets=[...t[o].offset]),t[o].progress=t[o].interpolate(t[o].current)}(e,n,r)},notify:()=>t(n)}}(t,e,en(),n);if(o.add(a),!ef.has(t)){let e=()=>{for(let e of o)e.measure()},n=()=>{for(let e of o)e.update(T.uv.timestamp)},a=()=>{for(let e of o)e.notify()},s=()=>{T.Gt.read(e,!1,!0),T.Gt.read(n,!1,!0),T.Gt.update(a,!1,!0)};ef.set(t,s);let l=eh(t);window.addEventListener("resize",s,{passive:!0}),t!==document.documentElement&&ed.set(t,"function"==typeof t?(Z.add(t),i||(i=()=>{let e={width:window.innerWidth,height:window.innerHeight},t={target:window,size:e,contentSize:e};Z.forEach(e=>e(t))},window.addEventListener("resize",i)),()=>{Z.delete(t),!Z.size&&i&&(i=void 0)}):function(e,t){r||"undefined"==typeof ResizeObserver||(r=new ResizeObserver(_));let n=Y(e);return n.forEach(e=>{let n=q.get(e);n||(n=new Set,q.set(e,n)),n.add(t),null==r||r.observe(e)}),()=>{n.forEach(e=>{let n=q.get(e);null==n||n.delete(t),(null==n?void 0:n.size)||null==r||r.unobserve(e)})}}(t,s)),l.addEventListener("scroll",s,{passive:!0})}let s=ef.get(t);return T.Gt.read(s,!1,!0),()=>{var e;(0,T.WG)(s);let n=em.get(t);if(!n||(n.delete(a),n.size))return;let r=ef.get(t);ef.delete(t),r&&(eh(t).removeEventListener("scroll",r),null===(e=ed.get(t))||void 0===e||e(),window.removeEventListener("resize",r))}}function eg(e,t){let n;let r=()=>{let{currentTime:r}=t,i=(null===r?0:r.value)/100;n!==i&&e(i),n=i};return T.Gt.update(r,!0),()=>(0,T.WG)(r)}var ev=n(59743),ey=n(63679);let ew=new Map;function eM({source:e,container:t=document.documentElement,axis:n="y"}={}){e&&(t=e),ew.has(t)||ew.set(t,{});let r=ew.get(t);return r[n]||(r[n]=(0,ev.J)()?new ScrollTimeline({source:t,axis:n}):function({source:e,container:t,axis:n="y"}){e&&(t=e);let r={value:0},i=ep(e=>{r.value=100*e[n].progress},{container:t,axis:n});return{currentTime:r,cancel:i}}({source:t,axis:n})),r[n]}function ex(e){return e&&(e.target||e.offset)}function eE(e,{axis:t="y",...n}={}){let r={axis:t,...n};return"function"==typeof e?2===e.length||ex(r)?ep(t=>{e(t[r.axis].progress,t)},r):eg(e,eM(r)):function(e,t){if(e.flatten(),ex(t))return e.pause(),ep(n=>{e.time=e.duration*n[t.axis].progress},t);{let n=eM(t);return e.attachTimeline?e.attachTimeline(n,e=>(e.pause(),eg(t=>{e.time=e.duration*t},n))):ey.l}}(e,r)}function eS(e,t){(0,U.$)(!!(!t||t.current),`You have defined a ${e} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \`layoutEffect: false\` option.`)}let eb=()=>({scrollX:(0,A.OQ)(0),scrollY:(0,A.OQ)(0),scrollXProgress:(0,A.OQ)(0),scrollYProgress:(0,A.OQ)(0)});function eC({container:e,target:t,layoutEffect:n=!0,...r}={}){let i=(0,I.M)(eb);return(n?P.E:C.useEffect)(()=>(eS("target",t),eS("container",e),eE((e,{x:t,y:n})=>{i.scrollX.set(t.current),i.scrollXProgress.set(t.progress),i.scrollY.set(n.current),i.scrollYProgress.set(n.progress)},{...r,container:(null==e?void 0:e.current)||void 0,target:(null==t?void 0:t.current)||void 0})),[e,t,JSON.stringify(r.offset)]),i}function eA(e){return eC({container:e})}function eV(){return eC()}function eI(e){let t=(0,C.useRef)(0),{isStatic:n}=(0,C.useContext)(V.Q);(0,C.useEffect)(()=>{if(n)return;let r=({timestamp:n,delta:r})=>{t.current||(t.current=n),e(n-t.current,r)};return T.Gt.update(r,!0),()=>(0,T.WG)(r)},[e])}function eO(){let e=O(0);return eI(t=>e.set(t)),e}var eP=n(86099),eT=n(36198),eG=n(93218),eW=n(39704);class ek extends A.KG{constructor(){super(...arguments),this.values=[]}add(e){let t=eG.f.has(e)?"transform":eP.M.has(e)?(0,eT.I)(e):void 0;t&&((0,eW.Kq)(this.values,t),this.update())}update(){this.set(this.values.length?this.values.join(", "):"auto")}}function eL(){return(0,I.M)(()=>new ek("auto"))}var eR=n(12463),eF=n(56076);function eB(){eF.r.current||(0,eR.U)();let[e]=(0,C.useState)(eF.O.current);return e}function ez(){let e=eB(),{reducedMotion:t}=(0,C.useContext)(V.Q);return"never"!==t&&("always"===t||e)}var eH=n(62902),eN=n(88325);function eQ(e,t){[...t].reverse().forEach(n=>{let r=e.getVariant(n);r&&(0,eH.U)(e,r),e.variantChildren&&e.variantChildren.forEach(e=>{eQ(e,t)})})}function eD(){let e=!1,t=new Set,n={subscribe:e=>(t.add(e),()=>void t.delete(e)),start(n,r){(0,U.V)(e,"controls.start() should only be called after a component has mounted. Consider calling within a useEffect hook.");let i=[];return t.forEach(e=>{i.push((0,eN._)(e,n,{transitionOverride:r}))}),Promise.all(i)},set:n=>((0,U.V)(e,"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook."),t.forEach(e=>{Array.isArray(n)?eQ(e,n):"string"==typeof n?eQ(e,[n]):(0,eH.U)(e,n)})),stop(){t.forEach(e=>{!function(e){e.values.forEach(e=>e.stop())}(e)})},mount:()=>(e=!0,()=>{e=!1,n.stop()})};return n}function ej(e){return(0,C.useEffect)(()=>()=>e(),[])}var e$=n(74034),eX=n(90293),eU=n(63801),eY=n(27915);function eq(e,t=100,n){let r=n({...e,keyframes:[0,t]}),i=Math.min((0,eU.t)(r),eU.Y);return{type:"keyframes",ease:e=>r.next(i*e).value/t,duration:(0,eY.X)(i)}}var eJ=n(30095);function e_(e){return"object"==typeof e&&!Array.isArray(e)}function eZ(e,t,n,r){return"string"==typeof e&&e_(t)?Y(e,n,r):e instanceof NodeList?Array.from(e):Array.isArray(e)?e:[e]}var eK=n(74445);function e0(e,t,n,r){var i;return"number"==typeof t?t:t.startsWith("-")||t.startsWith("+")?Math.max(0,e+parseFloat(t)):"<"===t?n:null!==(i=r.get(t))&&void 0!==i?i:e}let e1=(e,t,n)=>{let r=t-e;return((n-e)%r+r)%r+e};var e9=n(58159),e2=n(77841);function e3(e,t){return e.at!==t.at?e.at-t.at:null===e.value?1:null===t.value?-1:0}function e5(e,t){return t.has(e)||t.set(e,{}),t.get(e)}function e7(e,t){return t[e]||(t[e]=[]),t[e]}let e4=e=>"number"==typeof e,e6=e=>e.every(e4);var e8=n(49155),te=n(79110),tt=n(16057),tn=n(83921),tr=n(66273),ti=n(70486),to=n(35763);class ta extends to.B{constructor(){super(...arguments),this.type="object"}readValueFromInstance(e,t){if(t in e){let n=e[t];if("string"==typeof n||"number"==typeof n)return n}}getBaseTargetFromProps(){}removeValueFromRenderState(e,t){delete t.output[e]}measureInstanceViewportBox(){return(0,ti.ge)()}build(e,t){Object.assign(e.output,t)}renderInstance(e,{output:t}){Object.assign(e,t)}sortInstanceNodePosition(){return 0}}function ts(e){let t={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},n=(0,tt.x)(e)?new tn.l(t):new tr.M(t);n.mount(e),e8.C.set(e,n)}function tl(e){let t=new ta({presenceContext:null,props:{},visualState:{renderState:{output:{}},latestValues:{}}});t.mount(e),e8.C.set(e,t)}var tu=n(32392);function tc(e,t,n,r){let i=[];if((0,W.S)(e)||"number"==typeof e||"string"==typeof e&&!e_(t))i.push((0,tu.z)(e,e_(t)&&t.default||t,n&&n.default||n));else{let o=eZ(e,t,r),a=o.length;(0,U.V)(!!a,"No valid elements provided.");for(let e=0;e<a;e++){let r=o[e],s=r instanceof Element?ts:tl;e8.C.has(r)||s(r);let l=e8.C.get(r),u={...n};"delay"in u&&"function"==typeof u.delay&&(u.delay=u.delay(e,a)),i.push(...(0,te.$)(l,{...t,transition:u},{}))}}return i}function tf(e){return function(t,n,r){let i=[];i=Array.isArray(t)&&Array.isArray(t[0])?function(e,t,n){let r=[];return(function(e,{defaultTransition:t={},...n}={},r,i){let o=t.duration||.3,a=new Map,s=new Map,l={},u=new Map,c=0,f=0,d=0;for(let n=0;n<e.length;n++){let a=e[n];if("string"==typeof a){u.set(a,f);continue}if(!Array.isArray(a)){u.set(a.name,e0(f,a.at,c,u));continue}let[m,h,p={}]=a;void 0!==p.at&&(f=e0(f,p.at,c,u));let g=0,v=(e,n,r,a=0,s=0)=>{let l=Array.isArray(e)?e:[e],{delay:u=0,times:c=(0,eu.Z)(l),type:m="keyframes",...h}=n,{ease:p=t.ease||"easeOut",duration:v}=n,y="function"==typeof u?u(a,s):u,w=l.length,M=(0,eK.W)(m)?m:null==i?void 0:i[m];if(w<=2&&M){let e=100;2===w&&e6(l)&&(e=Math.abs(l[1]-l[0]));let t={...h};void 0!==v&&(t.duration=(0,eY.f)(v));let n=eq(t,e,M);p=n.ease,v=n.duration}null!=v||(v=o);let x=f+y,E=x+v;1===c.length&&0===c[0]&&(c[1]=1);let S=c.length-l.length;S>0&&(0,eJ.f)(c,S),1===l.length&&l.unshift(null),function(e,t,n,r,i,o){!function(e,t,n){for(let r=0;r<e.length;r++){let i=e[r];i.at>t&&i.at<n&&((0,eW.Ai)(e,i),r--)}}(e,i,o);for(let s=0;s<t.length;s++){var a;e.push({value:t[s],at:(0,e2.k)(i,o,r[s]),easing:(a=s,(0,e9.h)(n)?n[e1(0,n.length,a)]:n)})}}(r,l,p,c,x,E),g=Math.max(y+v,g),d=Math.max(E,d)};if((0,W.S)(m))v(h,p,e7("default",e5(m,s)));else{let e=eZ(m,h,r,l),t=e.length;for(let n=0;n<t;n++){let r=e5(e[n],s);for(let e in h)v(h[e],p&&p[e]?{...p,...p[e]}:{...p},e7(e,r),n,t)}}c=f,f+=g}return s.forEach((e,r)=>{for(let i in e){let o=e[i];o.sort(e3);let s=[],l=[],u=[];for(let e=0;e<o.length;e++){let{at:t,value:n,easing:r}=o[e];s.push(n),l.push((0,K.q)(0,d,t)),u.push(r||"easeOut")}0!==l[0]&&(l.unshift(0),s.unshift(s[0]),u.unshift("easeInOut")),1!==l[l.length-1]&&(l.push(1),s.push(null)),a.has(r)||a.set(r,{keyframes:{},transition:{}});let c=a.get(r);c.keyframes[i]=s,c.transition[i]={...t,duration:d,ease:u,times:l,...n}}}),a})(e,t,n,{spring:eX.o}).forEach(({keyframes:e,transition:t},n)=>{r.push(...tc(n,e,t))}),r}(t,n,e):tc(t,n,r,e);let o=new e$.P(i);return e&&e.animations.push(o),o}}let td=tf();function tm(){let e=(0,I.M)(()=>({current:null,animations:[]})),t=(0,I.M)(()=>tf(e));return ej(()=>{e.animations.forEach(e=>e.stop())}),[e,t]}var th=n(93098),tp=n(60360),tg=n(56399),tv=n(76359),ty=n(84036);function tw(e,t,n){e.style.setProperty(`--${t}`,n)}function tM(e,t,n){e.style[t]=n}var tx=n(19431);let tE=(0,n(21713).p)(()=>{try{document.createElement("div").animate({opacity:[1]})}catch(e){return!1}return!0});var tS=n(51175);let tb=new WeakMap,tC="easeOut";function tA(e){let t=tb.get(e)||new Map;return tb.set(e,t),tb.get(e)}class tV{constructor(e,t,n,r){let i=t.startsWith("--");this.setValue=i?tw:tM,this.options=r,this.updateFinishedPromise(),(0,U.V)("string"!=typeof r.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "framer-motion"?');let o=tA(e).get(t);if(o&&o.stop(),Array.isArray(n)||(n=[n]),!function(e,t,n){for(let r=0;r<t.length;r++)null===t[r]&&(t[r]=0===r?n():t[r-1]),"number"==typeof t[r]&&tg.o[e]&&(t[r]=tg.o[e].transform(t[r]));!tE()&&t.length<2&&t.unshift(n())}(t,n,()=>t.startsWith("--")?e.style.getPropertyValue(t):window.getComputedStyle(e)[t]),(0,eK.W)(r.type)){let e=eq(r,100,r.type);r.ease=(0,tx.n)()?e.ease:tC,r.duration=(0,eY.f)(e.duration),r.type="keyframes"}else r.ease=r.ease||tC;this.removeAnimation=()=>{var n;return null===(n=tb.get(e))||void 0===n?void 0:n.delete(t)};let a=()=>{this.setValue(e,t,(0,ty.X)(n,this.options)),this.cancel(),this.resolveFinishedPromise()};(0,tS.B)()?(this.animation=(0,tp.R)(e,t,n,r),!1===r.autoplay&&this.animation.pause(),this.animation.onfinish=a,this.pendingTimeline&&(0,tv.v)(this.animation,this.pendingTimeline),tA(e).set(t,this)):a()}get duration(){return(0,eY.X)(this.options.duration||300)}get time(){var e;return this.animation?(0,eY.X)((null===(e=this.animation)||void 0===e?void 0:e.currentTime)||0):0}set time(e){this.animation&&(this.animation.currentTime=(0,eY.f)(e))}get speed(){return this.animation?this.animation.playbackRate:1}set speed(e){this.animation&&(this.animation.playbackRate=e)}get state(){return this.animation?this.animation.playState:"finished"}get startTime(){return this.animation?this.animation.startTime:null}flatten(){var e;this.animation&&(null===(e=this.animation.effect)||void 0===e||e.updateTiming({easing:"linear"}))}play(){"finished"===this.state&&this.updateFinishedPromise(),this.animation&&this.animation.play()}pause(){this.animation&&this.animation.pause()}stop(){this.animation&&"idle"!==this.state&&"finished"!==this.state&&(this.animation.commitStyles&&this.animation.commitStyles(),this.cancel())}complete(){this.animation&&this.animation.finish()}cancel(){this.removeAnimation();try{this.animation&&this.animation.cancel()}catch(e){}}then(e,t){return this.currentFinishedPromise.then(e,t)}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}attachTimeline(e){return this.animation?(0,tv.v)(this.animation,e):this.pendingTimeline=e,ey.l}}let tI=e=>function(t,n,r){return new e$.P(function(e,t,n,r){let i=Y(e,r),o=i.length;(0,U.V)(!!o,"No valid element provided.");let a=[];for(let e=0;e<o;e++){let r=i[e],s={...n};for(let n in"function"==typeof s.delay&&(s.delay=s.delay(e,o)),t){let e=t[n],i={...(0,th.r)(s,n)};i.duration=i.duration?(0,eY.f)(i.duration):i.duration,i.delay=(0,eY.f)(i.delay||0),a.push(new tV(r,n,e,i))}}return a}(t,n,r,e))},tO=tI();function tP(){let e=(0,I.M)(()=>({current:null,animations:[]})),t=(0,I.M)(()=>tI(e));return ej(()=>{e.animations.forEach(e=>e.stop())}),[e,t]}function tT(){let e=(0,I.M)(eD);return(0,P.E)(e.mount,[]),e}let tG=tT;function tW(...e){let t=(0,C.useRef)(0),[n,r]=(0,C.useState)(e[t.current]);return[n,(0,C.useCallback)(n=>{t.current="number"!=typeof n?e1(0,e.length,t.current+1):n,r(e[t.current])},[e.length,...e])]}var tk=n(49674),tL=n(50342);let tR={some:0,all:1};function tF(e,t,{root:n,margin:r,amount:i="some"}={}){let o=Y(e),a=new WeakMap,s=new IntersectionObserver(e=>{e.forEach(e=>{let n=a.get(e.target);if(!!n!==e.isIntersecting){if(e.isIntersecting){let n=t(e);"function"==typeof n?a.set(e.target,n):s.unobserve(e.target)}else n&&(n(e),a.delete(e.target))}})},{root:n,rootMargin:r,threshold:"number"==typeof i?i:tR[i]});return o.forEach(e=>s.observe(e)),()=>s.disconnect()}function tB(e,{root:t,margin:n,amount:r,once:i=!1}={}){let[o,a]=(0,C.useState)(!1);return(0,C.useEffect)(()=>{if(!e.current||i&&o)return;let s={root:t&&t.current||void 0,margin:n,amount:r};return tF(e.current,()=>(a(!0),i?void 0:()=>a(!1)),s)},[t,e,n,i,r]),o}class tz{constructor(){this.componentControls=new Set}subscribe(e){return this.componentControls.add(e),()=>this.componentControls.delete(e)}start(e,t){this.componentControls.forEach(n=>{n.start(e.nativeEvent||e,t)})}}let tH=()=>new tz;function tN(){return(0,I.M)(tH)}var tQ=n(16478);function tD(e,t,n,r){(0,C.useEffect)(()=>{let i=e.current;if(n&&i)return(0,tQ.k)(i,t,n,r)},[e,t,n,r])}var tj=n(27484),t$=n(2350);function tX(e){return null!==e&&"object"==typeof e&&t$.o in e}function tU(e){if(tX(e))return e[t$.o]}var tY=n(7769),tq=n(18041);function tJ(){return t_}function t_(e){tq.i.current&&(tq.i.current.isUpdating=!1,tq.i.current.blockUpdate(),e&&e())}var tZ=n(48565),tK=n(18036);function t0(){let[e,t]=(0,tZ.C)(),n=(0,C.useRef)();return(0,C.useEffect)(()=>{T.Gt.postRender(()=>T.Gt.postRender(()=>{t===n.current&&(tK.d.current=!1)}))},[t]),r=>{t_(()=>{tK.d.current=!0,e(),r(),n.current=t+1})}}function t1(){tK.d.current=!1}function t9(){return(0,C.useCallback)(()=>{let e=tq.i.current;e&&e.resetTree()},[])}var t2=n(15381),t3=n(31661),t5=n(64376),t7=n(97299),t4=n(79864),t6=n(11348);let t8=(e,t)=>{let n=eG.f.has(t)?"transform":t;return`${e}: ${n}`};var ne=n(11208);let nt=new Map,nn=new Map;function nr(e,t,n){var r;let i=t8(e,t),o=nt.get(i);if(!o)return null;let{animation:a,startTime:s}=o;function l(){var r;null===(r=window.MotionCancelOptimisedAnimation)||void 0===r||r.call(window,e,t,n)}return(a.onfinish=l,null===s||(null===(r=window.MotionHandoffIsComplete)||void 0===r?void 0:r.call(window,e)))?(l(),null):s}var ni=n(2898);let no=new Set;function na(){no.forEach(e=>{e.animation.play(),e.animation.startTime=e.startTime}),no.clear()}function ns(e,t,n,r,i){if(window.MotionIsMounted)return;let s=e.dataset[ne.c];if(!s)return;window.MotionHandoffAnimation=nr;let l=t8(s,t);a||(a=(0,tp.R)(e,t,[n[0],n[0]],{duration:1e4,ease:"linear"}),nt.set(l,{animation:a,startTime:null}),window.MotionHandoffAnimation=nr,window.MotionHasOptimisedAnimation=(e,t)=>{if(!e)return!1;if(!t)return nn.has(e);let n=t8(e,t);return!!nt.get(n)},window.MotionHandoffMarkAsComplete=e=>{nn.has(e)&&nn.set(e,!0)},window.MotionHandoffIsComplete=e=>!0===nn.get(e),window.MotionCancelOptimisedAnimation=(e,t,n,r)=>{let i=t8(e,t),o=nt.get(i);o&&(n&&void 0===r?n.postRender(()=>{n.postRender(()=>{o.animation.cancel()})}):o.animation.cancel(),n&&r?(no.add(o),n.render(na)):(nt.delete(i),nt.size||(window.MotionCancelOptimisedAnimation=void 0)))},window.MotionCheckAppearSync=(e,t,n)=>{var r,i;let o=(0,ni.P)(e);if(!o)return;let a=null===(r=window.MotionHasOptimisedAnimation)||void 0===r?void 0:r.call(window,o,t),s=null===(i=e.props.values)||void 0===i?void 0:i[t];if(!a||!s)return;let l=n.on("change",e=>{var n;s.get()!==e&&(null===(n=window.MotionCancelOptimisedAnimation)||void 0===n||n.call(window,o,t),l())});return l});let u=()=>{a.cancel();let s=(0,tp.R)(e,t,n,r);void 0===o&&(o=performance.now()),s.startTime=o,nt.set(l,{animation:s,startTime:o}),i&&i(s)};nn.set(s,!1),a.ready?a.ready.then(u).catch(ey.l):u()}var nl=n(50512),nu=n(99979),nc=n(92729),nf=n(76177),nd=n(24297),nm=n(5843),nh=n(9314),np=n(25116);let ng=()=>({});class nv extends to.B{constructor(){super(...arguments),this.measureInstanceViewportBox=ti.ge}build(){}resetTransform(){}restoreTransform(){}removeValueFromRenderState(){}renderInstance(){}scrapeMotionValuesFromProps(){return ng()}getBaseTargetFromProps(){}readValueFromInstance(e,t,n){return n.initialState[t]||0}sortInstanceNodePosition(){return 0}}let ny=(0,np.T)({scrapeMotionValuesFromProps:ng,createRenderState:ng});function nw(e){let[t,n]=(0,C.useState)(e),r=ny({},!1),i=(0,I.M)(()=>new nv({props:{onUpdate:e=>{n({...e})}},visualState:r,presenceContext:null},{initialState:e}));return(0,C.useLayoutEffect)(()=>(i.mount({}),()=>i.unmount()),[i]),[t,(0,I.M)(()=>e=>(0,eN._)(i,e))]}let nM=e=>e>.001?1/e:1e5,nx=!1;function nE(e){let t=O(1),n=O(1),{visualElement:r}=(0,C.useContext)(nu.A);return(0,U.V)(!!(e||r),"If no scale values are provided, useInvertedScale must be used within a child of another motion component."),(0,U.$)(nx,"useInvertedScale is deprecated and will be removed in 3.0. Use the layout prop instead."),nx=!0,e?(t=e.scaleX||t,n=e.scaleY||n):r&&(t=r.getValue("scaleX",1),n=r.getValue("scaleY",1)),{scaleX:H(t,nM),scaleY:H(n,nM)}}var nS=n(12389);let nb=0,nC=({children:e})=>(C.useEffect(()=>{(0,U.V)(!1,"AnimateSharedLayout is deprecated: https://www.framer.com/docs/guide-upgrade/##shared-layout-animations")},[]),(0,nS.jsx)(x.o,{id:(0,I.M)(()=>`asl-${nb++}`),children:e}));var nA=n(15426);let nV=(0,C.createContext)(null),nI=(0,C.forwardRef)(function(e,t){let{children:n,as:r="ul",axis:i="y",onReorder:o,values:a,...s}=e,l=(0,I.M)(()=>g[r]),u=[],c=(0,C.useRef)(!1);return(0,U.V)(!!a,"Reorder.Group must be provided a values prop"),(0,C.useEffect)(()=>{c.current=!1}),(0,nS.jsx)(l,{...s,ref:t,ignoreStrict:!0,children:(0,nS.jsx)(nV.Provider,{value:{axis:i,registerItem:(e,t)=>{let n=u.findIndex(t=>e===t.value);-1!==n?u[n].layout=t[i]:u.push({value:e,layout:t[i]}),u.sort(nP)},updateOrder:(e,t,n)=>{if(c.current)return;let r=function(e,t,n,r){if(!r)return e;let i=e.findIndex(e=>e.value===t);if(-1===i)return e;let o=r>0?1:-1,a=e[i+o];if(!a)return e;let s=e[i],l=a.layout,u=(0,e2.k)(l.min,l.max,.5);return 1===o&&s.layout.max+n>u||-1===o&&s.layout.min+n<u?(0,eW.Pe)(e,i,i+o):e}(u,e,t,n);u!==r&&(c.current=!0,o(r.map(nO).filter(e=>-1!==a.indexOf(e))))}},children:n})})});function nO(e){return e.value}function nP(e,t){return e.layout.min-t.layout.min}function nT(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(0,W.S)(e)?e:O(t)}let nG=(0,C.forwardRef)(function(e,t){let{children:n,style:r={},value:i,as:o="li",onDrag:a,layout:s=!0,...l}=e,u=(0,I.M)(()=>g[o]),c=(0,C.useContext)(nV),f={x:nT(r.x),y:nT(r.y)},d=H([f.x,f.y],e=>{let[t,n]=e;return t||n?1:"unset"});(0,U.V)(!!c,"Reorder.Item must be a child of Reorder.Group");let{axis:m,registerItem:h,updateOrder:p}=c;return(0,nS.jsx)(u,{drag:m,...l,dragSnapToOrigin:!0,style:{...r,x:f.x,y:f.y,zIndex:d},layout:s,onDrag:(e,t)=>{let{velocity:n}=t;n[m]&&p(i,f[m].get(),n[m]),a&&a(e,t)},onLayoutMeasure:e=>h(i,e),ref:t,ignoreStrict:!0,children:n})});var nW=n(53457),nk=n(64548),nL=n(82571);function nR(e=.1,{startDelay:t=0,from:n=0,ease:r}={}){return(i,o)=>{let a=e*Math.abs(("number"==typeof n?n:function(e,t){if("first"===e)return 0;{let n=t-1;return"last"===e?n:n/2}}(n,o))-i);if(r){let t=o*e;a=(0,nL.K)(r)(a/t)*t}return t+a}}var nF=n(57162),nB=n(51019),nz=n(291),nH=n(69756);let nN=T.Gt,nQ=nH.q.reduce((e,t)=>(e[t]=e=>(0,T.WG)(e),e),{});var nD=n(53179),nj=n(25562),n$=n(32038),nX=n(28437),nU=n(86381);function nY(e,t="end"){return n=>{let r=(n="end"===t?Math.min(n,.999):Math.max(n,.001))*e,i="end"===t?Math.floor(r):Math.ceil(r);return(0,nF.q)(0,1,i/e)}}var nq=n(47019),nJ=n(50542),n_=n(89622),nZ=n(40748),nK=n(43228),n0=n(17158),n1=n(81997),n9=n(24014),n2=n(27112)}}]);