"use strict";exports.id=432,exports.ids=[432],exports.modules={38802:(t,e,i)=>{i.d(e,{z:()=>n});var s=i(19977),o=i(91607),r=i(94222);function n(t,e,i){let n=(0,r.S)(t)?t:(0,o.OQ)(t);return n.start((0,s.f)("",n,e,i)),n.animation}},93106:(t,e,i)=>{i.d(e,{tF:()=>n,xQ:()=>r});var s=i(423),o=i(28905);function r(){let t=(0,s.useContext)(o.t);if(null===t)return[!0,null];let{isPresent:e,onExitComplete:i,register:r}=t,n=(0,s.useId)();(0,s.useEffect)(()=>r(n),[]);let a=(0,s.useCallback)(()=>i&&i(n),[n,i]);return!e&&i?[!1,a]:[!0]}function n(){var t;return null===(t=(0,s.useContext)(o.t))||t.isPresent}},64538:(t,e,i)=>{i.d(e,{o:()=>d});var s=i(15238),o=i(423),r=i(44091),n=i(92708),a=i(88554);let l=t=>!t.isLayoutDirty&&t.willUpdate(!1),h=t=>!0===t,u=t=>h(!0===t)||"id"===t,d=({children:t,id:e,inherit:i=!0})=>{let d=(0,o.useContext)(r.L),c=(0,o.useContext)(n.Q),[p,m]=(0,a.C)(),v=(0,o.useRef)(null),g=d.id||c;null===v.current&&(u(i)&&g&&(e=e?g+"-"+e:g),v.current={id:e,group:h(i)&&d.group||function(){let t=new Set,e=new WeakMap,i=()=>t.forEach(l);return{add:s=>{t.add(s),e.set(s,s.addEventListener("willUpdate",i))},remove:s=>{t.delete(s);let o=e.get(s);o&&(o(),e.delete(s)),i()},dirty:i}}()});let y=(0,o.useMemo)(()=>({...v.current,forceRender:p}),[m]);return(0,s.jsx)(r.L.Provider,{value:y,children:t})}},92708:(t,e,i)=>{i.d(e,{Q:()=>s});let s=(0,i(423).createContext)(null)},37016:(t,e,i)=>{i.d(e,{$:()=>I});var s=i(32310),o=i(34929),r=i(48148),n=i(28110),a=i(23851),l=i(16927),h=i(95021),u=i(62112),d=i(12140),c=i(39846);class p{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=g(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=(0,u.w)(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:o}=c.uv;this.history.push({...s,timestamp:o});let{onStart:r,onMove:n}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),n&&n(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=m(e,this.transformPagePoint),c.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:o}=this.handlers;if(this.dragSnapToOrigin&&o&&o(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=g("pointercancel"===t.type?this.lastMoveEventInfo:m(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!(0,d.M)(t))return;this.dragSnapToOrigin=o,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=m((0,n.e)(t),this.transformPagePoint),{point:a}=r,{timestamp:p}=c.uv;this.history=[{...a,timestamp:p}];let{onSessionStart:v}=e;v&&v(t,g(r,this.history)),this.removeListeners=(0,h.F)((0,l.h)(this.contextWindow,"pointermove",this.handlePointerMove),(0,l.h)(this.contextWindow,"pointerup",this.handlePointerUp),(0,l.h)(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,c.WG)(this.updatePoint)}}function m(t,e){return e?{point:e(t.point)}:t}function v(t,e){return{x:t.x-e.x,y:t.y-e.y}}function g({point:t},e){return{point:t,delta:v(t,y(e)),offset:v(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,o=y(t);for(;i>=0&&(s=t[i],!(o.timestamp-s.timestamp>(0,a.f)(.1)));)i--;if(!s)return{x:0,y:0};let r=(0,a.X)(o.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let n={x:(o.x-s.x)/r,y:(o.y-s.y)/r};return n.x===1/0&&(n.x=0),n.y===1/0&&(n.y=0),n}(e,0)}}function y(t){return t[t.length-1]}var f=i(18064),x=i(78751),P=i(92948),T=i(96082),D=i(80590),A=i(77031);function R(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function E(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function S(t,e,i){return{min:j(t,e),max:j(t,i)}}function j(t,e){return"number"==typeof t?t:t[e]||0}var w=i(34848),L=i(38328),k=i(45631),C=i(84226),B=i(68124),V=i(82075),b=i(19977);let M=({current:t})=>t?t.ownerDocument.defaultView:null;var U=i(1660);let F=new WeakMap;class G{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=(0,w.ge)(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new p(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor((0,n.e)(t,"page").point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:o}=this.getProps();if(i&&!s&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=(0,f.nQ)(i),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),(0,L.X)(t=>{let e=this.getAxisMotionValue(t).get()||0;if(V.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=(0,T.CQ)(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),o&&c.Gt.postRender(()=>o(t,e)),(0,U.g)(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:o,onDrag:r}=this.getProps();if(!i&&!this.openGlobalLock)return;let{offset:n}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(n),null!==this.currentDirection&&o&&o(this.currentDirection);return}this.updateAxis("x",e.point,n),this.updateAxis("y",e.point,n),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>(0,L.X)(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:M(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&c.Gt.postRender(()=>o(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!O(t,s,this.currentDirection))return;let o=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?(0,A.k)(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?(0,A.k)(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),o.set(r)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,o=this.constraints;e&&(0,x.X)(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=function(t,{top:e,left:i,bottom:s,right:o}){return{x:R(t.x,i,o),y:R(t.y,e,s)}}(s.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:S(t,"left","right"),y:S(t,"top","bottom")}}(i),o!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&(0,L.X)(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(s.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!(0,x.X)(e))return!1;let s=e.current;(0,r.V)(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:o}=this.visualElement;if(!o||!o.layout)return!1;let n=(0,k.L)(s,o.root,this.visualElement.getTransformPagePoint()),a={x:E((t=o.layout.layoutBox).x,n.x),y:E(t.y,n.y)};if(i){let t=i((0,C.pA)(a));this.hasMutatedConstraints=!!t,t&&(a=(0,C.FY)(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:n}=this.getProps(),a=this.constraints||{};return Promise.all((0,L.X)(n=>{if(!O(n,e,this.currentDirection))return;let l=a&&a[n]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[n]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...o,...l};return this.startAxisValueAnimation(n,h)})).then(n)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return(0,U.g)(this.visualElement,t),i.start((0,b.f)(t,i,0,e,this.visualElement,!1))}stopAnimation(){(0,L.X)(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){(0,L.X)(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){(0,L.X)(e=>{let{drag:i}=this.getProps();if(!O(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,o=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];o.set(t[e]-(0,A.k)(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!(0,x.X)(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};(0,L.X)(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=(0,T.CQ)(t),o=(0,T.CQ)(e);return o>s?i=(0,P.q)(e.min,e.max-s,t.min):s>o&&(i=(0,P.q)(t.min,t.max-o,e.min)),(0,D.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),(0,L.X)(e=>{if(!O(e,t,null))return;let i=this.getAxisMotionValue(e),{min:o,max:r}=this.constraints[e];i.set((0,A.k)(o,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;F.set(this.visualElement,this);let t=this.visualElement.current,e=(0,l.h)(t,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),i=()=>{let{dragConstraints:t}=this.getProps();(0,x.X)(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:s}=this.visualElement,o=s.addEventListener("measure",i);s&&!s.layout&&(s.root&&s.root.updateScroll(),s.updateLayout()),c.Gt.read(i);let r=(0,B.k)(window,"resize",()=>this.scalePositionWithinConstraints()),n=s.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&((0,L.X)(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),e(),o(),n&&n()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:o=!1,dragElastic:r=.35,dragMomentum:n=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:o,dragElastic:r,dragMomentum:n}}}function O(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class X extends s.X{constructor(t){super(t),this.removeGroupControls=o.l,this.removeListeners=o.l,this.controls=new G(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||o.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let $=t=>(e,i)=>{t&&c.Gt.postRender(()=>t(e,i))};class N extends s.X{constructor(){super(...arguments),this.removePointerDownListener=o.l}onPointerDown(t){this.session=new p(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:M(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:$(t),onStart:$(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&c.Gt.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=(0,l.h)(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var W=i(34281);let I={pan:{Feature:N},drag:{Feature:X,ProjectionNode:i(20357).P,MeasureLayout:W.$}}},34414:(t,e,i)=>{i.d(e,{Z:()=>r});var s=i(20357),o=i(34281);let r={layout:{ProjectionNode:s.P,MeasureLayout:o.$}}},34281:(t,e,i)=>{i.d(e,{$:()=>f});var s=i(15238),o=i(423),r=i(93106),n=i(44091),a=i(41931),l=i(59561),h=i(82075);function u(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let d={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!h.px.test(t))return t;t=parseFloat(t)}let i=u(t,e.target.x),s=u(t,e.target.y);return`${i}% ${s}%`}};var c=i(77031),p=i(9915),m=i(94575),v=i(23614),g=i(39846);class y extends o.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:o}=t;(0,m.$)(x),o&&(e.group&&e.group.add(o),i&&i.register&&s&&i.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),l.w.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:o}=this.props,r=i.projection;return r&&(r.isPresent=o,s||t.layoutDependency!==e||void 0===e?r.willUpdate():this.safeToRemove(),t.isPresent===o||(o?r.promote():r.relegate()||g.Gt.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),v.k.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function f(t){let[e,i]=(0,r.xQ)(),l=(0,o.useContext)(n.L);return(0,s.jsx)(y,{...t,layoutGroup:l,switchLayoutGroup:(0,o.useContext)(a.N),isPresent:e,safeToRemove:i})}let x={borderRadius:{...d,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:d,borderTopRightRadius:d,borderBottomLeftRadius:d,borderBottomRightRadius:d,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=p.f.parse(t);if(s.length>5)return t;let o=p.f.createTransformer(t),r="number"!=typeof s[0]?1:0,n=i.x.scale*e.x,a=i.y.scale*e.y;s[0+r]/=n,s[1+r]/=a;let l=(0,c.k)(n,a,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),o(s)}}}},96082:(t,e,i)=>{i.d(e,{CQ:()=>o,HQ:()=>r,N:()=>h,jA:()=>d,vb:()=>a});var s=i(77031);function o(t){return t.max-t.min}function r(t,e,i){return Math.abs(t-e)<=i}function n(t,e,i,r=.5){t.origin=r,t.originPoint=(0,s.k)(e.min,e.max,t.origin),t.scale=o(i)/o(e),t.translate=(0,s.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function a(t,e,i,s){n(t.x,e.x,i.x,s?s.originX:void 0),n(t.y,e.y,i.y,s?s.originY:void 0)}function l(t,e,i){t.min=i.min+e.min,t.max=t.min+o(e)}function h(t,e,i){l(t.x,e.x,i.x),l(t.y,e.y,i.y)}function u(t,e,i){t.min=e.min-i.min,t.max=t.min+o(e)}function d(t,e,i){u(t.x,e.x,i.x),u(t.y,e.y,i.y)}},20357:(t,e,i)=>{i.d(e,{P:()=>tk,i:()=>tL});var s=i(200),o=i(18338),r=i(92948),n=i(77031),a=i(34929),l=i(82075);let h=["TopLeft","TopRight","BottomLeft","BottomRight"],u=h.length,d=t=>"string"==typeof t?parseFloat(t):t,c=t=>"number"==typeof t||l.px.test(t);function p(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let m=g(0,.5,o.yT),v=g(.5,.95,a.l);function g(t,e,i){return s=>s<t?0:s>e?1:i((0,r.q)(t,e,s))}function y(t,e){t.min=e.min,t.max=e.max}function f(t,e){y(t.x,e.x),y(t.y,e.y)}function x(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}var P=i(91327),T=i(96082);function D(t,e,i,s,o){return t-=e,t=(0,P.hq)(t,1/i,s),void 0!==o&&(t=(0,P.hq)(t,1/o,s)),t}function A(t,e,[i,s,o],r,a){!function(t,e=0,i=1,s=.5,o,r=t,a=t){if(l.KN.test(e)&&(e=parseFloat(e),e=(0,n.k)(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let h=(0,n.k)(r.min,r.max,s);t===r&&(h-=e),t.min=D(t.min,e,i,h,o),t.max=D(t.max,e,i,h,o)}(t,e[i],e[s],e[o],e.scale,r,a)}let R=["x","scaleX","originX"],E=["y","scaleY","originY"];function S(t,e,i,s){A(t.x,e,R,i?i.x:void 0,s?s.x:void 0),A(t.y,e,E,i?i.y:void 0,s?s.y:void 0)}var j=i(50368);function w(t){return 0===t.translate&&1===t.scale}function L(t){return w(t.x)&&w(t.y)}function k(t,e){return t.min===e.min&&t.max===e.max}function C(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function B(t,e){return C(t.x,e.x)&&C(t.y,e.y)}function V(t){return(0,T.CQ)(t.x)/(0,T.CQ)(t.y)}function b(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}var M=i(80212);class U{constructor(){this.members=[]}add(t){(0,M.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,M.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var F=i(94575),G=i(38328),O=i(13072),X=i(74955),$=i(41596),N=i(59561),W=i(94954),I=i(43857),H=i(38802),Q=i(80590),z=i(39846),Y=i(9468),q=i(23614),K=i(49388),Z=i(34848);let _={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},J="undefined"!=typeof window&&void 0!==window.MotionDebug,tt=["","X","Y","Z"],te={visibility:"hidden"},ti=0;function ts(t,e,i,s){let{latestValues:o}=e;o[t]&&(i[t]=o[t],e.setStaticValue(t,0),s&&(s[t]=0))}function to({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:o,resetTransform:r}){return class{constructor(t={},i=null==e?void 0:e()){this.id=ti++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,J&&(_.totalNodes=_.resolvedTargetDeltas=_.recalculatedProjection=0),this.nodes.forEach(ta),this.nodes.forEach(tm),this.nodes.forEach(tv),this.nodes.forEach(tl),J&&window.MotionDebug.record(_)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new X.Y)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new s.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=(0,I.x)(e),this.instance=e;let{layoutId:s,layout:o,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(o||s)&&(this.isLayoutDirty=!0),t){let i;let s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=(0,W.c)(s,250),N.w.hasAnimatedSinceResize&&(N.w.hasAnimatedSinceResize=!1,this.nodes.forEach(tp))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&r&&(s||o)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let o=this.options.transition||r.getDefaultTransition()||tT,{onLayoutAnimationStart:n,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!B(this.targetLayout,s)||i,h=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,h);let e={...(0,j.r)(o,"layout"),onPlay:n,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||tp(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,z.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(tg),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=(0,K.P)(i);if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",z.Gt,!(t||i))}let{parent:o}=e;o&&!o.hasCheckedOptimisedAppear&&t(o)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(tu);return}this.isUpdating||this.nodes.forEach(td),this.isUpdating=!1,this.nodes.forEach(tc),this.nodes.forEach(tr),this.nodes.forEach(tn),this.clearAllSnapshots();let t=Y.k.now();z.uv.delta=(0,Q.q)(0,1e3/60,t-z.uv.timestamp),z.uv.timestamp=t,z.uv.isProcessing=!0,z.PP.update.process(z.uv),z.PP.preRender.process(z.uv),z.PP.render.process(z.uv),z.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,q.k.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(th),this.sharedNodes.forEach(ty)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,z.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){z.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=(0,Z.ge)(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=o(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!L(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,o=s!==this.prevTransformTemplateValue;t&&(e||(0,O.HD)(this.latestValues)||o)&&(r(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),tR((e=s).x),tR(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return(0,Z.ge)();let i=e.measureViewportBox();if(!((null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)||this.path.some(tS))){let{scroll:t}=this.root;t&&((0,P.Ql)(i.x,t.offset.x),(0,P.Ql)(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=(0,Z.ge)();if(f(i,t),null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let s=this.path[e],{scroll:o,options:r}=s;s!==this.root&&o&&r.layoutScroll&&(o.wasRoot&&f(i,t),(0,P.Ql)(i.x,o.offset.x),(0,P.Ql)(i.y,o.offset.y))}return i}applyTransform(t,e=!1){let i=(0,Z.ge)();f(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&(0,P.Ww)(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),(0,O.HD)(s.latestValues)&&(0,P.Ww)(i,s.latestValues)}return(0,O.HD)(this.latestValues)&&(0,P.Ww)(i,this.latestValues),i}removeTransform(t){let e=(0,Z.ge)();f(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!(0,O.HD)(i.latestValues))continue;(0,O.vk)(i.latestValues)&&i.updateSnapshot();let s=(0,Z.ge)();f(s,i.measurePageBox()),S(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return(0,O.HD)(this.latestValues)&&S(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==z.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e;let i=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=i.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=i.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=i.isSharedProjectionDirty);let s=!!this.resumingFrom||this!==i;if(!(t||s&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:o,layoutId:r}=this.options;if(this.layout&&(o||r)){if(this.resolvedRelativeTargetAt=z.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,Z.ge)(),this.relativeTargetOrigin=(0,Z.ge)(),(0,T.jA)(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),f(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=(0,Z.ge)(),this.targetWithTransforms=(0,Z.ge)()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),(0,T.N)(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):f(this.target,this.layout.layoutBox),(0,P.o4)(this.target,this.targetDelta)):f(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=(0,Z.ge)(),this.relativeTargetOrigin=(0,Z.ge)(),(0,T.jA)(this.relativeTargetOrigin,this.target,t.target),f(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}J&&_.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||(0,O.vk)(this.parent.latestValues)||(0,O.vF)(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,s=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(s=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===z.uv.timestamp&&(s=!1),s)return;let{layout:o,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(o||r))return;f(this.layoutCorrected,this.layout.layoutBox);let n=this.treeScale.x,a=this.treeScale.y;(0,P.OU)(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=(0,Z.ge)());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(x(this.prevProjectionDelta.x,this.projectionDelta.x),x(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),(0,T.vb)(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===n&&this.treeScale.y===a&&b(this.projectionDelta.x,this.prevProjectionDelta.x)&&b(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),J&&_.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=(0,Z.xU)(),this.projectionDelta=(0,Z.xU)(),this.projectionDeltaWithTransform=(0,Z.xU)()}setAnimationOrigin(t,e=!1){let i;let s=this.snapshot,o=s?s.latestValues:{},r={...this.latestValues},a=(0,Z.xU)();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let g=(0,Z.ge)(),y=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),x=this.getStack(),P=!x||x.members.length<=1,D=!!(y&&!P&&!0===this.options.crossfade&&!this.path.some(tP));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(tf(a.x,t.x,s),tf(a.y,t.y,s),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var x,A,R,E;(0,T.jA)(g,this.layout.layoutBox,this.relativeParent.layout.layoutBox),R=this.relativeTarget,E=this.relativeTargetOrigin,tx(R.x,E.x,g.x,s),tx(R.y,E.y,g.y,s),i&&(x=this.relativeTarget,A=i,k(x.x,A.x)&&k(x.y,A.y))&&(this.isProjectionDirty=!1),i||(i=(0,Z.ge)()),f(i,this.relativeTarget)}y&&(this.animationValues=r,function(t,e,i,s,o,r){o?(t.opacity=(0,n.k)(0,void 0!==i.opacity?i.opacity:1,m(s)),t.opacityExit=(0,n.k)(void 0!==e.opacity?e.opacity:1,0,v(s))):r&&(t.opacity=(0,n.k)(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,s));for(let o=0;o<u;o++){let r=`border${h[o]}Radius`,a=p(e,r),u=p(i,r);(void 0!==a||void 0!==u)&&(a||(a=0),u||(u=0),0===a||0===u||c(a)===c(u)?(t[r]=Math.max((0,n.k)(d(a),d(u),s),0),(l.KN.test(u)||l.KN.test(a))&&(t[r]+="%")):t[r]=u)}(e.rotate||i.rotate)&&(t.rotate=(0,n.k)(e.rotate||0,i.rotate||0,s))}(r,o,this.latestValues,s,D,P)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,z.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=z.Gt.update(()=>{N.w.hasAnimatedSinceResize=!0,this.currentAnimation=(0,H.z)(0,1e3,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:o}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&tE(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||(0,Z.ge)();let e=(0,T.CQ)(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=(0,T.CQ)(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}f(e,i),(0,P.Ww)(e,o),(0,T.vb)(this.projectionDeltaWithTransform,this.layoutCorrected,e,o)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new U),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&ts("z",t,s,this.animationValues);for(let e=0;e<tt.length;e++)ts(`rotate${tt[e]}`,t,s,this.animationValues),ts(`skew${tt[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return te;let s={visibility:""},o=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,s.opacity="",s.pointerEvents=(0,$.u)(null==t?void 0:t.pointerEvents)||"",s.transform=o?o(this.latestValues,""):"none",s;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=(0,$.u)(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!(0,O.HD)(this.latestValues)&&(e.transform=o?o({},""):"none",this.hasProjected=!1),e}let n=r.animationValues||r.latestValues;this.applyTransformsToTarget(),s.transform=function(t,e,i){let s="",o=t.x.translate/e.x,r=t.y.translate/e.y,n=(null==i?void 0:i.z)||0;if((o||r||n)&&(s=`translate3d(${o}px, ${r}px, ${n}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:o,rotateY:r,skewX:n,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),o&&(s+=`rotateX(${o}deg) `),r&&(s+=`rotateY(${r}deg) `),n&&(s+=`skewX(${n}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),o&&(s.transform=o(n,s.transform));let{x:a,y:l}=this.projectionDelta;for(let t in s.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,r.animationValues?s.opacity=r===this?null!==(i=null!==(e=n.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:s.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,F.H){if(void 0===n[t])continue;let{correct:e,applyTo:i}=F.H[t],o="none"===s.transform?n[t]:e(n[t],r);if(i){let t=i.length;for(let e=0;e<t;e++)s[i[e]]=o}else s[t]=o}return this.options.layoutId&&(s.pointerEvents=r===this?(0,$.u)(null==t?void 0:t.pointerEvents)||"":"none"),s}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(tu),this.root.sharedNodes.clear()}}}function tr(t){t.updateLayout()}function tn(t){var e;let i=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:s}=t.layout,{animationType:o}=t.options,r=i.source!==t.layout.source;"size"===o?(0,G.X)(t=>{let s=r?i.measuredBox[t]:i.layoutBox[t],o=(0,T.CQ)(s);s.min=e[t].min,s.max=s.min+o}):tE(o,i.layoutBox,e)&&(0,G.X)(s=>{let o=r?i.measuredBox[s]:i.layoutBox[s],n=(0,T.CQ)(e[s]);o.max=o.min+n,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+n)});let n=(0,Z.xU)();(0,T.vb)(n,e,i.layoutBox);let a=(0,Z.xU)();r?(0,T.vb)(a,t.applyTransform(s,!0),i.measuredBox):(0,T.vb)(a,e,i.layoutBox);let l=!L(n),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:o,layout:r}=s;if(o&&r){let n=(0,Z.ge)();(0,T.jA)(n,i.layoutBox,o.layoutBox);let a=(0,Z.ge)();(0,T.jA)(a,e,r.layoutBox),B(n,a)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=n,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:n,hasLayoutChanged:l,hasRelativeTargetChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function ta(t){J&&_.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function tl(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function th(t){t.clearSnapshot()}function tu(t){t.clearMeasurements()}function td(t){t.isLayoutDirty=!1}function tc(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function tp(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function tm(t){t.resolveTargetDelta()}function tv(t){t.calcProjection()}function tg(t){t.resetSkewAndRotation()}function ty(t){t.removeLeadSnapshot()}function tf(t,e,i){t.translate=(0,n.k)(e.translate,0,i),t.scale=(0,n.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function tx(t,e,i,s){t.min=(0,n.k)(e.min,i.min,s),t.max=(0,n.k)(e.max,i.max,s)}function tP(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let tT={duration:.45,ease:[.4,0,.1,1]},tD=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),tA=tD("applewebkit/")&&!tD("chrome/")?Math.round:a.l;function tR(t){t.min=tA(t.min),t.max=tA(t.max)}function tE(t,e,i){return"position"===t||"preserve-aspect"===t&&!(0,T.HQ)(V(e),V(i),.2)}function tS(t){var e;return t!==t.root&&(null===(e=t.scroll)||void 0===e?void 0:e.wasRoot)}var tj=i(68124);let tw=to({attachResizeListener:(t,e)=>(0,tj.k)(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),tL={current:void 0},tk=to({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!tL.current){let t=new tw({});t.mount(window),t.setOptions({layoutScroll:!0}),tL.current=t}return tL.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position})},59561:(t,e,i)=>{i.d(e,{w:()=>s});let s={hasAnimatedSinceResize:!0,hasEverUpdated:!1}},38328:(t,e,i)=>{i.d(e,{X:()=>s});function s(t){return[t("x"),t("y")]}},76021:(t,e,i)=>{i.d(e,{z:()=>r});var s=i(37016),o=i(34414);let r={...i(76393).l,...s.$,...o.Z}},43857:(t,e,i)=>{i.d(e,{x:()=>s});function s(t){return t instanceof SVGElement&&"svg"!==t.tagName}},74955:(t,e,i)=>{i.d(e,{Y:()=>r});var s=i(80212);let o=(t,e)=>t.depth-e.depth;class r{constructor(){this.children=[],this.isDirty=!1}add(t){(0,s.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,s.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(o),this.isDirty=!1,this.children.forEach(t)}}},94954:(t,e,i)=>{i.d(e,{c:()=>r});var s=i(9468),o=i(39846);function r(t,e){let i=s.k.now(),r=({timestamp:s})=>{let n=s-i;n>=e&&((0,o.WG)(r),t(n-e))};return o.Gt.read(r,!0),()=>(0,o.WG)(r)}},62112:(t,e,i)=>{i.d(e,{I:()=>s,w:()=>o});let s=(t,e)=>Math.abs(t-e);function o(t,e){return Math.sqrt(s(t.x,e.x)**2+s(t.y,e.y)**2)}},88554:(t,e,i)=>{i.d(e,{C:()=>n});var s=i(423),o=i(43430),r=i(39846);function n(){let t=function(){let t=(0,s.useRef)(!1);return(0,o.E)(()=>(t.current=!0,()=>{t.current=!1}),[]),t}(),[e,i]=(0,s.useState)(0),n=(0,s.useCallback)(()=>{t.current&&i(e+1)},[e]);return[(0,s.useCallback)(()=>r.Gt.postRender(n),[n]),e]}}};