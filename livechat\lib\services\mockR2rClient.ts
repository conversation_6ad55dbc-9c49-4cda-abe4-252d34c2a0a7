/**
 * 模拟的r2r客户端
 * 用于在没有安装r2r-js库的情况下测试登录功能
 */

// 模拟的用户数据
const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'admin',
    role: 'admin',
  },
  {
    id: '2',
    email: '<EMAIL>',
    password: 'user',
    role: 'user',
  },
];

// 模拟的r2r客户端
export class MockR2RClient {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;
  private serverUrl: string;

  constructor(serverUrl: string) {
    this.serverUrl = serverUrl;
  }

  setTokens(accessToken: string, refreshToken: string) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
  }

  get users() {
    return {
      login: async ({ email, password }: { email: string; password: string }) => {
        // 模拟登录
        const user = mockUsers.find(
          (u) => u.email === email && u.password === password
        );

        if (!user) {
          throw new Error('Invalid email or password');
        }

        // 生成模拟的令牌
        const accessToken = `mock-access-token-${Date.now()}`;
        const refreshToken = `mock-refresh-token-${Date.now()}`;

        return {
          results: {
            accessToken: {
              token: accessToken,
            },
            refreshToken: {
              token: refreshToken,
            },
          },
        };
      },
      loginWithToken: async ({ accessToken }: { accessToken: string }) => {
        // 模拟使用令牌登录
        if (!accessToken.startsWith('mock-access-token-')) {
          throw new Error('Invalid token');
        }

        return {
          accessToken: {
            token: accessToken,
          },
        };
      },
      me: async () => {
        // 模拟获取用户信息
        if (!this.accessToken) {
          throw new Error('Not authenticated');
        }

        // 根据令牌确定用户角色
        const isAdmin = this.accessToken.includes('admin');

        return {
          results: {
            id: isAdmin ? '1' : '2',
            email: isAdmin ? '<EMAIL>' : '<EMAIL>',
            role: isAdmin ? 'admin' : 'user',
          },
        };
      },
      logout: async () => {
        // 模拟登出
        this.accessToken = null;
        this.refreshToken = null;
        return { success: true };
      },
    };
  }

  get system() {
    return {
      settings: async () => {
        // 模拟获取系统设置
        if (!this.accessToken) {
          throw new Error('Not authenticated');
        }

        // 只有管理员可以访问系统设置
        if (!this.accessToken.includes('admin')) {
          throw { status: 403, message: 'Forbidden' };
        }

        return {
          results: {
            settings: {},
          },
        };
      },
    };
  }
}

/**
 * 创建模拟的r2r客户端
 * @param instanceUrl R2R服务器URL
 */
export const createMockR2RClient = (instanceUrl: string) => {
  return new MockR2RClient(instanceUrl);
};

/**
 * 检查R2R服务器健康状态
 * @param url R2R服务器URL
 */
export const checkMockR2RServerHealth = async (): Promise<boolean> => {
  // 模拟服务器健康检查
  return true;
};