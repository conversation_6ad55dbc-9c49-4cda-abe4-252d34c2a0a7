(()=>{var e={};e.id=859,e.ids=[536,859],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},12412:e=>{"use strict";e.exports=require("assert")},55511:e=>{"use strict";e.exports=require("crypto")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},81630:e=>{"use strict";e.exports=require("http")},55591:e=>{"use strict";e.exports=require("https")},21820:e=>{"use strict";e.exports=require("os")},33873:e=>{"use strict";e.exports=require("path")},27910:e=>{"use strict";e.exports=require("stream")},83997:e=>{"use strict";e.exports=require("tty")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},77464:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var a=r(13950),s=r(14953),i=r(13905),n=r.n(i),o=r(98238),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["auth",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,65143)),"D:\\LLM\\Learning\\ai_coding\\anythingchat\\livechat\\app\\auth\\login\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,66965))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,42221)),"D:\\LLM\\Learning\\ai_coding\\anythingchat\\livechat\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,35453)),"D:\\LLM\\Learning\\ai_coding\\anythingchat\\livechat\\app\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,66965))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["D:\\LLM\\Learning\\ai_coding\\anythingchat\\livechat\\app\\auth\\login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/auth/login/page",pathname:"/auth/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},76801:(e,t,r)=>{Promise.resolve().then(r.bind(r,65143))},13249:(e,t,r)=>{Promise.resolve().then(r.bind(r,16708))},16708:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var a=r(15238),s=r(423),i=r(78320);let n=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))}),o=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))});var l=r(59579),c=r(65838),d=r(1998),u=r(33175),p=r(77260),h=r(37619),m=r(83031),x=(0,p.Rf)((e,t)=>{var r;let{as:s,className:i,children:n,...o}=e,l=(0,h.zD)(t),{slots:c,classNames:d}=(0,u.f)(),p=(0,m.$z)(null==d?void 0:d.header,i);return(0,a.jsx)(s||"div",{ref:l,className:null==(r=c.header)?void 0:r.call(c,{class:p}),...o,children:n})});x.displayName="HeroUI.CardHeader";var g=r(6186),v=r(54851),f=r(60567),b=r(59536);let y=()=>{let e=(0,i.useRouter)(),{login:t,isAuthenticated:r}=(0,l.J)(),[u,p]=(0,s.useState)(""),[h,m]=(0,s.useState)(""),[y,w]=(0,s.useState)(""),[j,k]=(0,s.useState)(!1),[L,N]=(0,s.useState)(!1),[S,C]=(0,s.useState)(null),[_,q]=(0,s.useState)(!1),[M,P]=(0,s.useState)(null);(0,s.useEffect)(()=>{(async()=>{try{let e=await (0,b.loadChatConfig)(),t=(0,b.getDeploymentUrl)(e);w(t)}catch(e){console.error("Failed to load configuration:",e),w("http://localhost:7272")}})()},[]),(0,s.useEffect)(()=>{r&&e.push("/sentio")},[r,e]),(0,s.useEffect)(()=>{if(_){let t=setTimeout(()=>{e.push("/sentio")},1e3);return()=>clearTimeout(t)}},[_,e]);let E=async()=>{try{let e=(await fetch(`${y}/v3/health`)).ok;return P(e),e}catch(e){return console.error("Health check failed:",e),P(!1),!1}},T=async e=>{e.preventDefault(),N(!0),C(null);try{await t(u,h,y),q(!0)}catch(r){console.error("Login failed:",r);let e=await E(),t="An unknown error occurred";r instanceof Error?t=r.message:"string"==typeof r&&(t=r),C(`${t} ${e?"服务器运行正常，请检查您的凭据后重试。":"无法与服务器通信，请检查配置文件中的API地址是否正确。"}`)}finally{N(!1)}};return _?(0,a.jsx)("div",{className:"min-h-screen bg-zinc-900 flex flex-col items-center justify-center p-8",children:(0,a.jsx)(c.Z,{className:"w-full max-w-md",children:(0,a.jsx)(d.U,{className:"pt-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-green-600 text-lg font-semibold mb-2",children:"登录成功！"}),(0,a.jsx)("div",{className:"text-gray-600 text-sm",children:"正在跳转到聊天页面..."})]})})})}):(0,a.jsx)("div",{className:"min-h-screen bg-zinc-900 flex flex-col items-center justify-center p-8",children:(0,a.jsxs)(c.Z,{className:"w-full max-w-md shadow-2xl",children:[(0,a.jsxs)(x,{className:"text-center pb-2",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-600 bg-clip-text text-transparent",children:"LiveChat"}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"欢迎登录"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm",children:"请输入您的邮箱和密码"})]}),!1===M&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md",children:(0,a.jsx)("span",{className:"text-red-600 dark:text-red-400 text-sm font-medium",children:"无法连接到服务器，请检查网络连接或联系管理员。"})})]}),(0,a.jsx)(d.U,{children:(0,a.jsxs)("form",{onSubmit:T,className:"space-y-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)(g.r,{id:"email",name:"email",type:"email",label:"邮箱",placeholder:"请输入邮箱",value:u,onChange:e=>p(e.target.value),autoComplete:"email",required:!0,variant:"bordered",classNames:{label:"text-gray-700 dark:text-gray-300",input:"text-gray-900 dark:text-white"}})}),(0,a.jsx)("div",{children:(0,a.jsx)(g.r,{id:"password",name:"password",type:j?"text":"password",label:"密码",placeholder:"请输入密码",value:h,onChange:e=>m(e.target.value),autoComplete:"current-password",required:!0,variant:"bordered",classNames:{label:"text-gray-700 dark:text-gray-300",input:"text-gray-900 dark:text-white"},endContent:(0,a.jsx)("button",{type:"button",onClick:()=>{k(!j)},className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 focus:outline-none","aria-label":j?"隐藏密码":"显示密码",children:j?(0,a.jsx)(n,{className:"h-5 w-5"}):(0,a.jsx)(o,{className:"h-5 w-5"})})})}),(0,a.jsx)(v.T,{type:"submit",className:"w-full py-3 text-base font-medium bg-gradient-to-r from-blue-500 to-purple-600 text-white",disabled:L,size:"lg",children:L?(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(f.o,{size:"sm",color:"white"}),(0,a.jsx)("span",{children:"登录中..."})]}):"登录"}),S&&(0,a.jsx)("div",{className:"mt-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-md",children:(0,a.jsx)("span",{className:"text-red-600 dark:text-red-400 text-sm",children:S})})]})})]})})}},59536:(e,t,r)=>{"use strict";r.d(t,{getDeploymentUrl:()=>i,loadChatConfig:()=>s});let a={server:{apiUrl:"http://localhost:7272",useHttps:!1,apiVersion:"v3",timeout:3e4},app:{appName:"LiveChat",appDescription:"Live chat application with R2R integration",version:"1.0.0",defaultMode:"rag_agent",conversationHistoryLimit:10},vectorSearch:{enabled:!0,searchLimit:10,searchFilters:"{}",indexMeasure:"cosine_distance",includeMetadatas:!1,probes:void 0,efSearch:void 0},hybridSearch:{enabled:!1,fullTextWeight:void 0,semanticWeight:void 0,fullTextLimit:void 0,rrfK:void 0},graphSearch:{enabled:!0,kgSearchLevel:null,maxCommunityDescriptionLength:100,localSearchLimits:{},maxLlmQueries:void 0},ragGeneration:{temperature:.1,topP:1,topK:100,maxTokensToSample:1024,kgTemperature:.1,kgTopP:1,kgTopK:100,kgMaxTokensToSample:1024}},s=async()=>{try{let e=await fetch("/config.json");if(!e.ok)return console.warn("Failed to load config.json, using default configuration"),a;let t=await e.json();return{server:{...a.server,...t.server},app:{...a.app,...t.app},vectorSearch:{...a.vectorSearch,...t.vectorSearch},hybridSearch:{...a.hybridSearch,...t.hybridSearch},graphSearch:{...a.graphSearch,...t.graphSearch},ragGeneration:{...a.ragGeneration,...t.ragGeneration}}}catch(e){return console.error("Error loading configuration:",e),a}},i=e=>{let t=e||a;if(t.server.apiUrl.startsWith("http://")||t.server.apiUrl.startsWith("https://"))return t.server.apiUrl;let r=t.server.useHttps?"https":"http";return`${r}://${t.server.apiUrl}`}},65143:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(29622).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\livechat\\\\app\\\\auth\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\LLM\\Learning\\ai_coding\\anythingchat\\livechat\\app\\auth\\login\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[233,497,94,875],()=>r(77464));module.exports=a})();