"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-from-markdown@2.0.2";
exports.ids = ["vendor-chunks/mdast-util-from-markdown@2.0.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/mdast-util-from-markdown@2.0.2/node_modules/mdast-util-from-markdown/dev/lib/index.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/mdast-util-from-markdown@2.0.2/node_modules/mdast-util-from-markdown/dev/lib/index.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromMarkdown: () => (/* binding */ fromMarkdown)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/.pnpm/devlop@1.1.0/node_modules/devlop/lib/development.js\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/.pnpm/mdast-util-to-string@4.0.0/node_modules/mdast-util-to-string/lib/index.js\");\n/* harmony import */ var micromark__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark */ \"(ssr)/./node_modules/.pnpm/micromark@4.0.1/node_modules/micromark/dev/lib/postprocess.js\");\n/* harmony import */ var micromark__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark */ \"(ssr)/./node_modules/.pnpm/micromark@4.0.1/node_modules/micromark/dev/lib/parse.js\");\n/* harmony import */ var micromark__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark */ \"(ssr)/./node_modules/.pnpm/micromark@4.0.1/node_modules/micromark/dev/lib/preprocess.js\");\n/* harmony import */ var micromark_util_decode_numeric_character_reference__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-util-decode-numeric-character-reference */ \"(ssr)/./node_modules/.pnpm/micromark-util-decode-numeric-character-reference@2.0.2/node_modules/micromark-util-decode-numeric-character-reference/dev/index.js\");\n/* harmony import */ var micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-util-decode-string */ \"(ssr)/./node_modules/.pnpm/micromark-util-decode-string@2.0.1/node_modules/micromark-util-decode-string/dev/index.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/.pnpm/micromark-util-normalize-identifier@2.0.1/node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/.pnpm/micromark-util-symbol@2.0.1/node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var decode_named_character_reference__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! decode-named-character-reference */ \"(ssr)/./node_modules/.pnpm/decode-named-character-reference@1.0.2/node_modules/decode-named-character-reference/index.js\");\n/* harmony import */ var unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! unist-util-stringify-position */ \"(ssr)/./node_modules/.pnpm/unist-util-stringify-position@4.0.0/node_modules/unist-util-stringify-position/lib/index.js\");\n/**\n * @import {\n *   Break,\n *   Blockquote,\n *   Code,\n *   Definition,\n *   Emphasis,\n *   Heading,\n *   Html,\n *   Image,\n *   InlineCode,\n *   Link,\n *   ListItem,\n *   List,\n *   Nodes,\n *   Paragraph,\n *   PhrasingContent,\n *   ReferenceType,\n *   Root,\n *   Strong,\n *   Text,\n *   ThematicBreak\n * } from 'mdast'\n * @import {\n *   Encoding,\n *   Event,\n *   Token,\n *   Value\n * } from 'micromark-util-types'\n * @import {Point} from 'unist'\n * @import {\n *   CompileContext,\n *   CompileData,\n *   Config,\n *   Extension,\n *   Handle,\n *   OnEnterError,\n *   Options\n * } from './types.js'\n */\n\n\n\n\n\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * Turn markdown into a syntax tree.\n *\n * @overload\n * @param {Value} value\n * @param {Encoding | null | undefined} [encoding]\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @overload\n * @param {Value} value\n * @param {Options | null | undefined} [options]\n * @returns {Root}\n *\n * @param {Value} value\n *   Markdown to parse.\n * @param {Encoding | Options | null | undefined} [encoding]\n *   Character encoding for when `value` is `Buffer`.\n * @param {Options | null | undefined} [options]\n *   Configuration.\n * @returns {Root}\n *   mdast tree.\n */\nfunction fromMarkdown(value, encoding, options) {\n  if (typeof encoding !== 'string') {\n    options = encoding\n    encoding = undefined\n  }\n\n  return compiler(options)(\n    (0,micromark__WEBPACK_IMPORTED_MODULE_0__.postprocess)(\n      (0,micromark__WEBPACK_IMPORTED_MODULE_1__.parse)(options)\n        .document()\n        .write((0,micromark__WEBPACK_IMPORTED_MODULE_2__.preprocess)()(value, encoding, true))\n    )\n  )\n}\n\n/**\n * Note this compiler only understand complete buffering, not streaming.\n *\n * @param {Options | null | undefined} [options]\n */\nfunction compiler(options) {\n  /** @type {Config} */\n  const config = {\n    transforms: [],\n    canContainEols: ['emphasis', 'fragment', 'heading', 'paragraph', 'strong'],\n    enter: {\n      autolink: opener(link),\n      autolinkProtocol: onenterdata,\n      autolinkEmail: onenterdata,\n      atxHeading: opener(heading),\n      blockQuote: opener(blockQuote),\n      characterEscape: onenterdata,\n      characterReference: onenterdata,\n      codeFenced: opener(codeFlow),\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: opener(codeFlow, buffer),\n      codeText: opener(codeText, buffer),\n      codeTextData: onenterdata,\n      data: onenterdata,\n      codeFlowValue: onenterdata,\n      definition: opener(definition),\n      definitionDestinationString: buffer,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: opener(emphasis),\n      hardBreakEscape: opener(hardBreak),\n      hardBreakTrailing: opener(hardBreak),\n      htmlFlow: opener(html, buffer),\n      htmlFlowData: onenterdata,\n      htmlText: opener(html, buffer),\n      htmlTextData: onenterdata,\n      image: opener(image),\n      label: buffer,\n      link: opener(link),\n      listItem: opener(listItem),\n      listItemValue: onenterlistitemvalue,\n      listOrdered: opener(list, onenterlistordered),\n      listUnordered: opener(list),\n      paragraph: opener(paragraph),\n      reference: onenterreference,\n      referenceString: buffer,\n      resourceDestinationString: buffer,\n      resourceTitleString: buffer,\n      setextHeading: opener(heading),\n      strong: opener(strong),\n      thematicBreak: opener(thematicBreak)\n    },\n    exit: {\n      atxHeading: closer(),\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolink: closer(),\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: closer(),\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      characterReference: onexitcharacterreference,\n      codeFenced: closer(onexitcodefenced),\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onexitcodefencedfencemeta,\n      codeFlowValue: onexitdata,\n      codeIndented: closer(onexitcodeindented),\n      codeText: closer(onexitcodetext),\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: closer(),\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: closer(),\n      hardBreakEscape: closer(onexithardbreak),\n      hardBreakTrailing: closer(onexithardbreak),\n      htmlFlow: closer(onexithtmlflow),\n      htmlFlowData: onexitdata,\n      htmlText: closer(onexithtmltext),\n      htmlTextData: onexitdata,\n      image: closer(onexitimage),\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: closer(onexitlink),\n      listItem: closer(),\n      listOrdered: closer(),\n      listUnordered: closer(),\n      paragraph: closer(),\n      referenceString: onexitreferencestring,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      resource: onexitresource,\n      setextHeading: closer(onexitsetextheading),\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: closer(),\n      thematicBreak: closer()\n    }\n  }\n\n  configure(config, (options || {}).mdastExtensions || [])\n\n  /** @type {CompileData} */\n  const data = {}\n\n  return compile\n\n  /**\n   * Turn micromark events into an mdast tree.\n   *\n   * @param {Array<Event>} events\n   *   Events.\n   * @returns {Root}\n   *   mdast tree.\n   */\n  function compile(events) {\n    /** @type {Root} */\n    let tree = {type: 'root', children: []}\n    /** @type {Omit<CompileContext, 'sliceSerialize'>} */\n    const context = {\n      stack: [tree],\n      tokenStack: [],\n      config,\n      enter,\n      exit,\n      buffer,\n      resume,\n      data\n    }\n    /** @type {Array<number>} */\n    const listStack = []\n    let index = -1\n\n    while (++index < events.length) {\n      // We preprocess lists to add `listItem` tokens, and to infer whether\n      // items the list itself are spread out.\n      if (\n        events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listOrdered ||\n        events[index][1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listUnordered\n      ) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index)\n        } else {\n          const tail = listStack.pop()\n          ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(typeof tail === 'number', 'expected list ot be open')\n          index = prepareList(events, tail, index)\n        }\n      }\n    }\n\n    index = -1\n\n    while (++index < events.length) {\n      const handler = config[events[index][0]]\n\n      if (own.call(handler, events[index][1].type)) {\n        handler[events[index][1].type].call(\n          Object.assign(\n            {sliceSerialize: events[index][2].sliceSerialize},\n            context\n          ),\n          events[index][1]\n        )\n      }\n    }\n\n    // Handle tokens still being open.\n    if (context.tokenStack.length > 0) {\n      const tail = context.tokenStack[context.tokenStack.length - 1]\n      const handler = tail[1] || defaultOnError\n      handler.call(context, undefined, tail[0])\n    }\n\n    // Figure out `root` position.\n    tree.position = {\n      start: point(\n        events.length > 0 ? events[0][1].start : {line: 1, column: 1, offset: 0}\n      ),\n      end: point(\n        events.length > 0\n          ? events[events.length - 2][1].end\n          : {line: 1, column: 1, offset: 0}\n      )\n    }\n\n    // Call transforms.\n    index = -1\n    while (++index < config.transforms.length) {\n      tree = config.transforms[index](tree) || tree\n    }\n\n    return tree\n  }\n\n  /**\n   * @param {Array<Event>} events\n   * @param {number} start\n   * @param {number} length\n   * @returns {number}\n   */\n  function prepareList(events, start, length) {\n    let index = start - 1\n    let containerBalance = -1\n    let listSpread = false\n    /** @type {Token | undefined} */\n    let listItem\n    /** @type {number | undefined} */\n    let lineIndex\n    /** @type {number | undefined} */\n    let firstBlankLineIndex\n    /** @type {boolean | undefined} */\n    let atMarker\n\n    while (++index <= length) {\n      const event = events[index]\n\n      switch (event[1].type) {\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listUnordered:\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listOrdered:\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuote: {\n          if (event[0] === 'enter') {\n            containerBalance++\n          } else {\n            containerBalance--\n          }\n\n          atMarker = undefined\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank: {\n          if (event[0] === 'enter') {\n            if (\n              listItem &&\n              !atMarker &&\n              !containerBalance &&\n              !firstBlankLineIndex\n            ) {\n              firstBlankLineIndex = index\n            }\n\n            atMarker = undefined\n          }\n\n          break\n        }\n\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix:\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemValue:\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemMarker:\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefix:\n        case micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefixWhitespace: {\n          // Empty.\n\n          break\n        }\n\n        default: {\n          atMarker = undefined\n        }\n      }\n\n      if (\n        (!containerBalance &&\n          event[0] === 'enter' &&\n          event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefix) ||\n        (containerBalance === -1 &&\n          event[0] === 'exit' &&\n          (event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listUnordered ||\n            event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listOrdered))\n      ) {\n        if (listItem) {\n          let tailIndex = index\n          lineIndex = undefined\n\n          while (tailIndex--) {\n            const tailEvent = events[tailIndex]\n\n            if (\n              tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding ||\n              tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank\n            ) {\n              if (tailEvent[0] === 'exit') continue\n\n              if (lineIndex) {\n                events[lineIndex][1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEndingBlank\n                listSpread = true\n              }\n\n              tailEvent[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.lineEnding\n              lineIndex = tailIndex\n            } else if (\n              tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.linePrefix ||\n              tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuotePrefix ||\n              tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuotePrefixWhitespace ||\n              tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.blockQuoteMarker ||\n              tailEvent[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemIndent\n            ) {\n              // Empty\n            } else {\n              break\n            }\n          }\n\n          if (\n            firstBlankLineIndex &&\n            (!lineIndex || firstBlankLineIndex < lineIndex)\n          ) {\n            listItem._spread = true\n          }\n\n          // Fix position.\n          listItem.end = Object.assign(\n            {},\n            lineIndex ? events[lineIndex][1].start : event[1].end\n          )\n\n          events.splice(lineIndex || index, 0, ['exit', listItem, event[2]])\n          index++\n          length++\n        }\n\n        // Create a new list item.\n        if (event[1].type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.listItemPrefix) {\n          /** @type {Token} */\n          const item = {\n            type: 'listItem',\n            _spread: false,\n            start: Object.assign({}, event[1].start),\n            // @ts-expect-error: we’ll add `end` in a second.\n            end: undefined\n          }\n          listItem = item\n          events.splice(index, 0, ['enter', item, event[2]])\n          index++\n          length++\n          firstBlankLineIndex = undefined\n          atMarker = true\n        }\n      }\n    }\n\n    events[start][1]._spread = listSpread\n    return length\n  }\n\n  /**\n   * Create an opener handle.\n   *\n   * @param {(token: Token) => Nodes} create\n   *   Create a node.\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function opener(create, and) {\n    return open\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */\n    function open(token) {\n      enter.call(this, create(token), token)\n      if (and) and.call(this, token)\n    }\n  }\n\n  /**\n   * @type {CompileContext['buffer']}\n   */\n  function buffer() {\n    this.stack.push({type: 'fragment', children: []})\n  }\n\n  /**\n   * @type {CompileContext['enter']}\n   */\n  function enter(node, token, errorHandler) {\n    const parent = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(parent, 'expected `parent`')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)('children' in parent, 'expected `parent`')\n    /** @type {Array<Nodes>} */\n    const siblings = parent.children\n    siblings.push(node)\n    this.stack.push(node)\n    this.tokenStack.push([token, errorHandler || undefined])\n    node.position = {\n      start: point(token.start),\n      // @ts-expect-error: `end` will be patched later.\n      end: undefined\n    }\n  }\n\n  /**\n   * Create a closer handle.\n   *\n   * @param {Handle | undefined} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function closer(and) {\n    return close\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {undefined}\n     */\n    function close(token) {\n      if (and) and.call(this, token)\n      exit.call(this, token)\n    }\n  }\n\n  /**\n   * @type {CompileContext['exit']}\n   */\n  function exit(token, onExitError) {\n    const node = this.stack.pop()\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected `node`')\n    const open = this.tokenStack.pop()\n\n    if (!open) {\n      throw new Error(\n        'Cannot close `' +\n          token.type +\n          '` (' +\n          (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({start: token.start, end: token.end}) +\n          '): it’s not open'\n      )\n    } else if (open[0].type !== token.type) {\n      if (onExitError) {\n        onExitError.call(this, token, open[0])\n      } else {\n        const handler = open[1] || defaultOnError\n        handler.call(this, token, open[0])\n      }\n    }\n\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type !== 'fragment', 'unexpected fragment `exit`ed')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.position, 'expected `position` to be defined')\n    node.position.end = point(token.end)\n  }\n\n  /**\n   * @type {CompileContext['resume']}\n   */\n  function resume() {\n    return (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_6__.toString)(this.stack.pop())\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered() {\n    this.data.expectingFirstListItemValue = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (this.data.expectingFirstListItemValue) {\n      const ancestor = this.stack[this.stack.length - 2]\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(ancestor, 'expected nodes on stack')\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(ancestor.type === 'list', 'expected list on stack')\n      ancestor.start = Number.parseInt(\n        this.sliceSerialize(token),\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseDecimal\n      )\n      this.data.expectingFirstListItemValue = undefined\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'code', 'expected code on stack')\n    node.lang = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfencemeta() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'code', 'expected code on stack')\n    node.meta = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    // Exit if this is the closing fence.\n    if (this.data.flowCodeInside) return\n    this.buffer()\n    this.data.flowCodeInside = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefenced() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g, '')\n    this.data.flowCodeInside = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeindented() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/(\\r?\\n|\\r)$/g, '')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'definition', 'expected definition on stack')\n\n    node.label = label\n    node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'definition', 'expected definition on stack')\n\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'definition', 'expected definition on stack')\n\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'heading', 'expected heading on stack')\n\n    if (!node.depth) {\n      const depth = this.sliceSerialize(token).length\n\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n        depth === 1 ||\n          depth === 2 ||\n          depth === 3 ||\n          depth === 4 ||\n          depth === 5 ||\n          depth === 6,\n        'expected `depth` between `1` and `6`'\n      )\n\n      node.depth = depth\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    this.data.setextHeadingSlurpLineEnding = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'heading', 'expected heading on stack')\n\n    node.depth =\n      this.sliceSerialize(token).codePointAt(0) === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_9__.codes.equalsTo ? 1 : 2\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    this.data.setextHeadingSlurpLineEnding = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterdata(token) {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)('children' in node, 'expected parent on stack')\n    /** @type {Array<Nodes>} */\n    const siblings = node.children\n\n    let tail = siblings[siblings.length - 1]\n\n    if (!tail || tail.type !== 'text') {\n      // Add a new text node.\n      tail = text()\n      tail.position = {\n        start: point(token.start),\n        // @ts-expect-error: we’ll add `end` later.\n        end: undefined\n      }\n      siblings.push(tail)\n    }\n\n    this.stack.push(tail)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitdata(token) {\n    const tail = this.stack.pop()\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail, 'expected a `node` to be on the stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)('value' in tail, 'expected a `literal` to be on the stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail.position, 'expected `node` to have an open position')\n    tail.value += this.sliceSerialize(token)\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlineending(token) {\n    const context = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(context, 'expected `node`')\n\n    // If we’re at a hard break, include the line ending in there.\n    if (this.data.atHardBreak) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)('children' in context, 'expected `parent`')\n      const tail = context.children[context.children.length - 1]\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail.position, 'expected tail to have a starting position')\n      tail.position.end = point(token.end)\n      this.data.atHardBreak = undefined\n      return\n    }\n\n    if (\n      !this.data.setextHeadingSlurpLineEnding &&\n      config.canContainEols.includes(context.type)\n    ) {\n      onenterdata.call(this, token)\n      onexitdata.call(this, token)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithardbreak() {\n    this.data.atHardBreak = true\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmlflow() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmltext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcodetext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'inlineCode', 'expected inline code on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlink() {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'link', 'expected link on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (this.data.inReference) {\n      /** @type {ReferenceType} */\n      const referenceType = this.data.referenceType || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    this.data.referenceType = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitimage() {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'image', 'expected image on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (this.data.inReference) {\n      /** @type {ReferenceType} */\n      const referenceType = this.data.referenceType || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    this.data.referenceType = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabeltext(token) {\n    const string = this.sliceSerialize(token)\n    const ancestor = this.stack[this.stack.length - 2]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(ancestor, 'expected ancestor on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      ancestor.type === 'image' || ancestor.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    ancestor.label = (0,micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_10__.decodeString)(string)\n    // @ts-expect-error: same as above.\n    ancestor.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(string).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabel() {\n    const fragment = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(fragment, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(fragment.type === 'fragment', 'expected fragment on stack')\n    const value = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // Assume a reference.\n    this.data.inReference = true\n\n    if (node.type === 'link') {\n      /** @type {Array<PhrasingContent>} */\n      const children = fragment.children\n\n      node.children = children\n    } else {\n      node.alt = value\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcedestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcetitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresource() {\n    this.data.inReference = undefined\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterreference() {\n    this.data.referenceType = 'collapsed'\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitreferencestring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      node.type === 'image' || node.type === 'link',\n      'expected image reference or link reference on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    node.label = label\n    // @ts-expect-error: same as above.\n    node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n    this.data.referenceType = 'full'\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcharacterreferencemarker(token) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(\n      token.type === 'characterReferenceMarkerNumeric' ||\n        token.type === 'characterReferenceMarkerHexadecimal'\n    )\n    this.data.characterReferenceType = token.type\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const data = this.sliceSerialize(token)\n    const type = this.data.characterReferenceType\n    /** @type {string} */\n    let value\n\n    if (type) {\n      value = (0,micromark_util_decode_numeric_character_reference__WEBPACK_IMPORTED_MODULE_11__.decodeNumericCharacterReference)(\n        data,\n        type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_3__.types.characterReferenceMarkerNumeric\n          ? micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseDecimal\n          : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseHexadecimal\n      )\n      this.data.characterReferenceType = undefined\n    } else {\n      const result = (0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_12__.decodeNamedCharacterReference)(data)\n      ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(result !== false, 'expected reference to decode')\n      value = result\n    }\n\n    const tail = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail, 'expected `node`')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)('value' in tail, 'expected `node.value`')\n    tail.value += value\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreference(token) {\n    const tail = this.stack.pop()\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail, 'expected `node`')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(tail.position, 'expected `node.position`')\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'link', 'expected link on stack')\n\n    node.url = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node, 'expected node on stack')\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(node.type === 'link', 'expected link on stack')\n\n    node.url = 'mailto:' + this.sliceSerialize(token)\n  }\n\n  //\n  // Creaters.\n  //\n\n  /** @returns {Blockquote} */\n  function blockQuote() {\n    return {type: 'blockquote', children: []}\n  }\n\n  /** @returns {Code} */\n  function codeFlow() {\n    return {type: 'code', lang: null, meta: null, value: ''}\n  }\n\n  /** @returns {InlineCode} */\n  function codeText() {\n    return {type: 'inlineCode', value: ''}\n  }\n\n  /** @returns {Definition} */\n  function definition() {\n    return {\n      type: 'definition',\n      identifier: '',\n      label: null,\n      title: null,\n      url: ''\n    }\n  }\n\n  /** @returns {Emphasis} */\n  function emphasis() {\n    return {type: 'emphasis', children: []}\n  }\n\n  /** @returns {Heading} */\n  function heading() {\n    return {\n      type: 'heading',\n      // @ts-expect-error `depth` will be set later.\n      depth: 0,\n      children: []\n    }\n  }\n\n  /** @returns {Break} */\n  function hardBreak() {\n    return {type: 'break'}\n  }\n\n  /** @returns {Html} */\n  function html() {\n    return {type: 'html', value: ''}\n  }\n\n  /** @returns {Image} */\n  function image() {\n    return {type: 'image', title: null, url: '', alt: null}\n  }\n\n  /** @returns {Link} */\n  function link() {\n    return {type: 'link', title: null, url: '', children: []}\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {List}\n   */\n  function list(token) {\n    return {\n      type: 'list',\n      ordered: token.type === 'listOrdered',\n      start: null,\n      spread: token._spread,\n      children: []\n    }\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {ListItem}\n   */\n  function listItem(token) {\n    return {\n      type: 'listItem',\n      spread: token._spread,\n      checked: null,\n      children: []\n    }\n  }\n\n  /** @returns {Paragraph} */\n  function paragraph() {\n    return {type: 'paragraph', children: []}\n  }\n\n  /** @returns {Strong} */\n  function strong() {\n    return {type: 'strong', children: []}\n  }\n\n  /** @returns {Text} */\n  function text() {\n    return {type: 'text', value: ''}\n  }\n\n  /** @returns {ThematicBreak} */\n  function thematicBreak() {\n    return {type: 'thematicBreak'}\n  }\n}\n\n/**\n * Copy a point-like value.\n *\n * @param {Point} d\n *   Point-like value.\n * @returns {Point}\n *   unist point.\n */\nfunction point(d) {\n  return {line: d.line, column: d.column, offset: d.offset}\n}\n\n/**\n * @param {Config} combined\n * @param {Array<Array<Extension> | Extension>} extensions\n * @returns {undefined}\n */\nfunction configure(combined, extensions) {\n  let index = -1\n\n  while (++index < extensions.length) {\n    const value = extensions[index]\n\n    if (Array.isArray(value)) {\n      configure(combined, value)\n    } else {\n      extension(combined, value)\n    }\n  }\n}\n\n/**\n * @param {Config} combined\n * @param {Extension} extension\n * @returns {undefined}\n */\nfunction extension(combined, extension) {\n  /** @type {keyof Extension} */\n  let key\n\n  for (key in extension) {\n    if (own.call(extension, key)) {\n      switch (key) {\n        case 'canContainEols': {\n          const right = extension[key]\n          if (right) {\n            combined[key].push(...right)\n          }\n\n          break\n        }\n\n        case 'transforms': {\n          const right = extension[key]\n          if (right) {\n            combined[key].push(...right)\n          }\n\n          break\n        }\n\n        case 'enter':\n        case 'exit': {\n          const right = extension[key]\n          if (right) {\n            Object.assign(combined[key], right)\n          }\n\n          break\n        }\n        // No default\n      }\n    }\n  }\n}\n\n/** @type {OnEnterError} */\nfunction defaultOnError(left, right) {\n  if (left) {\n    throw new Error(\n      'Cannot close `' +\n        left.type +\n        '` (' +\n        (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({start: left.start, end: left.end}) +\n        '): a different token (`' +\n        right.type +\n        '`, ' +\n        (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({start: right.start, end: right.end}) +\n        ') is open'\n    )\n  } else {\n    throw new Error(\n      'Cannot close document, a token (`' +\n        right.type +\n        '`, ' +\n        (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({start: right.start, end: right.end}) +\n        ') is still open'\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/mdast-util-from-markdown@2.0.2/node_modules/mdast-util-from-markdown/dev/lib/index.js\n");

/***/ })

};
;