{"Version": 3, "Meta": {"Duration": 2.1, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 38, "TotalSegmentCount": 120, "TotalPointCount": 322, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, 8, 0.5, 8, 1, 0.644, 8, 0.789, -7, 0.933, -7, 0, 2.1, -7]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, 30, 0.5, 30, 1, 0.644, 30, 0.789, -30, 0.933, -30, 0, 2.1, -30]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, 9, 0.5, 9, 1, 0.644, 9, 0.789, -23, 0.933, -23, 0, 2.1, -23]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.033, 1, 0.067, 1, 0.1, 1, 1, 0.189, 1, 0.278, 0.98, 0.367, 0.75, 1, 0.411, 0.635, 0.456, 0, 0.5, 0, 1, 0.678, 0, 0.856, 0, 1.033, 0, 1, 1.1, 0, 1.167, 0.75, 1.233, 0.75, 0, 2.1, 0.75]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 1, 1, 0.033, 1, 0.067, 1, 0.1, 1, 1, 0.233, 1, 0.367, 0, 0.5, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.033, 1, 0.067, 1, 0.1, 1, 1, 0.189, 1, 0.278, 0.98, 0.367, 0.75, 1, 0.411, 0.635, 0.456, 0, 0.5, 0, 1, 0.678, 0, 0.856, 0, 1.033, 0, 1, 1.1, 0, 1.167, 0.75, 1.233, 0.75, 0, 2.1, 0.75]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 1, 1, 0.033, 1, 0.067, 1, 0.1, 1, 1, 0.233, 1, 0.367, 0, 0.5, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, -1, 0.5, -1, 1, 0.744, -1, 0.989, -1, 1.233, -1, 0, 2.1, -1]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -1, 1, 0.033, -1, 0.067, -1, 0.1, -1, 1, 0.233, -1, 0.367, -1, 0.5, -1, 1, 0.744, -1, 0.989, -1, 1.233, -1, 0, 2.1, -1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, -0.31, 0.5, -0.31, 1, 0.744, -0.31, 0.989, -0.31, 1.233, -0.31, 0, 2.1, -0.31]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, -0.36, 0.5, -0.36, 1, 0.744, -0.36, 0.989, -0.36, 1.233, -0.36, 0, 2.1, -0.36]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, 0.61, 0.5, 0.61, 1, 0.744, 0.61, 0.989, 0.61, 1.233, 0.61, 0, 2.1, 0.61]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, 0.62, 0.5, 0.62, 1, 0.744, 0.62, 0.989, 0.62, 1.233, 0.62, 0, 2.1, 0.62]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, -1, 0.5, -1, 1, 0.744, -1, 0.989, -1, 1.233, -1, 0, 2.1, -1]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, -1, 0.5, -1, 1, 0.744, -1, 0.989, -1, 1.233, -1, 0, 2.1, -1]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -1, 1, 0.033, -1, 0.067, -1, 0.1, -1, 1, 0.233, -1, 0.367, -1, 0.5, -1, 1, 0.744, -1, 0.989, -0.53, 1.233, -0.53, 0, 2.1, -0.53]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, 0, 0.5, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamSweat", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamRage", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, 3, 0.5, 3, 1, 0.644, 3, 0.789, -1, 0.933, -1, 1, 1.033, -1, 1.133, 0, 1.233, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, 4, 0.5, 4, 1, 0.644, 4, 0.789, -6, 0.933, -6, 1, 1.011, -6, 1.089, -4, 1.167, -4, 0, 2.1, -4]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, 0, 0.5, 0, 1, 0.644, 0, 0.789, -10, 0.933, -10, 0, 2.1, -10]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 1, 0.233, 0, 0.367, 0, 0.5, 0, 1, 0.644, 0, 0.789, -10, 0.933, -10, 0, 2.1, -10]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamHairSide_L", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamHairSide_R", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamHairBack_L", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "Parameter", "Id": "ParamHairBack_R", "Segments": [0, 0, 1, 0.033, 0, 0.067, 0, 0.1, 0, 0, 2.1, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_FACE_001_c", "Segments": [0, 0, 0, 2.1, 0]}]}