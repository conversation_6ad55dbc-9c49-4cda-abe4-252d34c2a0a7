/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/chat";
exports.ids = ["pages/chat"];
exports.modules = {

/***/ "__barrel_optimize__?names=Check,ChevronRight,Circle!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Check,ChevronRight,Circle!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Check: () => (/* reexport safe */ _icons_check_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ChevronRight: () => (/* reexport safe */ _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Circle: () => (/* reexport safe */ _icons_circle_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_check_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check.js */ \"./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/chevron-right.js */ \"./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _icons_circle_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/circle.js */ \"./node_modules/lucide-react/dist/esm/icons/circle.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGVjayxDaGV2cm9uUmlnaHQsQ2lyY2xlIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQ21EO0FBQ2UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcmNoYXQvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz8zYTU0Il0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGVjayB9IGZyb20gXCIuL2ljb25zL2NoZWNrLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hldnJvblJpZ2h0IH0gZnJvbSBcIi4vaWNvbnMvY2hldnJvbi1yaWdodC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENpcmNsZSB9IGZyb20gXCIuL2ljb25zL2NpcmNsZS5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Check,ChevronRight,Circle!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Check,Copy,Edit,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Check,Copy,Edit,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Check: () => (/* reexport safe */ _icons_check_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Copy: () => (/* reexport safe */ _icons_copy_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Edit: () => (/* reexport safe */ _icons_square_pen_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Trash2: () => (/* reexport safe */ _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_check_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/check.js */ \"./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _icons_copy_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/copy.js */ \"./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _icons_square_pen_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/square-pen.js */ \"./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/trash-2.js */ \"./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGVjayxDb3B5LEVkaXQsVHJhc2gyIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDbUQ7QUFDRjtBQUNNIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXJjaGF0Ly4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/ZTA4NCJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hlY2sgfSBmcm9tIFwiLi9pY29ucy9jaGVjay5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIENvcHkgfSBmcm9tIFwiLi9pY29ucy9jb3B5LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgRWRpdCB9IGZyb20gXCIuL2ljb25zL3NxdWFyZS1wZW4uanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUcmFzaDIgfSBmcm9tIFwiLi9pY29ucy90cmFzaC0yLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Check,Copy,Edit,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=ChevronLeft,ChevronRight,MessageSquare,Plus,Trash2,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*********************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=ChevronLeft,ChevronRight,MessageSquare,Plus,Trash2,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChevronLeft: () => (/* reexport safe */ _icons_chevron_left_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   ChevronRight: () => (/* reexport safe */ _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   MessageSquare: () => (/* reexport safe */ _icons_message_square_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Plus: () => (/* reexport safe */ _icons_plus_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Trash2: () => (/* reexport safe */ _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_chevron_left_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/chevron-left.js */ \"./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/chevron-right.js */ \"./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _icons_message_square_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/message-square.js */ \"./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _icons_plus_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/plus.js */ \"./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _icons_trash_2_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/trash-2.js */ \"./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1DaGV2cm9uTGVmdCxDaGV2cm9uUmlnaHQsTWVzc2FnZVNxdWFyZSxQbHVzLFRyYXNoMixYIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQ2dFO0FBQ0U7QUFDRTtBQUNuQjtBQUNLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXJjaGF0Ly4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/Mjg1NSJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hldnJvbkxlZnQgfSBmcm9tIFwiLi9pY29ucy9jaGV2cm9uLWxlZnQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBDaGV2cm9uUmlnaHQgfSBmcm9tIFwiLi9pY29ucy9jaGV2cm9uLXJpZ2h0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTWVzc2FnZVNxdWFyZSB9IGZyb20gXCIuL2ljb25zL21lc3NhZ2Utc3F1YXJlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGx1cyB9IGZyb20gXCIuL2ljb25zL3BsdXMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBUcmFzaDIgfSBmcm9tIFwiLi9pY29ucy90cmFzaC0yLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgWCB9IGZyb20gXCIuL2ljb25zL3guanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=ChevronLeft,ChevronRight,MessageSquare,Plus,Trash2,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Loader2!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!************************************************************************************************!*\
  !*** __barrel_optimize__?names=Loader2!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Loader2: () => (/* reexport safe */ _icons_loader_circle_js__WEBPACK_IMPORTED_MODULE_0__["default"])
/* harmony export */ });
/* harmony import */ var _icons_loader_circle_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/loader-circle.js */ "./node_modules/lucide-react/dist/esm/icons/loader-circle.js");



/***/ }),

/***/ "__barrel_optimize__?names=LogOut,Menu,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************************!*\
  !*** __barrel_optimize__?names=LogOut,Menu,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogOut: () => (/* reexport safe */ _icons_log_out_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Menu: () => (/* reexport safe */ _icons_menu_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_log_out_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/log-out.js */ \"./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _icons_menu_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/menu.js */ \"./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/settings.js */ \"./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/user.js */ \"./node_modules/lucide-react/dist/esm/icons/user.js\");\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1Mb2dPdXQsTWVudSxTZXR0aW5ncyxVc2VyIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDc0Q7QUFDTDtBQUNRIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXJjaGF0Ly4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/OTRhZiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgTG9nT3V0IH0gZnJvbSBcIi4vaWNvbnMvbG9nLW91dC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIE1lbnUgfSBmcm9tIFwiLi9pY29ucy9tZW51LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2V0dGluZ3MgfSBmcm9tIFwiLi9pY29ucy9zZXR0aW5ncy5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXIgfSBmcm9tIFwiLi9pY29ucy91c2VyLmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=LogOut,Menu,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Mic,Send,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***************************************************************************************************!*\
  !*** __barrel_optimize__?names=Mic,Send,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Mic: () => (/* reexport safe */ _icons_mic_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Send: () => (/* reexport safe */ _icons_send_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_mic_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/mic.js */ \"./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _icons_send_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/send.js */ \"./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1NaWMsU2VuZCxYIT0hLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQytDO0FBQ0UiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hcmNoYXQvLi9ub2RlX21vZHVsZXMvbHVjaWRlLXJlYWN0L2Rpc3QvZXNtL2x1Y2lkZS1yZWFjdC5qcz82ZWYxIl0sInNvdXJjZXNDb250ZW50IjpbIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBNaWMgfSBmcm9tIFwiLi9pY29ucy9taWMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTZW5kIH0gZnJvbSBcIi4vaWNvbnMvc2VuZC5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFggfSBmcm9tIFwiLi9pY29ucy94LmpzXCIiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Mic,Send,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "__barrel_optimize__?names=Send,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!***********************************************************************************************!*\
  !*** __barrel_optimize__?names=Send,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Send: () => (/* reexport safe */ _icons_send_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   X: () => (/* reexport safe */ _icons_x_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_send_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/send.js */ \"./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _icons_x_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/x.js */ \"./node_modules/lucide-react/dist/esm/icons/x.js\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1TZW5kLFghPSEuL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQ2lEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXJjaGF0Ly4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanM/NWRlNiJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU2VuZCB9IGZyb20gXCIuL2ljb25zL3NlbmQuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBYIH0gZnJvbSBcIi4vaWNvbnMveC5qc1wiIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=Send,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cchat.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cchat.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\chat.tsx */ \"./src/pages/chat.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/chat\",\n        pathname: \"/chat\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_chat_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cchat.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/chat/ChatHeader.tsx":
/*!********************************************!*\
  !*** ./src/components/chat/ChatHeader.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_LogOut_Menu_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=LogOut,Menu,Settings,User!=!lucide-react */ \"__barrel_optimize__?names=LogOut,Menu,Settings,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"./src/components/ui/avatar.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_3__, _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__, _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__, _lib_utils__WEBPACK_IMPORTED_MODULE_6__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_3__, _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__, _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__, _lib_utils__WEBPACK_IMPORTED_MODULE_6__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\nconst ChatHeader = ({ onSidebarToggle, sidebarOpen, mode })=>{\n    const { authState, logout } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_2__.useUser)();\n    const handleLogout = async ()=>{\n        await logout();\n    };\n    const getModeDisplayName = (mode)=>{\n        return mode === \"rag_agent\" ? \"智能助手\" : \"问答模式\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"px-2.5 pt-1 backdrop-blur-xl drag-region border-b border-border\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `${sidebarOpen ? \"md:hidden\" : \"\"} self-center flex flex-none items-center`,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        onClick: onSidebarToggle,\n                        className: \"cursor-pointer p-1.5 flex rounded-xl hover:bg-gray-100 dark:hover:bg-gray-850 transition\",\n                        \"aria-label\": \"Toggle Sidebar\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Menu, {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2 ml-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-1 bg-primary/10 text-primary rounded-full text-sm font-medium\",\n                        children: getModeDisplayName(mode)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-lg font-semibold text-foreground\",\n                        children: \"ARChat\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"relative h-10 w-10 rounded-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                        className: \"h-10 w-10\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                            className: \"bg-primary text-primary-foreground\",\n                                            children: authState.email ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getInitials)(authState.email) : \"U\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {\n                                className: \"w-56\",\n                                align: \"end\",\n                                forceMount: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-1 p-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium leading-none\",\n                                                children: authState.email || \"User\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs leading-none text-muted-foreground\",\n                                                children: authState.userRole === \"admin\" ? \"管理员\" : \"用户\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                        className: \"cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__.User, {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                lineNumber: 89,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"个人账户\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                lineNumber: 90,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                        className: \"cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__.Settings, {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"设置\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                        className: \"cursor-pointer text-red-600 focus:text-red-600\",\n                                        onClick: handleLogout,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LogOut_Menu_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__.LogOut, {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"退出登录\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatHeader.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatHeader);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/chat/ChatHeader.tsx\n");

/***/ }),

/***/ "./src/components/chat/ChatInput.tsx":
/*!*******************************************!*\
  !*** ./src/components/chat/ChatInput.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Mic_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,Send,X!=!lucide-react */ \"__barrel_optimize__?names=Mic,Send,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _VoiceRecording__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./VoiceRecording */ \"./src/components/chat/VoiceRecording.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _VoiceRecording__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _VoiceRecording__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst ChatInput = ({ onSendMessage, isLoading, error, onClearError })=>{\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [recording, setRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-resize textarea\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const textarea = textareaRef.current;\n        if (textarea) {\n            textarea.style.height = \"auto\";\n            textarea.style.height = `${Math.min(textarea.scrollHeight, 120)}px`;\n        }\n    }, [\n        message\n    ]);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (message.trim() && !isLoading) {\n            onSendMessage(message.trim());\n            setMessage(\"\");\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleClearError = ()=>{\n        if (onClearError) {\n            onClearError();\n        }\n    };\n    // Handle voice recording\n    const handleVoiceClick = async ()=>{\n        try {\n            // Request microphone permission\n            const stream = await navigator.mediaDevices.getUserMedia({\n                audio: true\n            });\n            if (stream) {\n                setRecording(true);\n                // Stop the stream immediately as VoiceRecording will handle it\n                stream.getTracks().forEach((track)=>track.stop());\n            }\n        } catch (error) {\n            console.error(\"Error accessing microphone:\", error);\n            alert(\"无法访问麦克风，请检查权限设置\");\n        }\n    };\n    const handleVoiceCancel = ()=>{\n        setRecording(false);\n        // Focus back to textarea\n        setTimeout(()=>{\n            textareaRef.current?.focus();\n        }, 100);\n    };\n    const handleVoiceConfirm = (data)=>{\n        const { text } = data;\n        setMessage((prev)=>`${prev}${text} `);\n        setRecording(false);\n        // Focus back to textarea\n        setTimeout(()=>{\n            textareaRef.current?.focus();\n        }, 100);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-background border-t border-border\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 bg-destructive/10 border-b border-destructive/20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between max-w-4xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-destructive text-sm\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: handleClearError,\n                            className: \"h-6 w-6 p-0 text-destructive hover:text-destructive/80\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__.X, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                lineNumber: 93,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-4xl mx-auto\",\n                    children: [\n                        recording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_VoiceRecording__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            recording: recording,\n                            onCancel: handleVoiceCancel,\n                            onConfirm: handleVoiceConfirm,\n                            className: \"p-3 w-full\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit,\n                            className: \"flex items-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                            ref: textareaRef,\n                                            value: message,\n                                            onChange: (e)=>setMessage(e.target.value),\n                                            onKeyDown: handleKeyDown,\n                                            placeholder: \"输入您的消息... (Shift + Enter 换行)\",\n                                            className: \"w-full min-h-[44px] max-h-[120px] px-4 py-3 pr-16 border border-input rounded-lg resize-none bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n                                            disabled: isLoading,\n                                            rows: 1\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        message.trim() === \"\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            type: \"button\",\n                                            variant: \"ghost\",\n                                            size: \"icon\",\n                                            onClick: handleVoiceClick,\n                                            className: \"absolute right-12 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 text-muted-foreground hover:text-foreground\",\n                                            title: \"语音输入\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Mic, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                            lineNumber: 134,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-1 right-1 text-xs text-muted-foreground\",\n                                            children: message.length\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    type: \"submit\",\n                                    disabled: !message.trim() || isLoading,\n                                    className: \"h-11 w-11 p-0 flex-shrink-0\",\n                                    \"aria-label\": \"发送消息\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Send, {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2 text-xs text-muted-foreground text-center\",\n                            children: recording ? \"正在录音中，说话即可转换为文字\" : \"按 Enter 发送，Shift + Enter 换行，点击麦克风图标进行语音输入\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n        lineNumber: 90,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInput);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/chat/ChatInput.tsx\n");

/***/ }),

/***/ "./src/components/chat/ConversationSidebar.tsx":
/*!*****************************************************!*\
  !*** ./src/components/chat/ConversationSidebar.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_MessageSquare_Plus_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,MessageSquare,Plus,Trash2,X!=!lucide-react */ \"__barrel_optimize__?names=ChevronLeft,ChevronRight,MessageSquare,Plus,Trash2,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nconst ConversationSidebar = ({ isOpen, onClose, onToggle, onConversationSelect, onNewChat, currentConversationId })=>{\n    const { getClient, authState } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_4__.useUser)();\n    const [conversations, setConversations] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load conversations when sidebar opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            loadConversations();\n        }\n    }, [\n        isOpen\n    ]);\n    const loadConversations = async ()=>{\n        setIsLoading(true);\n        try {\n            const client = getClient();\n            if (!client) {\n                console.error(\"No authenticated client available\");\n                return;\n            }\n            // For now, we'll use mock data since R2R conversation API might not be fully implemented\n            // In a real implementation, you would call something like:\n            // const response = await client.conversations.list();\n            // Mock conversations for demonstration\n            const mockConversations = [\n                {\n                    id: \"1\",\n                    title: \"关于机器学习的讨论\",\n                    messages: [\n                        {\n                            id: \"1-1\",\n                            content: \"什么是机器学习？\",\n                            role: \"user\",\n                            timestamp: new Date(Date.now() - 86400000)\n                        },\n                        {\n                            id: \"1-2\",\n                            content: \"机器学习是人工智能的一个分支，它使计算机能够在没有明确编程的情况下学习和改进。\",\n                            role: \"assistant\",\n                            timestamp: new Date(Date.now() - 86400000 + 1000)\n                        }\n                    ],\n                    createdAt: new Date(Date.now() - 86400000),\n                    updatedAt: new Date(Date.now() - 86400000 + 1000)\n                },\n                {\n                    id: \"2\",\n                    title: \"数据分析方法\",\n                    messages: [\n                        {\n                            id: \"2-1\",\n                            content: \"有哪些常用的数据分析方法？\",\n                            role: \"user\",\n                            timestamp: new Date(Date.now() - 172800000)\n                        },\n                        {\n                            id: \"2-2\",\n                            content: \"常用的数据分析方法包括描述性分析、诊断性分析、预测性分析和规范性分析。\",\n                            role: \"assistant\",\n                            timestamp: new Date(Date.now() - 172800000 + 1000)\n                        }\n                    ],\n                    createdAt: new Date(Date.now() - 172800000),\n                    updatedAt: new Date(Date.now() - 172800000 + 1000)\n                }\n            ];\n            setConversations(mockConversations);\n        } catch (error) {\n            console.error(\"Failed to load conversations:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleConversationClick = (conversation)=>{\n        onConversationSelect(conversation.id, conversation.messages);\n    // 不自动关闭侧边栏，让用户可以继续浏览其他对话\n    };\n    const handleDeleteConversation = async (conversationId, e)=>{\n        e.stopPropagation();\n        if (!confirm(\"确定要删除这个对话吗？\")) {\n            return;\n        }\n        try {\n            // In a real implementation, you would call the API to delete the conversation\n            // await client.conversations.delete(conversationId);\n            setConversations((prev)=>prev.filter((conv)=>conv.id !== conversationId));\n            // If the deleted conversation was the current one, start a new chat\n            // 但不关闭侧边栏，让用户可以继续操作\n            if (conversationId === currentConversationId) {\n                onNewChat();\n            }\n        } catch (error) {\n            console.error(\"Failed to delete conversation:\", error);\n        }\n    };\n    const getConversationTitle = (conversation)=>{\n        if (conversation.title) {\n            return conversation.title;\n        }\n        // Generate title from first user message\n        const firstUserMessage = conversation.messages.find((msg)=>msg.role === \"user\");\n        if (firstUserMessage) {\n            return (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.truncateText)(firstUserMessage.content, 30);\n        }\n        return \"新对话\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/50 z-40 md:hidden\",\n                onClick: onClose\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                lineNumber: 147,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `h-screen max-h-screen min-h-screen select-none ${isOpen ? \"md:relative w-[260px] max-w-[260px]\" : \"-translate-x-[260px] w-[0px]\"} transition-all duration-200 ease-in-out shrink-0 bg-gray-50 text-gray-900 dark:bg-gray-950 dark:text-gray-200 text-sm fixed z-50 top-0 left-0 overflow-x-hidden`,\n                \"data-state\": isOpen,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `py-2 my-auto flex flex-col justify-between h-screen max-h-screen w-[260px] overflow-x-hidden z-50 ${isOpen ? \"\" : \"invisible\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-1.5 flex justify-between space-x-1 text-gray-600 dark:text-gray-400\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: onNewChat,\n                                        className: \"cursor-pointer p-[7px] flex rounded-xl hover:bg-gray-100 dark:hover:bg-gray-900 transition\",\n                                        title: \"新建对话\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_MessageSquare_Plus_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Plus, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        onClick: onClose,\n                                        className: \"cursor-pointer p-[7px] flex rounded-xl hover:bg-gray-100 dark:hover:bg-gray-900 transition md:hidden\",\n                                        title: \"关闭侧边栏\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_MessageSquare_Plus_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.X, {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 overflow-y-auto scrollbar-thin px-2\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center p-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 dark:text-gray-400\",\n                                        children: \"加载中...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, undefined) : conversations.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center p-8 text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_MessageSquare_Plus_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.MessageSquare, {\n                                            className: \"h-12 w-12 text-gray-400 dark:text-gray-500 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 dark:text-gray-400\",\n                                            children: \"还没有对话历史\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400 dark:text-gray-500 mt-1\",\n                                            children: \"开始新的对话来创建历史记录\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1\",\n                                    children: conversations.map((conversation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: `group relative p-2.5 rounded-lg cursor-pointer transition-colors hover:bg-gray-100 dark:hover:bg-gray-900 ${conversation.id === currentConversationId ? \"bg-gray-100 dark:bg-gray-900\" : \"\"}`,\n                                            onClick: ()=>handleConversationClick(conversation),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"font-medium text-sm truncate text-gray-900 dark:text-gray-100\",\n                                                                children: getConversationTitle(conversation)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-500 dark:text-gray-400 mt-1\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(conversation.updatedAt)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-gray-400 dark:text-gray-500 mt-1\",\n                                                                children: [\n                                                                    conversation.messages.length,\n                                                                    \" 条消息\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"icon\",\n                                                        onClick: (e)=>handleDeleteConversation(conversation.id, e),\n                                                        className: \"h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400\",\n                                                        title: \"删除对话\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_MessageSquare_Plus_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Trash2, {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                                        lineNumber: 229,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, conversation.id, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col font-primary\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center rounded-xl py-2.5 px-2.5 w-full hover:bg-gray-100 dark:hover:bg-gray-900 transition\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"self-center mr-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-[30px] h-[30px] bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium\",\n                                                    children: authState.email ? (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.getInitials)(authState.email) : \"U\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"self-center font-medium text-gray-900 dark:text-gray-100 truncate\",\n                                                children: authState.email || \"User\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"fixed left-0 top-0 z-50 h-full w-6 bg-gray-800 dark:bg-gray-900 flex items-center justify-center transition-transform duration-200 ease-in-out hover:bg-gray-700 dark:hover:bg-gray-800\",\n                        style: {\n                            transform: isOpen ? \"translateX(260px)\" : \"translateX(0px)\"\n                        },\n                        onClick: onToggle,\n                        children: isOpen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_MessageSquare_Plus_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ChevronLeft, {\n                            className: \"h-4 w-4 text-white\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_MessageSquare_Plus_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ChevronRight, {\n                            className: \"h-4 w-4 text-white\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                        lineNumber: 263,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\ConversationSidebar.tsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ConversationSidebar);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/chat/ConversationSidebar.tsx\n");

/***/ }),

/***/ "./src/components/chat/MessageBubble.tsx":
/*!***********************************************!*\
  !*** ./src/components/chat/MessageBubble.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-markdown */ \"react-markdown\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_Copy_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Copy,Edit,Trash2!=!lucide-react */ \"__barrel_optimize__?names=Check,Copy,Edit,Trash2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([react_markdown__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__, _components_ui_button__WEBPACK_IMPORTED_MODULE_4__, _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__]);\n([react_markdown__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__, _components_ui_button__WEBPACK_IMPORTED_MODULE_4__, _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst MessageBubble = ({ message, onEdit, onDelete })=>{\n    const [copied, setCopied] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editContent, setEditContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(message.content);\n    const handleCopy = async ()=>{\n        try {\n            await navigator.clipboard.writeText(message.content);\n            setCopied(true);\n            setTimeout(()=>setCopied(false), 2000);\n        } catch (error) {\n            console.error(\"Failed to copy text:\", error);\n        }\n    };\n    const handleEdit = ()=>{\n        if (isEditing && onEdit) {\n            onEdit(message.id, editContent);\n            setIsEditing(false);\n        } else {\n            setIsEditing(true);\n        }\n    };\n    const handleCancelEdit = ()=>{\n        setEditContent(message.content);\n        setIsEditing(false);\n    };\n    const handleDelete = ()=>{\n        if (onDelete && confirm(\"确定要删除这条消息吗？\")) {\n            onDelete(message.id);\n        }\n    };\n    const isUser = message.role === \"user\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex ${isUser ? \"justify-end\" : \"justify-start\"} mb-4`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `flex max-w-[80%] ${isUser ? \"flex-row-reverse\" : \"flex-row\"} items-start space-x-3 ${isUser ? \"space-x-reverse\" : \"\"}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                    className: \"h-8 w-8 flex-shrink-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                        className: isUser ? \"bg-primary text-primary-foreground\" : \"bg-muted\",\n                        children: isUser ? \"U\" : \"A\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex flex-col ${isUser ? \"items-end\" : \"items-start\"}`,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `message-content rounded-lg px-4 py-3 ${isUser ? \"message-user bg-primary text-primary-foreground\" : \"message-assistant bg-muted text-muted-foreground\"}`,\n                            children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        value: editContent,\n                                        onChange: (e)=>setEditContent(e.target.value),\n                                        className: \"w-full min-h-[100px] p-2 border rounded resize-none bg-background text-foreground\",\n                                        autoFocus: true\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"sm\",\n                                                onClick: handleEdit,\n                                                children: \"保存\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                size: \"sm\",\n                                                variant: \"outline\",\n                                                onClick: handleCancelEdit,\n                                                children: \"取消\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                                lineNumber: 87,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-sm max-w-none dark:prose-invert\",\n                                children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"whitespace-pre-wrap\",\n                                    children: message.content\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 19\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    components: {\n                                        code: ({ className, children, ...props })=>{\n                                            const isInline = !className?.includes(\"language-\");\n                                            if (isInline) {\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"bg-muted px-1 py-0.5 rounded text-sm font-mono\",\n                                                    ...props,\n                                                    children: children\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 29\n                                                }, void 0);\n                                            }\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"bg-muted p-3 rounded-lg overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    className: \"text-sm font-mono\",\n                                                    ...props,\n                                                    children: children\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 29\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 27\n                                            }, void 0);\n                                        },\n                                        p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"mb-2 last:mb-0\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 44\n                                            }, void 0),\n                                        ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"list-disc list-inside mb-2\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                                lineNumber: 120,\n                                                columnNumber: 45\n                                            }, void 0),\n                                        ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                className: \"list-decimal list-inside mb-2\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 45\n                                            }, void 0),\n                                        li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                className: \"mb-1\",\n                                                children: children\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 45\n                                            }, void 0)\n                                    },\n                                    children: message.content\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex items-center mt-1 space-x-2 ${isUser ? \"flex-row-reverse space-x-reverse\" : \"\"}`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.formatDate)(message.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 13\n                                }, undefined),\n                                !isEditing && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: handleCopy,\n                                            className: \"h-6 w-6 p-0\",\n                                            title: \"复制\",\n                                            children: copied ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Check, {\n                                                className: \"h-3 w-3 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Copy, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                            lineNumber: 140,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isUser && onEdit && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: handleEdit,\n                                            className: \"h-6 w-6 p-0\",\n                                            title: \"编辑\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Edit, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            onClick: handleDelete,\n                                            className: \"h-6 w-6 p-0 text-red-600 hover:text-red-700\",\n                                            title: \"删除\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Copy_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Trash2, {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n            lineNumber: 58,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageBubble.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MessageBubble);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/chat/MessageBubble.tsx\n");

/***/ }),

/***/ "./src/components/chat/MessageList.tsx":
/*!*********************************************!*\
  !*** ./src/components/chat/MessageList.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MessageBubble */ \"./src/components/chat/MessageBubble.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2!=!lucide-react */ \"__barrel_optimize__?names=Loader2!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_MessageBubble__WEBPACK_IMPORTED_MODULE_2__]);\n_MessageBubble__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst MessageList = ({ messages, isLoading, onMessageEdit, onMessageDelete })=>{\n    if (messages.length === 0 && !isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full text-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold text-foreground mb-4\",\n                        children: \"欢迎使用 ARChat\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground mb-6\",\n                        children: \"这是一个基于 R2R 的智能对话助手。您可以向我提问任何问题，我会尽力为您提供帮助。\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-3 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-muted rounded-lg text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium mb-1\",\n                                        children: \"\\uD83D\\uDCA1 提示\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"您可以询问关于文档、数据分析或任何需要帮助的问题\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-muted rounded-lg text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium mb-1\",\n                                        children: \"\\uD83D\\uDD0D 搜索\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"我可以搜索相关信息并为您提供详细的答案\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-muted rounded-lg text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium mb-1\",\n                                        children: \"\\uD83D\\uDCAC 对话\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"我会记住我们的对话历史，让交流更加自然\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 42,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto px-4 py-6 space-y-6 min-h-full\",\n            children: [\n                messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_MessageBubble__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        message: message,\n                        onEdit: onMessageEdit,\n                        onDelete: onMessageDelete\n                    }, message.id, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, undefined)),\n                isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2 text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Loader2, {\n                                className: \"h-4 w-4 animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"正在思考中...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n            lineNumber: 56,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\MessageList.tsx\",\n        lineNumber: 55,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MessageList);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/chat/MessageList.tsx\n");

/***/ }),

/***/ "./src/components/chat/VoiceRecording.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/VoiceRecording.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Send,X!=!lucide-react */ \"__barrel_optimize__?names=Send,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_button__WEBPACK_IMPORTED_MODULE_2__]);\n_components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\nconst VoiceRecording = ({ recording, onCancel, onConfirm, className = \"p-2.5 w-full max-w-full\" })=>{\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [confirmed, setConfirmed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [transcription, setTranscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [duration, setDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [audioLevel, setAudioLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const mediaRecorderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioChunksRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)([]);\n    const speechRecognitionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const durationIntervalRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const audioContextRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const analyserRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const animationFrameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Start duration counter\n    const startDurationCounter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setDuration(0);\n        durationIntervalRef.current = setInterval(()=>{\n            setDuration((prev)=>prev + 1);\n        }, 1000);\n    }, []);\n    // Stop duration counter\n    const stopDurationCounter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (durationIntervalRef.current) {\n            clearInterval(durationIntervalRef.current);\n            durationIntervalRef.current = null;\n        }\n    }, []);\n    // Analyze audio for visual feedback\n    const analyzeAudio = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((stream)=>{\n        try {\n            audioContextRef.current = new AudioContext();\n            analyserRef.current = audioContextRef.current.createAnalyser();\n            const source = audioContextRef.current.createMediaStreamSource(stream);\n            source.connect(analyserRef.current);\n            analyserRef.current.fftSize = 256;\n            const bufferLength = analyserRef.current.frequencyBinCount;\n            const dataArray = new Uint8Array(bufferLength);\n            const processFrame = ()=>{\n                if (analyserRef.current && recording) {\n                    analyserRef.current.getByteFrequencyData(dataArray);\n                    // Calculate average audio level\n                    const average = dataArray.reduce((sum, value)=>sum + value, 0) / bufferLength;\n                    setAudioLevel(average / 255); // Normalize to 0-1\n                    animationFrameRef.current = requestAnimationFrame(processFrame);\n                }\n            };\n            animationFrameRef.current = requestAnimationFrame(processFrame);\n        } catch (error) {\n            console.error(\"Error analyzing audio:\", error);\n        }\n    }, [\n        recording\n    ]);\n    // Start recording\n    const startRecording = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setLoading(true);\n        setTranscription(\"\");\n        try {\n            // Get user media\n            streamRef.current = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    echoCancellation: true,\n                    noiseSuppression: true,\n                    autoGainControl: true\n                }\n            });\n            // Setup MediaRecorder\n            const mimeTypes = [\n                \"audio/webm; codecs=opus\",\n                \"audio/mp4\"\n            ];\n            const mimeType = mimeTypes.find((type)=>MediaRecorder.isTypeSupported(type)) || \"audio/webm\";\n            mediaRecorderRef.current = new MediaRecorder(streamRef.current, {\n                mimeType\n            });\n            mediaRecorderRef.current.onstart = ()=>{\n                console.log(\"Recording started\");\n                setLoading(false);\n                startDurationCounter();\n                audioChunksRef.current = [];\n                analyzeAudio(streamRef.current);\n            };\n            mediaRecorderRef.current.ondataavailable = (event)=>{\n                audioChunksRef.current.push(event.data);\n            };\n            mediaRecorderRef.current.onstop = async ()=>{\n                console.log(\"Recording stopped\");\n                stopDurationCounter();\n                if (confirmed && audioChunksRef.current.length > 0) {\n                    const audioBlob = new Blob(audioChunksRef.current, {\n                        type: mimeType\n                    });\n                    const filename = `Recording-${new Date().toLocaleString()}.webm`;\n                    // For now, we'll use the transcription from speech recognition\n                    // In a real implementation, you might send the audio to a server for transcription\n                    onConfirm({\n                        text: transcription,\n                        filename: filename\n                    });\n                    setConfirmed(false);\n                    setLoading(false);\n                }\n                audioChunksRef.current = [];\n            };\n            // Setup Speech Recognition for real-time transcription\n            if (\"SpeechRecognition\" in window || \"webkitSpeechRecognition\" in window) {\n                const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n                speechRecognitionRef.current = new SpeechRecognition();\n                speechRecognitionRef.current.continuous = true;\n                speechRecognitionRef.current.interimResults = true;\n                speechRecognitionRef.current.lang = \"zh-CN\"; // Set to Chinese, can be configurable\n                speechRecognitionRef.current.onresult = (event)=>{\n                    let finalTranscript = \"\";\n                    let interimTranscript = \"\";\n                    for(let i = event.resultIndex; i < event.results.length; i++){\n                        const transcript = event.results[i][0].transcript;\n                        if (event.results[i].isFinal) {\n                            finalTranscript += transcript;\n                        } else {\n                            interimTranscript += transcript;\n                        }\n                    }\n                    setTranscription((prev)=>prev + finalTranscript + interimTranscript);\n                };\n                speechRecognitionRef.current.onerror = (event)=>{\n                    console.error(\"Speech recognition error:\", event.error);\n                };\n                speechRecognitionRef.current.start();\n            }\n            mediaRecorderRef.current.start();\n        } catch (error) {\n            console.error(\"Error starting recording:\", error);\n            setLoading(false);\n            onCancel();\n        }\n    }, [\n        confirmed,\n        transcription,\n        startDurationCounter,\n        stopDurationCounter,\n        analyzeAudio,\n        onConfirm,\n        onCancel\n    ]);\n    // Stop recording\n    const stopRecording = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        if (mediaRecorderRef.current && mediaRecorderRef.current.state === \"recording\") {\n            mediaRecorderRef.current.stop();\n        }\n        if (speechRecognitionRef.current) {\n            speechRecognitionRef.current.stop();\n        }\n        if (streamRef.current) {\n            streamRef.current.getTracks().forEach((track)=>track.stop());\n        }\n        if (audioContextRef.current) {\n            audioContextRef.current.close();\n        }\n        if (animationFrameRef.current) {\n            cancelAnimationFrame(animationFrameRef.current);\n        }\n        stopDurationCounter();\n    }, [\n        stopDurationCounter\n    ]);\n    // Handle confirm\n    const handleConfirm = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setConfirmed(true);\n        stopRecording();\n    }, [\n        stopRecording\n    ]);\n    // Handle cancel\n    const handleCancel = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        stopRecording();\n        onCancel();\n    }, [\n        stopRecording,\n        onCancel\n    ]);\n    // Format duration\n    const formatDuration = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return `${mins}:${secs.toString().padStart(2, \"0\")}`;\n    };\n    // Start recording when component mounts and recording is true\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (recording && !loading) {\n            startRecording();\n        }\n        return ()=>{\n            stopRecording();\n        };\n    }, [\n        recording,\n        loading,\n        startRecording,\n        stopRecording\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `${loading ? \"bg-gray-100/50 dark:bg-gray-850/50\" : \"bg-indigo-300/10 dark:bg-indigo-500/10\"} rounded-full flex justify-between ${className}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mr-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    type: \"button\",\n                    variant: \"ghost\",\n                    size: \"icon\",\n                    className: `p-1.5 rounded-full ${loading ? \"bg-gray-200 dark:bg-gray-700/50\" : \"bg-indigo-400/20 text-indigo-600 dark:text-indigo-300\"}`,\n                    onClick: handleCancel,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.X, {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                    lineNumber: 234,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col justify-center px-2 min-w-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-2 h-2 rounded-full ${loading ? \"bg-gray-400\" : \"bg-red-500 animate-pulse\"}`\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 15\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium text-gray-700 dark:text-gray-300\",\n                                                children: loading ? \"准备中...\" : \"录音中\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                        children: formatDuration(duration)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: [\n                                    ...Array(5)\n                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: `w-1 h-4 rounded-full transition-all duration-100 ${audioLevel > (i + 1) * 0.2 ? \"bg-indigo-500 dark:bg-indigo-400\" : \"bg-gray-300 dark:bg-gray-600\"}`\n                                    }, i, false, {\n                                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, undefined),\n                    transcription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-1 text-sm text-gray-600 dark:text-gray-400 truncate\",\n                        children: transcription\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                lineNumber: 246,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center ml-1\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    type: \"button\",\n                    variant: \"ghost\",\n                    size: \"icon\",\n                    className: \"p-1.5 bg-indigo-500 hover:bg-indigo-600 text-white rounded-full\",\n                    onClick: handleConfirm,\n                    disabled: loading,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Send_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__.Send, {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\chat\\\\VoiceRecording.tsx\",\n        lineNumber: 231,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VoiceRecording);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/chat/VoiceRecording.tsx\n");

/***/ }),

/***/ "./src/components/ui/avatar.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/avatar.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-avatar */ \"@radix-ui/react-avatar\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nAvatar.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Root.displayName;\nconst AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Image, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"aspect-square h-full w-full\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nAvatarImage.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Image.displayName;\nconst AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Fallback, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex h-full w-full items-center justify-center rounded-full bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\avatar.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nAvatarFallback.displayName = _radix_ui_react_avatar__WEBPACK_IMPORTED_MODULE_2__.Fallback.displayName;\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/avatar.tsx\n");

/***/ }),

/***/ "./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-slot */ \"@radix-ui/react-slot\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/button.tsx\n");

/***/ }),

/***/ "./src/components/ui/dropdown-menu.tsx":
/*!*********************************************!*\
  !*** ./src/components/ui/dropdown-menu.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"@radix-ui/react-dropdown-menu\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"__barrel_optimize__?names=Check,ChevronRight,Circle!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__.ChevronRight, {\n                className: \"ml-auto h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 34,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 44,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 61,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 60,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 80,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Check, {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 105,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 96,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__.Circle, {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 128,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 120,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 144,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 160,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_2__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "./src/config/chatConfig.ts":
/*!**********************************!*\
  !*** ./src/config/chatConfig.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultChatConfig: () => (/* binding */ defaultChatConfig),\n/* harmony export */   getDeploymentUrl: () => (/* binding */ getDeploymentUrl),\n/* harmony export */   loadChatConfig: () => (/* binding */ loadChatConfig)\n/* harmony export */ });\n/**\n * Default configuration values\n */ const defaultChatConfig = {\n    server: {\n        apiUrl: \"http://localhost:7272\",\n        useHttps: false,\n        apiVersion: \"v3\",\n        timeout: 30000\n    },\n    app: {\n        appName: \"ARChat\",\n        appDescription: \"Agent-powered R2R chat application\",\n        version: \"1.0.0\",\n        defaultMode: \"rag_agent\",\n        conversationHistoryLimit: 10\n    },\n    vectorSearch: {\n        enabled: true,\n        searchLimit: 10,\n        searchFilters: \"{}\",\n        indexMeasure: \"cosine_distance\",\n        includeMetadatas: false,\n        probes: undefined,\n        efSearch: undefined\n    },\n    hybridSearch: {\n        enabled: false,\n        fullTextWeight: undefined,\n        semanticWeight: undefined,\n        fullTextLimit: undefined,\n        rrfK: undefined\n    },\n    graphSearch: {\n        enabled: true,\n        kgSearchLevel: null,\n        maxCommunityDescriptionLength: 100,\n        localSearchLimits: {},\n        maxLlmQueries: undefined\n    },\n    ragGeneration: {\n        temperature: 0.1,\n        topP: 1.0,\n        topK: 100,\n        maxTokensToSample: 1024,\n        kgTemperature: 0.1,\n        kgTopP: 1.0,\n        kgTopK: 100,\n        kgMaxTokensToSample: 1024\n    }\n};\n/**\n * Load configuration from public/config.json with fallback to defaults\n */ const loadChatConfig = async ()=>{\n    try {\n        const response = await fetch(\"/config.json\");\n        if (!response.ok) {\n            console.warn(\"Failed to load config.json, using default configuration\");\n            return defaultChatConfig;\n        }\n        const config = await response.json();\n        // Merge with defaults to ensure all required fields are present\n        return {\n            server: {\n                ...defaultChatConfig.server,\n                ...config.server\n            },\n            app: {\n                ...defaultChatConfig.app,\n                ...config.app\n            },\n            vectorSearch: {\n                ...defaultChatConfig.vectorSearch,\n                ...config.vectorSearch\n            },\n            hybridSearch: {\n                ...defaultChatConfig.hybridSearch,\n                ...config.hybridSearch\n            },\n            graphSearch: {\n                ...defaultChatConfig.graphSearch,\n                ...config.graphSearch\n            },\n            ragGeneration: {\n                ...defaultChatConfig.ragGeneration,\n                ...config.ragGeneration\n            }\n        };\n    } catch (error) {\n        console.error(\"Error loading configuration:\", error);\n        return defaultChatConfig;\n    }\n};\n/**\n * Get the deployment URL from configuration\n */ const getDeploymentUrl = (config)=>{\n    const cfg = config || defaultChatConfig;\n    // If apiUrl already includes protocol, use it as-is\n    if (cfg.server.apiUrl.startsWith(\"http://\") || cfg.server.apiUrl.startsWith(\"https://\")) {\n        return cfg.server.apiUrl;\n    }\n    // Otherwise, construct URL with protocol\n    const protocol = cfg.server.useHttps ? \"https\" : \"http\";\n    return `${protocol}://${cfg.server.apiUrl}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/config/chatConfig.ts\n");

/***/ }),

/***/ "./src/context/UserContext.tsx":
/*!*************************************!*\
  !*** ./src/context/UserContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useUser: () => (/* binding */ useUser)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! r2r-js */ \"r2r-js\");\n/* harmony import */ var r2r_js__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(r2r_js__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../config/chatConfig */ \"./src/config/chatConfig.ts\");\n\n\n\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    pipeline: null,\n    setPipeline: ()=>{},\n    selectedModel: \"null\",\n    setSelectedModel: ()=>{},\n    isAuthenticated: false,\n    login: async ()=>({\n            success: false,\n            userRole: \"user\"\n        }),\n    loginWithToken: async ()=>({\n            success: false,\n            userRole: \"user\"\n        }),\n    logout: async ()=>{},\n    unsetCredentials: async ()=>{},\n    register: async ()=>{},\n    authState: {\n        isAuthenticated: false,\n        email: null,\n        userRole: null,\n        userId: null\n    },\n    getClient: ()=>null,\n    client: null,\n    viewMode: \"user\",\n    setViewMode: ()=>{},\n    isSuperUser: ()=>false,\n    createUser: async ()=>{\n        throw new Error(\"createUser is not implemented in the default context\");\n    },\n    deleteUser: async ()=>{\n        throw new Error(\"deleteUser is not implemented in the default context\");\n    },\n    updateUser: async ()=>{\n        throw new Error(\"updateUser is not implemented in the default context\");\n    }\n});\nconst UserProvider = ({ children })=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isReady, setIsReady] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [client, setClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"user\"); // Default to user mode\n    const [pipeline, setPipeline] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        if (false) {}\n        return null;\n    });\n    const [selectedModel, setSelectedModel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>{\n        if (false) {}\n        return \"null\";\n    });\n    const [authState, setAuthState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        isAuthenticated: false,\n        email: null,\n        userRole: null,\n        userId: null\n    });\n    const isSuperUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return authState.userRole === \"admin\" && viewMode === \"admin\";\n    }, [\n        authState.userRole,\n        viewMode\n    ]);\n    const [lastLoginTime, setLastLoginTime] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const login = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (email, password, instanceUrl)=>{\n        const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_3__.r2rClient(instanceUrl);\n        try {\n            const tokens = await newClient.users.login({\n                email: email,\n                password: password\n            });\n            localStorage.setItem(\"chatAccessToken\", tokens.results.accessToken.token);\n            localStorage.setItem(\"chatRefreshToken\", tokens.results.refreshToken.token);\n            newClient.setTokens(tokens.results.accessToken.token, tokens.results.refreshToken.token);\n            setClient(newClient);\n            // Get user info\n            const userInfo = await newClient.users.me();\n            if (!userInfo.results) {\n                throw new Error(\"Failed to get user information\");\n            }\n            // Check user role\n            let userRole = \"user\";\n            try {\n                await newClient.system.settings();\n                userRole = \"admin\";\n            } catch (error) {\n                if (error instanceof Error && \"status\" in error && error.status === 403) {\n                // User doesn't have admin access, keep as \"user\"\n                } else {\n                    console.error(\"Unexpected error when checking user role:\", error);\n                }\n            }\n            setAuthState({\n                isAuthenticated: true,\n                email: userInfo.results.email || email,\n                userRole: userRole,\n                userId: userInfo.results.id\n            });\n            // Store pipeline\n            const newPipeline = {\n                deploymentUrl: instanceUrl\n            };\n            setPipeline(newPipeline);\n            localStorage.setItem(\"pipeline\", JSON.stringify(newPipeline));\n            setLastLoginTime(Date.now());\n            // Redirect to chat page\n            router.push(\"/chat\");\n            return {\n                success: true,\n                userRole\n            };\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            throw error;\n        }\n    }, [\n        router\n    ]);\n    const loginWithToken = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (token, instanceUrl)=>{\n        const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_3__.r2rClient(instanceUrl);\n        try {\n            const result = await newClient.users.loginWithToken({\n                accessToken: token\n            });\n            const userInfo = await newClient.users.me();\n            localStorage.setItem(\"chatAccessToken\", result.accessToken.token);\n            newClient.setTokens(result.accessToken.token, \"\");\n            setClient(newClient);\n            let userRole = \"user\";\n            try {\n                await newClient.system.settings();\n                userRole = \"admin\";\n            } catch (error) {\n                if (error instanceof Error && \"status\" in error && error.status === 403) {\n                // User doesn't have admin access\n                } else {\n                    console.error(\"Unexpected error when checking user role:\", error);\n                }\n            }\n            setAuthState({\n                isAuthenticated: true,\n                email: userInfo.results?.email || \"\",\n                userRole: userRole,\n                userId: userInfo.results?.id || \"\"\n            });\n            const newPipeline = {\n                deploymentUrl: instanceUrl\n            };\n            setPipeline(newPipeline);\n            localStorage.setItem(\"pipeline\", JSON.stringify(newPipeline));\n            return {\n                success: true,\n                userRole\n            };\n        } catch (error) {\n            console.error(\"Token login error:\", error);\n            throw error;\n        }\n    }, []);\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        try {\n            if (client) {\n                await client.users.logout();\n            }\n        } catch (error) {\n            console.error(\"Logout error:\", error);\n        } finally{\n            // Clear all stored data\n            localStorage.removeItem(\"chatAccessToken\");\n            localStorage.removeItem(\"chatRefreshToken\");\n            localStorage.removeItem(\"pipeline\");\n            setAuthState({\n                isAuthenticated: false,\n                email: null,\n                userRole: null,\n                userId: null\n            });\n            setClient(null);\n            setPipeline(null);\n            // Redirect to login\n            router.push(\"/auth/login\");\n        }\n    }, [\n        client,\n        router\n    ]);\n    const unsetCredentials = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        localStorage.removeItem(\"chatAccessToken\");\n        localStorage.removeItem(\"chatRefreshToken\");\n        setAuthState({\n            isAuthenticated: false,\n            email: null,\n            userRole: null,\n            userId: null\n        });\n        setClient(null);\n    }, []);\n    const register = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (email, password, instanceUrl)=>{\n        const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_3__.r2rClient(instanceUrl);\n        try {\n            await newClient.users.create({\n                email: email,\n                password: password\n            });\n            // After successful registration, log in\n            await login(email, password, instanceUrl);\n        } catch (error) {\n            console.error(\"Registration error:\", error);\n            throw error;\n        }\n    }, [\n        login\n    ]);\n    const getClient = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return client;\n    }, [\n        client\n    ]);\n    const createUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (email, password, name)=>{\n        if (!client) {\n            throw new Error(\"No client available\");\n        }\n        const result = await client.users.create({\n            email,\n            password,\n            name\n        });\n        return {\n            id: result.results.id,\n            email: result.results.email,\n            name: result.results.name,\n            role: \"user\"\n        };\n    }, [\n        client\n    ]);\n    const deleteUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (userId)=>{\n        if (!client) {\n            throw new Error(\"No client available\");\n        }\n        // Note: R2R delete user API might require additional parameters\n        // For now, we'll implement a basic version\n        try {\n            await client.users.delete({\n                id: userId,\n                password: \"\"\n            });\n        } catch (error) {\n            console.error(\"Delete user error:\", error);\n            throw error;\n        }\n    }, [\n        client\n    ]);\n    const updateUser = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (userId, updates)=>{\n        if (!client) {\n            throw new Error(\"No client available\");\n        }\n        const result = await client.users.update({\n            id: userId,\n            ...updates\n        });\n        return {\n            id: result.results.id,\n            email: result.results.email,\n            name: result.results.name,\n            role: updates.role || \"user\"\n        };\n    }, [\n        client\n    ]);\n    // Initialize authentication state on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                const config = await (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_4__.loadChatConfig)();\n                const deploymentUrl = (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_4__.getDeploymentUrl)(config);\n                // Check for stored tokens\n                const accessToken = localStorage.getItem(\"chatAccessToken\");\n                const refreshToken = localStorage.getItem(\"chatRefreshToken\");\n                if (accessToken) {\n                    const newClient = new r2r_js__WEBPACK_IMPORTED_MODULE_3__.r2rClient(deploymentUrl);\n                    newClient.setTokens(accessToken, refreshToken || \"\");\n                    try {\n                        // Verify token is still valid\n                        const userInfo = await newClient.users.me();\n                        if (userInfo.results) {\n                            setClient(newClient);\n                            // Check user role\n                            let userRole = \"user\";\n                            try {\n                                await newClient.system.settings();\n                                userRole = \"admin\";\n                            } catch (error) {\n                            // User doesn't have admin access\n                            }\n                            setAuthState({\n                                isAuthenticated: true,\n                                email: userInfo.results.email || \"\",\n                                userRole: userRole,\n                                userId: userInfo.results.id\n                            });\n                            // Set pipeline if not already set\n                            if (!pipeline) {\n                                const newPipeline = {\n                                    deploymentUrl\n                                };\n                                setPipeline(newPipeline);\n                                localStorage.setItem(\"pipeline\", JSON.stringify(newPipeline));\n                            }\n                        }\n                    } catch (error) {\n                        // Token is invalid, clear it\n                        localStorage.removeItem(\"chatAccessToken\");\n                        localStorage.removeItem(\"chatRefreshToken\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"Auth initialization error:\", error);\n            } finally{\n                setIsReady(true);\n            }\n        };\n        initializeAuth();\n    }, [\n        pipeline\n    ]);\n    // Save selected model to localStorage\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (false) {}\n    }, [\n        selectedModel\n    ]);\n    const contextValue = react__WEBPACK_IMPORTED_MODULE_1___default().useMemo(()=>({\n            pipeline,\n            setPipeline,\n            selectedModel,\n            setSelectedModel,\n            isAuthenticated: authState.isAuthenticated,\n            authState,\n            login,\n            loginWithToken,\n            logout,\n            unsetCredentials,\n            register,\n            getClient,\n            client,\n            viewMode,\n            setViewMode,\n            isSuperUser,\n            createUser,\n            deleteUser,\n            updateUser\n        }), [\n        pipeline,\n        selectedModel,\n        authState,\n        client,\n        viewMode,\n        isSuperUser,\n        login,\n        loginWithToken,\n        logout,\n        unsetCredentials,\n        register,\n        getClient,\n        createUser,\n        deleteUser,\n        updateUser\n    ]);\n    if (!isReady) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: contextValue,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\context\\\\UserContext.tsx\",\n        lineNumber: 467,\n        columnNumber: 5\n    }, undefined);\n};\nconst useUser = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (context === undefined) {\n        throw new Error(\"useUser must be used within a UserProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/UserContext.tsx\n");

/***/ }),

/***/ "./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   generateId: () => (/* binding */ generateId),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   safeJsonParse: () => (/* binding */ safeJsonParse),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(date);\n}\nfunction generateId() {\n    return Math.random().toString(36).substring(2) + Date.now().toString(36);\n}\nfunction safeJsonParse(jsonString, fallback = {}) {\n    try {\n        return JSON.parse(jsonString);\n    } catch (error) {\n        console.warn(\"Failed to parse JSON:\", error);\n        return fallback;\n    }\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substring(0, maxLength) + \"...\";\n}\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction getInitials(name) {\n    return name.split(\" \").map((word)=>word.charAt(0).toUpperCase()).join(\"\").substring(0, 2);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/utils.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_1__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: \"dark\",\n        enableSystem: true,\n        disableTransitionOnChange: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_UserContext__WEBPACK_IMPORTED_MODULE_2__.UserProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQzRDO0FBQ1M7QUFDdkI7QUFFZixTQUFTRSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQzVELHFCQUNFLDhEQUFDSixzREFBYUE7UUFDWkssV0FBVTtRQUNWQyxjQUFhO1FBQ2JDLFlBQVk7UUFDWkMseUJBQXlCO2tCQUV6Qiw0RUFBQ1AsOERBQVlBO3NCQUNYLDRFQUFDRTtnQkFBVyxHQUFHQyxTQUFTOzs7Ozs7Ozs7Ozs7Ozs7O0FBSWhDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYXJjaGF0Ly4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJztcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tICduZXh0LXRoZW1lcyc7XG5pbXBvcnQgeyBVc2VyUHJvdmlkZXIgfSBmcm9tICdAL2NvbnRleHQvVXNlckNvbnRleHQnO1xuaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcyc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH06IEFwcFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPFRoZW1lUHJvdmlkZXJcbiAgICAgIGF0dHJpYnV0ZT1cImNsYXNzXCJcbiAgICAgIGRlZmF1bHRUaGVtZT1cImRhcmtcIlxuICAgICAgZW5hYmxlU3lzdGVtXG4gICAgICBkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlXG4gICAgPlxuICAgICAgPFVzZXJQcm92aWRlcj5cbiAgICAgICAgPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPlxuICAgICAgPC9Vc2VyUHJvdmlkZXI+XG4gICAgPC9UaGVtZVByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlRoZW1lUHJvdmlkZXIiLCJVc2VyUHJvdmlkZXIiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJhdHRyaWJ1dGUiLCJkZWZhdWx0VGhlbWUiLCJlbmFibGVTeXN0ZW0iLCJkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/chat.tsx":
/*!****************************!*\
  !*** ./src/pages/chat.tsx ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _context_UserContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/context/UserContext */ \"./src/context/UserContext.tsx\");\n/* harmony import */ var _config_chatConfig__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/config/chatConfig */ \"./src/config/chatConfig.ts\");\n/* harmony import */ var _components_chat_ChatHeader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/chat/ChatHeader */ \"./src/components/chat/ChatHeader.tsx\");\n/* harmony import */ var _components_chat_MessageList__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat/MessageList */ \"./src/components/chat/MessageList.tsx\");\n/* harmony import */ var _components_chat_ChatInput__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/chat/ChatInput */ \"./src/components/chat/ChatInput.tsx\");\n/* harmony import */ var _components_chat_ConversationSidebar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/chat/ConversationSidebar */ \"./src/components/chat/ConversationSidebar.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_chat_ChatHeader__WEBPACK_IMPORTED_MODULE_5__, _components_chat_MessageList__WEBPACK_IMPORTED_MODULE_6__, _components_chat_ChatInput__WEBPACK_IMPORTED_MODULE_7__, _components_chat_ConversationSidebar__WEBPACK_IMPORTED_MODULE_8__, _lib_utils__WEBPACK_IMPORTED_MODULE_9__]);\n([_components_chat_ChatHeader__WEBPACK_IMPORTED_MODULE_5__, _components_chat_MessageList__WEBPACK_IMPORTED_MODULE_6__, _components_chat_ChatInput__WEBPACK_IMPORTED_MODULE_7__, _components_chat_ConversationSidebar__WEBPACK_IMPORTED_MODULE_8__, _lib_utils__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\nconst ChatPage = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { isAuthenticated, getClient, authState } = (0,_context_UserContext__WEBPACK_IMPORTED_MODULE_3__.useUser)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentConversationId, setCurrentConversationId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"rag_agent\"); // Fixed to Agent Mode\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Redirect to login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!isAuthenticated) {\n            router.push(\"/auth/login\");\n        }\n    }, [\n        isAuthenticated,\n        router\n    ]);\n    // Auto-scroll to bottom when new messages are added\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const scrollToBottom = ()=>{\n            const messagesContainer = document.getElementById(\"messages-container\");\n            if (messagesContainer) {\n                messagesContainer.scrollTo({\n                    top: messagesContainer.scrollHeight,\n                    behavior: \"smooth\"\n                });\n            }\n        };\n        // Small delay to ensure DOM is updated\n        const timeoutId = setTimeout(scrollToBottom, 100);\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        messages\n    ]);\n    const handleSendMessage = async (content)=>{\n        if (!content.trim() || isLoading) return;\n        const client = getClient();\n        if (!client) {\n            setError(\"No authenticated client available\");\n            return;\n        }\n        // Create user message\n        const userMessage = {\n            id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.generateId)(),\n            content: content.trim(),\n            role: \"user\",\n            timestamp: new Date()\n        };\n        // Add user message to the conversation\n        setMessages((prev)=>[\n                ...prev,\n                userMessage\n            ]);\n        setIsLoading(true);\n        setError(null);\n        // Create assistant message placeholder\n        const assistantMessage = {\n            id: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.generateId)(),\n            content: \"\",\n            role: \"assistant\",\n            timestamp: new Date()\n        };\n        try {\n            // Load configuration for chat settings\n            const config = await (0,_config_chatConfig__WEBPACK_IMPORTED_MODULE_4__.loadChatConfig)();\n            setMessages((prev)=>[\n                    ...prev,\n                    assistantMessage\n                ]);\n            // Prepare search settings\n            const searchSettings = {\n                vectorSearchSettings: {\n                    enabled: config.vectorSearch.enabled,\n                    searchLimit: config.vectorSearch.searchLimit,\n                    searchFilters: JSON.parse(config.vectorSearch.searchFilters),\n                    indexMeasure: config.vectorSearch.indexMeasure,\n                    includeMetadatas: config.vectorSearch.includeMetadatas,\n                    probes: config.vectorSearch.probes,\n                    efSearch: config.vectorSearch.efSearch\n                },\n                hybridSearchSettings: config.hybridSearch.enabled ? {\n                    fullTextWeight: config.hybridSearch.fullTextWeight,\n                    semanticWeight: config.hybridSearch.semanticWeight,\n                    fullTextLimit: config.hybridSearch.fullTextLimit,\n                    rrfK: config.hybridSearch.rrfK\n                } : undefined,\n                kgSearchSettings: config.graphSearch.enabled ? {\n                    kgSearchLevel: config.graphSearch.kgSearchLevel,\n                    maxCommunityDescriptionLength: config.graphSearch.maxCommunityDescriptionLength,\n                    localSearchLimits: config.graphSearch.localSearchLimits,\n                    maxLlmQueries: config.graphSearch.maxLlmQueries\n                } : undefined\n            };\n            // Prepare RAG generation config\n            const ragGenerationConfig = {\n                stream: true,\n                temperature: config.ragGeneration.temperature,\n                topP: config.ragGeneration.topP,\n                topK: config.ragGeneration.topK,\n                maxTokensToSample: config.ragGeneration.maxTokensToSample,\n                kgTemperature: config.ragGeneration.kgTemperature,\n                kgTopP: config.ragGeneration.kgTopP,\n                kgTopK: config.ragGeneration.kgTopK,\n                kgMaxTokensToSample: config.ragGeneration.kgMaxTokensToSample\n            };\n            // Use agent mode (fixed to rag_agent)\n            const streamResponse = await client.retrieval.agent({\n                message: {\n                    role: \"user\",\n                    content: content.trim()\n                },\n                ragGenerationConfig,\n                searchSettings,\n                conversationId: currentConversationId || undefined\n            });\n            // Handle streaming response\n            const reader = streamResponse.getReader();\n            const decoder = new TextDecoder();\n            let buffer = \"\";\n            let fullResponse = \"\";\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) break;\n                    buffer += decoder.decode(value, {\n                        stream: true\n                    });\n                    // Process complete SSE events from the buffer\n                    const events = buffer.split(\"\\n\\n\");\n                    buffer = events.pop() || \"\"; // Keep the last potentially incomplete event in the buffer\n                    for (const event of events){\n                        if (!event.trim()) continue;\n                        const lines = event.split(\"\\n\");\n                        const eventType = lines[0].startsWith(\"event: \") ? lines[0].slice(7) : \"\";\n                        const dataLine = lines.find((line)=>line.startsWith(\"data: \"));\n                        if (!dataLine) continue;\n                        const jsonStr = dataLine.slice(6).trim();\n                        // Check for stream completion marker\n                        if (jsonStr === \"[DONE]\") {\n                            console.log(\"Stream completed\");\n                            break;\n                        }\n                        // Skip empty data\n                        if (!jsonStr) continue;\n                        try {\n                            const eventData = JSON.parse(jsonStr);\n                            // Handle search results event\n                            if (eventType === \"search_results\") {\n                                console.log(\"Search results received\");\n                            // Handle search results if needed\n                            } else if (eventType === \"message\" && eventData.delta && eventData.delta.content) {\n                                const contentItems = eventData.delta.content;\n                                for (const item of contentItems){\n                                    if (item.type === \"text\" && item.payload && item.payload.value) {\n                                        fullResponse += item.payload.value;\n                                    }\n                                }\n                                // Update the assistant message with streaming content\n                                setMessages((prev)=>prev.map((msg)=>msg.id === assistantMessage.id ? {\n                                            ...msg,\n                                            content: fullResponse\n                                        } : msg));\n                            }\n                        } catch (err) {\n                            console.error(\"Error parsing SSE event data:\", err, \"Raw data:\", jsonStr);\n                        }\n                    }\n                }\n            } finally{\n                reader.releaseLock();\n            }\n            // If this is a new conversation, we might get a conversation ID back\n            // For now, we'll generate one if we don't have one\n            if (!currentConversationId) {\n                setCurrentConversationId((0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.generateId)());\n            }\n        } catch (error) {\n            console.error(\"Error sending message:\", error);\n            setError(error instanceof Error ? error.message : \"Failed to send message\");\n            // Remove the assistant message placeholder on error\n            setMessages((prev)=>prev.filter((msg)=>msg.id !== assistantMessage.id));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleConversationSelect = (conversationId, conversationMessages)=>{\n        setCurrentConversationId(conversationId);\n        setMessages(conversationMessages);\n    // 不自动关闭侧边栏，让用户手动控制\n    };\n    const handleNewChat = ()=>{\n        setMessages([]);\n        setCurrentConversationId(null);\n        setError(null);\n    // 不自动关闭侧边栏，让用户手动控制\n    };\n    const toggleSidebar = ()=>{\n        setSidebarOpen(!sidebarOpen);\n    };\n    const handleMessageEdit = (messageId, newContent)=>{\n        setMessages((prev)=>prev.map((msg)=>msg.id === messageId ? {\n                    ...msg,\n                    content: newContent\n                } : msg));\n    };\n    const handleMessageDelete = (messageId)=>{\n        setMessages((prev)=>prev.filter((msg)=>msg.id !== messageId));\n    };\n    if (!isAuthenticated) {\n        return null; // Will redirect to login\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen max-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ConversationSidebar__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                isOpen: sidebarOpen,\n                onClose: ()=>setSidebarOpen(false),\n                onToggle: toggleSidebar,\n                onConversationSelect: handleConversationSelect,\n                onNewChat: handleNewChat,\n                currentConversationId: currentConversationId\n            }, void 0, false, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\chat.tsx\",\n                lineNumber: 261,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex flex-col w-full h-screen max-h-screen transition-all duration-200 ease-in-out ${sidebarOpen ? \"md:max-w-[calc(100%-260px)]\" : \"max-w-full\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatHeader__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            onSidebarToggle: toggleSidebar,\n                            sidebarOpen: sidebarOpen,\n                            mode: mode\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\chat.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\chat.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden relative\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 overflow-y-auto scrollbar-thin\",\n                            id: \"messages-container\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_MessageList__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    messages: messages,\n                                    isLoading: isLoading,\n                                    onMessageEdit: handleMessageEdit,\n                                    onMessageDelete: handleMessageDelete\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\chat.tsx\",\n                                    lineNumber: 289,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\chat.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\chat.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\chat.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatInput__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            onSendMessage: handleSendMessage,\n                            isLoading: isLoading,\n                            error: error,\n                            onClearError: ()=>setError(null)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\chat.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\chat.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\chat.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\LLM\\\\Learning\\\\ai_coding\\\\anythingchat\\\\archat\\\\src\\\\pages\\\\chat.tsx\",\n        lineNumber: 259,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatPage);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/chat.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next-themes":
/*!******************************!*\
  !*** external "next-themes" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-themes");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "r2r-js":
/*!*************************!*\
  !*** external "r2r-js" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("r2r-js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "@radix-ui/react-avatar":
/*!*****************************************!*\
  !*** external "@radix-ui/react-avatar" ***!
  \*****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-avatar");;

/***/ }),

/***/ "@radix-ui/react-dropdown-menu":
/*!************************************************!*\
  !*** external "@radix-ui/react-dropdown-menu" ***!
  \************************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-dropdown-menu");;

/***/ }),

/***/ "@radix-ui/react-slot":
/*!***************************************!*\
  !*** external "@radix-ui/react-slot" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-slot");;

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "react-markdown":
/*!*********************************!*\
  !*** external "react-markdown" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("react-markdown");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fchat&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cchat.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();