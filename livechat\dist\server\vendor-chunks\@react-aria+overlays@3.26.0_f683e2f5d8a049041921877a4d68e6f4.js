"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-aria+overlays@3.26.0_f683e2f5d8a049041921877a4d68e6f4";
exports.ids = ["vendor-chunks/@react-aria+overlays@3.26.0_f683e2f5d8a049041921877a4d68e6f4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.26.0_f683e2f5d8a049041921877a4d68e6f4/node_modules/@react-aria/overlays/dist/useModal.mjs":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-aria+overlays@3.26.0_f683e2f5d8a049041921877a4d68e6f4/node_modules/@react-aria/overlays/dist/useModal.mjs ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ModalProvider: () => (/* binding */ $f57aed4a881a3485$export$178405afcd8c5eb),\n/* harmony export */   OverlayContainer: () => (/* binding */ $f57aed4a881a3485$export$b47c3594eab58386),\n/* harmony export */   OverlayProvider: () => (/* binding */ $f57aed4a881a3485$export$bf688221f59024e5),\n/* harmony export */   useModal: () => (/* binding */ $f57aed4a881a3485$export$33ffd74ebf07f060),\n/* harmony export */   useModalProvider: () => (/* binding */ $f57aed4a881a3485$export$d9aaed4c3ece1bc0)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _react_aria_ssr__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/ssr */ \"(ssr)/./node_modules/.pnpm/@react-aria+ssr@3.9.7_react@19.0.0-rc-69d4b800-20241021/node_modules/@react-aria/ssr/dist/SSRProvider.mjs\");\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\nconst $f57aed4a881a3485$var$Context = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createContext(null);\nfunction $f57aed4a881a3485$export$178405afcd8c5eb(props) {\n    let { children: children } = props;\n    let parent = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($f57aed4a881a3485$var$Context);\n    let [modalCount, setModalCount] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    let context = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>({\n            parent: parent,\n            modalCount: modalCount,\n            addModal () {\n                setModalCount((count)=>count + 1);\n                if (parent) parent.addModal();\n            },\n            removeModal () {\n                setModalCount((count)=>count - 1);\n                if (parent) parent.removeModal();\n            }\n        }), [\n        parent,\n        modalCount\n    ]);\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($f57aed4a881a3485$var$Context.Provider, {\n        value: context\n    }, children);\n}\nfunction $f57aed4a881a3485$export$d9aaed4c3ece1bc0() {\n    let context = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($f57aed4a881a3485$var$Context);\n    return {\n        modalProviderProps: {\n            'aria-hidden': context && context.modalCount > 0 ? true : undefined\n        }\n    };\n}\n/**\n * Creates a root node that will be aria-hidden if there are other modals open.\n */ function $f57aed4a881a3485$var$OverlayContainerDOM(props) {\n    let { modalProviderProps: modalProviderProps } = $f57aed4a881a3485$export$d9aaed4c3ece1bc0();\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement(\"div\", {\n        \"data-overlay-container\": true,\n        ...props,\n        ...modalProviderProps\n    });\n}\nfunction $f57aed4a881a3485$export$bf688221f59024e5(props) {\n    return /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($f57aed4a881a3485$export$178405afcd8c5eb, null, /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($f57aed4a881a3485$var$OverlayContainerDOM, props));\n}\nfunction $f57aed4a881a3485$export$b47c3594eab58386(props) {\n    let isSSR = (0, _react_aria_ssr__WEBPACK_IMPORTED_MODULE_2__.useIsSSR)();\n    let { portalContainer: portalContainer = isSSR ? null : document.body, ...rest } = props;\n    (0, react__WEBPACK_IMPORTED_MODULE_0__).useEffect(()=>{\n        if (portalContainer === null || portalContainer === void 0 ? void 0 : portalContainer.closest('[data-overlay-container]')) throw new Error('An OverlayContainer must not be inside another container. Please change the portalContainer prop.');\n    }, [\n        portalContainer\n    ]);\n    if (!portalContainer) return null;\n    let contents = /*#__PURE__*/ (0, react__WEBPACK_IMPORTED_MODULE_0__).createElement($f57aed4a881a3485$export$bf688221f59024e5, rest);\n    return /*#__PURE__*/ (0, react_dom__WEBPACK_IMPORTED_MODULE_1__).createPortal(contents, portalContainer);\n}\nfunction $f57aed4a881a3485$export$33ffd74ebf07f060(options) {\n    // Add aria-hidden to all parent providers on mount, and restore on unmount.\n    let context = (0, react__WEBPACK_IMPORTED_MODULE_0__.useContext)($f57aed4a881a3485$var$Context);\n    if (!context) throw new Error('Modal is not contained within a provider');\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if ((options === null || options === void 0 ? void 0 : options.isDisabled) || !context || !context.parent) return;\n        // The immediate context is from the provider containing this modal, so we only\n        // want to trigger aria-hidden on its parents not on the modal provider itself.\n        context.parent.addModal();\n        return ()=>{\n            if (context && context.parent) context.parent.removeModal();\n        };\n    }, [\n        context,\n        context.parent,\n        options === null || options === void 0 ? void 0 : options.isDisabled\n    ]);\n    return {\n        modalProps: {\n            'data-ismodal': !(options === null || options === void 0 ? void 0 : options.isDisabled)\n        }\n    };\n}\n\n\n\n//# sourceMappingURL=useModal.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-aria+overlays@3.26.0_f683e2f5d8a049041921877a4d68e6f4/node_modules/@react-aria/overlays/dist/useModal.mjs\n");

/***/ })

};
;