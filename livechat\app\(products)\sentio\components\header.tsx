import { Items } from "../items";
import { Switch, addToast, Dropdown, DropdownTrigger, DropdownMenu, DropdownItem, Avatar, Button } from "@heroui/react";
import { UserMinusIcon, UserPlusIcon, ArrowRightOnRectangleIcon, UserIcon } from "@heroicons/react/24/solid";
import { useSentioChatModeStore, useChatRecordStore, useSentioAsrStore } from "@/lib/store/sentio";
import { CHAT_MODE } from "@/lib/protocol";
import { useTranslations } from 'next-intl';
import { useUser } from "@/lib/context/UserContext";
import { useRouter } from "next/navigation";

function ChatModeSwitch() {
    const t = useTranslations('Products.sentio');
    const { chatMode, setChatMode } = useSentioChatModeStore();
    const { enable } = useSentioAsrStore();
    const { clearChatRecord } = useChatRecordStore();
    const onSelect = (isSelected: boolean) => {
        if (enable) {
            setChatMode(isSelected ? CHAT_MODE.IMMSERSIVE : CHAT_MODE.DIALOGUE)
            clearChatRecord();   
        } else {
            addToast({
                title: t('asrEnableTip'),
                color: "warning"
            })
        }
    }
    return (
        <Switch
            color="secondary"
            startContent={<UserPlusIcon/>}
            endContent={<UserMinusIcon/>}
            isSelected={chatMode == CHAT_MODE.IMMSERSIVE}
            onValueChange={onSelect}
        />
    )
}

function UserMenu() {
    const { authState, logout } = useUser();
    const router = useRouter();

    const handleLogout = async () => {
        try {
            await logout();
            router.push('/auth/login');
        } catch (error) {
            console.error('Logout failed:', error);
            addToast({
                title: "登出失败",
                color: "danger"
            });
        }
    };

    return (
        <Dropdown placement="bottom-end">
            <DropdownTrigger>
                <Avatar
                    as="button"
                    className="transition-transform"
                    color="secondary"
                    name={authState.email?.charAt(0).toUpperCase() || "U"}
                    size="sm"
                    icon={<UserIcon />}
                />
            </DropdownTrigger>
            <DropdownMenu aria-label="用户菜单" variant="flat">
                <DropdownItem key="profile" className="h-14 gap-2">
                    <p className="font-semibold">登录为</p>
                    <p className="font-semibold">{authState.email}</p>
                </DropdownItem>
                <DropdownItem
                    key="logout"
                    color="danger"
                    startContent={<ArrowRightOnRectangleIcon className="w-4 h-4" />}
                    onClick={handleLogout}
                >
                    登出
                </DropdownItem>
            </DropdownMenu>
        </Dropdown>
    );
}

export function Header() {
    return (
        <div className="flex w-full h-[64px] p-6 justify-end z-10">
            <div className="flex flex-row gap-4 items-center">
                <ChatModeSwitch />
                <UserMenu />
                <Items />
            </div>
        </div>
    )
}