'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@/lib/context/UserContext';
import { Spinner } from '@heroui/react';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const router = useRouter();
  const { isAuthenticated } = useUser();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Give the UserContext time to initialize
    const timer = setTimeout(() => {
      if (!isAuthenticated) {
        router.push('/auth/login');
      } else {
        setIsLoading(false);
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [isAuthenticated, router]);

  // If authenticated, stop loading
  useEffect(() => {
    if (isAuthenticated) {
      setIsLoading(false);
    }
  }, [isAuthenticated]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return (
      <div className="min-h-screen bg-zinc-900 flex flex-col items-center justify-center">
        <Spinner size="lg" color="secondary" />
        <p className="mt-4 text-gray-400">正在验证身份...</p>
      </div>
    );
  }

  // If authenticated, render children
  if (isAuthenticated) {
    return <>{children}</>;
  }

  // This should not happen due to redirects above, but just in case
  return (
    <div className="min-h-screen bg-zinc-900 flex flex-col items-center justify-center">
      <p className="text-gray-400">正在跳转到登录页面...</p>
    </div>
  );
};
