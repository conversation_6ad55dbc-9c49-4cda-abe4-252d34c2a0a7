{"version": 3, "file": "fetch.js", "sourceRoot": "", "sources": ["../../src/fetch.ts"], "names": [], "mappings": ";;;;;;;;;;;AAAA,OAAO,EAAsB,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAE9E,MAAM,CAAC,MAAM,sBAAsB,GAAG,mBAAmB,CAAC;AAE1D,MAAM,oBAAoB,GAAG,IAAI,CAAC;AAClC,MAAM,WAAW,GAAG,eAAe,CAAC;AAkDpC,MAAM,UAAU,gBAAgB,CAAC,KAAkB,EAAE,EAU9B;QAV8B,EACjD,MAAM,EAAE,WAAW,EACnB,OAAO,EAAE,YAAY,EACrB,MAAM,EAAE,WAAW,EACnB,SAAS,EACT,OAAO,EACP,OAAO,EACP,cAAc,EACd,KAAK,EAAE,UAAU,OAEE,EADhB,IAAI,cAT0C,6FAUpD,CADU;IAEP,OAAO,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QAEzC,MAAM,OAAO,qBAAQ,YAAY,CAAE,CAAC;QACpC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACjB,OAAO,CAAC,MAAM,GAAG,sBAAsB,CAAC;SAC3C;QAED,IAAI,oBAAqC,CAAC;QAC1C,SAAS,kBAAkB;YACvB,oBAAoB,CAAC,KAAK,EAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;gBAClB,MAAM,EAAE,CAAC;aACZ;QACL,CAAC;QAED,IAAI,CAAC,cAAc,EAAE;YACjB,QAAQ,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;SACrE;QAED,IAAI,aAAa,GAAG,oBAAoB,CAAC;QACzC,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,SAAS,OAAO;YACZ,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,EAAE,kBAAkB,CAAC,CAAC;YACrE,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAChC,oBAAoB,CAAC,KAAK,EAAE,CAAC;QACjC,CAAC;QAGD,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;YACxC,OAAO,EAAE,CAAC;YACV,OAAO,EAAE,CAAC;QACd,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,MAAM,CAAC,KAAK,CAAC;QACzC,MAAM,MAAM,GAAG,WAAW,aAAX,WAAW,cAAX,WAAW,GAAI,aAAa,CAAC;QAC5C,KAAK,UAAU,MAAM;;YACjB,oBAAoB,GAAG,IAAI,eAAe,EAAE,CAAC;YAC7C,IAAI;gBACA,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,KAAK,kCAC3B,IAAI,KACP,OAAO,EACP,MAAM,EAAE,oBAAoB,CAAC,MAAM,IACrC,CAAC;gBAEH,MAAM,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAEvB,MAAM,QAAQ,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC,EAAE;oBACpD,IAAI,EAAE,EAAE;wBAEJ,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;qBAC7B;yBAAM;wBAEH,OAAO,OAAO,CAAC,WAAW,CAAC,CAAC;qBAC/B;gBACL,CAAC,EAAE,KAAK,CAAC,EAAE;oBACP,aAAa,GAAG,KAAK,CAAC;gBAC1B,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;gBAEhB,OAAO,aAAP,OAAO,uBAAP,OAAO,EAAI,CAAC;gBACZ,OAAO,EAAE,CAAC;gBACV,OAAO,EAAE,CAAC;aACb;YAAC,OAAO,GAAG,EAAE;gBACV,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,OAAO,EAAE;oBAEtC,IAAI;wBAEA,MAAM,QAAQ,GAAQ,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAG,GAAG,CAAC,mCAAI,aAAa,CAAC;wBACtD,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;wBAChC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;qBACpD;oBAAC,OAAO,QAAQ,EAAE;wBAEf,OAAO,EAAE,CAAC;wBACV,MAAM,CAAC,QAAQ,CAAC,CAAC;qBACpB;iBACJ;aACJ;QACL,CAAC;QAED,MAAM,EAAE,CAAC;IACb,CAAC,CAAC,CAAC;AACP,CAAC;AAED,SAAS,aAAa,CAAC,QAAkB;IACrC,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IACzD,IAAI,CAAC,CAAA,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,UAAU,CAAC,sBAAsB,CAAC,CAAA,EAAE;QAClD,MAAM,IAAI,KAAK,CAAC,+BAA+B,sBAAsB,aAAa,WAAW,EAAE,CAAC,CAAC;KACpG;AACL,CAAC"}