"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[111],{12492:(e,r,a)=>{a.d(r,{getDeploymentUrl:()=>o,loadChatConfig:()=>i});let t={server:{apiUrl:"http://localhost:7272",useHttps:!1,apiVersion:"v3",timeout:3e4},app:{appName:"LiveChat",appDescription:"Live chat application with R2R integration",version:"1.0.0",defaultMode:"rag_agent",conversationHistoryLimit:10},vectorSearch:{enabled:!0,searchLimit:10,searchFilters:"{}",indexMeasure:"cosine_distance",includeMetadatas:!1,probes:void 0,efSearch:void 0},hybridSearch:{enabled:!1,fullTextWeight:void 0,semanticWeight:void 0,fullTextLimit:void 0,rrfK:void 0},graphSearch:{enabled:!0,kgSearchLevel:null,maxCommunityDescriptionLength:100,localSearchLimits:{},maxLlmQueries:void 0},ragGeneration:{temperature:.1,topP:1,topK:100,maxTokensToSample:1024,kgTemperature:.1,kgTopP:1,kgTopK:100,kgMaxTokensToSample:1024}},i=async()=>{try{let e=await fetch("/config.json");if(!e.ok)return console.warn("Failed to load config.json, using default configuration"),t;let r=await e.json();return{server:{...t.server,...r.server},app:{...t.app,...r.app},vectorSearch:{...t.vectorSearch,...r.vectorSearch},hybridSearch:{...t.hybridSearch,...r.hybridSearch},graphSearch:{...t.graphSearch,...r.graphSearch},ragGeneration:{...t.ragGeneration,...r.ragGeneration}}}catch(e){return console.error("Error loading configuration:",e),t}},o=e=>{let r=e||t;if(r.server.apiUrl.startsWith("http://")||r.server.apiUrl.startsWith("https://"))return r.server.apiUrl;let a=r.server.useHttps?"https":"http";return"".concat(a,"://").concat(r.server.apiUrl)}}}]);