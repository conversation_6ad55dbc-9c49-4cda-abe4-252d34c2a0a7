"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_lib_config_chatConfig_ts";
exports.ids = ["_ssr_lib_config_chatConfig_ts"];
exports.modules = {

/***/ "(ssr)/./lib/config/chatConfig.ts":
/*!**********************************!*\
  !*** ./lib/config/chatConfig.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultChatConfig: () => (/* binding */ defaultChatConfig),\n/* harmony export */   getDeploymentUrl: () => (/* binding */ getDeploymentUrl),\n/* harmony export */   loadChatConfig: () => (/* binding */ loadChatConfig)\n/* harmony export */ });\n/**\r\n * Default configuration values\r\n */ const defaultChatConfig = {\n    server: {\n        apiUrl: \"http://localhost:7272\",\n        useHttps: false,\n        apiVersion: \"v3\",\n        timeout: 30000\n    },\n    app: {\n        appName: \"LiveChat\",\n        appDescription: \"Live chat application with R2R integration\",\n        version: \"1.0.0\",\n        defaultMode: \"rag_agent\",\n        conversationHistoryLimit: 10\n    },\n    vectorSearch: {\n        enabled: true,\n        searchLimit: 10,\n        searchFilters: \"{}\",\n        indexMeasure: \"cosine_distance\",\n        includeMetadatas: false,\n        probes: undefined,\n        efSearch: undefined\n    },\n    hybridSearch: {\n        enabled: false,\n        fullTextWeight: undefined,\n        semanticWeight: undefined,\n        fullTextLimit: undefined,\n        rrfK: undefined\n    },\n    graphSearch: {\n        enabled: true,\n        kgSearchLevel: null,\n        maxCommunityDescriptionLength: 100,\n        localSearchLimits: {},\n        maxLlmQueries: undefined\n    },\n    ragGeneration: {\n        temperature: 0.1,\n        topP: 1.0,\n        topK: 100,\n        maxTokensToSample: 1024,\n        kgTemperature: 0.1,\n        kgTopP: 1.0,\n        kgTopK: 100,\n        kgMaxTokensToSample: 1024\n    }\n};\n/**\r\n * Load configuration from public/config.json with fallback to defaults\r\n */ const loadChatConfig = async ()=>{\n    try {\n        const response = await fetch('/config.json');\n        if (!response.ok) {\n            console.warn('Failed to load config.json, using default configuration');\n            return defaultChatConfig;\n        }\n        const config = await response.json();\n        // Merge with defaults to ensure all required fields are present\n        return {\n            server: {\n                ...defaultChatConfig.server,\n                ...config.server\n            },\n            app: {\n                ...defaultChatConfig.app,\n                ...config.app\n            },\n            vectorSearch: {\n                ...defaultChatConfig.vectorSearch,\n                ...config.vectorSearch\n            },\n            hybridSearch: {\n                ...defaultChatConfig.hybridSearch,\n                ...config.hybridSearch\n            },\n            graphSearch: {\n                ...defaultChatConfig.graphSearch,\n                ...config.graphSearch\n            },\n            ragGeneration: {\n                ...defaultChatConfig.ragGeneration,\n                ...config.ragGeneration\n            }\n        };\n    } catch (error) {\n        console.error('Error loading configuration:', error);\n        return defaultChatConfig;\n    }\n};\n/**\r\n * Get the deployment URL from configuration\r\n */ const getDeploymentUrl = (config)=>{\n    const cfg = config || defaultChatConfig;\n    // If apiUrl already includes protocol, use it as-is\n    if (cfg.server.apiUrl.startsWith('http://') || cfg.server.apiUrl.startsWith('https://')) {\n        return cfg.server.apiUrl;\n    }\n    // Otherwise, construct URL with protocol\n    const protocol = cfg.server.useHttps ? 'https' : 'http';\n    return `${protocol}://${cfg.server.apiUrl}`;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvY29uZmlnL2NoYXRDb25maWcudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBRUE7O0NBRUMsR0FDTSxNQUFNQSxvQkFBZ0M7SUFDM0NDLFFBQVE7UUFDTkMsUUFBUTtRQUNSQyxVQUFVO1FBQ1ZDLFlBQVk7UUFDWkMsU0FBUztJQUNYO0lBQ0FDLEtBQUs7UUFDSEMsU0FBUztRQUNUQyxnQkFBZ0I7UUFDaEJDLFNBQVM7UUFDVEMsYUFBYTtRQUNiQywwQkFBMEI7SUFDNUI7SUFDQUMsY0FBYztRQUNaQyxTQUFTO1FBQ1RDLGFBQWE7UUFDYkMsZUFBZTtRQUNmQyxjQUFjO1FBQ2RDLGtCQUFrQjtRQUNsQkMsUUFBUUM7UUFDUkMsVUFBVUQ7SUFDWjtJQUNBRSxjQUFjO1FBQ1pSLFNBQVM7UUFDVFMsZ0JBQWdCSDtRQUNoQkksZ0JBQWdCSjtRQUNoQkssZUFBZUw7UUFDZk0sTUFBTU47SUFDUjtJQUNBTyxhQUFhO1FBQ1hiLFNBQVM7UUFDVGMsZUFBZTtRQUNmQywrQkFBK0I7UUFDL0JDLG1CQUFtQixDQUFDO1FBQ3BCQyxlQUFlWDtJQUNqQjtJQUNBWSxlQUFlO1FBQ2JDLGFBQWE7UUFDYkMsTUFBTTtRQUNOQyxNQUFNO1FBQ05DLG1CQUFtQjtRQUNuQkMsZUFBZTtRQUNmQyxRQUFRO1FBQ1JDLFFBQVE7UUFDUkMscUJBQXFCO0lBQ3ZCO0FBQ0YsRUFBRTtBQUVGOztDQUVDLEdBQ00sTUFBTUMsaUJBQWlCO0lBQzVCLElBQUk7UUFDRixNQUFNQyxXQUFXLE1BQU1DLE1BQU07UUFDN0IsSUFBSSxDQUFDRCxTQUFTRSxFQUFFLEVBQUU7WUFDaEJDLFFBQVFDLElBQUksQ0FBQztZQUNiLE9BQU83QztRQUNUO1FBRUEsTUFBTThDLFNBQVMsTUFBTUwsU0FBU00sSUFBSTtRQUVsQyxnRUFBZ0U7UUFDaEUsT0FBTztZQUNMOUMsUUFBUTtnQkFBRSxHQUFHRCxrQkFBa0JDLE1BQU07Z0JBQUUsR0FBRzZDLE9BQU83QyxNQUFNO1lBQUM7WUFDeERLLEtBQUs7Z0JBQUUsR0FBR04sa0JBQWtCTSxHQUFHO2dCQUFFLEdBQUd3QyxPQUFPeEMsR0FBRztZQUFDO1lBQy9DTSxjQUFjO2dCQUFFLEdBQUdaLGtCQUFrQlksWUFBWTtnQkFBRSxHQUFHa0MsT0FBT2xDLFlBQVk7WUFBQztZQUMxRVMsY0FBYztnQkFBRSxHQUFHckIsa0JBQWtCcUIsWUFBWTtnQkFBRSxHQUFHeUIsT0FBT3pCLFlBQVk7WUFBQztZQUMxRUssYUFBYTtnQkFBRSxHQUFHMUIsa0JBQWtCMEIsV0FBVztnQkFBRSxHQUFHb0IsT0FBT3BCLFdBQVc7WUFBQztZQUN2RUssZUFBZTtnQkFBRSxHQUFHL0Isa0JBQWtCK0IsYUFBYTtnQkFBRSxHQUFHZSxPQUFPZixhQUFhO1lBQUM7UUFDL0U7SUFDRixFQUFFLE9BQU9pQixPQUFPO1FBQ2RKLFFBQVFJLEtBQUssQ0FBQyxnQ0FBZ0NBO1FBQzlDLE9BQU9oRDtJQUNUO0FBQ0YsRUFBRTtBQUVGOztDQUVDLEdBQ00sTUFBTWlELG1CQUFtQixDQUFDSDtJQUMvQixNQUFNSSxNQUFNSixVQUFVOUM7SUFFdEIsb0RBQW9EO0lBQ3BELElBQUlrRCxJQUFJakQsTUFBTSxDQUFDQyxNQUFNLENBQUNpRCxVQUFVLENBQUMsY0FBY0QsSUFBSWpELE1BQU0sQ0FBQ0MsTUFBTSxDQUFDaUQsVUFBVSxDQUFDLGFBQWE7UUFDdkYsT0FBT0QsSUFBSWpELE1BQU0sQ0FBQ0MsTUFBTTtJQUMxQjtJQUVBLHlDQUF5QztJQUN6QyxNQUFNa0QsV0FBV0YsSUFBSWpELE1BQU0sQ0FBQ0UsUUFBUSxHQUFHLFVBQVU7SUFDakQsT0FBTyxHQUFHaUQsU0FBUyxHQUFHLEVBQUVGLElBQUlqRCxNQUFNLENBQUNDLE1BQU0sRUFBRTtBQUM3QyxFQUFFIiwic291cmNlcyI6WyJEOlxcTExNXFxMZWFybmluZ1xcYWlfY29kaW5nXFxhbnl0aGluZ2NoYXRcXGxpdmVjaGF0XFxsaWJcXGNvbmZpZ1xcY2hhdENvbmZpZy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBDaGF0Q29uZmlnIH0gZnJvbSAnLi4vdHlwZXMnO1xyXG5cclxuLyoqXHJcbiAqIERlZmF1bHQgY29uZmlndXJhdGlvbiB2YWx1ZXNcclxuICovXHJcbmV4cG9ydCBjb25zdCBkZWZhdWx0Q2hhdENvbmZpZzogQ2hhdENvbmZpZyA9IHtcclxuICBzZXJ2ZXI6IHtcclxuICAgIGFwaVVybDogXCJodHRwOi8vbG9jYWxob3N0OjcyNzJcIixcclxuICAgIHVzZUh0dHBzOiBmYWxzZSxcclxuICAgIGFwaVZlcnNpb246IFwidjNcIixcclxuICAgIHRpbWVvdXQ6IDMwMDAwLFxyXG4gIH0sXHJcbiAgYXBwOiB7XHJcbiAgICBhcHBOYW1lOiBcIkxpdmVDaGF0XCIsXHJcbiAgICBhcHBEZXNjcmlwdGlvbjogXCJMaXZlIGNoYXQgYXBwbGljYXRpb24gd2l0aCBSMlIgaW50ZWdyYXRpb25cIixcclxuICAgIHZlcnNpb246IFwiMS4wLjBcIixcclxuICAgIGRlZmF1bHRNb2RlOiBcInJhZ19hZ2VudFwiLCAvLyBEZWZhdWx0IHRvIEFnZW50IE1vZGVcclxuICAgIGNvbnZlcnNhdGlvbkhpc3RvcnlMaW1pdDogMTAsXHJcbiAgfSxcclxuICB2ZWN0b3JTZWFyY2g6IHtcclxuICAgIGVuYWJsZWQ6IHRydWUsXHJcbiAgICBzZWFyY2hMaW1pdDogMTAsXHJcbiAgICBzZWFyY2hGaWx0ZXJzOiBcInt9XCIsXHJcbiAgICBpbmRleE1lYXN1cmU6IFwiY29zaW5lX2Rpc3RhbmNlXCIsXHJcbiAgICBpbmNsdWRlTWV0YWRhdGFzOiBmYWxzZSxcclxuICAgIHByb2JlczogdW5kZWZpbmVkLFxyXG4gICAgZWZTZWFyY2g6IHVuZGVmaW5lZCxcclxuICB9LFxyXG4gIGh5YnJpZFNlYXJjaDoge1xyXG4gICAgZW5hYmxlZDogZmFsc2UsXHJcbiAgICBmdWxsVGV4dFdlaWdodDogdW5kZWZpbmVkLFxyXG4gICAgc2VtYW50aWNXZWlnaHQ6IHVuZGVmaW5lZCxcclxuICAgIGZ1bGxUZXh0TGltaXQ6IHVuZGVmaW5lZCxcclxuICAgIHJyZks6IHVuZGVmaW5lZCxcclxuICB9LFxyXG4gIGdyYXBoU2VhcmNoOiB7XHJcbiAgICBlbmFibGVkOiB0cnVlLFxyXG4gICAga2dTZWFyY2hMZXZlbDogbnVsbCxcclxuICAgIG1heENvbW11bml0eURlc2NyaXB0aW9uTGVuZ3RoOiAxMDAsXHJcbiAgICBsb2NhbFNlYXJjaExpbWl0czoge30sXHJcbiAgICBtYXhMbG1RdWVyaWVzOiB1bmRlZmluZWQsXHJcbiAgfSxcclxuICByYWdHZW5lcmF0aW9uOiB7XHJcbiAgICB0ZW1wZXJhdHVyZTogMC4xLFxyXG4gICAgdG9wUDogMS4wLFxyXG4gICAgdG9wSzogMTAwLFxyXG4gICAgbWF4VG9rZW5zVG9TYW1wbGU6IDEwMjQsXHJcbiAgICBrZ1RlbXBlcmF0dXJlOiAwLjEsXHJcbiAgICBrZ1RvcFA6IDEuMCxcclxuICAgIGtnVG9wSzogMTAwLFxyXG4gICAga2dNYXhUb2tlbnNUb1NhbXBsZTogMTAyNCxcclxuICB9LFxyXG59O1xyXG5cclxuLyoqXHJcbiAqIExvYWQgY29uZmlndXJhdGlvbiBmcm9tIHB1YmxpYy9jb25maWcuanNvbiB3aXRoIGZhbGxiYWNrIHRvIGRlZmF1bHRzXHJcbiAqL1xyXG5leHBvcnQgY29uc3QgbG9hZENoYXRDb25maWcgPSBhc3luYyAoKTogUHJvbWlzZTxDaGF0Q29uZmlnPiA9PiB7XHJcbiAgdHJ5IHtcclxuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9jb25maWcuanNvbicpO1xyXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xyXG4gICAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byBsb2FkIGNvbmZpZy5qc29uLCB1c2luZyBkZWZhdWx0IGNvbmZpZ3VyYXRpb24nKTtcclxuICAgICAgcmV0dXJuIGRlZmF1bHRDaGF0Q29uZmlnO1xyXG4gICAgfVxyXG4gICAgXHJcbiAgICBjb25zdCBjb25maWcgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XHJcbiAgICBcclxuICAgIC8vIE1lcmdlIHdpdGggZGVmYXVsdHMgdG8gZW5zdXJlIGFsbCByZXF1aXJlZCBmaWVsZHMgYXJlIHByZXNlbnRcclxuICAgIHJldHVybiB7XHJcbiAgICAgIHNlcnZlcjogeyAuLi5kZWZhdWx0Q2hhdENvbmZpZy5zZXJ2ZXIsIC4uLmNvbmZpZy5zZXJ2ZXIgfSxcclxuICAgICAgYXBwOiB7IC4uLmRlZmF1bHRDaGF0Q29uZmlnLmFwcCwgLi4uY29uZmlnLmFwcCB9LFxyXG4gICAgICB2ZWN0b3JTZWFyY2g6IHsgLi4uZGVmYXVsdENoYXRDb25maWcudmVjdG9yU2VhcmNoLCAuLi5jb25maWcudmVjdG9yU2VhcmNoIH0sXHJcbiAgICAgIGh5YnJpZFNlYXJjaDogeyAuLi5kZWZhdWx0Q2hhdENvbmZpZy5oeWJyaWRTZWFyY2gsIC4uLmNvbmZpZy5oeWJyaWRTZWFyY2ggfSxcclxuICAgICAgZ3JhcGhTZWFyY2g6IHsgLi4uZGVmYXVsdENoYXRDb25maWcuZ3JhcGhTZWFyY2gsIC4uLmNvbmZpZy5ncmFwaFNlYXJjaCB9LFxyXG4gICAgICByYWdHZW5lcmF0aW9uOiB7IC4uLmRlZmF1bHRDaGF0Q29uZmlnLnJhZ0dlbmVyYXRpb24sIC4uLmNvbmZpZy5yYWdHZW5lcmF0aW9uIH0sXHJcbiAgICB9O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIGNvbmZpZ3VyYXRpb246JywgZXJyb3IpO1xyXG4gICAgcmV0dXJuIGRlZmF1bHRDaGF0Q29uZmlnO1xyXG4gIH1cclxufTtcclxuXHJcbi8qKlxyXG4gKiBHZXQgdGhlIGRlcGxveW1lbnQgVVJMIGZyb20gY29uZmlndXJhdGlvblxyXG4gKi9cclxuZXhwb3J0IGNvbnN0IGdldERlcGxveW1lbnRVcmwgPSAoY29uZmlnPzogQ2hhdENvbmZpZyk6IHN0cmluZyA9PiB7XHJcbiAgY29uc3QgY2ZnID0gY29uZmlnIHx8IGRlZmF1bHRDaGF0Q29uZmlnO1xyXG5cclxuICAvLyBJZiBhcGlVcmwgYWxyZWFkeSBpbmNsdWRlcyBwcm90b2NvbCwgdXNlIGl0IGFzLWlzXHJcbiAgaWYgKGNmZy5zZXJ2ZXIuYXBpVXJsLnN0YXJ0c1dpdGgoJ2h0dHA6Ly8nKSB8fCBjZmcuc2VydmVyLmFwaVVybC5zdGFydHNXaXRoKCdodHRwczovLycpKSB7XHJcbiAgICByZXR1cm4gY2ZnLnNlcnZlci5hcGlVcmw7XHJcbiAgfVxyXG5cclxuICAvLyBPdGhlcndpc2UsIGNvbnN0cnVjdCBVUkwgd2l0aCBwcm90b2NvbFxyXG4gIGNvbnN0IHByb3RvY29sID0gY2ZnLnNlcnZlci51c2VIdHRwcyA/ICdodHRwcycgOiAnaHR0cCc7XHJcbiAgcmV0dXJuIGAke3Byb3RvY29sfTovLyR7Y2ZnLnNlcnZlci5hcGlVcmx9YDtcclxufTsiXSwibmFtZXMiOlsiZGVmYXVsdENoYXRDb25maWciLCJzZXJ2ZXIiLCJhcGlVcmwiLCJ1c2VIdHRwcyIsImFwaVZlcnNpb24iLCJ0aW1lb3V0IiwiYXBwIiwiYXBwTmFtZSIsImFwcERlc2NyaXB0aW9uIiwidmVyc2lvbiIsImRlZmF1bHRNb2RlIiwiY29udmVyc2F0aW9uSGlzdG9yeUxpbWl0IiwidmVjdG9yU2VhcmNoIiwiZW5hYmxlZCIsInNlYXJjaExpbWl0Iiwic2VhcmNoRmlsdGVycyIsImluZGV4TWVhc3VyZSIsImluY2x1ZGVNZXRhZGF0YXMiLCJwcm9iZXMiLCJ1bmRlZmluZWQiLCJlZlNlYXJjaCIsImh5YnJpZFNlYXJjaCIsImZ1bGxUZXh0V2VpZ2h0Iiwic2VtYW50aWNXZWlnaHQiLCJmdWxsVGV4dExpbWl0IiwicnJmSyIsImdyYXBoU2VhcmNoIiwia2dTZWFyY2hMZXZlbCIsIm1heENvbW11bml0eURlc2NyaXB0aW9uTGVuZ3RoIiwibG9jYWxTZWFyY2hMaW1pdHMiLCJtYXhMbG1RdWVyaWVzIiwicmFnR2VuZXJhdGlvbiIsInRlbXBlcmF0dXJlIiwidG9wUCIsInRvcEsiLCJtYXhUb2tlbnNUb1NhbXBsZSIsImtnVGVtcGVyYXR1cmUiLCJrZ1RvcFAiLCJrZ1RvcEsiLCJrZ01heFRva2Vuc1RvU2FtcGxlIiwibG9hZENoYXRDb25maWciLCJyZXNwb25zZSIsImZldGNoIiwib2siLCJjb25zb2xlIiwid2FybiIsImNvbmZpZyIsImpzb24iLCJlcnJvciIsImdldERlcGxveW1lbnRVcmwiLCJjZmciLCJzdGFydHNXaXRoIiwicHJvdG9jb2wiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/config/chatConfig.ts\n");

/***/ })

};
;