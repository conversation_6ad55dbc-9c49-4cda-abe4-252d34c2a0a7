{"Version": 3, "Meta": {"Duration": 2.53, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 37, "TotalSegmentCount": 116, "TotalPointCount": 311, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, -15, 1, 0.333, -15, 0.667, -15, 1, -15, 1, 1.078, -15, 1.156, 10.166, 1.233, 19, 1, 1.278, 24.048, 1.322, 23, 1.367, 23, 1, 1.433, 23, 1.5, 23, 1.567, 23, 1, 1.767, 23, 1.967, 8, 2.167, 8, 0, 2.533, 8]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -14, 1, 0.333, -14, 0.667, -14, 1, -14, 1, 1.122, -14, 1.244, -13, 1.367, -13, 1, 1.433, -13, 1.5, -13, 1.567, -13, 1, 1.767, -13, 1.967, -8, 2.167, -8, 0, 2.533, -8]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -15, 1, 0.333, -15, 0.667, -15, 1, -15, 1, 1.122, -15, 1.244, -8, 1.367, -8, 1, 1.433, -8, 1.5, -8, 1.567, -8, 0, 2.533, -8]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.044, 1, 1.089, 0, 1.133, 0, 1, 1.278, 0, 1.422, 0, 1.567, 0, 1, 1.711, 0, 1.856, 0, 2, 0, 1, 2.056, 0, 2.111, 1, 2.167, 1, 0, 2.533, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.333, 1, 0.667, 1, 1, 1, 1, 1.044, 1, 1.089, 0, 1.133, 0, 1, 1.278, 0, 1.422, 0, 1.567, 0, 1, 1.711, 0, 1.856, 0, 2, 0, 1, 2.056, 0, 2.111, 1, 2.167, 1, 0, 2.533, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, -1, 1, 0.333, -1, 0.667, -1, 1, -1, 1, 1.189, -1, 1.378, -1, 1.567, -1, 0, 2.533, -1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0.43, 1, 0.333, 0.43, 0.667, 0.43, 1, 0.43, 1, 1.122, 0.43, 1.244, 0.96, 1.367, 0.96, 1, 1.433, 0.96, 1.5, 0.96, 1.567, 0.96, 1, 1.767, 0.96, 1.967, -0.19, 2.167, -0.19, 0, 2.533, -0.19]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0.33, 1, 0.333, 0.33, 0.667, 0.33, 1, 0.33, 1, 1.122, 0.33, 1.244, 0.54, 1.367, 0.54, 1, 1.433, 0.54, 1.5, 0.54, 1.567, 0.54, 1, 1.767, 0.54, 1.967, 0.63, 2.167, 0.63, 0, 2.533, 0.63]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, -0.5, 1, 0.333, -0.5, 0.667, -0.5, 1, -0.5, 1, 1.189, -0.5, 1.378, -0.5, 1.567, -0.5, 0, 2.533, -0.5]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, -0.5, 1, 0.333, -0.5, 0.667, -0.5, 1, -0.5, 1, 1.189, -0.5, 1.378, -0.5, 1.567, -0.5, 0, 2.533, -0.5]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, -0.5, 1, 0.333, -0.5, 0.667, -0.5, 1, -0.5, 1, 1.189, -0.5, 1.378, -0.5, 1.567, -0.5, 0, 2.533, -0.5]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, -0.5, 1, 0.333, -0.5, 0.667, -0.5, 1, -0.5, 1, 1.189, -0.5, 1.378, -0.5, 1.567, -0.5, 0, 2.533, -0.5]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, -1, 1, 0.333, -1, 0.667, -1, 1, -1, 1, 1.189, -1, 1.378, -1, 1.567, -1, 0, 2.533, -1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, -8, 1, 0.333, -8, 0.667, -8, 1, -8, 1, 1.033, -8, 1.067, -6.846, 1.1, -6, 1, 1.189, -3.744, 1.278, -3, 1.367, -3, 1, 1.433, -3, 1.5, -3, 1.567, -3, 1, 1.767, -3, 1.967, 0, 2.167, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.033, 0, 1.067, 0, 1.1, 0, 1, 1.256, 0, 1.411, 0, 1.567, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 5, 1, 0.333, 5, 0.667, 5, 1, 5, 1, 1.033, 5, 1.067, 5, 1.1, 5, 1, 1.256, 5, 1.411, 5, 1.567, 5, 0, 2.533, 5]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, -1, 0, 2.533, -1]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, -1, 0, 2.533, -1]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 0, 2.533, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 1, 0.333, 0, 0.667, 0, 1, 0, 1, 1.189, 0, 1.378, 0, 1.567, 0, 0, 2.533, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_A_001", "Segments": [0, 1, 0, 2.53, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_A_001", "Segments": [0, 1, 0, 2.53, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_B_001", "Segments": [0, 0, 0, 2.53, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_B_001", "Segments": [0, 0, 0, 2.53, 0]}]}