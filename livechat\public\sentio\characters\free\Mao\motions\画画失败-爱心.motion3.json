{"Version": 3, "Meta": {"Duration": 9.37, "Fps": 30.0, "FadeInTime": 0.25, "FadeOutTime": 0.25, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 116, "TotalSegmentCount": 516, "TotalPointCount": 1400, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.089, 0, 0.178, 0.507, 0.267, -1.466, 1, 0.411, -4.672, 0.556, -22.794, 0.7, -22.794, 1, 0.911, -22.794, 1.122, -22.336, 1.333, -22.336, 1, 1.533, -22.336, 1.733, -22.362, 1.933, -22.794, 1, 2.067, -23.081, 2.2, -24.238, 2.333, -25.081, 1, 2.511, -26.204, 2.689, -26.911, 2.867, -26.911, 1, 3.844, -26.911, 4.822, -26.911, 5.8, -26.911, 1, 5.833, -26.911, 5.867, -29.911, 5.9, -29.911, 1, 5.944, -29.911, 5.989, -13, 6.033, -13, 1, 6.056, -13, 6.078, -15, 6.1, -15, 1, 6.589, -15, 7.078, -15, 7.567, -15, 0, 9.367, -15]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.1, 0, 0.2, -2.583, 0.3, -2.583, 1, 0.433, -2.583, 0.567, 28, 0.7, 28, 1, 0.822, 28, 0.944, 25, 1.067, 25, 1, 1.4, 25, 1.733, 25, 2.067, 25, 1, 2.178, 25, 2.289, 9.282, 2.4, 6, 1, 2.467, 4.031, 2.533, 4.503, 2.6, 4.503, 1, 2.733, 4.503, 2.867, 30, 3, 30, 1, 3.922, 30, 4.844, 30, 5.767, 30, 1, 5.811, 30, 5.856, 4, 5.9, 4, 1, 5.956, 4, 6.011, 30, 6.067, 30, 1, 6.578, 30, 7.089, 30, 7.6, 30, 1, 7.867, 30, 8.133, -17, 8.4, -17, 1, 8.722, -17, 9.044, -15, 9.367, -15]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.233, 0, 0.467, -0.018, 0.7, 3, 1, 0.9, 5.587, 1.1, 14, 1.3, 14, 1, 1.544, 14, 1.789, 6.914, 2.033, 0, 1, 2.244, -5.971, 2.456, -7, 2.667, -7, 1, 2.778, -7, 2.889, 5.204, 3, 7, 1, 3.222, 10.592, 3.444, 11, 3.667, 11, 1, 4.333, 11, 5, 11, 5.667, 11, 1, 5.756, 11, 5.844, 5, 5.933, 5, 1, 6.011, 5, 6.089, 21, 6.167, 21, 1, 6.622, 21, 7.078, 21, 7.533, 21, 1, 7.6, 21, 7.667, 23, 7.733, 23, 1, 7.967, 23, 8.2, -17, 8.433, -17, 1, 8.611, -17, 8.789, -16, 8.967, -16, 0, 9.367, -16]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamFaceInkOn", "Segments": [0, 0, 1, 2.011, 0, 4.022, 0, 6.033, 0, 1, 6.144, 0, 6.256, 1, 6.367, 1, 0, 9.367, 1]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.089, 1, 0.178, 1, 0.267, 1, 1, 0.3, 1, 0.333, 0, 0.367, 0, 1, 0.389, 0, 0.411, 0, 0.433, 0, 1, 0.467, 0, 0.5, 1, 0.533, 1, 1, 1.078, 1, 1.622, 1, 2.167, 1, 1, 2.2, 1, 2.233, 0, 2.267, 0, 1, 2.367, 0, 2.467, 0, 2.567, 0, 1, 2.6, 0, 2.633, 1, 2.667, 1, 1, 3.211, 1, 3.756, 1, 4.3, 1, 1, 4.333, 1, 4.367, 0, 4.4, 0, 1, 4.422, 0, 4.444, 1, 4.467, 1, 1, 4.511, 1, 4.556, 1, 4.6, 1, 1, 4.633, 1, 4.667, 0, 4.7, 0, 1, 4.722, 0, 4.744, 1, 4.767, 1, 1, 5.144, 1, 5.522, 1, 5.9, 1, 1, 5.933, 1, 5.967, 0, 6, 0, 1, 6.2, 0, 6.4, 0, 6.6, 0, 1, 6.633, 0, 6.667, 1.2, 6.7, 1.2, 1, 6.822, 1.2, 6.944, 1.2, 7.067, 1.2, 1, 7.1, 1.2, 7.133, 0, 7.167, 0, 1, 7.2, 0, 7.233, 1.2, 7.267, 1.2, 1, 7.3, 1.2, 7.333, 0, 7.367, 0, 1, 7.4, 0, 7.433, 1.2, 7.467, 1.2, 1, 7.656, 1.2, 7.844, 1.149, 8.033, 1, 1, 8.056, 0.983, 8.078, 0, 8.1, 0, 1, 8.144, 0, 8.189, 0, 8.233, 0, 1, 8.256, 0, 8.278, 1, 8.3, 1, 0, 9.367, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 1.956, 0, 3.911, 0, 5.867, 0, 1, 5.878, 0, 5.889, 0.898, 5.9, 0.9, 1, 6.256, 0.969, 6.611, 1, 6.967, 1, 1, 6.978, 1, 6.989, 0, 7, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamEyeLForm", "Segments": [0, 0, 1, 2.744, 0, 5.489, 0, 8.233, 0, 1, 8.256, 0, 8.278, 1, 8.3, 1, 0, 9.367, 1]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.089, 1, 0.178, 1, 0.267, 1, 1, 0.3, 1, 0.333, 0, 0.367, 0, 1, 0.389, 0, 0.411, 0, 0.433, 0, 1, 0.467, 0, 0.5, 1, 0.533, 1, 1, 1.078, 1, 1.622, 1, 2.167, 1, 1, 2.2, 1, 2.233, 0, 2.267, 0, 1, 2.367, 0, 2.467, 0, 2.567, 0, 1, 2.6, 0, 2.633, 1, 2.667, 1, 1, 3.211, 1, 3.756, 1, 4.3, 1, 1, 4.333, 1, 4.367, 0, 4.4, 0, 1, 4.422, 0, 4.444, 1, 4.467, 1, 1, 4.511, 1, 4.556, 1, 4.6, 1, 1, 4.633, 1, 4.667, 0, 4.7, 0, 1, 4.722, 0, 4.744, 1, 4.767, 1, 1, 5.144, 1, 5.522, 1, 5.9, 1, 1, 5.933, 1, 5.967, 0, 6, 0, 1, 6.2, 0, 6.4, 0, 6.6, 0, 1, 6.633, 0, 6.667, 1.2, 6.7, 1.2, 1, 6.822, 1.2, 6.944, 1.2, 7.067, 1.2, 1, 7.1, 1.2, 7.133, 0, 7.167, 0, 1, 7.2, 0, 7.233, 1.2, 7.267, 1.2, 1, 7.3, 1.2, 7.333, 0, 7.367, 0, 1, 7.4, 0, 7.433, 1.2, 7.467, 1.2, 1, 7.656, 1.2, 7.844, 1.149, 8.033, 1, 1, 8.056, 0.983, 8.078, 0, 8.1, 0, 1, 8.144, 0, 8.189, 0, 8.233, 0, 1, 8.256, 0, 8.278, 1, 8.3, 1, 0, 9.367, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 1.956, 0, 3.911, 0, 5.867, 0, 1, 5.878, 0, 5.889, 1, 5.9, 1, 1, 6.256, 1, 6.611, 1, 6.967, 1, 1, 6.978, 1, 6.989, 0, 7, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamEyeRForm", "Segments": [0, 0, 1, 2.744, 0, 5.489, 0, 8.233, 0, 1, 8.256, 0, 8.278, 1, 8.3, 1, 0, 9.367, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.233, 0, 0.467, -0.2, 0.7, -0.2, 1, 2.478, -0.2, 4.256, -0.2, 6.033, -0.2, 1, 6.256, -0.2, 6.478, -0.4, 6.7, -0.4, 1, 7.078, -0.4, 7.456, -0.4, 7.833, -0.4, 1, 7.978, -0.4, 8.122, 0.6, 8.267, 0.6, 0, 9.367, 0.6]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.233, 0, 0.467, 0.2, 0.7, 0.2, 1, 2.478, 0.2, 4.256, 0.2, 6.033, 0.2, 1, 6.256, 0.2, 6.478, 0.7, 6.7, 0.7, 1, 6.922, 0.7, 7.144, 0.7, 7.367, 0.7, 1, 7.522, 0.7, 7.678, 0, 7.833, 0, 1, 7.978, 0, 8.122, 1, 8.267, 1, 0, 9.367, 1]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 2.189, 0, 4.378, 0, 6.567, 0, 1, 6.611, 0, 6.656, -1, 6.7, -1, 1, 7.011, -1, 7.322, -0.986, 7.633, -0.9, 1, 7.789, -0.857, 7.944, -0.707, 8.1, -0.6, 1, 8.111, -0.592, 8.122, 0, 8.133, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamEyeEffect", "Segments": [0, 0, 1, 0.778, 0, 1.556, 0, 2.333, 0, 1, 2.389, 0, 2.444, 1, 2.5, 1, 1, 3.667, 1, 4.833, 1, 6, 1, 1, 6.011, 1, 6.022, 0, 6.033, 0, 1, 6.256, 0, 6.478, 0, 6.7, 0, 1, 6.9, 0, 7.1, 0, 7.3, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 2.644, 0, 5.289, 0, 7.933, 0, 1, 8.044, 0, 8.156, -0.4, 8.267, -0.4, 0, 9.367, -0.4]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 2.644, 0, 5.289, 0, 7.933, 0, 1, 8.044, 0, 8.156, -0.4, 8.267, -0.4, 0, 9.367, -0.4]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 2.178, 0, 4.356, 0, 6.533, 0, 1, 6.6, 0, 6.667, -0.4, 6.733, -0.4, 1, 7.044, -0.4, 7.356, -0.4, 7.667, -0.4, 1, 7.756, -0.4, 7.844, -0.234, 7.933, 0, 1, 8.044, 0.293, 8.156, 0.4, 8.267, 0.4, 0, 9.367, 0.4]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 2.178, 0, 4.356, 0, 6.533, 0, 1, 6.6, 0, 6.667, -0.4, 6.733, -0.4, 1, 7.044, -0.4, 7.356, -0.4, 7.667, -0.4, 1, 7.756, -0.4, 7.844, -0.234, 7.933, 0, 1, 8.044, 0.293, 8.156, 0.4, 8.267, 0.4, 0, 9.367, 0.4]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 2.178, 0, 4.356, 0, 6.533, 0, 1, 6.6, 0, 6.667, 0.6, 6.733, 0.6, 1, 7.044, 0.6, 7.356, 0.6, 7.667, 0.6, 1, 7.756, 0.6, 7.844, 0.351, 7.933, 0, 1, 8.044, -0.439, 8.156, -0.6, 8.267, -0.6, 0, 9.367, -0.6]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 2.178, 0, 4.356, 0, 6.533, 0, 1, 6.6, 0, 6.667, 0.6, 6.733, 0.6, 1, 7.044, 0.6, 7.356, 0.6, 7.667, 0.6, 1, 7.756, 0.6, 7.844, 0.351, 7.933, 0, 1, 8.044, -0.439, 8.156, -0.6, 8.267, -0.6, 0, 9.367, -0.6]}, {"Target": "Parameter", "Id": "ParamMouthA", "Segments": [0, 0, 1, 2.189, 0, 4.378, 0, 6.567, 0, 1, 6.622, 0, 6.678, 1, 6.733, 1, 1, 7.056, 1, 7.378, 1, 7.7, 1, 1, 7.744, 1, 7.789, 0, 7.833, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamMouthI", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamMouthU", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamMouthE", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamMouthO", "Segments": [0, 0, 1, 0.833, 0, 1.667, 0, 2.5, 0, 1, 2.611, 0, 2.722, 1, 2.833, 1, 1, 3.844, 1, 4.856, 1, 5.867, 1, 1, 5.911, 1, 5.956, 0, 6, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamMouthUp", "Segments": [0, 1, 1, 1.922, 1, 3.844, 1, 5.767, 1, 1, 5.811, 1, 5.856, 0, 5.9, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamMouthDown", "Segments": [0, 0, 1, 1.956, 0, 3.911, 0, 5.867, 0, 1, 5.911, 0, 5.956, 1, 6, 1, 0, 9.367, 1]}, {"Target": "Parameter", "Id": "ParamMouthAngry", "Segments": [0, 0, 1, 2.633, 0, 5.267, 0, 7.9, 0, 1, 8.033, 0, 8.167, 1, 8.3, 1, 0, 9.367, 1]}, {"Target": "Parameter", "Id": "ParamMouthAngryLine", "Segments": [0, 0, 1, 2.633, 0, 5.267, 0, 7.9, 0, 1, 8.033, 0, 8.167, 1, 8.3, 1, 0, 9.367, 1]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.433, 0, 0.867, -5, 1.3, -5, 1, 1.511, -5, 1.722, -3, 1.933, -3, 1, 2.089, -3, 2.244, -6, 2.4, -6, 1, 2.667, -6, 2.933, -4.776, 3.2, -4, 1, 3.511, -3.095, 3.822, -3, 4.133, -3, 1, 4.867, -3, 5.6, -5, 6.333, -5, 1, 6.744, -5, 7.156, -5, 7.567, -5, 1, 7.744, -5, 7.922, -8, 8.1, -8, 0, 9.367, -8]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.1, 0, 0.2, -1, 0.3, -1, 1, 0.4, -1, 0.5, 4.328, 0.6, 7, 1, 0.678, 9.079, 0.756, 9, 0.833, 9, 1, 0.978, 9, 1.122, 5, 1.267, 5, 1, 1.344, 5, 1.422, 6.297, 1.5, 7, 1, 1.6, 7.904, 1.7, 8, 1.8, 8, 1, 1.944, 8, 2.089, 3.885, 2.233, 0, 1, 2.278, -1.195, 2.322, -1, 2.367, -1, 1, 2.533, -1, 2.7, 7, 2.867, 7, 1, 3.022, 7, 3.178, 6.6, 3.333, 6.6, 1, 4.133, 6.6, 4.933, 7, 5.733, 7, 1, 5.8, 7, 5.867, 3, 5.933, 3, 1, 5.967, 3, 6, 10, 6.033, 10, 1, 6.144, 10, 6.256, 6.493, 6.367, 6.399, 1, 6.767, 6.062, 7.167, 6, 7.567, 6, 1, 7.644, 6, 7.722, 10, 7.8, 10, 1, 7.856, 10, 7.911, 9.54, 7.967, 6, 1, 8.044, 1.043, 8.122, -4, 8.2, -4, 1, 8.356, -4, 8.511, -2, 8.667, -2, 0, 9.367, -2]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.378, 0, 0.756, 7, 1.133, 7, 1, 1.511, 7, 1.889, 5.295, 2.267, 1, 1, 2.333, 0.242, 2.4, -1, 2.467, -1, 1, 2.611, -1, 2.756, 5.286, 2.9, 6, 1, 3.4, 8.471, 3.9, 9, 4.4, 9, 1, 4.844, 9, 5.289, 8.25, 5.733, 6, 1, 5.8, 5.662, 5.867, 2, 5.933, 2, 1, 5.978, 2, 6.022, 10, 6.067, 10, 1, 6.156, 10, 6.244, 9, 6.333, 9, 1, 6.822, 9, 7.311, 9, 7.8, 9, 1, 8.011, 9, 8.222, -3, 8.433, -3, 1, 8.633, -3, 8.833, -2, 9.033, -2, 0, 9.367, -2]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamLeftShoulderUp", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.633, 0, 0.833, -1.599, 1.033, -1.599, 1, 1.489, -1.599, 1.944, -1.599, 2.4, -1.599, 1, 2.744, -1.599, 3.089, -0.772, 3.433, -0.64, 1, 4.189, -0.349, 4.944, -0.229, 5.7, 0, 1, 5.756, 0.017, 5.811, 8.644, 5.867, 8.644, 1, 5.978, 8.644, 6.089, -1, 6.2, -1, 1, 6.267, -1, 6.333, 0, 6.4, 0, 1, 6.844, 0, 7.289, 0, 7.733, 0, 1, 7.856, 0, 7.978, 8.298, 8.1, 8.298, 1, 8.222, 8.298, 8.344, 4.495, 8.467, 4.495, 1, 8.556, 4.495, 8.644, 6.569, 8.733, 6.569, 0, 9.367, 6.569]}, {"Target": "Parameter", "Id": "ParamRightShoulderUp", "Segments": [0, 0, 1, 0.156, 0, 0.311, -10, 0.467, -10, 1, 0.667, -10, 0.867, -10, 1.067, -10, 1, 1.222, -10, 1.378, -10, 1.533, -10, 1, 1.822, -10, 2.111, -10, 2.4, -10, 1, 2.633, -10, 2.867, -10, 3.1, -10, 1, 3.967, -10, 4.833, -10, 5.7, -10, 1, 5.756, -10, 5.811, 4, 5.867, 4, 1, 5.978, 4, 6.089, -2, 6.2, -2, 1, 6.267, -2, 6.333, 0, 6.4, 0, 1, 6.844, 0, 7.289, 0, 7.733, 0, 1, 7.844, 0, 7.956, 8, 8.067, 8, 1, 8.167, 8, 8.267, -10, 8.367, -10, 0, 9.367, -10]}, {"Target": "Parameter", "Id": "ParamArmLA01", "Segments": [0, 0, 1, 0.211, 0, 0.422, -3, 0.633, -3, 1, 0.9, -3, 1.167, 3, 1.433, 3, 1, 1.778, 3, 2.122, -6, 2.467, -6, 1, 2.611, -6, 2.756, 3.403, 2.9, 4, 1, 3.144, 5.01, 3.389, 5.852, 3.633, 6, 1, 4.333, 6.424, 5.033, 6.646, 5.733, 7, 1, 5.778, 7.022, 5.822, 13, 5.867, 13, 1, 5.944, 13, 6.022, -4.497, 6.1, -4.497, 1, 6.211, -4.497, 6.322, -3, 6.433, -3, 1, 6.756, -3, 7.078, -3, 7.4, -3, 1, 7.511, -3, 7.622, -5, 7.733, -5, 1, 7.833, -5, 7.933, 24, 8.033, 24, 1, 8.122, 24, 8.211, 17, 8.3, 17, 1, 8.4, 17, 8.5, 18, 8.6, 18, 0, 9.367, 18]}, {"Target": "Parameter", "Id": "ParamArmLA02", "Segments": [0, 0, 1, 0.3, 0, 0.6, -3, 0.9, -3, 1, 1.178, -3, 1.456, 0, 1.733, 0, 1, 2.022, 0, 2.311, -5, 2.6, -5, 1, 2.744, -5, 2.889, -3.126, 3.033, -3, 1, 3.911, -2.234, 4.789, -2, 5.667, -2, 1, 5.722, -2, 5.778, -3, 5.833, -3, 1, 5.878, -3, 5.922, 4, 5.967, 4, 1, 6.022, 4, 6.078, -4, 6.133, -4, 1, 6.222, -4, 6.311, -3, 6.4, -3, 1, 6.8, -3, 7.2, -3, 7.6, -3, 1, 7.722, -3, 7.844, -6.441, 7.967, -13, 1, 8.056, -17.77, 8.144, -20, 8.233, -20, 0, 9.367, -20]}, {"Target": "Parameter", "Id": "ParamArmLA03", "Segments": [0, 0, 1, 0.3, 0, 0.6, -1, 0.9, -1, 1, 1.122, -1, 1.344, 1, 1.567, 1, 1, 1.844, 1, 2.122, -3, 2.4, -3, 1, 2.622, -3, 2.844, -1.208, 3.067, -1, 1, 3.933, -0.189, 4.8, 0, 5.667, 0, 1, 5.733, 0, 5.8, -7, 5.867, -7, 1, 5.956, -7, 6.044, 14, 6.133, 14, 1, 6.222, 14, 6.311, 0, 6.4, 0, 1, 6.944, 0, 7.489, 0, 8.033, 0, 1, 8.111, 0, 8.189, 30, 8.267, 30, 0, 9.367, 30]}, {"Target": "Parameter", "Id": "ParamHandLA", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamArmRA01", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamArmRA02", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamArmRA03", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamWandRotate", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHandRA", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamInkDrop", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamInkDropRotate", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamInkDropOn", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamArmLB01", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamArmLB02", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamArmLB03", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHandLB", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHatForm", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamArmRB01", "Segments": [0, 0, 1, 0.178, 0, 0.356, -30, 0.533, -30, 1, 0.622, -30, 0.711, -13.204, 0.8, -3, 1, 0.889, 7.204, 0.978, 8, 1.067, 8, 1, 1.144, 8, 1.222, 3, 1.3, 3, 1, 1.378, 3, 1.456, 8, 1.533, 8, 1, 1.656, 8, 1.778, -3.927, 1.9, -11, 1, 2.1, -22.575, 2.3, -25, 2.5, -25, 1, 2.711, -25, 2.922, -18, 3.133, -18, 1, 4.033, -18, 4.933, -21, 5.833, -21, 1, 5.889, -21, 5.944, -19, 6, -19, 1, 6.067, -19, 6.133, -28, 6.2, -28, 1, 6.6, -28, 7, -28, 7.4, -28, 1, 7.511, -28, 7.622, -30, 7.733, -30, 1, 7.822, -30, 7.911, -11, 8, -11, 1, 8.122, -11, 8.244, -30, 8.367, -30, 1, 8.5, -30, 8.633, -28, 8.767, -28, 0, 9.367, -28]}, {"Target": "Parameter", "Id": "ParamArmRB02", "Segments": [0, 0, 1, 0.067, 0, 0.133, -0.16, 0.2, 1.053, 1, 0.289, 2.67, 0.378, 9.239, 0.467, 13.846, 1, 0.556, 18.453, 0.644, 26.27, 0.733, 28.907, 1, 0.778, 30, 0.822, 30, 0.867, 30, 1, 1.011, 30, 1.156, 28.04, 1.3, 22.85, 1, 1.367, 20.455, 1.433, 18.257, 1.5, 13.85, 1, 1.622, 5.77, 1.744, -3.15, 1.867, -3.15, 1, 2.033, -3.15, 2.2, -2.846, 2.367, 0, 1, 2.456, 1.518, 2.544, 10, 2.633, 10, 1, 2.822, 10, 3.011, 1.174, 3.2, 0, 1, 4.078, -5.454, 4.956, -7, 5.833, -7, 1, 5.867, -7, 5.9, 30, 5.933, 30, 1, 6.022, 30, 6.111, -16, 6.2, -16, 1, 6.289, -16, 6.378, -13, 6.467, -13, 1, 6.889, -13, 7.311, -13, 7.733, -13, 1, 7.856, -13, 7.978, -16, 8.1, -16, 1, 8.211, -16, 8.322, -4, 8.433, -4, 1, 8.578, -4, 8.722, -5, 8.867, -5, 0, 9.367, -5]}, {"Target": "Parameter", "Id": "ParamArmRB02Y", "Segments": [0, 0, 1, 0.122, 0, 0.244, 9.78, 0.367, 23, 1, 0.422, 29.009, 0.478, 30, 0.533, 30, 1, 0.644, 30, 0.756, 0, 0.867, 0, 1, 1.1, 0, 1.333, 0, 1.567, 0, 1, 1.756, 0, 1.944, 30, 2.133, 30, 1, 2.289, 30, 2.444, 30, 2.6, 29.514, 1, 3.633, 22.99, 4.667, 15.313, 5.7, 15.313, 1, 5.789, 15.313, 5.878, 22, 5.967, 22, 1, 6.011, 22, 6.056, 0, 6.1, 0, 1, 7.189, 0, 8.278, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamArmRB03", "Segments": [0, 0, 1, 0.144, 0, 0.289, -19.973, 0.433, -19.973, 1, 0.522, -19.973, 0.611, -18.904, 0.7, -8.205, 1, 0.767, -0.181, 0.833, 18, 0.9, 18, 1, 1.033, 18, 1.167, 8.193, 1.3, -2.506, 1, 1.4, -10.53, 1.5, -11.984, 1.6, -11.984, 1, 1.756, -11.984, 1.911, -6.248, 2.067, 3, 1, 2.178, 9.606, 2.289, 12, 2.4, 12, 1, 3.544, 12, 4.689, 0, 5.833, 0, 1, 5.889, 0, 5.944, 18, 6, 18, 1, 6.078, 18, 6.156, -2, 6.233, -2, 1, 6.344, -2, 6.456, 0, 6.567, 0, 1, 6.956, 0, 7.344, 0, 7.733, 0, 1, 7.856, 0, 7.978, -9, 8.1, -9, 1, 8.133, -9, 8.167, -3.91, 8.2, 0, 1, 8.278, 9.124, 8.356, 12, 8.433, 12, 1, 8.578, 12, 8.722, 8, 8.867, 8, 0, 9.367, 8]}, {"Target": "Parameter", "Id": "ParamHandRB", "Segments": [0, 0, 1, 0.156, 0, 0.311, -10, 0.467, -10, 1, 1.167, -10, 1.867, -10, 2.567, -10, 1, 2.789, -10, 3.011, -4.373, 3.233, 0, 1, 3.633, 7.872, 4.033, 10, 4.433, 10, 1, 4.9, 10, 5.367, 10, 5.833, 10, 1, 5.889, 10, 5.944, -10, 6, -10, 1, 6.1, -10, 6.2, 0, 6.3, 0, 1, 6.8, 0, 7.3, 0, 7.8, 0, 1, 7.878, 0, 7.956, 5, 8.033, 5, 1, 8.167, 5, 8.3, -10, 8.433, -10, 0, 9.367, -10]}, {"Target": "Parameter", "Id": "ParamAllX", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamAllY", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamAllRotate", "Segments": [0, 0, 1, 1.933, 0, 3.867, 0, 5.8, 0, 1, 5.844, 0, 5.889, 2, 5.933, 2, 1, 6.067, 2, 6.2, 0.797, 6.333, 0.496, 1, 6.544, 0.02, 6.756, 0, 6.967, 0, 1, 7.4, 0, 7.833, 0, 8.267, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHairSideL", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHairSideR", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHairBackR", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHairBackL", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHairFrontFuwa", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHairSideFuwa", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHairBackFuwa", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamWing", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamRibbon", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHatBrim", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHatTop", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamAccessory1", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamAccessory2", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamString", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamRobeL", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamRobeR", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamRobeFuwa", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamSmokeOn", "Segments": [0, 0, 1, 1.833, 0, 3.667, 0, 5.5, 0, 1, 5.667, 0, 5.833, 1, 6, 1, 1, 6.511, 1, 7.022, 1, 7.533, 1, 1, 7.856, 1, 8.178, 0, 8.5, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamSmoke", "Segments": [0, 0, 1, 1.778, 0, 3.556, 0, 5.333, 0, 1, 5.833, 0, 6.333, 14.59, 6.833, 21.7, 1, 7.389, 29.6, 7.944, 30, 8.5, 30, 0, 9.367, 30]}, {"Target": "Parameter", "Id": "ParamExplosionChargeOn", "Segments": [0, 0, 1, 0.944, 0, 1.889, 0, 2.833, 0, 1, 3.278, 0, 3.722, 1, 4.167, 1, 1, 4.444, 1, 4.722, 1, 5, 1, 1, 5.278, 1, 5.556, 0, 5.833, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamExplosionLightCharge", "Segments": [0, 0, 1, 0.767, 0, 1.533, 0, 2.3, 0, 1, 2.7, 0, 3.1, 14.776, 3.5, 28.8, 2, 3.533, 0, 0, 4.233, 28.636, 2, 4.267, 0, 0, 4.967, 28.636, 2, 5, 0, 1, 5.367, 13.273, 5.733, 27.986, 6.1, 28.8, 0, 9.367, 28.8]}, {"Target": "Parameter", "Id": "ParamExplosionOn", "Segments": [0, 0, 1, 0.644, 0, 1.289, 0, 1.933, 0, 1, 1.944, 0, 1.956, 1, 1.967, 1, 1, 2.678, 1, 3.389, 1, 4.1, 1, 1, 5.556, 1, 7.011, 1, 8.467, 1, 1, 8.478, 1, 8.489, 0, 8.5, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamExplosion", "Segments": [0, 0, 1, 1.833, 0, 3.667, 0, 5.5, 0, 1, 5.822, 0, 6.144, 30, 6.467, 30, 1, 6.933, 30, 7.4, 30, 7.867, 30, 0, 9.367, 30]}, {"Target": "Parameter", "Id": "ParamWandInkColorRainbow", "Segments": [0, 0, 1, 0.656, 0, 1.311, 0, 1.967, 0, 0, 2.267, 5, 0, 3.6, 34.3, 2, 3.633, 5, 0, 4.967, 34.3, 2, 5, 5, 0, 6.333, 34.3, 1, 6.344, 34.3, 6.356, 5.15, 6.367, 5, 1, 6.622, 1.548, 6.878, 0, 7.133, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHeartMissOn", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0, 0.333, 0.01, 1, 0.444, 0.023, 0.556, 1, 0.667, 1, 1, 2.2, 1, 3.733, 1, 5.267, 1, 1, 5.411, 1, 5.556, 0, 5.7, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHeartBackMissOn", "Segments": [0, 0, 1, 0.9, 0, 1.8, 0, 2.7, 0, 1, 3.156, 0, 3.611, 1, 4.067, 1, 1, 4.467, 1, 4.867, 1, 5.267, 1, 1, 5.411, 1, 5.556, 0, 5.7, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHeartColorRainbow", "Segments": [0, 0, 1, 0.756, 0, 1.511, 0, 2.267, 0, 0, 3.6, 29.3, 2, 3.633, 0, 0, 4.967, 29.3, 2, 5, 0, 0, 6.333, 29.3, 0, 9.367, 29.3]}, {"Target": "Parameter", "Id": "ParamWandInkColorHeal", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHeartHealOn", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHeartBackHealOn", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHeartColorHeal", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHeartLightOn", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHeartLight", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHeartLightColor", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamMagicPositionX", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamMagicPositionY", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamWandInk", "Segments": [0, 0, 0, 0.5, 0, 1, 0.744, 0, 0.989, 0.25, 1.233, 0.489, 1, 1.489, 0.735, 1.744, 1, 2, 1, 0, 9.367, 1]}, {"Target": "Parameter", "Id": "ParamHeartDrow", "Segments": [0, 0, 0, 0.567, 0, 1, 0.8, 13.228, 1.033, 14.173, 1.267, 15, 1, 1.611, 20.079, 1.956, 27.892, 2.3, 30, 0, 9.367, 30]}, {"Target": "Parameter", "Id": "ParamHeartSize", "Segments": [0, 0, 1, 1.611, 0, 3.222, 0, 4.833, 0, 1, 4.944, 0, 5.056, 0.3, 5.167, 0.3, 1, 5.333, 0.3, 5.5, -0.9, 5.667, -0.9, 0, 9.367, -0.9]}, {"Target": "Parameter", "Id": "ParamHeartColorLight", "Segments": [0, 0, 1, 0.9, 0, 1.8, 0, 2.7, 0, 1, 3.156, 0, 3.611, 1, 4.067, 1, 0, 9.367, 1]}, {"Target": "Parameter", "Id": "ParamAllColor", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamAuraOn", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamAura", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamAuraColor", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHealOn", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "Parameter", "Id": "ParamHealLight", "Segments": [0, 0, 0, 9.367, 0]}, {"Target": "PartOpacity", "Id": "PartArmLA", "Segments": [0, 1, 0, 9.37, 1]}, {"Target": "PartOpacity", "Id": "PartArmRA", "Segments": [0, 0, 0, 9.37, 0]}, {"Target": "PartOpacity", "Id": "PartArmLB", "Segments": [0, 0, 0, 9.37, 0]}, {"Target": "PartOpacity", "Id": "PartArmRB", "Segments": [0, 1, 0, 9.37, 1]}]}