[INFO]2025-07-14 11:48:08,769 File 'main.py',line 11: [System] Welcome to Awesome digitalHuman System
[INFO]2025-07-14 11:48:08,770 File 'main.py',line 12: [System] Runing config:
COMMON:
  LOG_LEVEL: DEBUG
  NAME: Awesome-Digital-Human
  VERSION: v3.0.0
SERVER:
  AGENTS:
    DEFAULT: Repeater
    SUPPORT_LIST: [CfgNode({'NAME': 'Repeater', 'VERSION': 'v0.0.1', 'DESC': '复读机', 'META': CfgNode({'official': '', 'configuration': '', 'tips': '测试使用', 'fee': ''})}), CfgNode({'NAME': 'OpenAI', 'VERSION': 'v0.0.1', 'DESC': '接入Openai协议的服务', 'META': CfgNode({'official': '', 'configuration': '', 'tips': '兼容所有符合Openai协议的API', 'fee': ''}), 'PARAMETERS': [{'name': 'model', 'description': 'ID of the model to use.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'base_url', 'description': 'The base url for request.', 'type': 'string', 'required': False, 'choices': [], 'default': 'https://api.openai.com/v1'}, {'name': 'api_key', 'description': 'The api key for request.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用', 'fee': ''}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'FastGPT', 'VERSION': 'v0.0.1', 'DESC': '接入FastGPT应用', 'META': CfgNode({'official': 'https://fastgpt.cn', 'configuration': 'FastGPT云服务: https://cloud.fastgpt.cn', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'base_url', 'description': 'FastGPT base url.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'FastGPT API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'uid', 'description': 'FastGPT customUid.', 'type': 'string', 'required': False, 'choices': [], 'default': 'adh'}]})]
  ENGINES:
    ASR:
      DEFAULT: Dify
      SUPPORT_LIST: [CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用', 'fee': 'free'}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'Tencent-API', 'VERSION': 'v0.0.1', 'DESC': '接入腾讯服务', 'META': CfgNode({'official': '', 'configuration': 'https://console.cloud.tencent.com/asr', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'secret_id', 'description': 'tencent secret_id.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'secret_key', 'description': 'tencent secret_key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]}), CfgNode({'NAME': 'funasrStreaming', 'VERSION': 'v0.0.1', 'DESC': '接入Stream ASR', 'META': CfgNode({'official': 'https://github.com/modelscope/FunASR', 'tips': '支持本地部署的FunAsrStream应用', 'fee': 'free', 'infer_type': 'stream'}), 'PARAMETERS': [{'name': 'api_url', 'description': 'Funasr Streaming API URL', 'type': 'string', 'required': False, 'choices': [], 'default': 'ws://adh-funasr:10095'}, {'name': 'mode', 'description': 'Funasr Streaming mode', 'type': 'string', 'required': False, 'choices': ['2pass'], 'default': '2pass'}]})]
    LLM:
      DEFAULT: None
      SUPPORT_LIST: []
    TTS:
      DEFAULT: EdgeTTS
      SUPPORT_LIST: [CfgNode({'NAME': 'EdgeTTS', 'VERSION': 'v0.0.1', 'DESC': '适配EdgeTTS', 'META': CfgNode({'official': 'https://github.com/rany2/edge-tts', 'configuration': '', 'tips': '开源项目可能存在不稳定的情况', 'fee': 'free'}), 'PARAMETERS': [{'name': 'voice', 'description': 'Voice for TTS.', 'type': 'string', 'required': False, 'choices': ['Getting from voice api...'], 'default': 'zh-CN-XiaoxiaoNeural'}, {'name': 'rate', 'description': 'Set rate, default +0%.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}, {'name': 'volume', 'description': 'Set volume, default +0%.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}, {'name': 'pitch', 'description': 'Set pitch, default +0Hz.', 'type': 'int', 'required': False, 'range': [-100, 100], 'default': 0}]}), CfgNode({'NAME': 'Tencent-API', 'VERSION': 'v0.0.1', 'DESC': '接入腾讯服务', 'META': CfgNode({'official': '', 'configuration': 'https://console.cloud.tencent.com/tts', 'tips': '', 'fee': ''}), 'PARAMETERS': [{'name': 'secret_id', 'description': 'tencent secret_id.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'secret_key', 'description': 'tencent secret_key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'voice', 'description': 'Voice for TTS.', 'type': 'string', 'required': False, 'choices': [], 'default': '爱小璟'}, {'name': 'volume', 'description': 'Set volume, default +0%.', 'type': 'float', 'required': False, 'range': [-10, 10], 'default': 0.0}, {'name': 'speed', 'description': 'Set speed, default +0%.', 'type': 'float', 'required': False, 'range': [-2, 6], 'default': 0.0}]}), CfgNode({'NAME': 'Dify', 'VERSION': 'v0.0.1', 'DESC': '接入Dify应用', 'META': CfgNode({'official': 'https://dify.ai/', 'configuration': 'https://mp.weixin.qq.com/s/YXyHYN1dC_nJAOCco7ZJjg', 'tips': '支持本地部署的Dify应用全', 'fee': ''}), 'PARAMETERS': [{'name': 'api_server', 'description': 'Dify API Server.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'api_key', 'description': 'Dify API Key.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}, {'name': 'username', 'description': 'Dify Username.', 'type': 'string', 'required': False, 'choices': [], 'default': ''}]})]
  IP: 0.0.0.0
  PORT: 8880
  WORKSPACE_PATH: ./outputs
[INFO]2025-07-14 11:48:08,771 File 'asrFactory.py',line 23: [ASRFactory] Create engine: Dify
[INFO]2025-07-14 11:48:08,772 File 'enginePool.py',line 44: [EnginePool] ASR Engine Dify is created.
[INFO]2025-07-14 11:48:08,772 File 'asrFactory.py',line 23: [ASRFactory] Create engine: Tencent-API
[INFO]2025-07-14 11:48:08,772 File 'enginePool.py',line 44: [EnginePool] ASR Engine Tencent-API is created.
[INFO]2025-07-14 11:48:08,772 File 'asrFactory.py',line 23: [ASRFactory] Create engine: funasrStreaming
[INFO]2025-07-14 11:48:08,773 File 'enginePool.py',line 44: [EnginePool] ASR Engine funasrStreaming is created.
[INFO]2025-07-14 11:48:08,773 File 'enginePool.py',line 45: [EnginePool] ASR Engine default is Dify.
[INFO]2025-07-14 11:48:08,773 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: EdgeTTS
[INFO]2025-07-14 11:48:08,774 File 'enginePool.py',line 49: [EnginePool] TTS Engine EdgeTTS is created.
[INFO]2025-07-14 11:48:08,774 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: Tencent-API
[INFO]2025-07-14 11:48:08,774 File 'enginePool.py',line 49: [EnginePool] TTS Engine Tencent-API is created.
[INFO]2025-07-14 11:48:08,774 File 'ttsFactory.py',line 23: [TTSFactory] Create engine: Dify
[INFO]2025-07-14 11:48:08,774 File 'enginePool.py',line 49: [EnginePool] TTS Engine Dify is created.
[INFO]2025-07-14 11:48:08,774 File 'enginePool.py',line 50: [EnginePool] TTS Engine default is EdgeTTS.
[INFO]2025-07-14 11:48:08,775 File 'enginePool.py',line 55: [EnginePool] LLM Engine default is None.
[INFO]2025-07-14 11:48:08,775 File 'agentFactory.py',line 21: [AgentFactory] Create instance: Repeater
[INFO]2025-07-14 11:48:08,776 File 'agentPool.py',line 39: [AgentPool] AGENT Engine Repeater is created.
[INFO]2025-07-14 11:48:08,776 File 'agentFactory.py',line 21: [AgentFactory] Create instance: OpenAI
[INFO]2025-07-14 11:48:08,776 File 'agentPool.py',line 39: [AgentPool] AGENT Engine OpenAI is created.
[INFO]2025-07-14 11:48:08,776 File 'agentFactory.py',line 21: [AgentFactory] Create instance: Dify
[INFO]2025-07-14 11:48:08,777 File 'agentPool.py',line 39: [AgentPool] AGENT Engine Dify is created.
[INFO]2025-07-14 11:48:08,777 File 'agentFactory.py',line 21: [AgentFactory] Create instance: FastGPT
[INFO]2025-07-14 11:48:08,777 File 'agentPool.py',line 39: [AgentPool] AGENT Engine FastGPT is created.
[INFO]2025-07-14 11:48:08,777 File 'agentPool.py',line 40: [AgentPool] AGENT Engine default is Repeater.
[ERROR]2025-07-14 11:49:08,571 File 'reponse.py',line 34: Request URL is missing an 'http://' or 'https://' protocol.
Traceback (most recent call last):
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpcore\_async\connection_pool.py", line 207, in handle_async_request
    raise UnsupportedProtocol(
httpcore.UnsupportedProtocol: Request URL is missing an 'http://' or 'https://' protocol.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\digitalHuman\server\api\asr\asr_api_v0.py", line 103, in api_asr_infer_file
    output: TextMessage = await asr_infer(header, items)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\digitalHuman\server\core\api_asr_v0_impl.py", line 35, in asr_infer
    output: TextMessage = await engine.run(input=input, user=user, **items.config)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\digitalHuman\engine\asr\difyASR.py", line 40, in run
    response = await httpxAsyncClient.post(API_SERVER + "/audio-to-text", headers=headers, files=files, data=payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1859, in post
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\ProgramData\anaconda3\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.UnsupportedProtocol: Request URL is missing an 'http://' or 'https://' protocol.
[ERROR]2025-07-14 11:49:12,641 File 'reponse.py',line 34: Request URL is missing an 'http://' or 'https://' protocol.
Traceback (most recent call last):
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpcore\_async\connection_pool.py", line 207, in handle_async_request
    raise UnsupportedProtocol(
httpcore.UnsupportedProtocol: Request URL is missing an 'http://' or 'https://' protocol.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\digitalHuman\server\api\asr\asr_api_v0.py", line 103, in api_asr_infer_file
    output: TextMessage = await asr_infer(header, items)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\digitalHuman\server\core\api_asr_v0_impl.py", line 35, in asr_infer
    output: TextMessage = await engine.run(input=input, user=user, **items.config)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\digitalHuman\engine\asr\difyASR.py", line 40, in run
    response = await httpxAsyncClient.post(API_SERVER + "/audio-to-text", headers=headers, files=files, data=payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1859, in post
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\ProgramData\anaconda3\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.UnsupportedProtocol: Request URL is missing an 'http://' or 'https://' protocol.
[ERROR]2025-07-14 11:49:56,409 File 'reponse.py',line 34: Request URL is missing an 'http://' or 'https://' protocol.
Traceback (most recent call last):
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpcore\_async\connection_pool.py", line 207, in handle_async_request
    raise UnsupportedProtocol(
httpcore.UnsupportedProtocol: Request URL is missing an 'http://' or 'https://' protocol.

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\digitalHuman\server\api\asr\asr_api_v0.py", line 103, in api_asr_infer_file
    output: TextMessage = await asr_infer(header, items)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\digitalHuman\server\core\api_asr_v0_impl.py", line 35, in asr_infer
    output: TextMessage = await engine.run(input=input, user=user, **items.config)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\digitalHuman\engine\asr\difyASR.py", line 40, in run
    response = await httpxAsyncClient.post(API_SERVER + "/audio-to-text", headers=headers, files=files, data=payload)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1859, in post
    return await self.request(
           ^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\ProgramData\anaconda3\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "D:\OpenSource\DigitalHuman\awesome-digital-human-live2d\.venv\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.UnsupportedProtocol: Request URL is missing an 'http://' or 'https://' protocol.
