"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-stately+selection@3._e0d2f62db6af26827f64a1ae4b751db0";
exports.ids = ["vendor-chunks/@react-stately+selection@3._e0d2f62db6af26827f64a1ae4b751db0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+selection@3._e0d2f62db6af26827f64a1ae4b751db0/node_modules/@react-stately/selection/dist/Selection.mjs":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+selection@3._e0d2f62db6af26827f64a1ae4b751db0/node_modules/@react-stately/selection/dist/Selection.mjs ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Selection: () => (/* binding */ $e40ea825a81a3709$export$52baac22726c72bf)\n/* harmony export */ });\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ class $e40ea825a81a3709$export$52baac22726c72bf extends Set {\n    constructor(keys, anchorKey, currentKey){\n        super(keys);\n        if (keys instanceof $e40ea825a81a3709$export$52baac22726c72bf) {\n            this.anchorKey = anchorKey !== null && anchorKey !== void 0 ? anchorKey : keys.anchorKey;\n            this.currentKey = currentKey !== null && currentKey !== void 0 ? currentKey : keys.currentKey;\n        } else {\n            this.anchorKey = anchorKey !== null && anchorKey !== void 0 ? anchorKey : null;\n            this.currentKey = currentKey !== null && currentKey !== void 0 ? currentKey : null;\n        }\n    }\n}\n\n\n\n//# sourceMappingURL=Selection.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+selection@3._e0d2f62db6af26827f64a1ae4b751db0/node_modules/@react-stately/selection/dist/Selection.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+selection@3._e0d2f62db6af26827f64a1ae4b751db0/node_modules/@react-stately/selection/dist/SelectionManager.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+selection@3._e0d2f62db6af26827f64a1ae4b751db0/node_modules/@react-stately/selection/dist/SelectionManager.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SelectionManager: () => (/* binding */ $d496c0a20b6e58ec$export$6c8a5aaad13c9852)\n/* harmony export */ });\n/* harmony import */ var _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Selection.mjs */ \"(ssr)/./node_modules/.pnpm/@react-stately+selection@3._e0d2f62db6af26827f64a1ae4b751db0/node_modules/@react-stately/selection/dist/Selection.mjs\");\n/* harmony import */ var _react_stately_collections__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-stately/collections */ \"(ssr)/./node_modules/.pnpm/@react-stately+collections@_1ed9bb58ba6c8998638f0bad1166ce91/node_modules/@react-stately/collections/dist/getChildNodes.mjs\");\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\nclass $d496c0a20b6e58ec$export$6c8a5aaad13c9852 {\n    /**\n   * The type of selection that is allowed in the collection.\n   */ get selectionMode() {\n        return this.state.selectionMode;\n    }\n    /**\n   * Whether the collection allows empty selection.\n   */ get disallowEmptySelection() {\n        return this.state.disallowEmptySelection;\n    }\n    /**\n   * The selection behavior for the collection.\n   */ get selectionBehavior() {\n        return this.state.selectionBehavior;\n    }\n    /**\n   * Sets the selection behavior for the collection.\n   */ setSelectionBehavior(selectionBehavior) {\n        this.state.setSelectionBehavior(selectionBehavior);\n    }\n    /**\n   * Whether the collection is currently focused.\n   */ get isFocused() {\n        return this.state.isFocused;\n    }\n    /**\n   * Sets whether the collection is focused.\n   */ setFocused(isFocused) {\n        this.state.setFocused(isFocused);\n    }\n    /**\n   * The current focused key in the collection.\n   */ get focusedKey() {\n        return this.state.focusedKey;\n    }\n    /** Whether the first or last child of the focused key should receive focus. */ get childFocusStrategy() {\n        return this.state.childFocusStrategy;\n    }\n    /**\n   * Sets the focused key.\n   */ setFocusedKey(key, childFocusStrategy) {\n        if (key == null || this.collection.getItem(key)) this.state.setFocusedKey(key, childFocusStrategy);\n    }\n    /**\n   * The currently selected keys in the collection.\n   */ get selectedKeys() {\n        return this.state.selectedKeys === 'all' ? new Set(this.getSelectAllKeys()) : this.state.selectedKeys;\n    }\n    /**\n   * The raw selection value for the collection.\n   * Either 'all' for select all, or a set of keys.\n   */ get rawSelection() {\n        return this.state.selectedKeys;\n    }\n    /**\n   * Returns whether a key is selected.\n   */ isSelected(key) {\n        if (this.state.selectionMode === 'none') return false;\n        let mappedKey = this.getKey(key);\n        if (mappedKey == null) return false;\n        return this.state.selectedKeys === 'all' ? this.canSelectItem(mappedKey) : this.state.selectedKeys.has(mappedKey);\n    }\n    /**\n   * Whether the selection is empty.\n   */ get isEmpty() {\n        return this.state.selectedKeys !== 'all' && this.state.selectedKeys.size === 0;\n    }\n    /**\n   * Whether all items in the collection are selected.\n   */ get isSelectAll() {\n        if (this.isEmpty) return false;\n        if (this.state.selectedKeys === 'all') return true;\n        if (this._isSelectAll != null) return this._isSelectAll;\n        let allKeys = this.getSelectAllKeys();\n        let selectedKeys = this.state.selectedKeys;\n        this._isSelectAll = allKeys.every((k)=>selectedKeys.has(k));\n        return this._isSelectAll;\n    }\n    get firstSelectedKey() {\n        let first = null;\n        for (let key of this.state.selectedKeys){\n            let item = this.collection.getItem(key);\n            if (!first || item && (0, _react_stately_collections__WEBPACK_IMPORTED_MODULE_0__.compareNodeOrder)(this.collection, item, first) < 0) first = item;\n        }\n        var _first_key;\n        return (_first_key = first === null || first === void 0 ? void 0 : first.key) !== null && _first_key !== void 0 ? _first_key : null;\n    }\n    get lastSelectedKey() {\n        let last = null;\n        for (let key of this.state.selectedKeys){\n            let item = this.collection.getItem(key);\n            if (!last || item && (0, _react_stately_collections__WEBPACK_IMPORTED_MODULE_0__.compareNodeOrder)(this.collection, item, last) > 0) last = item;\n        }\n        var _last_key;\n        return (_last_key = last === null || last === void 0 ? void 0 : last.key) !== null && _last_key !== void 0 ? _last_key : null;\n    }\n    get disabledKeys() {\n        return this.state.disabledKeys;\n    }\n    get disabledBehavior() {\n        return this.state.disabledBehavior;\n    }\n    /**\n   * Extends the selection to the given key.\n   */ extendSelection(toKey) {\n        if (this.selectionMode === 'none') return;\n        if (this.selectionMode === 'single') {\n            this.replaceSelection(toKey);\n            return;\n        }\n        let mappedToKey = this.getKey(toKey);\n        if (mappedToKey == null) return;\n        let selection;\n        // Only select the one key if coming from a select all.\n        if (this.state.selectedKeys === 'all') selection = new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)([\n            mappedToKey\n        ], mappedToKey, mappedToKey);\n        else {\n            let selectedKeys = this.state.selectedKeys;\n            var _selectedKeys_anchorKey;\n            let anchorKey = (_selectedKeys_anchorKey = selectedKeys.anchorKey) !== null && _selectedKeys_anchorKey !== void 0 ? _selectedKeys_anchorKey : mappedToKey;\n            selection = new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)(selectedKeys, anchorKey, mappedToKey);\n            var _selectedKeys_currentKey;\n            for (let key of this.getKeyRange(anchorKey, (_selectedKeys_currentKey = selectedKeys.currentKey) !== null && _selectedKeys_currentKey !== void 0 ? _selectedKeys_currentKey : mappedToKey))selection.delete(key);\n            for (let key of this.getKeyRange(mappedToKey, anchorKey))if (this.canSelectItem(key)) selection.add(key);\n        }\n        this.state.setSelectedKeys(selection);\n    }\n    getKeyRange(from, to) {\n        let fromItem = this.collection.getItem(from);\n        let toItem = this.collection.getItem(to);\n        if (fromItem && toItem) {\n            if ((0, _react_stately_collections__WEBPACK_IMPORTED_MODULE_0__.compareNodeOrder)(this.collection, fromItem, toItem) <= 0) return this.getKeyRangeInternal(from, to);\n            return this.getKeyRangeInternal(to, from);\n        }\n        return [];\n    }\n    getKeyRangeInternal(from, to) {\n        var _this_layoutDelegate;\n        if ((_this_layoutDelegate = this.layoutDelegate) === null || _this_layoutDelegate === void 0 ? void 0 : _this_layoutDelegate.getKeyRange) return this.layoutDelegate.getKeyRange(from, to);\n        let keys = [];\n        let key = from;\n        while(key != null){\n            let item = this.collection.getItem(key);\n            if (item && (item.type === 'item' || item.type === 'cell' && this.allowsCellSelection)) keys.push(key);\n            if (key === to) return keys;\n            key = this.collection.getKeyAfter(key);\n        }\n        return [];\n    }\n    getKey(key) {\n        let item = this.collection.getItem(key);\n        if (!item) // ¯\\_(ツ)_/¯\n        return key;\n        // If cell selection is allowed, just return the key.\n        if (item.type === 'cell' && this.allowsCellSelection) return key;\n        // Find a parent item to select\n        while(item && item.type !== 'item' && item.parentKey != null)item = this.collection.getItem(item.parentKey);\n        if (!item || item.type !== 'item') return null;\n        return item.key;\n    }\n    /**\n   * Toggles whether the given key is selected.\n   */ toggleSelection(key) {\n        if (this.selectionMode === 'none') return;\n        if (this.selectionMode === 'single' && !this.isSelected(key)) {\n            this.replaceSelection(key);\n            return;\n        }\n        let mappedKey = this.getKey(key);\n        if (mappedKey == null) return;\n        let keys = new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)(this.state.selectedKeys === 'all' ? this.getSelectAllKeys() : this.state.selectedKeys);\n        if (keys.has(mappedKey)) keys.delete(mappedKey);\n        else if (this.canSelectItem(mappedKey)) {\n            keys.add(mappedKey);\n            keys.anchorKey = mappedKey;\n            keys.currentKey = mappedKey;\n        }\n        if (this.disallowEmptySelection && keys.size === 0) return;\n        this.state.setSelectedKeys(keys);\n    }\n    /**\n   * Replaces the selection with only the given key.\n   */ replaceSelection(key) {\n        if (this.selectionMode === 'none') return;\n        let mappedKey = this.getKey(key);\n        if (mappedKey == null) return;\n        let selection = this.canSelectItem(mappedKey) ? new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)([\n            mappedKey\n        ], mappedKey, mappedKey) : new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)();\n        this.state.setSelectedKeys(selection);\n    }\n    /**\n   * Replaces the selection with the given keys.\n   */ setSelectedKeys(keys) {\n        if (this.selectionMode === 'none') return;\n        let selection = new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)();\n        for (let key of keys){\n            let mappedKey = this.getKey(key);\n            if (mappedKey != null) {\n                selection.add(mappedKey);\n                if (this.selectionMode === 'single') break;\n            }\n        }\n        this.state.setSelectedKeys(selection);\n    }\n    getSelectAllKeys() {\n        let keys = [];\n        let addKeys = (key)=>{\n            while(key != null){\n                if (this.canSelectItem(key)) {\n                    var _getFirstItem;\n                    let item = this.collection.getItem(key);\n                    if ((item === null || item === void 0 ? void 0 : item.type) === 'item') keys.push(key);\n                    var _getFirstItem_key;\n                    // Add child keys. If cell selection is allowed, then include item children too.\n                    if ((item === null || item === void 0 ? void 0 : item.hasChildNodes) && (this.allowsCellSelection || item.type !== 'item')) addKeys((_getFirstItem_key = (_getFirstItem = (0, _react_stately_collections__WEBPACK_IMPORTED_MODULE_0__.getFirstItem)((0, _react_stately_collections__WEBPACK_IMPORTED_MODULE_0__.getChildNodes)(item, this.collection))) === null || _getFirstItem === void 0 ? void 0 : _getFirstItem.key) !== null && _getFirstItem_key !== void 0 ? _getFirstItem_key : null);\n                }\n                key = this.collection.getKeyAfter(key);\n            }\n        };\n        addKeys(this.collection.getFirstKey());\n        return keys;\n    }\n    /**\n   * Selects all items in the collection.\n   */ selectAll() {\n        if (!this.isSelectAll && this.selectionMode === 'multiple') this.state.setSelectedKeys('all');\n    }\n    /**\n   * Removes all keys from the selection.\n   */ clearSelection() {\n        if (!this.disallowEmptySelection && (this.state.selectedKeys === 'all' || this.state.selectedKeys.size > 0)) this.state.setSelectedKeys(new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)());\n    }\n    /**\n   * Toggles between select all and an empty selection.\n   */ toggleSelectAll() {\n        if (this.isSelectAll) this.clearSelection();\n        else this.selectAll();\n    }\n    select(key, e) {\n        if (this.selectionMode === 'none') return;\n        if (this.selectionMode === 'single') {\n            if (this.isSelected(key) && !this.disallowEmptySelection) this.toggleSelection(key);\n            else this.replaceSelection(key);\n        } else if (this.selectionBehavior === 'toggle' || e && (e.pointerType === 'touch' || e.pointerType === 'virtual')) // if touch or virtual (VO) then we just want to toggle, otherwise it's impossible to multi select because they don't have modifier keys\n        this.toggleSelection(key);\n        else this.replaceSelection(key);\n    }\n    /**\n   * Returns whether the current selection is equal to the given selection.\n   */ isSelectionEqual(selection) {\n        if (selection === this.state.selectedKeys) return true;\n        // Check if the set of keys match.\n        let selectedKeys = this.selectedKeys;\n        if (selection.size !== selectedKeys.size) return false;\n        for (let key of selection){\n            if (!selectedKeys.has(key)) return false;\n        }\n        for (let key of selectedKeys){\n            if (!selection.has(key)) return false;\n        }\n        return true;\n    }\n    canSelectItem(key) {\n        var _item_props;\n        if (this.state.selectionMode === 'none' || this.state.disabledKeys.has(key)) return false;\n        let item = this.collection.getItem(key);\n        if (!item || (item === null || item === void 0 ? void 0 : (_item_props = item.props) === null || _item_props === void 0 ? void 0 : _item_props.isDisabled) || item.type === 'cell' && !this.allowsCellSelection) return false;\n        return true;\n    }\n    isDisabled(key) {\n        var _this_collection_getItem_props, _this_collection_getItem;\n        return this.state.disabledBehavior === 'all' && (this.state.disabledKeys.has(key) || !!((_this_collection_getItem = this.collection.getItem(key)) === null || _this_collection_getItem === void 0 ? void 0 : (_this_collection_getItem_props = _this_collection_getItem.props) === null || _this_collection_getItem_props === void 0 ? void 0 : _this_collection_getItem_props.isDisabled));\n    }\n    isLink(key) {\n        var _this_collection_getItem_props, _this_collection_getItem;\n        return !!((_this_collection_getItem = this.collection.getItem(key)) === null || _this_collection_getItem === void 0 ? void 0 : (_this_collection_getItem_props = _this_collection_getItem.props) === null || _this_collection_getItem_props === void 0 ? void 0 : _this_collection_getItem_props.href);\n    }\n    getItemProps(key) {\n        var _this_collection_getItem;\n        return (_this_collection_getItem = this.collection.getItem(key)) === null || _this_collection_getItem === void 0 ? void 0 : _this_collection_getItem.props;\n    }\n    withCollection(collection) {\n        return new $d496c0a20b6e58ec$export$6c8a5aaad13c9852(collection, this.state, {\n            allowsCellSelection: this.allowsCellSelection,\n            layoutDelegate: this.layoutDelegate || undefined\n        });\n    }\n    constructor(collection, state, options){\n        this.collection = collection;\n        this.state = state;\n        var _options_allowsCellSelection;\n        this.allowsCellSelection = (_options_allowsCellSelection = options === null || options === void 0 ? void 0 : options.allowsCellSelection) !== null && _options_allowsCellSelection !== void 0 ? _options_allowsCellSelection : false;\n        this._isSelectAll = null;\n        this.layoutDelegate = (options === null || options === void 0 ? void 0 : options.layoutDelegate) || null;\n    }\n}\n\n\n\n//# sourceMappingURL=SelectionManager.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+selection@3._e0d2f62db6af26827f64a1ae4b751db0/node_modules/@react-stately/selection/dist/SelectionManager.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@react-stately+selection@3._e0d2f62db6af26827f64a1ae4b751db0/node_modules/@react-stately/selection/dist/useMultipleSelectionState.mjs":
/*!******************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@react-stately+selection@3._e0d2f62db6af26827f64a1ae4b751db0/node_modules/@react-stately/selection/dist/useMultipleSelectionState.mjs ***!
  \******************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMultipleSelectionState: () => (/* binding */ $7af3f5b51489e0b5$export$253fe78d46329472)\n/* harmony export */ });\n/* harmony import */ var _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Selection.mjs */ \"(ssr)/./node_modules/.pnpm/@react-stately+selection@3._e0d2f62db6af26827f64a1ae4b751db0/node_modules/@react-stately/selection/dist/Selection.mjs\");\n/* harmony import */ var _react_stately_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-stately/utils */ \"(ssr)/./node_modules/.pnpm/@react-stately+utils@3.10.5_643de144daf18c30f9f7e3eb5c877f70/node_modules/@react-stately/utils/dist/useControlledState.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n\n/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */ \n\n\nfunction $7af3f5b51489e0b5$var$equalSets(setA, setB) {\n    if (setA.size !== setB.size) return false;\n    for (let item of setA){\n        if (!setB.has(item)) return false;\n    }\n    return true;\n}\nfunction $7af3f5b51489e0b5$export$253fe78d46329472(props) {\n    let { selectionMode: selectionMode = 'none', disallowEmptySelection: disallowEmptySelection = false, allowDuplicateSelectionEvents: allowDuplicateSelectionEvents, selectionBehavior: selectionBehaviorProp = 'toggle', disabledBehavior: disabledBehavior = 'all' } = props;\n    // We want synchronous updates to `isFocused` and `focusedKey` after their setters are called.\n    // But we also need to trigger a react re-render. So, we have both a ref (sync) and state (async).\n    let isFocusedRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    let [, setFocused] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    let focusedKeyRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    let childFocusStrategyRef = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    let [, setFocusedKey] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    let selectedKeysProp = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>$7af3f5b51489e0b5$var$convertSelection(props.selectedKeys), [\n        props.selectedKeys\n    ]);\n    let defaultSelectedKeys = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>$7af3f5b51489e0b5$var$convertSelection(props.defaultSelectedKeys, new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)()), [\n        props.defaultSelectedKeys\n    ]);\n    let [selectedKeys, setSelectedKeys] = (0, _react_stately_utils__WEBPACK_IMPORTED_MODULE_2__.useControlledState)(selectedKeysProp, defaultSelectedKeys, props.onSelectionChange);\n    let disabledKeysProp = (0, react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>props.disabledKeys ? new Set(props.disabledKeys) : new Set(), [\n        props.disabledKeys\n    ]);\n    let [selectionBehavior, setSelectionBehavior] = (0, react__WEBPACK_IMPORTED_MODULE_0__.useState)(selectionBehaviorProp);\n    // If the selectionBehavior prop is set to replace, but the current state is toggle (e.g. due to long press\n    // to enter selection mode on touch), and the selection becomes empty, reset the selection behavior.\n    if (selectionBehaviorProp === 'replace' && selectionBehavior === 'toggle' && typeof selectedKeys === 'object' && selectedKeys.size === 0) setSelectionBehavior('replace');\n    // If the selectionBehavior prop changes, update the state as well.\n    let lastSelectionBehavior = (0, react__WEBPACK_IMPORTED_MODULE_0__.useRef)(selectionBehaviorProp);\n    (0, react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (selectionBehaviorProp !== lastSelectionBehavior.current) {\n            setSelectionBehavior(selectionBehaviorProp);\n            lastSelectionBehavior.current = selectionBehaviorProp;\n        }\n    }, [\n        selectionBehaviorProp\n    ]);\n    return {\n        selectionMode: selectionMode,\n        disallowEmptySelection: disallowEmptySelection,\n        selectionBehavior: selectionBehavior,\n        setSelectionBehavior: setSelectionBehavior,\n        get isFocused () {\n            return isFocusedRef.current;\n        },\n        setFocused (f) {\n            isFocusedRef.current = f;\n            setFocused(f);\n        },\n        get focusedKey () {\n            return focusedKeyRef.current;\n        },\n        get childFocusStrategy () {\n            return childFocusStrategyRef.current;\n        },\n        setFocusedKey (k, childFocusStrategy = 'first') {\n            focusedKeyRef.current = k;\n            childFocusStrategyRef.current = childFocusStrategy;\n            setFocusedKey(k);\n        },\n        selectedKeys: selectedKeys,\n        setSelectedKeys (keys) {\n            if (allowDuplicateSelectionEvents || !$7af3f5b51489e0b5$var$equalSets(keys, selectedKeys)) setSelectedKeys(keys);\n        },\n        disabledKeys: disabledKeysProp,\n        disabledBehavior: disabledBehavior\n    };\n}\nfunction $7af3f5b51489e0b5$var$convertSelection(selection, defaultValue) {\n    if (!selection) return defaultValue;\n    return selection === 'all' ? 'all' : new (0, _Selection_mjs__WEBPACK_IMPORTED_MODULE_1__.Selection)(selection);\n}\n\n\n\n//# sourceMappingURL=useMultipleSelectionState.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@react-stately+selection@3._e0d2f62db6af26827f64a1ae4b751db0/node_modules/@react-stately/selection/dist/useMultipleSelectionState.mjs\n");

/***/ })

};
;