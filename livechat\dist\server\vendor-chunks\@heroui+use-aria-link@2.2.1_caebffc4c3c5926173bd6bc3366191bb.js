"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@heroui+use-aria-link@2.2.1_caebffc4c3c5926173bd6bc3366191bb";
exports.ids = ["vendor-chunks/@heroui+use-aria-link@2.2.1_caebffc4c3c5926173bd6bc3366191bb"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@heroui+use-aria-link@2.2.1_caebffc4c3c5926173bd6bc3366191bb/node_modules/@heroui/use-aria-link/dist/index.mjs":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@heroui+use-aria-link@2.2.1_caebffc4c3c5926173bd6bc3366191bb/node_modules/@heroui/use-aria-link/dist/index.mjs ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAriaLink: () => (/* binding */ useAriaLink)\n/* harmony export */ });\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/platform.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/filterDOMProps.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/mergeProps.mjs\");\n/* harmony import */ var _react_aria_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @react-aria/utils */ \"(ssr)/./node_modules/.pnpm/@react-aria+utils@3.28.0_re_80b7635157877d4f16d8af24e070025d/node_modules/@react-aria/utils/dist/openLink.mjs\");\n/* harmony import */ var _heroui_shared_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @heroui/shared-utils */ \"(ssr)/./node_modules/.pnpm/@heroui+shared-utils@2.1.7/node_modules/@heroui/shared-utils/dist/index.mjs\");\n/* harmony import */ var _react_aria_focus__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @react-aria/focus */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f4c4e47fdd38dbbc2317f9013c56c4ca/node_modules/@react-aria/interactions/dist/useFocusable.mjs\");\n/* harmony import */ var _react_aria_interactions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @react-aria/interactions */ \"(ssr)/./node_modules/.pnpm/@react-aria+interactions@3._f4c4e47fdd38dbbc2317f9013c56c4ca/node_modules/@react-aria/interactions/dist/usePress.mjs\");\n// src/index.ts\n\n\n\n\nfunction useAriaLink(props, ref) {\n  let {\n    elementType = \"a\",\n    onPress,\n    onPressStart,\n    onPressEnd,\n    // @ts-ignore\n    onClick: deprecatedOnClick,\n    role,\n    isDisabled,\n    type,\n    ...otherProps\n  } = props;\n  let linkProps = {};\n  if (elementType !== \"a\") {\n    linkProps = {\n      role: \"link\",\n      tabIndex: !isDisabled ? 0 : void 0\n    };\n  }\n  let isMobile = (0,_react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.isIOS)() || (0,_react_aria_utils__WEBPACK_IMPORTED_MODULE_0__.isAndroid)();\n  if (deprecatedOnClick && typeof deprecatedOnClick === \"function\" && // bypass since onClick is passed from <Link as=\"button\" /> internally\n  type !== \"button\" && // bypass since onClick is passed from <Button as={Link} /> internally\n  role !== \"button\") {\n    (0,_heroui_shared_utils__WEBPACK_IMPORTED_MODULE_1__.warn)(\n      \"onClick is deprecated, please use onPress instead. See: https://github.com/heroui-inc/heroui/issues/4292\",\n      \"useLink\"\n    );\n  }\n  const handlePress = (e) => {\n    if (isMobile) {\n      deprecatedOnClick == null ? void 0 : deprecatedOnClick(e);\n    }\n    onPress == null ? void 0 : onPress(e);\n  };\n  let { focusableProps } = (0,_react_aria_focus__WEBPACK_IMPORTED_MODULE_2__.useFocusable)(props, ref);\n  let { pressProps, isPressed } = (0,_react_aria_interactions__WEBPACK_IMPORTED_MODULE_3__.usePress)({\n    onPress: handlePress,\n    onPressStart,\n    onPressEnd,\n    isDisabled,\n    ref\n  });\n  let domProps = (0,_react_aria_utils__WEBPACK_IMPORTED_MODULE_4__.filterDOMProps)(otherProps, { labelable: true, isLink: elementType === \"a\" });\n  let interactionHandlers = (0,_react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.mergeProps)(focusableProps, pressProps);\n  let router = (0,_react_aria_utils__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n  let routerLinkProps = (0,_react_aria_utils__WEBPACK_IMPORTED_MODULE_6__.useLinkProps)(props);\n  return {\n    isPressed,\n    // Used to indicate press state for visual\n    linkProps: (0,_react_aria_utils__WEBPACK_IMPORTED_MODULE_5__.mergeProps)(domProps, routerLinkProps, {\n      ...interactionHandlers,\n      ...linkProps,\n      \"aria-disabled\": isDisabled || void 0,\n      \"aria-current\": props[\"aria-current\"],\n      onClick: (e) => {\n        var _a;\n        (_a = pressProps.onClick) == null ? void 0 : _a.call(pressProps, e);\n        if (!isMobile && deprecatedOnClick) {\n          deprecatedOnClick(e);\n        }\n        if (!router.isNative && e.currentTarget instanceof HTMLAnchorElement && e.currentTarget.href && // If props are applied to a router Link component, it may have already prevented default.\n        !e.isDefaultPrevented() && (0,_react_aria_utils__WEBPACK_IMPORTED_MODULE_6__.shouldClientNavigate)(e.currentTarget, e) && props.href) {\n          e.preventDefault();\n          router.open(e.currentTarget, e, props.href, props.routerOptions);\n        }\n      }\n    })\n  };\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@heroui+use-aria-link@2.2.1_caebffc4c3c5926173bd6bc3366191bb/node_modules/@heroui/use-aria-link/dist/index.mjs\n");

/***/ })

};
;