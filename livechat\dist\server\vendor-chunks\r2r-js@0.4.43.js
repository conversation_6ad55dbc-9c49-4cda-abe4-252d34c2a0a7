"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/r2r-js@0.4.43";
exports.ids = ["vendor-chunks/r2r-js@0.4.43"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/baseClient.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/baseClient.js ***!
  \*********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.BaseClient = void 0;\nconst axios_1 = __importDefault(__webpack_require__(/*! axios */ \"(ssr)/./node_modules/.pnpm/axios@1.10.0/node_modules/axios/dist/node/axios.cjs\"));\nconst form_data_1 = __importDefault(__webpack_require__(/*! form-data */ \"(ssr)/./node_modules/.pnpm/form-data@4.0.4/node_modules/form-data/lib/form_data.js\"));\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/index.js\");\nlet fs;\nif (typeof window === \"undefined\") {\n    fs = __webpack_require__(/*! fs */ \"fs\");\n}\nfunction handleRequestError(response) {\n    if (response.status < 400) {\n        return;\n    }\n    let message;\n    const errorContent = (0, utils_1.ensureCamelCase)(response.data);\n    if (typeof errorContent === \"object\" && errorContent !== null) {\n        message =\n            errorContent.message ||\n                (errorContent.detail && errorContent.detail.message) ||\n                (typeof errorContent.detail === \"string\" && errorContent.detail) ||\n                JSON.stringify(errorContent);\n    }\n    else {\n        message = String(errorContent);\n    }\n    throw new Error(`Status ${response.status}: ${message}`);\n}\nclass BaseClient {\n    constructor(baseURL = \"http://localhost:7272\", prefix = \"\", anonymousTelemetry = true, enableAutoRefresh = false) {\n        this.baseUrl = `${baseURL}${prefix}`;\n        this.accessToken = null;\n        this.apiKey = process.env.R2R_API_KEY || null;\n        this.projectName = null;\n        this.refreshToken = null;\n        this.anonymousTelemetry = anonymousTelemetry;\n        this.enableAutoRefresh = enableAutoRefresh;\n        this.axiosInstance = axios_1.default.create({\n            baseURL: this.baseUrl,\n            headers: {\n                \"Content-Type\": \"application/json\",\n            },\n        });\n    }\n    async _makeRequest(method, endpoint, options = {}, version = \"v3\") {\n        const url = `/${version}/${endpoint}`;\n        const config = Object.assign(Object.assign({ method,\n            url, headers: Object.assign({}, options.headers), params: options.params }, options), { responseType: options.responseType || \"json\" });\n        config.headers = config.headers || {};\n        if (options.params) {\n            config.paramsSerializer = (params) => {\n                return Object.entries(params)\n                    .map(([key, value]) => {\n                    if (Array.isArray(value)) {\n                        return value\n                            .map((v) => `${encodeURIComponent(key)}=${encodeURIComponent(v)}`)\n                            .join(\"&\");\n                    }\n                    return `${encodeURIComponent(key)}=${encodeURIComponent(String(value))}`;\n                })\n                    .join(\"&\");\n            };\n        }\n        if (options.data) {\n            if (typeof form_data_1.default !== \"undefined\" && options.data instanceof form_data_1.default) {\n                config.data = options.data;\n                delete config.headers[\"Content-Type\"];\n            }\n            else if (typeof options.data === \"object\") {\n                if (config.headers[\"Content-Type\"] === \"application/x-www-form-urlencoded\") {\n                    config.data = Object.keys(options.data)\n                        .map((key) => `${encodeURIComponent(key)}=${encodeURIComponent(options.data[key])}`)\n                        .join(\"&\");\n                }\n                else {\n                    config.data = JSON.stringify(options.data);\n                    if (method !== \"DELETE\") {\n                        config.headers[\"Content-Type\"] = \"application/json\";\n                    }\n                    else {\n                        config.headers[\"Content-Type\"] = \"application/json\";\n                        config.data = JSON.stringify(options.data);\n                    }\n                }\n            }\n            else {\n                config.data = options.data;\n            }\n        }\n        if (this.accessToken && this.apiKey) {\n            throw new Error(\"Cannot have both access token and api key.\");\n        }\n        if (this.apiKey &&\n            ![\"register\", \"login\", \"verify_email\", \"health\"].includes(endpoint)) {\n            config.headers[\"x-api-key\"] = this.apiKey;\n        }\n        else if (this.accessToken &&\n            ![\"register\", \"login\", \"verify_email\", \"health\"].includes(endpoint)) {\n            config.headers.Authorization = `Bearer ${this.accessToken}`;\n        }\n        if (this.projectName) {\n            config.headers[\"x-project-name\"] = this.projectName;\n        }\n        if (options.responseType === \"stream\") {\n            return this.handleStreamingRequest(method, version, endpoint, config);\n        }\n        try {\n            const response = await this.axiosInstance.request(config);\n            if (options.responseType === \"blob\") {\n                return response.data;\n            }\n            else if (options.responseType === \"arraybuffer\") {\n                if (options.returnFullResponse) {\n                    return response;\n                }\n                return response.data;\n            }\n            const responseData = options.returnFullResponse\n                ? Object.assign(Object.assign({}, response), { data: (0, utils_1.ensureCamelCase)(response.data) }) : (0, utils_1.ensureCamelCase)(response.data);\n            return responseData;\n        }\n        catch (error) {\n            if (axios_1.default.isAxiosError(error) && error.response) {\n                handleRequestError(error.response);\n            }\n            throw error;\n        }\n    }\n    async handleStreamingRequest(method, version, endpoint, config) {\n        var _a;\n        const fetchHeaders = {};\n        // Convert Axios headers to Fetch headers\n        Object.entries(config.headers || {}).forEach(([key, value]) => {\n            if (typeof value === \"string\") {\n                fetchHeaders[key] = value;\n            }\n        });\n        try {\n            const response = await fetch(`${this.baseUrl}/${version}/${endpoint}`, {\n                method,\n                headers: fetchHeaders,\n                body: config.data,\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(() => ({}));\n                throw new Error(`HTTP error! status: ${response.status}: ${(0, utils_1.ensureCamelCase)(errorData).message || \"Unknown error\"}`);\n            }\n            // Create a TransformStream to process the response\n            const transformStream = new TransformStream({\n                transform(chunk, controller) {\n                    // Process each chunk here if needed\n                    controller.enqueue(chunk);\n                },\n            });\n            // Pipe the response through the transform stream\n            const streamedResponse = (_a = response.body) === null || _a === void 0 ? void 0 : _a.pipeThrough(transformStream);\n            if (!streamedResponse) {\n                throw new Error(\"No response body received from stream\");\n            }\n            return streamedResponse;\n        }\n        catch (error) {\n            console.error(\"Streaming request failed:\", error);\n            throw error;\n        }\n    }\n    _ensureAuthenticated() {\n        if (!this.accessToken) {\n            throw new Error(\"Not authenticated. Please login first.\");\n        }\n    }\n    setTokens(accessToken, refreshToken) {\n        this.accessToken = accessToken;\n        this.refreshToken = refreshToken;\n    }\n    setApiKey(apiKey) {\n        if (!apiKey) {\n            throw new Error(\"API key is required\");\n        }\n        this.apiKey = apiKey;\n    }\n    setProjectName(projectName) {\n        if (!projectName) {\n            throw new Error(\"Project name is required\");\n        }\n        this.projectName = projectName;\n    }\n    unsetProjectName() {\n        this.projectName = null;\n    }\n}\nexports.BaseClient = BaseClient;\n//# sourceMappingURL=baseClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/baseClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/index.js":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/index.js ***!
  \****************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.r2rClient = void 0;\nvar r2rClient_1 = __webpack_require__(/*! ./r2rClient */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/r2rClient.js\");\nObject.defineProperty(exports, \"r2rClient\", ({ enumerable: true, get: function () { return r2rClient_1.r2rClient; } }));\n__exportStar(__webpack_require__(/*! ./types */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/types.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/r2rClient.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/r2rClient.js ***!
  \********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.r2rClient = void 0;\nconst axios_1 = __importDefault(__webpack_require__(/*! axios */ \"(ssr)/./node_modules/.pnpm/axios@1.10.0/node_modules/axios/dist/node/axios.cjs\"));\nconst baseClient_1 = __webpack_require__(/*! ./baseClient */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/baseClient.js\");\nconst chunks_1 = __webpack_require__(/*! ./v3/clients/chunks */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/chunks.js\");\nconst collections_1 = __webpack_require__(/*! ./v3/clients/collections */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/collections.js\");\nconst conversations_1 = __webpack_require__(/*! ./v3/clients/conversations */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/conversations.js\");\nconst documents_1 = __webpack_require__(/*! ./v3/clients/documents */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/documents.js\");\nconst graphs_1 = __webpack_require__(/*! ./v3/clients/graphs */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/graphs.js\");\nconst indices_1 = __webpack_require__(/*! ./v3/clients/indices */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/indices.js\");\nconst prompts_1 = __webpack_require__(/*! ./v3/clients/prompts */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/prompts.js\");\nconst retrieval_1 = __webpack_require__(/*! ./v3/clients/retrieval */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/retrieval.js\");\nconst system_1 = __webpack_require__(/*! ./v3/clients/system */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/system.js\");\nconst users_1 = __webpack_require__(/*! ./v3/clients/users */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/users.js\");\nlet fs;\nif (typeof window === \"undefined\") {\n    fs = __webpack_require__(/*! fs */ \"fs\");\n}\nclass r2rClient extends baseClient_1.BaseClient {\n    constructor(baseURL, anonymousTelemetry = true, options = {}) {\n        super(baseURL, \"\", anonymousTelemetry, options.enableAutoRefresh);\n        this.chunks = new chunks_1.ChunksClient(this);\n        this.collections = new collections_1.CollectionsClient(this);\n        this.conversations = new conversations_1.ConversationsClient(this);\n        this.documents = new documents_1.DocumentsClient(this);\n        this.graphs = new graphs_1.GraphsClient(this);\n        this.indices = new indices_1.IndiciesClient(this);\n        this.prompts = new prompts_1.PromptsClient(this);\n        this.retrieval = new retrieval_1.RetrievalClient(this);\n        this.system = new system_1.SystemClient(this);\n        this.users = new users_1.UsersClient(this);\n        this.axiosInstance = axios_1.default.create({\n            baseURL: this.baseUrl,\n            headers: {\n                \"Content-Type\": \"application/json\",\n            },\n        });\n        this.getTokensCallback = options.getTokensCallback;\n        this.setTokensCallback = options.setTokensCallback;\n        this.onRefreshFailedCallback = options.onRefreshFailedCallback;\n        // 1) Request interceptor: attach current access token (if any)\n        this.axiosInstance.interceptors.request.use((config) => {\n            var _a;\n            const tokenData = (_a = this.getTokensCallback) === null || _a === void 0 ? void 0 : _a.call(this);\n            const accessToken = (tokenData === null || tokenData === void 0 ? void 0 : tokenData.accessToken) || null;\n            if (accessToken) {\n                config.headers[\"Authorization\"] = `Bearer ${accessToken}`;\n            }\n            return config;\n        }, (error) => {\n            console.error(\"[r2rClient] Request interceptor error:\", error);\n            return Promise.reject(error);\n        });\n        // 2) Response interceptor: see if we got 401/403 => attempt to refresh\n        this.setupResponseInterceptor();\n    }\n    setupResponseInterceptor() {\n        this.axiosInstance.interceptors.response.use((response) => response, async (error) => {\n            var _a, _b, _c, _d, _e, _f;\n            const status = (_a = error.response) === null || _a === void 0 ? void 0 : _a.status;\n            const failingUrl = (_b = error.config) === null || _b === void 0 ? void 0 : _b.url;\n            const errorData = (_c = error.response) === null || _c === void 0 ? void 0 : _c.data;\n            // 1) If the refresh endpoint itself fails => don't try again\n            if (failingUrl === null || failingUrl === void 0 ? void 0 : failingUrl.includes(\"/v3/users/refresh-token\")) {\n                console.error(\"[r2rClient] Refresh call itself returned 401/403 => logging out\");\n                (_d = this.onRefreshFailedCallback) === null || _d === void 0 ? void 0 : _d.call(this);\n                return Promise.reject(error);\n            }\n            // 2) If normal request => attempt refresh IF it's really an invalid/expired token\n            // We'll check either an explicit \"error_code\" or text in \"message\"\n            // Adjust to match your server's structure!\n            const isTokenError = !!(errorData === null || errorData === void 0 ? void 0 : errorData.error_code) &&\n                errorData.error_code.toUpperCase() === \"TOKEN_EXPIRED\";\n            // Or fallback to matching common phrases if no error_code is set:\n            const msg = ((errorData === null || errorData === void 0 ? void 0 : errorData.message) || \"\").toLowerCase();\n            const looksLikeTokenIssue = msg.includes(\"invalid token\") ||\n                msg.includes(\"token expired\") ||\n                msg.includes(\"credentials\");\n            // If either of those checks is true, we consider it an auth token error:\n            const isAuthError = isTokenError || looksLikeTokenIssue;\n            if ((status === 401 || status === 403) &&\n                this.getTokensCallback &&\n                isAuthError) {\n                // Check if we have a refresh token\n                const { refreshToken } = this.getTokensCallback();\n                if (!refreshToken) {\n                    console.error(\"[r2rClient] No refresh token found => logout\");\n                    (_e = this.onRefreshFailedCallback) === null || _e === void 0 ? void 0 : _e.call(this);\n                    return Promise.reject(error);\n                }\n                // Attempt refresh\n                try {\n                    const refreshResponse = await this.users.refreshAccessToken();\n                    const newAccessToken = refreshResponse.results.accessToken.token;\n                    const newRefreshToken = refreshResponse.results.refreshToken.token;\n                    // set new tokens\n                    this.setTokens(newAccessToken, newRefreshToken);\n                    // Re-try the original request\n                    if (error.config) {\n                        error.config.headers[\"Authorization\"] =\n                            `Bearer ${newAccessToken}`;\n                        return this.axiosInstance.request(error.config);\n                    }\n                    else {\n                        console.warn(\"[r2rClient] No request config found to retry. Possibly manual re-fetch needed\");\n                    }\n                }\n                catch (refreshError) {\n                    console.error(\"[r2rClient] Refresh attempt failed => logging out. Error was:\", refreshError);\n                    (_f = this.onRefreshFailedCallback) === null || _f === void 0 ? void 0 : _f.call(this);\n                    return Promise.reject(refreshError);\n                }\n            }\n            // 3) If not a 401/403 or it's a 401/403 that isn't token-related => just reject\n            return Promise.reject(error);\n        });\n    }\n    makeRequest(method, endpoint, options = {}) {\n        return this._makeRequest(method, endpoint, options, \"v3\");\n    }\n    getRefreshToken() {\n        return this.refreshToken;\n    }\n    setTokens(accessToken, refreshToken) {\n        var _a;\n        super.setTokens(accessToken || \"\", refreshToken || \"\");\n        (_a = this.setTokensCallback) === null || _a === void 0 ? void 0 : _a.call(this, accessToken, refreshToken);\n    }\n}\nexports.r2rClient = r2rClient;\nexports[\"default\"] = r2rClient;\n//# sourceMappingURL=r2rClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/r2rClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/types.js":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/types.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.IndexMeasure = void 0;\n// Index types\nvar IndexMeasure;\n(function (IndexMeasure) {\n    IndexMeasure[\"COSINE_DISTANCE\"] = \"cosine_distance\";\n    IndexMeasure[\"L2_DISTANCE\"] = \"l2_distance\";\n    IndexMeasure[\"MAX_INNER_PRODUCT\"] = \"max_inner_product\";\n})(IndexMeasure || (exports.IndexMeasure = IndexMeasure = {}));\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcjJyLWpzQDAuNC40My9ub2RlX21vZHVsZXMvcjJyLWpzL2Rpc3QvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMsbUJBQW1CLG9CQUFvQixvQkFBb0I7QUFDNUQiLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHIyci1qc0AwLjQuNDNcXG5vZGVfbW9kdWxlc1xccjJyLWpzXFxkaXN0XFx0eXBlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuSW5kZXhNZWFzdXJlID0gdm9pZCAwO1xuLy8gSW5kZXggdHlwZXNcbnZhciBJbmRleE1lYXN1cmU7XG4oZnVuY3Rpb24gKEluZGV4TWVhc3VyZSkge1xuICAgIEluZGV4TWVhc3VyZVtcIkNPU0lORV9ESVNUQU5DRVwiXSA9IFwiY29zaW5lX2Rpc3RhbmNlXCI7XG4gICAgSW5kZXhNZWFzdXJlW1wiTDJfRElTVEFOQ0VcIl0gPSBcImwyX2Rpc3RhbmNlXCI7XG4gICAgSW5kZXhNZWFzdXJlW1wiTUFYX0lOTkVSX1BST0RVQ1RcIl0gPSBcIm1heF9pbm5lcl9wcm9kdWN0XCI7XG59KShJbmRleE1lYXN1cmUgfHwgKGV4cG9ydHMuSW5kZXhNZWFzdXJlID0gSW5kZXhNZWFzdXJlID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/index.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/index.js ***!
  \**********************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n__exportStar(__webpack_require__(/*! ./typeTransformer */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/typeTransformer.js\"), exports);\n__exportStar(__webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/utils.js\"), exports);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcjJyLWpzQDAuNC40My9ub2RlX21vZHVsZXMvcjJyLWpzL2Rpc3QvdXRpbHMvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsb0NBQW9DO0FBQ25EO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsYUFBYSxtQkFBTyxDQUFDLHFIQUFtQjtBQUN4QyxhQUFhLG1CQUFPLENBQUMsaUdBQVM7QUFDOUIiLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHIyci1qc0AwLjQuNDNcXG5vZGVfbW9kdWxlc1xccjJyLWpzXFxkaXN0XFx1dGlsc1xcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19jcmVhdGVCaW5kaW5nID0gKHRoaXMgJiYgdGhpcy5fX2NyZWF0ZUJpbmRpbmcpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICB2YXIgZGVzYyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IobSwgayk7XG4gICAgaWYgKCFkZXNjIHx8IChcImdldFwiIGluIGRlc2MgPyAhbS5fX2VzTW9kdWxlIDogZGVzYy53cml0YWJsZSB8fCBkZXNjLmNvbmZpZ3VyYWJsZSkpIHtcbiAgICAgIGRlc2MgPSB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24oKSB7IHJldHVybiBtW2tdOyB9IH07XG4gICAgfVxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgZGVzYyk7XG59KSA6IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgb1trMl0gPSBtW2tdO1xufSkpO1xudmFyIF9fZXhwb3J0U3RhciA9ICh0aGlzICYmIHRoaXMuX19leHBvcnRTdGFyKSB8fCBmdW5jdGlvbihtLCBleHBvcnRzKSB7XG4gICAgZm9yICh2YXIgcCBpbiBtKSBpZiAocCAhPT0gXCJkZWZhdWx0XCIgJiYgIU9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChleHBvcnRzLCBwKSkgX19jcmVhdGVCaW5kaW5nKGV4cG9ydHMsIG0sIHApO1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbl9fZXhwb3J0U3RhcihyZXF1aXJlKFwiLi90eXBlVHJhbnNmb3JtZXJcIiksIGV4cG9ydHMpO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL3V0aWxzXCIpLCBleHBvcnRzKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/typeTransformer.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/typeTransformer.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ensureCamelCase = ensureCamelCase;\nexports.ensureSnakeCase = ensureSnakeCase;\nconst isObject = (value) => typeof value === \"object\" &&\n    value !== null &&\n    !Array.isArray(value) &&\n    !(value instanceof Date) &&\n    !(value instanceof Map) &&\n    !(value instanceof Set) &&\n    !(value instanceof Error) &&\n    !(value instanceof RegExp);\nconst isValidInput = (value) => value !== null && value !== undefined;\nconst convertToCamelCase = (str) => {\n    // Preserve leading underscores\n    const matches = str.match(/^(_+)/);\n    const leadingUnderscores = matches ? matches[1] : \"\";\n    const withoutLeadingUnderscores = str.slice(leadingUnderscores.length);\n    if (!withoutLeadingUnderscores) {\n        return str;\n    }\n    // Split by underscore and capitalize\n    const converted = withoutLeadingUnderscores\n        .split(\"_\")\n        .map((word, index) => {\n        if (index === 0) {\n            return word.toLowerCase();\n        }\n        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();\n    })\n        .join(\"\");\n    return leadingUnderscores + converted;\n};\nconst convertToSnakeCase = (str) => {\n    // Preserve leading underscores\n    const matches = str.match(/^(_+)/);\n    const leadingUnderscores = matches ? matches[1] : \"\";\n    const withoutLeadingUnderscores = str.slice(leadingUnderscores.length);\n    if (!withoutLeadingUnderscores) {\n        return str;\n    }\n    // Handle acronyms and regular camelCase\n    const withAcronyms = withoutLeadingUnderscores\n        .replace(/([A-Z]+)([A-Z][a-z])/g, \"$1_$2\")\n        .replace(/([a-z\\d])([A-Z])/g, \"$1_$2\")\n        .toLowerCase();\n    return leadingUnderscores + withAcronyms;\n};\nfunction ensureCamelCase(input) {\n    if (!isValidInput(input)) {\n        return input;\n    }\n    if (Array.isArray(input)) {\n        return input.map((item) => ensureCamelCase(item));\n    }\n    if (!isObject(input)) {\n        return input;\n    }\n    try {\n        const result = {};\n        // Handle all properties including symbols\n        const allKeys = [\n            ...Object.getOwnPropertyNames(input),\n            ...Object.getOwnPropertySymbols(input),\n        ];\n        for (const key of allKeys) {\n            const descriptor = Object.getOwnPropertyDescriptor(input, key);\n            if (typeof key === \"symbol\") {\n                Object.defineProperty(result, key, descriptor);\n            }\n            else {\n                const newKey = convertToCamelCase(key.toString());\n                const value = input[key];\n                if (isObject(value)) {\n                    // Transform nested object and preserve its symbol properties\n                    const transformed = ensureCamelCase(value);\n                    result[newKey] = transformed;\n                    // Copy all symbol properties from the original nested object\n                    Object.getOwnPropertySymbols(value).forEach((symKey) => {\n                        const symDesc = Object.getOwnPropertyDescriptor(value, symKey);\n                        Object.defineProperty(transformed, symKey, symDesc);\n                    });\n                }\n                else if (Array.isArray(value)) {\n                    result[newKey] = value.map((item) => ensureCamelCase(item));\n                }\n                else {\n                    result[newKey] = value;\n                }\n            }\n        }\n        return result;\n    }\n    catch (error) {\n        throw new Error(`Failed to transform to camelCase: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n    }\n}\nfunction ensureSnakeCase(input) {\n    if (!isValidInput(input)) {\n        return input;\n    }\n    if (Array.isArray(input)) {\n        return input.map((item) => ensureSnakeCase(item));\n    }\n    if (!isObject(input)) {\n        return input;\n    }\n    try {\n        const result = {};\n        const descriptors = Object.getOwnPropertyDescriptors(input);\n        for (const key of [\n            ...Object.getOwnPropertyNames(input),\n            ...Object.getOwnPropertySymbols(input),\n        ]) {\n            const desc = descriptors[key];\n            const { value } = desc;\n            if (typeof key === \"symbol\") {\n                if (isObject(value)) {\n                    const transformed = ensureSnakeCase(value);\n                    Object.defineProperty(result, key, {\n                        enumerable: true,\n                        configurable: true,\n                        writable: true,\n                        value: transformed,\n                    });\n                }\n                else {\n                    result[key] = value;\n                }\n            }\n            else {\n                const newKey = convertToSnakeCase(key.toString());\n                if (isObject(value)) {\n                    const transformed = ensureSnakeCase(value);\n                    result[newKey] = transformed;\n                    // Copy symbol properties\n                    Object.getOwnPropertySymbols(value).forEach((symKey) => {\n                        Object.defineProperty(transformed, symKey, Object.assign(Object.assign({}, Object.getOwnPropertyDescriptor(value, symKey)), { value: value[symKey] }));\n                    });\n                }\n                else if (Array.isArray(value)) {\n                    result[newKey] = value.map((item) => ensureSnakeCase(item));\n                }\n                else {\n                    result[newKey] = value;\n                }\n            }\n        }\n        return result;\n    }\n    catch (error) {\n        throw new Error(`Failed to transform to snake_case: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n    }\n}\n//# sourceMappingURL=typeTransformer.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/typeTransformer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/utils.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/utils.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.downloadBlob = downloadBlob;\nfunction downloadBlob(blob, filename) {\n    const url = window.URL.createObjectURL(blob);\n    const link = document.createElement(\"a\");\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    window.URL.revokeObjectURL(url);\n}\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcjJyLWpzQDAuNC40My9ub2RlX21vZHVsZXMvcjJyLWpzL2Rpc3QvdXRpbHMvdXRpbHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHIyci1qc0AwLjQuNDNcXG5vZGVfbW9kdWxlc1xccjJyLWpzXFxkaXN0XFx1dGlsc1xcdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmRvd25sb2FkQmxvYiA9IGRvd25sb2FkQmxvYjtcbmZ1bmN0aW9uIGRvd25sb2FkQmxvYihibG9iLCBmaWxlbmFtZSkge1xuICAgIGNvbnN0IHVybCA9IHdpbmRvdy5VUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpO1xuICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KFwiYVwiKTtcbiAgICBsaW5rLmhyZWYgPSB1cmw7XG4gICAgbGluay5kb3dubG9hZCA9IGZpbGVuYW1lO1xuICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluayk7XG4gICAgbGluay5jbGljaygpO1xuICAgIGRvY3VtZW50LmJvZHkucmVtb3ZlQ2hpbGQobGluayk7XG4gICAgd2luZG93LlVSTC5yZXZva2VPYmplY3RVUkwodXJsKTtcbn1cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXV0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/chunks.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/chunks.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ChunksClient = void 0;\nconst utils_1 = __webpack_require__(/*! ../../utils */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/index.js\");\nclass ChunksClient {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Create multiple chunks.\n     * @param chunks List of UnprocessedChunk objects containing:\n                - id: Optional UUID\n                - document_id: Optional UUID\n                - collection_ids: list UUID\n                - metadata: dict\n                - text: string\n      * @param runWithOrchestration Optional flag to run with orchestration\n      * @returns\n      */\n    async create(options) {\n        return this.client.makeRequest(\"POST\", \"chunks\", {\n            data: {\n                raw_chunks: (0, utils_1.ensureSnakeCase)(options.chunks),\n                runWithOrchestration: options.runWithOrchestration,\n            },\n        });\n    }\n    /**\n     * Update an existing chunk.\n     * @param id ID of the chunk to update\n     * @param text Optional new text for the chunk\n     * @param metadata Optional new metadata for the chunk\n     * @returns\n     */\n    async update(options) {\n        return this.client.makeRequest(\"POST\", `chunks/${options.id}`, {\n            data: options,\n        });\n    }\n    /**\n     * Get a specific chunk.\n     * @param id ID of the chunk to retrieve\n     * @returns\n     */\n    async retrieve(options) {\n        return this.client.makeRequest(\"GET\", `chunks/${options.id}`);\n    }\n    /**\n     * Delete a specific chunk.\n     * @param id ID of the chunk to delete\n     * @returns\n     */\n    async delete(options) {\n        return this.client.makeRequest(\"DELETE\", `chunks/${options.id}`);\n    }\n    /**\n     * List chunks.\n     * @param includeVectors Include vector data in response. Defaults to False.\n     * @param metadataFilters Filter by metadata. Defaults to None.\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 100. Defaults to 100.\n     * @returns\n     */\n    async list(options) {\n        var _a, _b;\n        const params = {\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options === null || options === void 0 ? void 0 : options.limit) !== null && _b !== void 0 ? _b : 100,\n        };\n        if (options === null || options === void 0 ? void 0 : options.includeVectors) {\n            params.include_vectors = options.includeVectors;\n        }\n        if (options === null || options === void 0 ? void 0 : options.metadataFilters) {\n            params.metadata_filters = options.metadataFilters;\n        }\n        return this.client.makeRequest(\"GET\", \"chunks\", {\n            params,\n        });\n    }\n}\nexports.ChunksClient = ChunksClient;\n//# sourceMappingURL=chunks.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/chunks.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/collections.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/collections.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.CollectionsClient = void 0;\nconst utils_1 = __webpack_require__(/*! ../../utils */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/index.js\");\nlet fs;\nif (typeof window === \"undefined\") {\n    fs = __webpack_require__(/*! fs */ \"fs\");\n}\nclass CollectionsClient {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Create a new collection.\n     * @param name Name of the collection\n     * @param description Optional description of the collection\n     * @returns A promise that resolves with the created collection\n     */\n    async create(options) {\n        return this.client.makeRequest(\"POST\", \"collections\", {\n            data: options,\n        });\n    }\n    /**\n     * List collections with pagination and filtering options.\n     * @param ids Optional list of collection IDs to filter by\n     * @param offset Optional offset for pagination\n     * @param limit Optional limit for pagination\n     * @param ownerOnly If true, only returns collections owned by the user, not all accessible collections\n     * @returns\n     */\n    async list(options) {\n        var _a, _b;\n        const params = {\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options === null || options === void 0 ? void 0 : options.limit) !== null && _b !== void 0 ? _b : 100,\n        };\n        if ((options === null || options === void 0 ? void 0 : options.ids) && options.ids.length > 0) {\n            params.ids = options.ids;\n        }\n        if (options === null || options === void 0 ? void 0 : options.ownerOnly) {\n            params.owner_only = options.ownerOnly;\n        }\n        return this.client.makeRequest(\"GET\", \"collections\", {\n            params,\n        });\n    }\n    /**\n     * Get detailed information about a specific collection.\n     * @param id Collection ID to retrieve\n     * @returns\n     */\n    async retrieve(options) {\n        return this.client.makeRequest(\"GET\", `collections/${options.id}`);\n    }\n    /**\n     * Update an existing collection.\n     * @param id Collection ID to update\n     * @param name Optional new name for the collection\n     * @param description Optional new description for the collection\n     * @param generateDescription Whether to generate a new synthetic description for the collection\n     * @returns\n     */\n    async update(options) {\n        const data = Object.assign(Object.assign(Object.assign({}, (options.name && { name: options.name })), (options.description && { description: options.description })), (options.generateDescription !== undefined && {\n            generate_description: options.generateDescription,\n        }));\n        return this.client.makeRequest(\"POST\", `collections/${options.id}`, {\n            data,\n        });\n    }\n    /**\n     * Delete a collection.\n     * @param id Collection ID to delete\n     * @returns\n     */\n    async delete(options) {\n        return this.client.makeRequest(\"DELETE\", `collections/${options.id}`);\n    }\n    /**\n     * List all documents in a collection.\n     * @param id Collection ID\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 100. Defaults to 100.\n     * @returns\n     */\n    async listDocuments(options) {\n        var _a, _b;\n        const params = {\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options === null || options === void 0 ? void 0 : options.limit) !== null && _b !== void 0 ? _b : 100,\n        };\n        return this.client.makeRequest(\"GET\", `collections/${options.id}/documents`, {\n            params,\n        });\n    }\n    /**\n     * Add a document to a collection.\n     * @param id Collection ID\n     * @param documentId Document ID to add\n     * @returns\n     */\n    async addDocument(options) {\n        return this.client.makeRequest(\"POST\", `collections/${options.id}/documents/${options.documentId}`);\n    }\n    /**\n     * Remove a document from a collection.\n     * @param id Collection ID\n     * @param documentId Document ID to remove\n     * @returns\n     */\n    async removeDocument(options) {\n        return this.client.makeRequest(\"DELETE\", `collections/${options.id}/documents/${options.documentId}`);\n    }\n    /**\n     * List all users in a collection.\n     * @param id Collection ID\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 100. Defaults to 100.\n     * @returns\n     */\n    async listUsers(options) {\n        var _a, _b;\n        const params = {\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options === null || options === void 0 ? void 0 : options.limit) !== null && _b !== void 0 ? _b : 100,\n        };\n        return this.client.makeRequest(\"GET\", `collections/${options.id}/users`, {\n            params,\n        });\n    }\n    /**\n     * Add a user to a collection.\n     * @param id Collection ID\n     * @param userId User ID to add\n     * @returns\n     */\n    async addUser(options) {\n        return this.client.makeRequest(\"POST\", `collections/${options.id}/users/${options.userId}`);\n    }\n    /**\n     * Remove a user from a collection.\n     * @param id Collection ID\n     * @param userId User ID to remove\n     * @returns\n     */\n    async removeUser(options) {\n        return this.client.makeRequest(\"DELETE\", `collections/${options.id}/users/${options.userId}`);\n    }\n    /**\n     * Creates communities in the graph by analyzing entity relationships and similarities.\n     *\n     * Communities are created through the following process:\n     *  1. Analyzes entity relationships and metadata to build a similarity graph\n     *  2. Applies advanced community detection algorithms (e.g. Leiden) to identify densely connected groups\n     *  3. Creates hierarchical community structure with multiple granularity levels\n     *  4. Generates natural language summaries and statistical insights for each community\n     *\n     * The resulting communities can be used to:\n     *  - Understand high-level graph structure and organization\n     *  - Identify key entity groupings and their relationships\n     *  - Navigate and explore the graph at different levels of detail\n     *  - Generate insights about entity clusters and their characteristics\n     *\n     * The community detection process is configurable through settings like:\n     *  - Community detection algorithm parameters\n     *  - Summary generation prompt\n     * @param collectionId The collection ID corresponding to the graph\n     * @returns\n     */\n    async extract(options) {\n        const data = Object.assign(Object.assign({}, (options.settings && { settings: options.settings })), (options.runWithOrchestration !== undefined && {\n            run_with_orchestration: options.runWithOrchestration,\n        }));\n        return this.client.makeRequest(\"POST\", `collections/${options.collectionId}/extract`, {\n            data,\n        });\n    }\n    /**\n     * Export collections as a CSV file with support for filtering and column selection.\n     *\n     * @param options Export configuration options\n     * @param options.outputPath Path where the CSV file should be saved (Node.js only)\n     * @param options.columns Optional list of specific columns to include\n     * @param options.filters Optional filters to limit which collections are exported\n     * @param options.includeHeader Whether to include column headers (default: true)\n     * @returns Promise<Blob> in browser environments, Promise<void> in Node.js\n     */\n    async export(options = {}) {\n        var _a;\n        const data = {\n            include_header: (_a = options.includeHeader) !== null && _a !== void 0 ? _a : true,\n        };\n        if (options.columns) {\n            data.columns = options.columns;\n        }\n        if (options.filters) {\n            data.filters = options.filters;\n        }\n        const response = await this.client.makeRequest(\"POST\", \"collections/export\", {\n            data,\n            responseType: \"arraybuffer\",\n            headers: { Accept: \"text/csv\" },\n        });\n        // Node environment\n        if (options.outputPath && typeof process !== \"undefined\") {\n            await fs.promises.writeFile(options.outputPath, Buffer.from(response));\n            return;\n        }\n        // Browser\n        return new Blob([response], { type: \"text/csv\" });\n    }\n    /**\n     * Export collections as a CSV file and save it to the user's device.\n     * @param filename\n     * @param options\n     */\n    async exportToFile(options) {\n        const blob = await this.export(options);\n        if (blob instanceof Blob) {\n            (0, utils_1.downloadBlob)(blob, options.filename);\n        }\n    }\n    /**\n     * Retrieve a collection by its name.\n     * @param name The name of the collection to retrieve.\n     * @returns A promise that resolves with the collection details.\n     */\n    async retrieveByName(options) {\n        const queryParams = {};\n        if (options.ownerId) {\n            queryParams.owner_id = options.ownerId;\n        }\n        return this.client.makeRequest(\"GET\", `collections/name/${options.name}`, {\n            params: queryParams,\n        });\n    }\n}\nexports.CollectionsClient = CollectionsClient;\n//# sourceMappingURL=collections.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/collections.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/conversations.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/conversations.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.ConversationsClient = void 0;\nconst utils_1 = __webpack_require__(/*! ../../utils */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/index.js\");\nlet fs;\nif (typeof window === \"undefined\") {\n    fs = __webpack_require__(/*! fs */ \"fs\");\n}\nclass ConversationsClient {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Create a new conversation.\n     * @param name The name of the conversation\n     * @returns The created conversation\n     */\n    async create(options) {\n        const data = Object.assign({}, ((options === null || options === void 0 ? void 0 : options.name) && { name: options === null || options === void 0 ? void 0 : options.name }));\n        return this.client.makeRequest(\"POST\", \"conversations\", {\n            data,\n        });\n    }\n    /**\n     * List conversations with pagination and sorting options.\n     * @param ids List of conversation IDs to retrieve\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 100. Defaults to 100.\n     * @returns A list of conversations\n     */\n    async list(options) {\n        var _a, _b;\n        const params = {\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options === null || options === void 0 ? void 0 : options.limit) !== null && _b !== void 0 ? _b : 100,\n        };\n        if ((options === null || options === void 0 ? void 0 : options.ids) && options.ids.length > 0) {\n            params.ids = options.ids;\n        }\n        return this.client.makeRequest(\"GET\", \"conversations\", {\n            params,\n        });\n    }\n    /**\n     * Get detailed information about a specific conversation.\n     * @param id The ID of the conversation to retrieve\n     * @returns The conversation\n     */\n    async retrieve(options) {\n        return this.client.makeRequest(\"GET\", `conversations/${options.id}`);\n    }\n    /**\n     * Update an existing conversation.\n     * @param id The ID of the conversation to update\n     * @param name The new name of the conversation\n     * @returns The updated conversation\n     */\n    async update(options) {\n        const data = {\n            name: options.name,\n        };\n        return this.client.makeRequest(\"POST\", `conversations/${options.id}`, {\n            data,\n        });\n    }\n    /**\n     * Delete a conversation.\n     * @param id The ID of the conversation to delete\n     * @returns Whether the conversation was successfully deleted\n     */\n    async delete(options) {\n        return this.client.makeRequest(\"DELETE\", `conversations/${options.id}`);\n    }\n    /**\n     * Add a new message to a conversation.\n     * @param id The ID of the conversation to add the message to\n     * @param content The content of the message\n     * @param role The role of the message (e.g., \"user\" or \"assistant\")\n     * @param parentID The ID of the parent message\n     * @param metadata Additional metadata to attach to the message\n     * @returns The created message\n     */\n    async addMessage(options) {\n        const data = Object.assign(Object.assign({ content: options.content, role: options.role }, (options.parentID && { parentID: options.parentID })), (options.metadata && { metadata: options.metadata }));\n        return this.client.makeRequest(\"POST\", `conversations/${options.id}/messages`, {\n            data,\n        });\n    }\n    /**\n     * Update an existing message in a conversation.\n     * @param id The ID of the conversation containing the message\n     * @param messageID The ID of the message to update\n     * @param content The new content of the message\n     * @param metadata Additional metadata to attach to the message\n     * @returns The updated message\n     */\n    async updateMessage(options) {\n        const data = Object.assign(Object.assign({}, (options.content && { content: options.content })), (options.metadata && { metadata: options.metadata }));\n        return this.client.makeRequest(\"POST\", `conversations/${options.id}/messages/${options.messageID}`, {\n            data,\n        });\n    }\n    /**\n     * Export conversations as a CSV file with support for filtering and column selection.\n     *\n     * @param options Export configuration options\n     * @param options.outputPath Path where the CSV file should be saved (Node.js only)\n     * @param options.columns Optional list of specific columns to include\n     * @param options.filters Optional filters to limit which conversations are exported\n     * @param options.includeHeader Whether to include column headers (default: true)\n     * @returns Promise<Blob> in browser environments, Promise<void> in Node.js\n     */\n    async export(options = {}) {\n        var _a;\n        const data = {\n            include_header: (_a = options.includeHeader) !== null && _a !== void 0 ? _a : true,\n        };\n        if (options.columns) {\n            data.columns = options.columns;\n        }\n        if (options.filters) {\n            data.filters = options.filters;\n        }\n        const response = await this.client.makeRequest(\"POST\", \"conversations/export\", {\n            data,\n            responseType: \"arraybuffer\",\n            headers: { Accept: \"text/csv\" },\n        });\n        // Node environment\n        if (options.outputPath && typeof process !== \"undefined\") {\n            await fs.promises.writeFile(options.outputPath, Buffer.from(response));\n            return;\n        }\n        // Browser\n        return new Blob([response], { type: \"text/csv\" });\n    }\n    /**\n     * Export users as a CSV file and save it to the user's device.\n     * @param filename\n     * @param options\n     */\n    async exportToFile(options) {\n        const blob = await this.export(options);\n        if (blob instanceof Blob) {\n            (0, utils_1.downloadBlob)(blob, options.filename);\n        }\n    }\n    /**\n     * Export messages as a CSV file with support for filtering and column selection.\n     *\n     * @param options Export configuration options\n     * @param options.outputPath Path where the CSV file should be saved (Node.js only)\n     * @param options.columns Optional list of specific columns to include\n     * @param options.filters Optional filters to limit which messages are exported\n     * @param options.includeHeader Whether to include column headers (default: true)\n     * @returns Promise<Blob> in browser environments, Promise<void> in Node.js\n     */\n    async exportMessages(options = {}) {\n        var _a;\n        const data = {\n            include_header: (_a = options.includeHeader) !== null && _a !== void 0 ? _a : true,\n        };\n        if (options.columns) {\n            data.columns = options.columns;\n        }\n        if (options.filters) {\n            data.filters = options.filters;\n        }\n        const response = await this.client.makeRequest(\"POST\", \"conversations/export_messages\", {\n            data,\n            responseType: \"arraybuffer\",\n            headers: { Accept: \"text/csv\" },\n        });\n        // Node environment\n        if (options.outputPath && typeof process !== \"undefined\") {\n            await fs.promises.writeFile(options.outputPath, Buffer.from(response));\n            return;\n        }\n        // Browser\n        return new Blob([response], { type: \"text/csv\" });\n    }\n    /**\n     * Export messages as a CSV file and save it to the user's device.\n     * @param filename\n     * @param options\n     */\n    async exportMessagesToFile(options) {\n        const blob = await this.exportMessages(options);\n        if (blob instanceof Blob) {\n            (0, utils_1.downloadBlob)(blob, options.filename);\n        }\n    }\n}\nexports.ConversationsClient = ConversationsClient;\n//# sourceMappingURL=conversations.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/conversations.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/documents.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/documents.js ***!
  \*******************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || (function () {\n    var ownKeys = function(o) {\n        ownKeys = Object.getOwnPropertyNames || function (o) {\n            var ar = [];\n            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n            return ar;\n        };\n        return ownKeys(o);\n    };\n    return function (mod) {\n        if (mod && mod.__esModule) return mod;\n        var result = {};\n        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n        __setModuleDefault(result, mod);\n        return result;\n    };\n})();\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DocumentsClient = void 0;\nconst form_data_1 = __importDefault(__webpack_require__(/*! form-data */ \"(ssr)/./node_modules/.pnpm/form-data@4.0.4/node_modules/form-data/lib/form_data.js\"));\nconst utils_1 = __webpack_require__(/*! ../../utils */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/index.js\");\nconst utils_2 = __webpack_require__(/*! ../../utils */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/index.js\");\nlet fs;\nif (typeof window === \"undefined\") {\n    fs = __webpack_require__(/*! fs */ \"fs\");\n}\nconst axios_1 = __importDefault(__webpack_require__(/*! axios */ \"(ssr)/./node_modules/.pnpm/axios@1.10.0/node_modules/axios/dist/node/axios.cjs\"));\nconst os = __importStar(__webpack_require__(/*! os */ \"os\"));\nconst path = __importStar(__webpack_require__(/*! path */ \"path\"));\nconst uuid_1 = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/.pnpm/uuid@10.0.0/node_modules/uuid/dist/esm-node/index.js\");\nclass DocumentsClient {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Create a new document from either a file or content.\n     *\n     * Note: Access control might apply based on user limits (max documents, chunks, collections).\n     *\n     * @param file The file to upload, if any\n     * @param raw_text Optional raw text content to upload, if no file path is provided\n     * @param chunks Optional array of pre-processed text chunks to ingest\n     * @param s3Url Optional presigned S3 URL to upload the file from, if any.\n     * @param id Optional ID to assign to the document\n     * @param collectionIds Collection IDs to associate with the document. If none are provided, the document will be assigned to the user's default collection.\n     * @param metadata Optional metadata to assign to the document\n     * @param ingestionConfig Optional ingestion configuration to use\n     * @param runWithOrchestration Optional flag to run with orchestration (default: true)\n     * @param ingestionMode Optional ingestion mode (default: 'custom')\n     * @returns Promise<WrappedIngestionResponse>\n     */\n    async create(options) {\n        var _a, _b, _c;\n        const inputCount = [\n            options.file,\n            options.raw_text,\n            options.chunks,\n            options.s3Url,\n        ].filter((x) => x !== undefined).length;\n        if (inputCount === 0) {\n            throw new Error(\"Either file, raw_text, chunks, or s3Url must be provided\");\n        }\n        if (inputCount > 1) {\n            throw new Error(\"Only one of file, raw_text, chunks, or s3Url may be provided\");\n        }\n        const formData = new form_data_1.default();\n        let tempFilePath = null;\n        const processPath = async (path) => {\n            const appendFile = (file, filename) => {\n                formData.append(`file`, file, filename);\n            };\n            if (typeof path === \"string\") {\n                if (typeof window === \"undefined\") {\n                    const stat = await fs.promises.stat(path);\n                    if (stat.isDirectory()) {\n                        throw new Error(\"Directories are not supported in create()\");\n                    }\n                    else {\n                        appendFile(fs.createReadStream(path), path.split(\"/\").pop() || \"\");\n                    }\n                }\n                else {\n                    console.warn(\"File path provided in browser environment. This is not supported. Use a File object instead.\");\n                    throw new Error(\"File paths are not supported in the browser. Use a File object.\");\n                }\n            }\n            else if (path instanceof File) {\n                appendFile(path, path.name);\n            }\n            else if (\"path\" in path && \"name\" in path) {\n                if (typeof window === \"undefined\") {\n                    appendFile(fs.createReadStream(path.path), path.name);\n                }\n                else {\n                    console.warn(\"File path object provided in browser environment. This is not supported. Use a File object instead.\");\n                    throw new Error(\"File path objects are not supported in the browser. Use a File object.\");\n                }\n            }\n        };\n        if (options.file) {\n            await processPath(options.file);\n        }\n        else if (options.raw_text) {\n            formData.append(\"raw_text\", options.raw_text);\n        }\n        else if (options.chunks) {\n            formData.append(\"chunks\", JSON.stringify(options.chunks));\n        }\n        else if (options.s3Url) {\n            // Download the S3 file first, then upload it\n            try {\n                let response;\n                let fileContent;\n                let filename;\n                if (typeof window === \"undefined\") {\n                    // Node.js environment\n                    response = await axios_1.default.get(options.s3Url, {\n                        responseType: \"arraybuffer\",\n                    });\n                    fileContent = Buffer.from(response.data);\n                    filename = options.s3Url.split(\"?\")[0].split(\"/\").pop() || \"s3_file\";\n                    const tmpDir = os.tmpdir();\n                    tempFilePath = path.join(tmpDir, `r2r_s3_${Date.now()}_${filename}`);\n                    try {\n                        await fs.promises.writeFile(tempFilePath, fileContent);\n                        formData.append(\"file\", fs.createReadStream(tempFilePath), filename);\n                    }\n                    finally {\n                    }\n                }\n                else {\n                    // Browser environment\n                    response = await fetch(options.s3Url);\n                    if (!response.ok) {\n                        throw new Error(`Failed to download file from S3 URL: ${response.status}`);\n                    }\n                    const blob = await response.blob();\n                    filename = options.s3Url.split(\"?\")[0].split(\"/\").pop() || \"s3_file\";\n                    const file = new File([blob], filename, { type: blob.type });\n                    formData.append(\"file\", file, filename);\n                }\n            }\n            catch (error) {\n                throw new Error(`Failed to download file from S3 URL: ${error.message}`);\n            }\n        }\n        if (options.id) {\n            formData.append(\"id\", options.id);\n        }\n        if (options.metadata) {\n            formData.append(\"metadata\", JSON.stringify(options.metadata));\n        }\n        if (options.ingestionConfig) {\n            formData.append(\"ingestion_config\", JSON.stringify((0, utils_2.ensureSnakeCase)(options.ingestionConfig)));\n        }\n        if ((_a = options.collectionIds) === null || _a === void 0 ? void 0 : _a.length) {\n            formData.append(\"collection_ids\", JSON.stringify(options.collectionIds));\n        }\n        if (options.runWithOrchestration !== undefined) {\n            formData.append(\"run_with_orchestration\", String(options.runWithOrchestration));\n        }\n        if (options.ingestionMode) {\n            formData.append(\"ingestion_mode\", options.ingestionMode);\n        }\n        try {\n            return this.client.makeRequest(\"POST\", \"documents\", {\n                data: formData,\n                headers: (_c = (_b = formData.getHeaders) === null || _b === void 0 ? void 0 : _b.call(formData)) !== null && _c !== void 0 ? _c : {\n                    \"Content-Type\": \"multipart/form-data\",\n                },\n                transformRequest: [\n                    (data, headers) => {\n                        return data;\n                    },\n                ],\n            });\n        }\n        finally {\n            if (tempFilePath && typeof window === \"undefined\") {\n                try {\n                    if (fs.existsSync(tempFilePath)) {\n                        await fs.promises.unlink(tempFilePath);\n                    }\n                }\n                catch (cleanupError) {\n                    console.error(\"Error cleaning up temporary file:\", cleanupError);\n                }\n            }\n        }\n    }\n    /**\n     * Append metadata to a document.\n     *\n     * Note: Users can typically only modify metadata for documents they own. Superusers may have broader access.\n     *\n     * @param id ID of document to append metadata to\n     * @param metadata List of metadata entries (key-value pairs) to append\n     * @returns Promise<WrappedDocumentResponse>\n     */\n    async appendMetadata(options) {\n        return this.client.makeRequest(\"PATCH\", `documents/${options.id}/metadata`, {\n            data: options.metadata,\n        });\n    }\n    /**\n     * Replace metadata for a document. This overwrites all existing metadata.\n     *\n     * Note: Users can typically only replace metadata for documents they own. Superusers may have broader access.\n     *\n     * @param id ID of document to replace metadata for\n     * @param metadata The new list of metadata entries (key-value pairs)\n     * @returns Promise<WrappedDocumentResponse>\n     */\n    async replaceMetadata(options) {\n        return this.client.makeRequest(\"PUT\", `documents/${options.id}/metadata`, {\n            data: options.metadata,\n        });\n    }\n    /**\n     * Get details for a specific document by ID.\n     *\n     * Note: Users can only retrieve documents they own or have access to through collections. Superusers can retrieve any document.\n     *\n     * @param id ID of document to retrieve\n     * @returns Promise<WrappedDocumentResponse>\n     */\n    async retrieve(options) {\n        return this.client.makeRequest(\"GET\", `documents/${options.id}`);\n    }\n    /**\n     * List documents with pagination.\n     *\n     * Note: Regular users will only see documents they own or have access to through collections. Superusers can see all documents.\n     *\n     * @param ids Optional list of document IDs to filter by\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 1000. Defaults to 100.\n     * @param includeSummaryEmbeddings Specifies whether or not to include embeddings of each document summary. Defaults to false.\n     * @param ownerOnly If true, only returns documents owned by the user, not all accessible documents\n     * @returns Promise<WrappedDocumentsResponse>\n     */\n    async list(options) {\n        var _a, _b, _c, _d;\n        const params = {\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options === null || options === void 0 ? void 0 : options.limit) !== null && _b !== void 0 ? _b : 100,\n            include_summary_embeddings: (_c = options === null || options === void 0 ? void 0 : options.includeSummaryEmbeddings) !== null && _c !== void 0 ? _c : false,\n        };\n        if ((_d = options === null || options === void 0 ? void 0 : options.ids) === null || _d === void 0 ? void 0 : _d.length) {\n            params.ids = options.ids;\n        }\n        if (options === null || options === void 0 ? void 0 : options.ownerOnly) {\n            params.owner_only = options.ownerOnly;\n        }\n        return this.client.makeRequest(\"GET\", \"documents\", {\n            params,\n        });\n    }\n    /**\n     * Download a document's original file content.\n     *\n     * Note: Users can only download documents they own or have access to through collections.\n     *\n     * @param id ID of document to download\n     * @returns Blob containing the document's file content\n     */\n    async download(options) {\n        var _a;\n        const response = await this.client.makeRequest(\"GET\", `documents/${options.id}/download`, {\n            responseType: \"arraybuffer\",\n            returnFullResponse: true, // Need full response to get headers\n        });\n        if (!response.data) {\n            throw new Error(\"No data received in response\");\n        }\n        // Extract content-type, default if not present\n        const contentType = ((_a = response.headers) === null || _a === void 0 ? void 0 : _a[\"content-type\"]) || \"application/octet-stream\";\n        // Handle different possible data types from axios\n        if (response.data instanceof Blob) {\n            // If it's already a Blob (less likely for arraybuffer type), return it\n            return response.data;\n        }\n        else if (response.data instanceof ArrayBuffer) {\n            // Common case for responseType: 'arraybuffer'\n            return new Blob([response.data], { type: contentType });\n        }\n        else if (typeof response.data === \"string\") {\n            // Less common, but handle if it returns a string\n            return new Blob([response.data], { type: contentType });\n        }\n        else {\n            // Try converting other types if necessary, fallback to empty blob\n            try {\n                return new Blob([JSON.stringify(response.data)], {\n                    type: contentType,\n                });\n            }\n            catch (e) {\n                console.error(\"Could not convert response data to Blob:\", e);\n                return new Blob([], { type: contentType }); // Return empty blob as fallback\n            }\n        }\n    }\n    /**\n     * Export documents metadata as a CSV file.\n     *\n     * Note: This operation is typically restricted to superusers.\n     *\n     * @param options Export configuration options\n     * @param options.outputPath Path where the CSV file should be saved (Node.js only). If provided, the function returns void.\n     * @param options.columns Optional list of specific columns to include\n     * @param options.filters Optional filters to limit which documents are exported\n     * @param options.includeHeader Whether to include column headers (default: true)\n     * @returns Promise<Blob> in browser environments (if outputPath is not provided), Promise<void> in Node.js (if outputPath is provided).\n     */\n    async export(options = {}) {\n        var _a;\n        const data = {\n            include_header: (_a = options.includeHeader) !== null && _a !== void 0 ? _a : true,\n        };\n        if (options.columns) {\n            data.columns = options.columns;\n        }\n        if (options.filters) {\n            data.filters = options.filters;\n        }\n        const response = await this.client.makeRequest(\"POST\", \"documents/export\", {\n            data,\n            responseType: \"arraybuffer\", // Expecting binary data for file saving / Blob creation\n            headers: { Accept: \"text/csv\" },\n            returnFullResponse: false, // We just need the data (ArrayBuffer)\n        });\n        // Node environment: write to file if outputPath is given\n        if (options.outputPath && typeof process !== \"undefined\" && (fs === null || fs === void 0 ? void 0 : fs.promises)) {\n            await fs.promises.writeFile(options.outputPath, Buffer.from(response));\n            return; // Return void\n        }\n        // Browser or Node without outputPath: return Blob\n        return new Blob([response], { type: \"text/csv\" });\n    }\n    /**\n     * Export entities for a specific document as a CSV file.\n     *\n     * Note: This operation is typically restricted to superusers or owners of the document.\n     *\n     * @param options Export configuration options\n     * @param options.id The ID of the document whose entities are to be exported.\n     * @param options.outputPath Path where the CSV file should be saved (Node.js only). If provided, the function returns void.\n     * @param options.columns Optional list of specific columns to include\n     * @param options.filters Optional filters to limit which entities are exported\n     * @param options.includeHeader Whether to include column headers (default: true)\n     * @returns Promise<Blob> in browser environments (if outputPath is not provided), Promise<void> in Node.js (if outputPath is provided).\n     */\n    async exportEntities(options) {\n        var _a;\n        const data = {\n            // Router expects ID in path, not body. Data contains export options.\n            include_header: (_a = options.includeHeader) !== null && _a !== void 0 ? _a : true,\n        };\n        if (options.columns) {\n            data.columns = options.columns;\n        }\n        if (options.filters) {\n            data.filters = options.filters;\n        }\n        const response = await this.client.makeRequest(\"POST\", `documents/${options.id}/entities/export`, // ID in path\n        {\n            data, // Export options in body\n            responseType: \"arraybuffer\",\n            headers: { Accept: \"text/csv\" },\n            returnFullResponse: false,\n        });\n        // Node environment: write to file if outputPath is given\n        if (options.outputPath && typeof process !== \"undefined\" && (fs === null || fs === void 0 ? void 0 : fs.promises)) {\n            await fs.promises.writeFile(options.outputPath, Buffer.from(response));\n            return; // Return void\n        }\n        // Browser or Node without outputPath: return Blob\n        return new Blob([response], { type: \"text/csv\" });\n    }\n    /**\n     * Export entities for a document as a CSV file and trigger download in the browser.\n     *\n     * Note: This method only works in browser environments.\n     * Note: Access control (superuser/owner) applies based on the underlying `exportEntities` call.\n     *\n     * @param options Export configuration options\n     * @param options.filename The desired filename for the downloaded file (e.g., \"entities.csv\").\n     * @param options.id The ID of the document whose entities are to be exported.\n     * @param options.columns Optional list of specific columns to include\n     * @param options.filters Optional filters to limit which entities are exported\n     * @param options.includeHeader Whether to include column headers (default: true)\n     */\n    async exportEntitiesToFile(options) {\n        if (typeof window === \"undefined\") {\n            console.warn(\"exportEntitiesToFile is intended for browser environments only.\");\n            return;\n        }\n        // Call exportEntities without outputPath to get the Blob\n        const blob = await this.exportEntities({\n            id: options.id,\n            columns: options.columns,\n            filters: options.filters,\n            includeHeader: options.includeHeader,\n        });\n        if (blob instanceof Blob) {\n            (0, utils_1.downloadBlob)(blob, options.filename);\n        }\n        else {\n            // This case should not happen if outputPath is undefined, but handle defensively\n            console.error(\"Expected a Blob but received void. Did you accidentally provide an outputPath in a browser context?\");\n        }\n    }\n    /**\n     * Export relationships for a specific document as a CSV file.\n     *\n     * Note: This operation is typically restricted to superusers or owners of the document.\n     *\n     * @param options Export configuration options\n     * @param options.id The ID of the document whose relationships are to be exported.\n     * @param options.outputPath Path where the CSV file should be saved (Node.js only). If provided, the function returns void.\n     * @param options.columns Optional list of specific columns to include\n     * @param options.filters Optional filters to limit which relationships are exported\n     * @param options.includeHeader Whether to include column headers (default: true)\n     * @returns Promise<Blob> in browser environments (if outputPath is not provided), Promise<void> in Node.js (if outputPath is provided).\n     */\n    async exportRelationships(options) {\n        var _a;\n        const data = {\n            include_header: (_a = options.includeHeader) !== null && _a !== void 0 ? _a : true,\n        };\n        if (options.columns) {\n            data.columns = options.columns;\n        }\n        if (options.filters) {\n            data.filters = options.filters;\n        }\n        const response = await this.client.makeRequest(\"POST\", `documents/${options.id}/relationships/export`, // ID in path\n        {\n            data, // Export options in body\n            responseType: \"arraybuffer\",\n            headers: { Accept: \"text/csv\" },\n            returnFullResponse: false,\n        });\n        // Node environment: write to file if outputPath is given\n        if (options.outputPath && typeof process !== \"undefined\" && (fs === null || fs === void 0 ? void 0 : fs.promises)) {\n            await fs.promises.writeFile(options.outputPath, Buffer.from(response));\n            return; // Return void\n        }\n        // Browser or Node without outputPath: return Blob\n        return new Blob([response], { type: \"text/csv\" });\n    }\n    /**\n     * Export relationships for a document as a CSV file and trigger download in the browser.\n     *\n     * Note: This method only works in browser environments.\n     * Note: Access control (superuser/owner) applies based on the underlying `exportRelationships` call.\n     *\n     * @param options Export configuration options\n     * @param options.filename The desired filename for the downloaded file (e.g., \"relationships.csv\").\n     * @param options.id The ID of the document whose relationships are to be exported.\n     * @param options.columns Optional list of specific columns to include\n     * @param options.filters Optional filters to limit which relationships are exported\n     * @param options.includeHeader Whether to include column headers (default: true)\n     */\n    async exportRelationshipsToFile(options) {\n        if (typeof window === \"undefined\") {\n            console.warn(\"exportRelationshipsToFile is intended for browser environments only.\");\n            return;\n        }\n        const blob = await this.exportRelationships({\n            id: options.id,\n            columns: options.columns,\n            filters: options.filters,\n            includeHeader: options.includeHeader,\n        });\n        if (blob instanceof Blob) {\n            (0, utils_1.downloadBlob)(blob, options.filename);\n        }\n        else {\n            console.error(\"Expected a Blob but received void. Did you accidentally provide an outputPath in a browser context?\");\n        }\n    }\n    /**\n     * Download multiple documents as a zip file.\n     *\n     * Note: Access control applies. Non-superusers might be restricted to exporting only documents they own or have access to, and might be required to provide document IDs. Superusers can typically export any documents.\n     *\n     * @param options Configuration options for the zip download\n     * @param options.documentIds Optional list of document IDs to include. May be required for non-superusers.\n     * @param options.startDate Optional filter for documents created on or after this date.\n     * @param options.endDate Optional filter for documents created on or before this date.\n     * @param options.outputPath Optional path to save the zip file (Node.js only). If provided, the function returns void.\n     * @returns Promise<Blob> in browser environments (if outputPath is not provided), Promise<void> in Node.js (if outputPath is provided).\n     */\n    async downloadZip(options) {\n        var _a;\n        const params = {};\n        if ((_a = options.documentIds) === null || _a === void 0 ? void 0 : _a.length) {\n            // Pass as array, backend expects list\n            params.document_ids = options.documentIds;\n        }\n        if (options.startDate) {\n            params.start_date = options.startDate.toISOString();\n        }\n        if (options.endDate) {\n            params.end_date = options.endDate.toISOString();\n        }\n        const response = await this.client.makeRequest(\"GET\", \"documents/download_zip\", {\n            params,\n            responseType: \"arraybuffer\",\n            headers: { Accept: \"application/zip\" }, // Correct mime type\n            returnFullResponse: false,\n        });\n        // Node environment: write to file if outputPath is given\n        if (options.outputPath && typeof process !== \"undefined\" && (fs === null || fs === void 0 ? void 0 : fs.promises)) {\n            await fs.promises.writeFile(options.outputPath, Buffer.from(response));\n            return; // Return void\n        }\n        // Browser or Node without outputPath: return Blob\n        return new Blob([response], { type: \"application/zip\" });\n    }\n    /**\n     * Download multiple documents as a zip file and trigger download in the browser.\n     *\n     * Note: This method only works in browser environments.\n     * Note: Access control applies based on the underlying `downloadZip` call.\n     *\n     * @param options Configuration options for the zip download\n     * @param options.filename The desired filename for the downloaded zip file (e.g., \"documents.zip\").\n     * @param options.documentIds Optional list of document IDs to include.\n     * @param options.startDate Optional filter for documents created on or after this date.\n     * @param options.endDate Optional filter for documents created on or before this date.\n     */\n    async downloadZipToFile(options) {\n        if (typeof window === \"undefined\") {\n            console.warn(\"downloadZipToFile is intended for browser environments only.\");\n            return;\n        }\n        const blob = await this.downloadZip({\n            documentIds: options.documentIds,\n            startDate: options.startDate,\n            endDate: options.endDate,\n        });\n        if (blob instanceof Blob) {\n            (0, utils_1.downloadBlob)(blob, options.filename);\n        }\n        else {\n            console.error(\"Expected a Blob but received void. Did you accidentally provide an outputPath in a browser context?\");\n        }\n    }\n    /**\n     * Export documents metadata as a CSV file and trigger download in the browser.\n     *\n     * Note: This method only works in browser environments.\n     * Note: Access control (superuser) applies based on the underlying `export` call.\n     *\n     * @param options Export configuration options\n     * @param options.filename The desired filename for the downloaded CSV file (e.g., \"export.csv\").\n     * @param options.columns Optional list of specific columns to include\n     * @param options.filters Optional filters to limit which documents are exported\n     * @param options.includeHeader Whether to include column headers (default: true)\n     */\n    async exportToFile(options) {\n        if (typeof window === \"undefined\") {\n            console.warn(\"exportToFile is intended for browser environments only.\");\n            return;\n        }\n        const blob = await this.export({\n            columns: options.columns,\n            filters: options.filters,\n            includeHeader: options.includeHeader,\n        });\n        if (blob instanceof Blob) {\n            (0, utils_1.downloadBlob)(blob, options.filename);\n        }\n        else {\n            console.error(\"Expected a Blob but received void. Did you accidentally provide an outputPath in a browser context?\");\n        }\n    }\n    /**\n     * Delete a specific document by ID. This also deletes associated chunks.\n     *\n     * Note: Users can typically only delete documents they own. Superusers may have broader access.\n     *\n     * @param id ID of document to delete\n     * @returns Promise<WrappedBooleanResponse>\n     */\n    async delete(options) {\n        return this.client.makeRequest(\"DELETE\", `documents/${options.id}`);\n    }\n    /**\n     * Get chunks for a specific document.\n     *\n     * Note: Users can only access chunks from documents they own or have access to through collections.\n     *\n     * @param id Document ID to retrieve chunks for\n     * @param includeVectors Whether to include vector embeddings in the response (default: false)\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 1000. Defaults to 100.\n     * @returns Promise<WrappedChunksResponse>\n     */\n    async listChunks(options) {\n        var _a, _b, _c;\n        const params = {\n            // Map to snake_case for the API\n            include_vectors: (_a = options.includeVectors) !== null && _a !== void 0 ? _a : false,\n            offset: (_b = options.offset) !== null && _b !== void 0 ? _b : 0,\n            limit: (_c = options.limit) !== null && _c !== void 0 ? _c : 100,\n        };\n        return this.client.makeRequest(\"GET\", `documents/${options.id}/chunks`, {\n            params,\n        });\n    }\n    /**\n     * List collections associated with a specific document.\n     *\n     * Note: This endpoint might be restricted to superusers depending on API implementation. Check API documentation.\n     *\n     * @param id ID of document to retrieve collections for\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 1000. Defaults to 100.\n     * @returns Promise<WrappedCollectionsResponse>\n     */\n    async listCollections(options) {\n        var _a, _b;\n        const params = {\n            offset: (_a = options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options.limit) !== null && _b !== void 0 ? _b : 100,\n        };\n        return this.client.makeRequest(\"GET\", `documents/${options.id}/collections`, {\n            params,\n        });\n    }\n    /**\n     * Delete documents based on metadata filters.\n     *\n     * Note: For non-superusers, deletion is implicitly limited to documents owned by the user, in addition to the provided filters.\n     *\n     * @param filters Filters to apply when selecting documents to delete (e.g., `{ \"metadata.year\": { \"$lt\": 2020 } }`)\n     * @returns Promise<WrappedBooleanResponse>\n     */\n    async deleteByFilter(options) {\n        // Filters are sent in the request body as JSON\n        return this.client.makeRequest(\"DELETE\", \"documents/by-filter\", {\n            data: options.filters,\n        });\n    }\n    /**\n     * Triggers the extraction of entities and relationships from a document.\n     *\n     * Note: Users typically need to own the document to trigger extraction. Superusers may have broader access.\n     * This is often an asynchronous process.\n     *\n     * @param id ID of the document to extract from.\n     * @param settings Optional settings to override the default extraction configuration.\n     * @param runWithOrchestration Whether to run with orchestration (recommended, default: true).\n     * @returns Promise<WrappedGenericMessageResponse> indicating the task was queued or completed.\n     */\n    async extract(options) {\n        const data = {};\n        if (options.settings) {\n            // Send settings in the body as per router\n            data.settings = options.settings;\n        }\n        if (options.runWithOrchestration !== undefined) {\n            // Send runWithOrchestration in the body\n            data.run_with_orchestration = options.runWithOrchestration;\n        }\n        return this.client.makeRequest(\"POST\", `documents/${options.id}/extract`, {\n            // Data goes in the body for POST\n            data: data,\n        });\n    }\n    /**\n     * Retrieves the entities that were extracted from a document.\n     *\n     * Note: Users can only access entities from documents they own or have access to through collections.\n     *\n     * @param id Document ID to retrieve entities for\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 1000. Defaults to 100.\n     * @param includeEmbeddings Whether to include vector embeddings in the response (default: false). Renamed from includeVectors for consistency with router.\n     * @returns Promise<WrappedEntitiesResponse>\n     */\n    async listEntities(options) {\n        var _a, _b, _c;\n        const params = {\n            offset: (_a = options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options.limit) !== null && _b !== void 0 ? _b : 100,\n            // Map to snake_case for the API\n            include_embeddings: (_c = options.includeEmbeddings) !== null && _c !== void 0 ? _c : false,\n        };\n        return this.client.makeRequest(\"GET\", `documents/${options.id}/entities`, {\n            params,\n        });\n    }\n    /**\n     * Retrieves the relationships between entities that were extracted from a document.\n     *\n     * Note: Users can only access relationships from documents they own or have access to through collections.\n     *\n     * @param id Document ID to retrieve relationships for\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 1000. Defaults to 100.\n     * @param entityNames Optional filter for relationships involving specific entity names.\n     * @param relationshipTypes Optional filter for specific relationship types.\n     * @returns Promise<WrappedRelationshipsResponse>\n     */\n    async listRelationships(options) {\n        var _a, _b, _c, _d;\n        const params = {\n            offset: (_a = options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options.limit) !== null && _b !== void 0 ? _b : 100,\n        };\n        // Add optional filters if provided\n        if ((_c = options.entityNames) === null || _c === void 0 ? void 0 : _c.length) {\n            params.entity_names = options.entityNames;\n        }\n        if ((_d = options.relationshipTypes) === null || _d === void 0 ? void 0 : _d.length) {\n            params.relationship_types = options.relationshipTypes;\n        }\n        return this.client.makeRequest(\"GET\", `documents/${options.id}/relationships`, {\n            params,\n        });\n    }\n    /**\n     * Triggers the deduplication of entities within a document.\n     *\n     * Note: Users typically need to own the document to trigger deduplication. Superusers may have broader access.\n     * This is often an asynchronous process.\n     *\n     * @param id Document ID to deduplicate entities for.\n     * @param settings Optional settings to override the default deduplication configuration.\n     * @param runWithOrchestration Whether to run with orchestration (recommended, default: true).\n     * @returns Promise<WrappedGenericMessageResponse> indicating the task was queued or completed.\n     */\n    async deduplicate(options) {\n        const data = {};\n        // Removed runType\n        if (options.settings) {\n            data.settings = options.settings; // Send settings in body\n        }\n        if (options.runWithOrchestration !== undefined) {\n            data.run_with_orchestration = options.runWithOrchestration; // Send in body\n        }\n        return this.client.makeRequest(\"POST\", `documents/${options.id}/deduplicate`, {\n            // Data goes in the body for POST\n            data: data,\n        });\n    }\n    /**\n     * Perform a search query on document summaries.\n     *\n     * Note: Access control (based on user ownership/collection access) is applied to the search results.\n     *\n     * @param query The search query string.\n     * @param searchMode The search mode to use ('basic', 'advanced', 'custom'). Defaults to 'custom'.\n     * @param searchSettings Optional settings to configure the search (filters, limits, hybrid search options, etc.).\n     * @returns Promise<WrappedDocumentSearchResponse>\n     */\n    async search(options) {\n        var _a, _b;\n        const data = {\n            query: options.query,\n            // Map to snake_case for API\n            search_mode: (_a = options.searchMode) !== null && _a !== void 0 ? _a : \"custom\",\n            search_settings: (_b = options.searchSettings) !== null && _b !== void 0 ? _b : {}, // Send empty object if undefined\n        };\n        return this.client.makeRequest(\"POST\", \"documents/search\", {\n            data: data, // Use data for POST body\n        });\n    }\n    /**\n     * Ingest a sample document into R2R. Downloads a sample PDF, ingests it, and cleans up.\n     *\n     * Note: This requires Node.js environment with 'fs', 'axios', 'os', 'path', 'uuid' modules. It will not work directly in a standard browser environment due to file system access.\n     *\n     * @param options Optional ingestion options.\n     * @param options.ingestionMode If provided, passes the ingestion mode (e.g. \"hi-res\") to the create() method.\n     * @returns Promise<WrappedIngestionResponse> The ingestion response.\n     */\n    async createSample(options) {\n        // Check if in Node.js environment\n        if (typeof window !== \"undefined\" || !fs || !axios_1.default || !os || !path) {\n            throw new Error(\"createSample method requires a Node.js environment with 'fs', 'axios', 'os', 'path', 'uuid' modules.\");\n        }\n        const sampleFileUrl = \"https://raw.githubusercontent.com/SciPhi-AI/R2R/main/py/core/examples/data/DeepSeek_R1.pdf\";\n        const parsedUrl = new URL(sampleFileUrl);\n        const filename = parsedUrl.pathname.split(\"/\").pop() || \"sample.pdf\"; // Default to .pdf\n        // Create a temporary file path using Node.js 'os' and 'path'\n        const tmpDir = os.tmpdir();\n        const tmpFilePath = path.join(tmpDir, `r2r_sample_${Date.now()}_${filename}`);\n        let ingestionResponse;\n        try {\n            // Download the file using axios\n            const response = await axios_1.default.get(sampleFileUrl, {\n                responseType: \"arraybuffer\", // Get data as ArrayBuffer\n            });\n            // Write the downloaded file to the temporary location using Node.js 'fs'\n            await fs.promises.writeFile(tmpFilePath, Buffer.from(response.data)); // Convert ArrayBuffer to Buffer\n            // Generate a stable document ID using uuid v5\n            const NAMESPACE_DNS = \"6ba7b810-9dad-11d1-80b4-00c04fd430c8\"; // Standard DNS namespace UUID\n            const docId = (0, uuid_1.v5)(sampleFileUrl, NAMESPACE_DNS);\n            const metadata = { title: filename };\n            // Ingest the file by calling the create() method, passing the file path\n            ingestionResponse = await this.create({\n                file: tmpFilePath, // Pass the path as string (Node.js compatible part of create)\n                metadata,\n                id: docId,\n                ingestionMode: options === null || options === void 0 ? void 0 : options.ingestionMode,\n            });\n        }\n        catch (error) {\n            // Ensure cleanup happens even on error during download or ingestion\n            console.error(\"Error during createSample:\", error);\n            throw error; // Re-throw the error after logging\n        }\n        finally {\n            // Clean up: remove the temporary file using Node.js 'fs'\n            try {\n                await fs.promises.unlink(tmpFilePath);\n            }\n            catch (unlinkError) {\n                // Log unlink error but don't overwrite original error if one occurred\n                console.error(`Failed to delete temporary file ${tmpFilePath}:`, unlinkError);\n            }\n        }\n        return ingestionResponse;\n    }\n}\nexports.DocumentsClient = DocumentsClient;\n//# sourceMappingURL=documents.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcjJyLWpzQDAuNC40My9ub2RlX21vZHVsZXMvcjJyLWpzL2Rpc3QvdjMvY2xpZW50cy9kb2N1bWVudHMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsb0NBQW9DO0FBQ25EO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLDBDQUEwQyw0QkFBNEI7QUFDdEUsQ0FBQztBQUNEO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJEQUEyRCxjQUFjO0FBQ3pFO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBLDZDQUE2QztBQUM3QztBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCx1QkFBdUI7QUFDdkIsb0NBQW9DLG1CQUFPLENBQUMscUdBQVc7QUFDdkQsZ0JBQWdCLG1CQUFPLENBQUMscUdBQWE7QUFDckMsZ0JBQWdCLG1CQUFPLENBQUMscUdBQWE7QUFDckM7QUFDQTtBQUNBLFNBQVMsbUJBQU8sQ0FBQyxjQUFJO0FBQ3JCO0FBQ0EsZ0NBQWdDLG1CQUFPLENBQUMsNkZBQU87QUFDL0Msd0JBQXdCLG1CQUFPLENBQUMsY0FBSTtBQUNwQywwQkFBMEIsbUJBQU8sQ0FBQyxrQkFBTTtBQUN4QyxlQUFlLG1CQUFPLENBQUMsNkZBQU07QUFDN0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0EsK0RBQStELFdBQVcsR0FBRyxTQUFTO0FBQ3RGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnRkFBZ0YsZ0JBQWdCO0FBQ2hHO0FBQ0E7QUFDQTtBQUNBLDhEQUE4RCxpQkFBaUI7QUFDL0U7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3RUFBd0UsY0FBYztBQUN0RjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxxQkFBcUI7QUFDckI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkRBQTZELFdBQVc7QUFDeEU7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJEQUEyRCxXQUFXO0FBQ3RFO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJELFdBQVc7QUFDdEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJFQUEyRSxXQUFXO0FBQ3RGO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0NBQStDLG1CQUFtQjtBQUNsRTtBQUNBO0FBQ0E7QUFDQSwrQ0FBK0MsbUJBQW1CO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsbUJBQW1CLEdBQUc7QUFDNUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLG9CQUFvQjtBQUMzQztBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEI7QUFDQTtBQUNBLHNDQUFzQyxrQkFBa0I7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRFQUE0RSxXQUFXO0FBQ3ZGO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QixvQkFBb0I7QUFDM0M7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQSxzQ0FBc0Msa0JBQWtCO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0RUFBNEUsV0FBVztBQUN2RjtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsb0JBQW9CO0FBQzNDO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQjtBQUNwQjtBQUNBO0FBQ0Esc0NBQXNDLGtCQUFrQjtBQUN4RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QiwyQkFBMkI7QUFDbEQ7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQSxzQ0FBc0MseUJBQXlCO0FBQy9EO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOERBQThELFdBQVc7QUFDekU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJEQUEyRCxXQUFXO0FBQ3RFO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQsV0FBVztBQUN0RTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvRkFBb0YsbUJBQW1CLGVBQWU7QUFDdEg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNERBQTRELFdBQVc7QUFDdkU7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJELFdBQVc7QUFDdEU7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJEQUEyRCxXQUFXO0FBQ3RFO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQThDO0FBQzlDO0FBQ0E7QUFDQSx3RUFBd0U7QUFDeEU7QUFDQSw0REFBNEQsV0FBVztBQUN2RTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4RkFBOEY7QUFDOUY7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4RUFBOEU7QUFDOUU7QUFDQTtBQUNBLDREQUE0RCxXQUFXLEdBQUcsU0FBUztBQUNuRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0Esa0ZBQWtGO0FBQ2xGO0FBQ0EsMEVBQTBFO0FBQzFFO0FBQ0EsK0JBQStCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUVBQWlFLFlBQVk7QUFDN0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QiIsInNvdXJjZXMiOlsiRDpcXExMTVxcTGVhcm5pbmdcXGFpX2NvZGluZ1xcYW55dGhpbmdjaGF0XFxsaXZlY2hhdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccjJyLWpzQDAuNC40M1xcbm9kZV9tb2R1bGVzXFxyMnItanNcXGRpc3RcXHYzXFxjbGllbnRzXFxkb2N1bWVudHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG52YXIgX19jcmVhdGVCaW5kaW5nID0gKHRoaXMgJiYgdGhpcy5fX2NyZWF0ZUJpbmRpbmcpIHx8IChPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIG0sIGssIGsyKSB7XG4gICAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgICB2YXIgZGVzYyA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eURlc2NyaXB0b3IobSwgayk7XG4gICAgaWYgKCFkZXNjIHx8IChcImdldFwiIGluIGRlc2MgPyAhbS5fX2VzTW9kdWxlIDogZGVzYy53cml0YWJsZSB8fCBkZXNjLmNvbmZpZ3VyYWJsZSkpIHtcbiAgICAgIGRlc2MgPSB7IGVudW1lcmFibGU6IHRydWUsIGdldDogZnVuY3Rpb24oKSB7IHJldHVybiBtW2tdOyB9IH07XG4gICAgfVxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShvLCBrMiwgZGVzYyk7XG59KSA6IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgb1trMl0gPSBtW2tdO1xufSkpO1xudmFyIF9fc2V0TW9kdWxlRGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19zZXRNb2R1bGVEZWZhdWx0KSB8fCAoT2JqZWN0LmNyZWF0ZSA/IChmdW5jdGlvbihvLCB2KSB7XG4gICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIFwiZGVmYXVsdFwiLCB7IGVudW1lcmFibGU6IHRydWUsIHZhbHVlOiB2IH0pO1xufSkgOiBmdW5jdGlvbihvLCB2KSB7XG4gICAgb1tcImRlZmF1bHRcIl0gPSB2O1xufSk7XG52YXIgX19pbXBvcnRTdGFyID0gKHRoaXMgJiYgdGhpcy5fX2ltcG9ydFN0YXIpIHx8IChmdW5jdGlvbiAoKSB7XG4gICAgdmFyIG93bktleXMgPSBmdW5jdGlvbihvKSB7XG4gICAgICAgIG93bktleXMgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlOYW1lcyB8fCBmdW5jdGlvbiAobykge1xuICAgICAgICAgICAgdmFyIGFyID0gW107XG4gICAgICAgICAgICBmb3IgKHZhciBrIGluIG8pIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwobywgaykpIGFyW2FyLmxlbmd0aF0gPSBrO1xuICAgICAgICAgICAgcmV0dXJuIGFyO1xuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gb3duS2V5cyhvKTtcbiAgICB9O1xuICAgIHJldHVybiBmdW5jdGlvbiAobW9kKSB7XG4gICAgICAgIGlmIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpIHJldHVybiBtb2Q7XG4gICAgICAgIHZhciByZXN1bHQgPSB7fTtcbiAgICAgICAgaWYgKG1vZCAhPSBudWxsKSBmb3IgKHZhciBrID0gb3duS2V5cyhtb2QpLCBpID0gMDsgaSA8IGsubGVuZ3RoOyBpKyspIGlmIChrW2ldICE9PSBcImRlZmF1bHRcIikgX19jcmVhdGVCaW5kaW5nKHJlc3VsdCwgbW9kLCBrW2ldKTtcbiAgICAgICAgX19zZXRNb2R1bGVEZWZhdWx0KHJlc3VsdCwgbW9kKTtcbiAgICAgICAgcmV0dXJuIHJlc3VsdDtcbiAgICB9O1xufSkoKTtcbnZhciBfX2ltcG9ydERlZmF1bHQgPSAodGhpcyAmJiB0aGlzLl9faW1wb3J0RGVmYXVsdCkgfHwgZnVuY3Rpb24gKG1vZCkge1xuICAgIHJldHVybiAobW9kICYmIG1vZC5fX2VzTW9kdWxlKSA/IG1vZCA6IHsgXCJkZWZhdWx0XCI6IG1vZCB9O1xufTtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuRG9jdW1lbnRzQ2xpZW50ID0gdm9pZCAwO1xuY29uc3QgZm9ybV9kYXRhXzEgPSBfX2ltcG9ydERlZmF1bHQocmVxdWlyZShcImZvcm0tZGF0YVwiKSk7XG5jb25zdCB1dGlsc18xID0gcmVxdWlyZShcIi4uLy4uL3V0aWxzXCIpO1xuY29uc3QgdXRpbHNfMiA9IHJlcXVpcmUoXCIuLi8uLi91dGlsc1wiKTtcbmxldCBmcztcbmlmICh0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgZnMgPSByZXF1aXJlKFwiZnNcIik7XG59XG5jb25zdCBheGlvc18xID0gX19pbXBvcnREZWZhdWx0KHJlcXVpcmUoXCJheGlvc1wiKSk7XG5jb25zdCBvcyA9IF9faW1wb3J0U3RhcihyZXF1aXJlKFwib3NcIikpO1xuY29uc3QgcGF0aCA9IF9faW1wb3J0U3RhcihyZXF1aXJlKFwicGF0aFwiKSk7XG5jb25zdCB1dWlkXzEgPSByZXF1aXJlKFwidXVpZFwiKTtcbmNsYXNzIERvY3VtZW50c0NsaWVudCB7XG4gICAgY29uc3RydWN0b3IoY2xpZW50KSB7XG4gICAgICAgIHRoaXMuY2xpZW50ID0gY2xpZW50O1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBDcmVhdGUgYSBuZXcgZG9jdW1lbnQgZnJvbSBlaXRoZXIgYSBmaWxlIG9yIGNvbnRlbnQuXG4gICAgICpcbiAgICAgKiBOb3RlOiBBY2Nlc3MgY29udHJvbCBtaWdodCBhcHBseSBiYXNlZCBvbiB1c2VyIGxpbWl0cyAobWF4IGRvY3VtZW50cywgY2h1bmtzLCBjb2xsZWN0aW9ucykuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gZmlsZSBUaGUgZmlsZSB0byB1cGxvYWQsIGlmIGFueVxuICAgICAqIEBwYXJhbSByYXdfdGV4dCBPcHRpb25hbCByYXcgdGV4dCBjb250ZW50IHRvIHVwbG9hZCwgaWYgbm8gZmlsZSBwYXRoIGlzIHByb3ZpZGVkXG4gICAgICogQHBhcmFtIGNodW5rcyBPcHRpb25hbCBhcnJheSBvZiBwcmUtcHJvY2Vzc2VkIHRleHQgY2h1bmtzIHRvIGluZ2VzdFxuICAgICAqIEBwYXJhbSBzM1VybCBPcHRpb25hbCBwcmVzaWduZWQgUzMgVVJMIHRvIHVwbG9hZCB0aGUgZmlsZSBmcm9tLCBpZiBhbnkuXG4gICAgICogQHBhcmFtIGlkIE9wdGlvbmFsIElEIHRvIGFzc2lnbiB0byB0aGUgZG9jdW1lbnRcbiAgICAgKiBAcGFyYW0gY29sbGVjdGlvbklkcyBDb2xsZWN0aW9uIElEcyB0byBhc3NvY2lhdGUgd2l0aCB0aGUgZG9jdW1lbnQuIElmIG5vbmUgYXJlIHByb3ZpZGVkLCB0aGUgZG9jdW1lbnQgd2lsbCBiZSBhc3NpZ25lZCB0byB0aGUgdXNlcidzIGRlZmF1bHQgY29sbGVjdGlvbi5cbiAgICAgKiBAcGFyYW0gbWV0YWRhdGEgT3B0aW9uYWwgbWV0YWRhdGEgdG8gYXNzaWduIHRvIHRoZSBkb2N1bWVudFxuICAgICAqIEBwYXJhbSBpbmdlc3Rpb25Db25maWcgT3B0aW9uYWwgaW5nZXN0aW9uIGNvbmZpZ3VyYXRpb24gdG8gdXNlXG4gICAgICogQHBhcmFtIHJ1bldpdGhPcmNoZXN0cmF0aW9uIE9wdGlvbmFsIGZsYWcgdG8gcnVuIHdpdGggb3JjaGVzdHJhdGlvbiAoZGVmYXVsdDogdHJ1ZSlcbiAgICAgKiBAcGFyYW0gaW5nZXN0aW9uTW9kZSBPcHRpb25hbCBpbmdlc3Rpb24gbW9kZSAoZGVmYXVsdDogJ2N1c3RvbScpXG4gICAgICogQHJldHVybnMgUHJvbWlzZTxXcmFwcGVkSW5nZXN0aW9uUmVzcG9uc2U+XG4gICAgICovXG4gICAgYXN5bmMgY3JlYXRlKG9wdGlvbnMpIHtcbiAgICAgICAgdmFyIF9hLCBfYiwgX2M7XG4gICAgICAgIGNvbnN0IGlucHV0Q291bnQgPSBbXG4gICAgICAgICAgICBvcHRpb25zLmZpbGUsXG4gICAgICAgICAgICBvcHRpb25zLnJhd190ZXh0LFxuICAgICAgICAgICAgb3B0aW9ucy5jaHVua3MsXG4gICAgICAgICAgICBvcHRpb25zLnMzVXJsLFxuICAgICAgICBdLmZpbHRlcigoeCkgPT4geCAhPT0gdW5kZWZpbmVkKS5sZW5ndGg7XG4gICAgICAgIGlmIChpbnB1dENvdW50ID09PSAwKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJFaXRoZXIgZmlsZSwgcmF3X3RleHQsIGNodW5rcywgb3IgczNVcmwgbXVzdCBiZSBwcm92aWRlZFwiKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoaW5wdXRDb3VudCA+IDEpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIk9ubHkgb25lIG9mIGZpbGUsIHJhd190ZXh0LCBjaHVua3MsIG9yIHMzVXJsIG1heSBiZSBwcm92aWRlZFwiKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBmb3JtX2RhdGFfMS5kZWZhdWx0KCk7XG4gICAgICAgIGxldCB0ZW1wRmlsZVBhdGggPSBudWxsO1xuICAgICAgICBjb25zdCBwcm9jZXNzUGF0aCA9IGFzeW5jIChwYXRoKSA9PiB7XG4gICAgICAgICAgICBjb25zdCBhcHBlbmRGaWxlID0gKGZpbGUsIGZpbGVuYW1lKSA9PiB7XG4gICAgICAgICAgICAgICAgZm9ybURhdGEuYXBwZW5kKGBmaWxlYCwgZmlsZSwgZmlsZW5hbWUpO1xuICAgICAgICAgICAgfTtcbiAgICAgICAgICAgIGlmICh0eXBlb2YgcGF0aCA9PT0gXCJzdHJpbmdcIikge1xuICAgICAgICAgICAgICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHN0YXQgPSBhd2FpdCBmcy5wcm9taXNlcy5zdGF0KHBhdGgpO1xuICAgICAgICAgICAgICAgICAgICBpZiAoc3RhdC5pc0RpcmVjdG9yeSgpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJEaXJlY3RvcmllcyBhcmUgbm90IHN1cHBvcnRlZCBpbiBjcmVhdGUoKVwiKTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGFwcGVuZEZpbGUoZnMuY3JlYXRlUmVhZFN0cmVhbShwYXRoKSwgcGF0aC5zcGxpdChcIi9cIikucG9wKCkgfHwgXCJcIik7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihcIkZpbGUgcGF0aCBwcm92aWRlZCBpbiBicm93c2VyIGVudmlyb25tZW50LiBUaGlzIGlzIG5vdCBzdXBwb3J0ZWQuIFVzZSBhIEZpbGUgb2JqZWN0IGluc3RlYWQuXCIpO1xuICAgICAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJGaWxlIHBhdGhzIGFyZSBub3Qgc3VwcG9ydGVkIGluIHRoZSBicm93c2VyLiBVc2UgYSBGaWxlIG9iamVjdC5cIik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgZWxzZSBpZiAocGF0aCBpbnN0YW5jZW9mIEZpbGUpIHtcbiAgICAgICAgICAgICAgICBhcHBlbmRGaWxlKHBhdGgsIHBhdGgubmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBlbHNlIGlmIChcInBhdGhcIiBpbiBwYXRoICYmIFwibmFtZVwiIGluIHBhdGgpIHtcbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgICAgICAgICAgICAgICBhcHBlbmRGaWxlKGZzLmNyZWF0ZVJlYWRTdHJlYW0ocGF0aC5wYXRoKSwgcGF0aC5uYW1lKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2FybihcIkZpbGUgcGF0aCBvYmplY3QgcHJvdmlkZWQgaW4gYnJvd3NlciBlbnZpcm9ubWVudC4gVGhpcyBpcyBub3Qgc3VwcG9ydGVkLiBVc2UgYSBGaWxlIG9iamVjdCBpbnN0ZWFkLlwiKTtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRmlsZSBwYXRoIG9iamVjdHMgYXJlIG5vdCBzdXBwb3J0ZWQgaW4gdGhlIGJyb3dzZXIuIFVzZSBhIEZpbGUgb2JqZWN0LlwiKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH07XG4gICAgICAgIGlmIChvcHRpb25zLmZpbGUpIHtcbiAgICAgICAgICAgIGF3YWl0IHByb2Nlc3NQYXRoKG9wdGlvbnMuZmlsZSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAob3B0aW9ucy5yYXdfdGV4dCkge1xuICAgICAgICAgICAgZm9ybURhdGEuYXBwZW5kKFwicmF3X3RleHRcIiwgb3B0aW9ucy5yYXdfdGV4dCk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAob3B0aW9ucy5jaHVua3MpIHtcbiAgICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZChcImNodW5rc1wiLCBKU09OLnN0cmluZ2lmeShvcHRpb25zLmNodW5rcykpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2UgaWYgKG9wdGlvbnMuczNVcmwpIHtcbiAgICAgICAgICAgIC8vIERvd25sb2FkIHRoZSBTMyBmaWxlIGZpcnN0LCB0aGVuIHVwbG9hZCBpdFxuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBsZXQgcmVzcG9uc2U7XG4gICAgICAgICAgICAgICAgbGV0IGZpbGVDb250ZW50O1xuICAgICAgICAgICAgICAgIGxldCBmaWxlbmFtZTtcbiAgICAgICAgICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgICAgICAgICAgICAgICAvLyBOb2RlLmpzIGVudmlyb25tZW50XG4gICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlID0gYXdhaXQgYXhpb3NfMS5kZWZhdWx0LmdldChvcHRpb25zLnMzVXJsLCB7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXNwb25zZVR5cGU6IFwiYXJyYXlidWZmZXJcIixcbiAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIGZpbGVDb250ZW50ID0gQnVmZmVyLmZyb20ocmVzcG9uc2UuZGF0YSk7XG4gICAgICAgICAgICAgICAgICAgIGZpbGVuYW1lID0gb3B0aW9ucy5zM1VybC5zcGxpdChcIj9cIilbMF0uc3BsaXQoXCIvXCIpLnBvcCgpIHx8IFwiczNfZmlsZVwiO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB0bXBEaXIgPSBvcy50bXBkaXIoKTtcbiAgICAgICAgICAgICAgICAgICAgdGVtcEZpbGVQYXRoID0gcGF0aC5qb2luKHRtcERpciwgYHIycl9zM18ke0RhdGUubm93KCl9XyR7ZmlsZW5hbWV9YCk7XG4gICAgICAgICAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBmcy5wcm9taXNlcy53cml0ZUZpbGUodGVtcEZpbGVQYXRoLCBmaWxlQ29udGVudCk7XG4gICAgICAgICAgICAgICAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoXCJmaWxlXCIsIGZzLmNyZWF0ZVJlYWRTdHJlYW0odGVtcEZpbGVQYXRoKSwgZmlsZW5hbWUpO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGZpbmFsbHkge1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICAvLyBCcm93c2VyIGVudmlyb25tZW50XG4gICAgICAgICAgICAgICAgICAgIHJlc3BvbnNlID0gYXdhaXQgZmV0Y2gob3B0aW9ucy5zM1VybCk7XG4gICAgICAgICAgICAgICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGRvd25sb2FkIGZpbGUgZnJvbSBTMyBVUkw6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGJsb2IgPSBhd2FpdCByZXNwb25zZS5ibG9iKCk7XG4gICAgICAgICAgICAgICAgICAgIGZpbGVuYW1lID0gb3B0aW9ucy5zM1VybC5zcGxpdChcIj9cIilbMF0uc3BsaXQoXCIvXCIpLnBvcCgpIHx8IFwiczNfZmlsZVwiO1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBmaWxlID0gbmV3IEZpbGUoW2Jsb2JdLCBmaWxlbmFtZSwgeyB0eXBlOiBibG9iLnR5cGUgfSk7XG4gICAgICAgICAgICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZChcImZpbGVcIiwgZmlsZSwgZmlsZW5hbWUpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGRvd25sb2FkIGZpbGUgZnJvbSBTMyBVUkw6ICR7ZXJyb3IubWVzc2FnZX1gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBpZiAob3B0aW9ucy5pZCkge1xuICAgICAgICAgICAgZm9ybURhdGEuYXBwZW5kKFwiaWRcIiwgb3B0aW9ucy5pZCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG9wdGlvbnMubWV0YWRhdGEpIHtcbiAgICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZChcIm1ldGFkYXRhXCIsIEpTT04uc3RyaW5naWZ5KG9wdGlvbnMubWV0YWRhdGEpKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAob3B0aW9ucy5pbmdlc3Rpb25Db25maWcpIHtcbiAgICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZChcImluZ2VzdGlvbl9jb25maWdcIiwgSlNPTi5zdHJpbmdpZnkoKDAsIHV0aWxzXzIuZW5zdXJlU25ha2VDYXNlKShvcHRpb25zLmluZ2VzdGlvbkNvbmZpZykpKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoKF9hID0gb3B0aW9ucy5jb2xsZWN0aW9uSWRzKSA9PT0gbnVsbCB8fCBfYSA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2EubGVuZ3RoKSB7XG4gICAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoXCJjb2xsZWN0aW9uX2lkc1wiLCBKU09OLnN0cmluZ2lmeShvcHRpb25zLmNvbGxlY3Rpb25JZHMpKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAob3B0aW9ucy5ydW5XaXRoT3JjaGVzdHJhdGlvbiAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoXCJydW5fd2l0aF9vcmNoZXN0cmF0aW9uXCIsIFN0cmluZyhvcHRpb25zLnJ1bldpdGhPcmNoZXN0cmF0aW9uKSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG9wdGlvbnMuaW5nZXN0aW9uTW9kZSkge1xuICAgICAgICAgICAgZm9ybURhdGEuYXBwZW5kKFwiaW5nZXN0aW9uX21vZGVcIiwgb3B0aW9ucy5pbmdlc3Rpb25Nb2RlKTtcbiAgICAgICAgfVxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50Lm1ha2VSZXF1ZXN0KFwiUE9TVFwiLCBcImRvY3VtZW50c1wiLCB7XG4gICAgICAgICAgICAgICAgZGF0YTogZm9ybURhdGEsXG4gICAgICAgICAgICAgICAgaGVhZGVyczogKF9jID0gKF9iID0gZm9ybURhdGEuZ2V0SGVhZGVycykgPT09IG51bGwgfHwgX2IgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9iLmNhbGwoZm9ybURhdGEpKSAhPT0gbnVsbCAmJiBfYyAhPT0gdm9pZCAwID8gX2MgOiB7XG4gICAgICAgICAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwibXVsdGlwYXJ0L2Zvcm0tZGF0YVwiLFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgdHJhbnNmb3JtUmVxdWVzdDogW1xuICAgICAgICAgICAgICAgICAgICAoZGF0YSwgaGVhZGVycykgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGRhdGE7XG4gICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgXSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgICAgIGZpbmFsbHkge1xuICAgICAgICAgICAgaWYgKHRlbXBGaWxlUGF0aCAmJiB0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgaWYgKGZzLmV4aXN0c1N5bmModGVtcEZpbGVQYXRoKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgZnMucHJvbWlzZXMudW5saW5rKHRlbXBGaWxlUGF0aCk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgY2F0Y2ggKGNsZWFudXBFcnJvcikge1xuICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgY2xlYW5pbmcgdXAgdGVtcG9yYXJ5IGZpbGU6XCIsIGNsZWFudXBFcnJvcik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEFwcGVuZCBtZXRhZGF0YSB0byBhIGRvY3VtZW50LlxuICAgICAqXG4gICAgICogTm90ZTogVXNlcnMgY2FuIHR5cGljYWxseSBvbmx5IG1vZGlmeSBtZXRhZGF0YSBmb3IgZG9jdW1lbnRzIHRoZXkgb3duLiBTdXBlcnVzZXJzIG1heSBoYXZlIGJyb2FkZXIgYWNjZXNzLlxuICAgICAqXG4gICAgICogQHBhcmFtIGlkIElEIG9mIGRvY3VtZW50IHRvIGFwcGVuZCBtZXRhZGF0YSB0b1xuICAgICAqIEBwYXJhbSBtZXRhZGF0YSBMaXN0IG9mIG1ldGFkYXRhIGVudHJpZXMgKGtleS12YWx1ZSBwYWlycykgdG8gYXBwZW5kXG4gICAgICogQHJldHVybnMgUHJvbWlzZTxXcmFwcGVkRG9jdW1lbnRSZXNwb25zZT5cbiAgICAgKi9cbiAgICBhc3luYyBhcHBlbmRNZXRhZGF0YShvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5tYWtlUmVxdWVzdChcIlBBVENIXCIsIGBkb2N1bWVudHMvJHtvcHRpb25zLmlkfS9tZXRhZGF0YWAsIHtcbiAgICAgICAgICAgIGRhdGE6IG9wdGlvbnMubWV0YWRhdGEsXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZXBsYWNlIG1ldGFkYXRhIGZvciBhIGRvY3VtZW50LiBUaGlzIG92ZXJ3cml0ZXMgYWxsIGV4aXN0aW5nIG1ldGFkYXRhLlxuICAgICAqXG4gICAgICogTm90ZTogVXNlcnMgY2FuIHR5cGljYWxseSBvbmx5IHJlcGxhY2UgbWV0YWRhdGEgZm9yIGRvY3VtZW50cyB0aGV5IG93bi4gU3VwZXJ1c2VycyBtYXkgaGF2ZSBicm9hZGVyIGFjY2Vzcy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSBpZCBJRCBvZiBkb2N1bWVudCB0byByZXBsYWNlIG1ldGFkYXRhIGZvclxuICAgICAqIEBwYXJhbSBtZXRhZGF0YSBUaGUgbmV3IGxpc3Qgb2YgbWV0YWRhdGEgZW50cmllcyAoa2V5LXZhbHVlIHBhaXJzKVxuICAgICAqIEByZXR1cm5zIFByb21pc2U8V3JhcHBlZERvY3VtZW50UmVzcG9uc2U+XG4gICAgICovXG4gICAgYXN5bmMgcmVwbGFjZU1ldGFkYXRhKG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50Lm1ha2VSZXF1ZXN0KFwiUFVUXCIsIGBkb2N1bWVudHMvJHtvcHRpb25zLmlkfS9tZXRhZGF0YWAsIHtcbiAgICAgICAgICAgIGRhdGE6IG9wdGlvbnMubWV0YWRhdGEsXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXQgZGV0YWlscyBmb3IgYSBzcGVjaWZpYyBkb2N1bWVudCBieSBJRC5cbiAgICAgKlxuICAgICAqIE5vdGU6IFVzZXJzIGNhbiBvbmx5IHJldHJpZXZlIGRvY3VtZW50cyB0aGV5IG93biBvciBoYXZlIGFjY2VzcyB0byB0aHJvdWdoIGNvbGxlY3Rpb25zLiBTdXBlcnVzZXJzIGNhbiByZXRyaWV2ZSBhbnkgZG9jdW1lbnQuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gaWQgSUQgb2YgZG9jdW1lbnQgdG8gcmV0cmlldmVcbiAgICAgKiBAcmV0dXJucyBQcm9taXNlPFdyYXBwZWREb2N1bWVudFJlc3BvbnNlPlxuICAgICAqL1xuICAgIGFzeW5jIHJldHJpZXZlKG9wdGlvbnMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50Lm1ha2VSZXF1ZXN0KFwiR0VUXCIsIGBkb2N1bWVudHMvJHtvcHRpb25zLmlkfWApO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBMaXN0IGRvY3VtZW50cyB3aXRoIHBhZ2luYXRpb24uXG4gICAgICpcbiAgICAgKiBOb3RlOiBSZWd1bGFyIHVzZXJzIHdpbGwgb25seSBzZWUgZG9jdW1lbnRzIHRoZXkgb3duIG9yIGhhdmUgYWNjZXNzIHRvIHRocm91Z2ggY29sbGVjdGlvbnMuIFN1cGVydXNlcnMgY2FuIHNlZSBhbGwgZG9jdW1lbnRzLlxuICAgICAqXG4gICAgICogQHBhcmFtIGlkcyBPcHRpb25hbCBsaXN0IG9mIGRvY3VtZW50IElEcyB0byBmaWx0ZXIgYnlcbiAgICAgKiBAcGFyYW0gb2Zmc2V0IFNwZWNpZmllcyB0aGUgbnVtYmVyIG9mIG9iamVjdHMgdG8gc2tpcC4gRGVmYXVsdHMgdG8gMC5cbiAgICAgKiBAcGFyYW0gbGltaXQgU3BlY2lmaWVzIGEgbGltaXQgb24gdGhlIG51bWJlciBvZiBvYmplY3RzIHRvIHJldHVybiwgcmFuZ2luZyBiZXR3ZWVuIDEgYW5kIDEwMDAuIERlZmF1bHRzIHRvIDEwMC5cbiAgICAgKiBAcGFyYW0gaW5jbHVkZVN1bW1hcnlFbWJlZGRpbmdzIFNwZWNpZmllcyB3aGV0aGVyIG9yIG5vdCB0byBpbmNsdWRlIGVtYmVkZGluZ3Mgb2YgZWFjaCBkb2N1bWVudCBzdW1tYXJ5LiBEZWZhdWx0cyB0byBmYWxzZS5cbiAgICAgKiBAcGFyYW0gb3duZXJPbmx5IElmIHRydWUsIG9ubHkgcmV0dXJucyBkb2N1bWVudHMgb3duZWQgYnkgdGhlIHVzZXIsIG5vdCBhbGwgYWNjZXNzaWJsZSBkb2N1bWVudHNcbiAgICAgKiBAcmV0dXJucyBQcm9taXNlPFdyYXBwZWREb2N1bWVudHNSZXNwb25zZT5cbiAgICAgKi9cbiAgICBhc3luYyBsaXN0KG9wdGlvbnMpIHtcbiAgICAgICAgdmFyIF9hLCBfYiwgX2MsIF9kO1xuICAgICAgICBjb25zdCBwYXJhbXMgPSB7XG4gICAgICAgICAgICBvZmZzZXQ6IChfYSA9IG9wdGlvbnMgPT09IG51bGwgfHwgb3B0aW9ucyA9PT0gdm9pZCAwID8gdm9pZCAwIDogb3B0aW9ucy5vZmZzZXQpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IDAsXG4gICAgICAgICAgICBsaW1pdDogKF9iID0gb3B0aW9ucyA9PT0gbnVsbCB8fCBvcHRpb25zID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvcHRpb25zLmxpbWl0KSAhPT0gbnVsbCAmJiBfYiAhPT0gdm9pZCAwID8gX2IgOiAxMDAsXG4gICAgICAgICAgICBpbmNsdWRlX3N1bW1hcnlfZW1iZWRkaW5nczogKF9jID0gb3B0aW9ucyA9PT0gbnVsbCB8fCBvcHRpb25zID09PSB2b2lkIDAgPyB2b2lkIDAgOiBvcHRpb25zLmluY2x1ZGVTdW1tYXJ5RW1iZWRkaW5ncykgIT09IG51bGwgJiYgX2MgIT09IHZvaWQgMCA/IF9jIDogZmFsc2UsXG4gICAgICAgIH07XG4gICAgICAgIGlmICgoX2QgPSBvcHRpb25zID09PSBudWxsIHx8IG9wdGlvbnMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IG9wdGlvbnMuaWRzKSA9PT0gbnVsbCB8fCBfZCA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2QubGVuZ3RoKSB7XG4gICAgICAgICAgICBwYXJhbXMuaWRzID0gb3B0aW9ucy5pZHM7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG9wdGlvbnMgPT09IG51bGwgfHwgb3B0aW9ucyA9PT0gdm9pZCAwID8gdm9pZCAwIDogb3B0aW9ucy5vd25lck9ubHkpIHtcbiAgICAgICAgICAgIHBhcmFtcy5vd25lcl9vbmx5ID0gb3B0aW9ucy5vd25lck9ubHk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50Lm1ha2VSZXF1ZXN0KFwiR0VUXCIsIFwiZG9jdW1lbnRzXCIsIHtcbiAgICAgICAgICAgIHBhcmFtcyxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIERvd25sb2FkIGEgZG9jdW1lbnQncyBvcmlnaW5hbCBmaWxlIGNvbnRlbnQuXG4gICAgICpcbiAgICAgKiBOb3RlOiBVc2VycyBjYW4gb25seSBkb3dubG9hZCBkb2N1bWVudHMgdGhleSBvd24gb3IgaGF2ZSBhY2Nlc3MgdG8gdGhyb3VnaCBjb2xsZWN0aW9ucy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSBpZCBJRCBvZiBkb2N1bWVudCB0byBkb3dubG9hZFxuICAgICAqIEByZXR1cm5zIEJsb2IgY29udGFpbmluZyB0aGUgZG9jdW1lbnQncyBmaWxlIGNvbnRlbnRcbiAgICAgKi9cbiAgICBhc3luYyBkb3dubG9hZChvcHRpb25zKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNsaWVudC5tYWtlUmVxdWVzdChcIkdFVFwiLCBgZG9jdW1lbnRzLyR7b3B0aW9ucy5pZH0vZG93bmxvYWRgLCB7XG4gICAgICAgICAgICByZXNwb25zZVR5cGU6IFwiYXJyYXlidWZmZXJcIixcbiAgICAgICAgICAgIHJldHVybkZ1bGxSZXNwb25zZTogdHJ1ZSwgLy8gTmVlZCBmdWxsIHJlc3BvbnNlIHRvIGdldCBoZWFkZXJzXG4gICAgICAgIH0pO1xuICAgICAgICBpZiAoIXJlc3BvbnNlLmRhdGEpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIk5vIGRhdGEgcmVjZWl2ZWQgaW4gcmVzcG9uc2VcIik7XG4gICAgICAgIH1cbiAgICAgICAgLy8gRXh0cmFjdCBjb250ZW50LXR5cGUsIGRlZmF1bHQgaWYgbm90IHByZXNlbnRcbiAgICAgICAgY29uc3QgY29udGVudFR5cGUgPSAoKF9hID0gcmVzcG9uc2UuaGVhZGVycykgPT09IG51bGwgfHwgX2EgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9hW1wiY29udGVudC10eXBlXCJdKSB8fCBcImFwcGxpY2F0aW9uL29jdGV0LXN0cmVhbVwiO1xuICAgICAgICAvLyBIYW5kbGUgZGlmZmVyZW50IHBvc3NpYmxlIGRhdGEgdHlwZXMgZnJvbSBheGlvc1xuICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSBpbnN0YW5jZW9mIEJsb2IpIHtcbiAgICAgICAgICAgIC8vIElmIGl0J3MgYWxyZWFkeSBhIEJsb2IgKGxlc3MgbGlrZWx5IGZvciBhcnJheWJ1ZmZlciB0eXBlKSwgcmV0dXJuIGl0XG4gICAgICAgICAgICByZXR1cm4gcmVzcG9uc2UuZGF0YTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIGlmIChyZXNwb25zZS5kYXRhIGluc3RhbmNlb2YgQXJyYXlCdWZmZXIpIHtcbiAgICAgICAgICAgIC8vIENvbW1vbiBjYXNlIGZvciByZXNwb25zZVR5cGU6ICdhcnJheWJ1ZmZlcidcbiAgICAgICAgICAgIHJldHVybiBuZXcgQmxvYihbcmVzcG9uc2UuZGF0YV0sIHsgdHlwZTogY29udGVudFR5cGUgfSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAodHlwZW9mIHJlc3BvbnNlLmRhdGEgPT09IFwic3RyaW5nXCIpIHtcbiAgICAgICAgICAgIC8vIExlc3MgY29tbW9uLCBidXQgaGFuZGxlIGlmIGl0IHJldHVybnMgYSBzdHJpbmdcbiAgICAgICAgICAgIHJldHVybiBuZXcgQmxvYihbcmVzcG9uc2UuZGF0YV0sIHsgdHlwZTogY29udGVudFR5cGUgfSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAvLyBUcnkgY29udmVydGluZyBvdGhlciB0eXBlcyBpZiBuZWNlc3NhcnksIGZhbGxiYWNrIHRvIGVtcHR5IGJsb2JcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIG5ldyBCbG9iKFtKU09OLnN0cmluZ2lmeShyZXNwb25zZS5kYXRhKV0sIHtcbiAgICAgICAgICAgICAgICAgICAgdHlwZTogY29udGVudFR5cGUsXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJDb3VsZCBub3QgY29udmVydCByZXNwb25zZSBkYXRhIHRvIEJsb2I6XCIsIGUpO1xuICAgICAgICAgICAgICAgIHJldHVybiBuZXcgQmxvYihbXSwgeyB0eXBlOiBjb250ZW50VHlwZSB9KTsgLy8gUmV0dXJuIGVtcHR5IGJsb2IgYXMgZmFsbGJhY2tcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBFeHBvcnQgZG9jdW1lbnRzIG1ldGFkYXRhIGFzIGEgQ1NWIGZpbGUuXG4gICAgICpcbiAgICAgKiBOb3RlOiBUaGlzIG9wZXJhdGlvbiBpcyB0eXBpY2FsbHkgcmVzdHJpY3RlZCB0byBzdXBlcnVzZXJzLlxuICAgICAqXG4gICAgICogQHBhcmFtIG9wdGlvbnMgRXhwb3J0IGNvbmZpZ3VyYXRpb24gb3B0aW9uc1xuICAgICAqIEBwYXJhbSBvcHRpb25zLm91dHB1dFBhdGggUGF0aCB3aGVyZSB0aGUgQ1NWIGZpbGUgc2hvdWxkIGJlIHNhdmVkIChOb2RlLmpzIG9ubHkpLiBJZiBwcm92aWRlZCwgdGhlIGZ1bmN0aW9uIHJldHVybnMgdm9pZC5cbiAgICAgKiBAcGFyYW0gb3B0aW9ucy5jb2x1bW5zIE9wdGlvbmFsIGxpc3Qgb2Ygc3BlY2lmaWMgY29sdW1ucyB0byBpbmNsdWRlXG4gICAgICogQHBhcmFtIG9wdGlvbnMuZmlsdGVycyBPcHRpb25hbCBmaWx0ZXJzIHRvIGxpbWl0IHdoaWNoIGRvY3VtZW50cyBhcmUgZXhwb3J0ZWRcbiAgICAgKiBAcGFyYW0gb3B0aW9ucy5pbmNsdWRlSGVhZGVyIFdoZXRoZXIgdG8gaW5jbHVkZSBjb2x1bW4gaGVhZGVycyAoZGVmYXVsdDogdHJ1ZSlcbiAgICAgKiBAcmV0dXJucyBQcm9taXNlPEJsb2I+IGluIGJyb3dzZXIgZW52aXJvbm1lbnRzIChpZiBvdXRwdXRQYXRoIGlzIG5vdCBwcm92aWRlZCksIFByb21pc2U8dm9pZD4gaW4gTm9kZS5qcyAoaWYgb3V0cHV0UGF0aCBpcyBwcm92aWRlZCkuXG4gICAgICovXG4gICAgYXN5bmMgZXhwb3J0KG9wdGlvbnMgPSB7fSkge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIGNvbnN0IGRhdGEgPSB7XG4gICAgICAgICAgICBpbmNsdWRlX2hlYWRlcjogKF9hID0gb3B0aW9ucy5pbmNsdWRlSGVhZGVyKSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiB0cnVlLFxuICAgICAgICB9O1xuICAgICAgICBpZiAob3B0aW9ucy5jb2x1bW5zKSB7XG4gICAgICAgICAgICBkYXRhLmNvbHVtbnMgPSBvcHRpb25zLmNvbHVtbnM7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG9wdGlvbnMuZmlsdGVycykge1xuICAgICAgICAgICAgZGF0YS5maWx0ZXJzID0gb3B0aW9ucy5maWx0ZXJzO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5jbGllbnQubWFrZVJlcXVlc3QoXCJQT1NUXCIsIFwiZG9jdW1lbnRzL2V4cG9ydFwiLCB7XG4gICAgICAgICAgICBkYXRhLFxuICAgICAgICAgICAgcmVzcG9uc2VUeXBlOiBcImFycmF5YnVmZmVyXCIsIC8vIEV4cGVjdGluZyBiaW5hcnkgZGF0YSBmb3IgZmlsZSBzYXZpbmcgLyBCbG9iIGNyZWF0aW9uXG4gICAgICAgICAgICBoZWFkZXJzOiB7IEFjY2VwdDogXCJ0ZXh0L2NzdlwiIH0sXG4gICAgICAgICAgICByZXR1cm5GdWxsUmVzcG9uc2U6IGZhbHNlLCAvLyBXZSBqdXN0IG5lZWQgdGhlIGRhdGEgKEFycmF5QnVmZmVyKVxuICAgICAgICB9KTtcbiAgICAgICAgLy8gTm9kZSBlbnZpcm9ubWVudDogd3JpdGUgdG8gZmlsZSBpZiBvdXRwdXRQYXRoIGlzIGdpdmVuXG4gICAgICAgIGlmIChvcHRpb25zLm91dHB1dFBhdGggJiYgdHlwZW9mIHByb2Nlc3MgIT09IFwidW5kZWZpbmVkXCIgJiYgKGZzID09PSBudWxsIHx8IGZzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBmcy5wcm9taXNlcykpIHtcbiAgICAgICAgICAgIGF3YWl0IGZzLnByb21pc2VzLndyaXRlRmlsZShvcHRpb25zLm91dHB1dFBhdGgsIEJ1ZmZlci5mcm9tKHJlc3BvbnNlKSk7XG4gICAgICAgICAgICByZXR1cm47IC8vIFJldHVybiB2b2lkXG4gICAgICAgIH1cbiAgICAgICAgLy8gQnJvd3NlciBvciBOb2RlIHdpdGhvdXQgb3V0cHV0UGF0aDogcmV0dXJuIEJsb2JcbiAgICAgICAgcmV0dXJuIG5ldyBCbG9iKFtyZXNwb25zZV0sIHsgdHlwZTogXCJ0ZXh0L2NzdlwiIH0pO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBFeHBvcnQgZW50aXRpZXMgZm9yIGEgc3BlY2lmaWMgZG9jdW1lbnQgYXMgYSBDU1YgZmlsZS5cbiAgICAgKlxuICAgICAqIE5vdGU6IFRoaXMgb3BlcmF0aW9uIGlzIHR5cGljYWxseSByZXN0cmljdGVkIHRvIHN1cGVydXNlcnMgb3Igb3duZXJzIG9mIHRoZSBkb2N1bWVudC5cbiAgICAgKlxuICAgICAqIEBwYXJhbSBvcHRpb25zIEV4cG9ydCBjb25maWd1cmF0aW9uIG9wdGlvbnNcbiAgICAgKiBAcGFyYW0gb3B0aW9ucy5pZCBUaGUgSUQgb2YgdGhlIGRvY3VtZW50IHdob3NlIGVudGl0aWVzIGFyZSB0byBiZSBleHBvcnRlZC5cbiAgICAgKiBAcGFyYW0gb3B0aW9ucy5vdXRwdXRQYXRoIFBhdGggd2hlcmUgdGhlIENTViBmaWxlIHNob3VsZCBiZSBzYXZlZCAoTm9kZS5qcyBvbmx5KS4gSWYgcHJvdmlkZWQsIHRoZSBmdW5jdGlvbiByZXR1cm5zIHZvaWQuXG4gICAgICogQHBhcmFtIG9wdGlvbnMuY29sdW1ucyBPcHRpb25hbCBsaXN0IG9mIHNwZWNpZmljIGNvbHVtbnMgdG8gaW5jbHVkZVxuICAgICAqIEBwYXJhbSBvcHRpb25zLmZpbHRlcnMgT3B0aW9uYWwgZmlsdGVycyB0byBsaW1pdCB3aGljaCBlbnRpdGllcyBhcmUgZXhwb3J0ZWRcbiAgICAgKiBAcGFyYW0gb3B0aW9ucy5pbmNsdWRlSGVhZGVyIFdoZXRoZXIgdG8gaW5jbHVkZSBjb2x1bW4gaGVhZGVycyAoZGVmYXVsdDogdHJ1ZSlcbiAgICAgKiBAcmV0dXJucyBQcm9taXNlPEJsb2I+IGluIGJyb3dzZXIgZW52aXJvbm1lbnRzIChpZiBvdXRwdXRQYXRoIGlzIG5vdCBwcm92aWRlZCksIFByb21pc2U8dm9pZD4gaW4gTm9kZS5qcyAoaWYgb3V0cHV0UGF0aCBpcyBwcm92aWRlZCkuXG4gICAgICovXG4gICAgYXN5bmMgZXhwb3J0RW50aXRpZXMob3B0aW9ucykge1xuICAgICAgICB2YXIgX2E7XG4gICAgICAgIGNvbnN0IGRhdGEgPSB7XG4gICAgICAgICAgICAvLyBSb3V0ZXIgZXhwZWN0cyBJRCBpbiBwYXRoLCBub3QgYm9keS4gRGF0YSBjb250YWlucyBleHBvcnQgb3B0aW9ucy5cbiAgICAgICAgICAgIGluY2x1ZGVfaGVhZGVyOiAoX2EgPSBvcHRpb25zLmluY2x1ZGVIZWFkZXIpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IHRydWUsXG4gICAgICAgIH07XG4gICAgICAgIGlmIChvcHRpb25zLmNvbHVtbnMpIHtcbiAgICAgICAgICAgIGRhdGEuY29sdW1ucyA9IG9wdGlvbnMuY29sdW1ucztcbiAgICAgICAgfVxuICAgICAgICBpZiAob3B0aW9ucy5maWx0ZXJzKSB7XG4gICAgICAgICAgICBkYXRhLmZpbHRlcnMgPSBvcHRpb25zLmZpbHRlcnM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNsaWVudC5tYWtlUmVxdWVzdChcIlBPU1RcIiwgYGRvY3VtZW50cy8ke29wdGlvbnMuaWR9L2VudGl0aWVzL2V4cG9ydGAsIC8vIElEIGluIHBhdGhcbiAgICAgICAge1xuICAgICAgICAgICAgZGF0YSwgLy8gRXhwb3J0IG9wdGlvbnMgaW4gYm9keVxuICAgICAgICAgICAgcmVzcG9uc2VUeXBlOiBcImFycmF5YnVmZmVyXCIsXG4gICAgICAgICAgICBoZWFkZXJzOiB7IEFjY2VwdDogXCJ0ZXh0L2NzdlwiIH0sXG4gICAgICAgICAgICByZXR1cm5GdWxsUmVzcG9uc2U6IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICAgICAgLy8gTm9kZSBlbnZpcm9ubWVudDogd3JpdGUgdG8gZmlsZSBpZiBvdXRwdXRQYXRoIGlzIGdpdmVuXG4gICAgICAgIGlmIChvcHRpb25zLm91dHB1dFBhdGggJiYgdHlwZW9mIHByb2Nlc3MgIT09IFwidW5kZWZpbmVkXCIgJiYgKGZzID09PSBudWxsIHx8IGZzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBmcy5wcm9taXNlcykpIHtcbiAgICAgICAgICAgIGF3YWl0IGZzLnByb21pc2VzLndyaXRlRmlsZShvcHRpb25zLm91dHB1dFBhdGgsIEJ1ZmZlci5mcm9tKHJlc3BvbnNlKSk7XG4gICAgICAgICAgICByZXR1cm47IC8vIFJldHVybiB2b2lkXG4gICAgICAgIH1cbiAgICAgICAgLy8gQnJvd3NlciBvciBOb2RlIHdpdGhvdXQgb3V0cHV0UGF0aDogcmV0dXJuIEJsb2JcbiAgICAgICAgcmV0dXJuIG5ldyBCbG9iKFtyZXNwb25zZV0sIHsgdHlwZTogXCJ0ZXh0L2NzdlwiIH0pO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBFeHBvcnQgZW50aXRpZXMgZm9yIGEgZG9jdW1lbnQgYXMgYSBDU1YgZmlsZSBhbmQgdHJpZ2dlciBkb3dubG9hZCBpbiB0aGUgYnJvd3Nlci5cbiAgICAgKlxuICAgICAqIE5vdGU6IFRoaXMgbWV0aG9kIG9ubHkgd29ya3MgaW4gYnJvd3NlciBlbnZpcm9ubWVudHMuXG4gICAgICogTm90ZTogQWNjZXNzIGNvbnRyb2wgKHN1cGVydXNlci9vd25lcikgYXBwbGllcyBiYXNlZCBvbiB0aGUgdW5kZXJseWluZyBgZXhwb3J0RW50aXRpZXNgIGNhbGwuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gb3B0aW9ucyBFeHBvcnQgY29uZmlndXJhdGlvbiBvcHRpb25zXG4gICAgICogQHBhcmFtIG9wdGlvbnMuZmlsZW5hbWUgVGhlIGRlc2lyZWQgZmlsZW5hbWUgZm9yIHRoZSBkb3dubG9hZGVkIGZpbGUgKGUuZy4sIFwiZW50aXRpZXMuY3N2XCIpLlxuICAgICAqIEBwYXJhbSBvcHRpb25zLmlkIFRoZSBJRCBvZiB0aGUgZG9jdW1lbnQgd2hvc2UgZW50aXRpZXMgYXJlIHRvIGJlIGV4cG9ydGVkLlxuICAgICAqIEBwYXJhbSBvcHRpb25zLmNvbHVtbnMgT3B0aW9uYWwgbGlzdCBvZiBzcGVjaWZpYyBjb2x1bW5zIHRvIGluY2x1ZGVcbiAgICAgKiBAcGFyYW0gb3B0aW9ucy5maWx0ZXJzIE9wdGlvbmFsIGZpbHRlcnMgdG8gbGltaXQgd2hpY2ggZW50aXRpZXMgYXJlIGV4cG9ydGVkXG4gICAgICogQHBhcmFtIG9wdGlvbnMuaW5jbHVkZUhlYWRlciBXaGV0aGVyIHRvIGluY2x1ZGUgY29sdW1uIGhlYWRlcnMgKGRlZmF1bHQ6IHRydWUpXG4gICAgICovXG4gICAgYXN5bmMgZXhwb3J0RW50aXRpZXNUb0ZpbGUob3B0aW9ucykge1xuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKFwiZXhwb3J0RW50aXRpZXNUb0ZpbGUgaXMgaW50ZW5kZWQgZm9yIGJyb3dzZXIgZW52aXJvbm1lbnRzIG9ubHkuXCIpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIC8vIENhbGwgZXhwb3J0RW50aXRpZXMgd2l0aG91dCBvdXRwdXRQYXRoIHRvIGdldCB0aGUgQmxvYlxuICAgICAgICBjb25zdCBibG9iID0gYXdhaXQgdGhpcy5leHBvcnRFbnRpdGllcyh7XG4gICAgICAgICAgICBpZDogb3B0aW9ucy5pZCxcbiAgICAgICAgICAgIGNvbHVtbnM6IG9wdGlvbnMuY29sdW1ucyxcbiAgICAgICAgICAgIGZpbHRlcnM6IG9wdGlvbnMuZmlsdGVycyxcbiAgICAgICAgICAgIGluY2x1ZGVIZWFkZXI6IG9wdGlvbnMuaW5jbHVkZUhlYWRlcixcbiAgICAgICAgfSk7XG4gICAgICAgIGlmIChibG9iIGluc3RhbmNlb2YgQmxvYikge1xuICAgICAgICAgICAgKDAsIHV0aWxzXzEuZG93bmxvYWRCbG9iKShibG9iLCBvcHRpb25zLmZpbGVuYW1lKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIC8vIFRoaXMgY2FzZSBzaG91bGQgbm90IGhhcHBlbiBpZiBvdXRwdXRQYXRoIGlzIHVuZGVmaW5lZCwgYnV0IGhhbmRsZSBkZWZlbnNpdmVseVxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkV4cGVjdGVkIGEgQmxvYiBidXQgcmVjZWl2ZWQgdm9pZC4gRGlkIHlvdSBhY2NpZGVudGFsbHkgcHJvdmlkZSBhbiBvdXRwdXRQYXRoIGluIGEgYnJvd3NlciBjb250ZXh0P1wiKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBFeHBvcnQgcmVsYXRpb25zaGlwcyBmb3IgYSBzcGVjaWZpYyBkb2N1bWVudCBhcyBhIENTViBmaWxlLlxuICAgICAqXG4gICAgICogTm90ZTogVGhpcyBvcGVyYXRpb24gaXMgdHlwaWNhbGx5IHJlc3RyaWN0ZWQgdG8gc3VwZXJ1c2VycyBvciBvd25lcnMgb2YgdGhlIGRvY3VtZW50LlxuICAgICAqXG4gICAgICogQHBhcmFtIG9wdGlvbnMgRXhwb3J0IGNvbmZpZ3VyYXRpb24gb3B0aW9uc1xuICAgICAqIEBwYXJhbSBvcHRpb25zLmlkIFRoZSBJRCBvZiB0aGUgZG9jdW1lbnQgd2hvc2UgcmVsYXRpb25zaGlwcyBhcmUgdG8gYmUgZXhwb3J0ZWQuXG4gICAgICogQHBhcmFtIG9wdGlvbnMub3V0cHV0UGF0aCBQYXRoIHdoZXJlIHRoZSBDU1YgZmlsZSBzaG91bGQgYmUgc2F2ZWQgKE5vZGUuanMgb25seSkuIElmIHByb3ZpZGVkLCB0aGUgZnVuY3Rpb24gcmV0dXJucyB2b2lkLlxuICAgICAqIEBwYXJhbSBvcHRpb25zLmNvbHVtbnMgT3B0aW9uYWwgbGlzdCBvZiBzcGVjaWZpYyBjb2x1bW5zIHRvIGluY2x1ZGVcbiAgICAgKiBAcGFyYW0gb3B0aW9ucy5maWx0ZXJzIE9wdGlvbmFsIGZpbHRlcnMgdG8gbGltaXQgd2hpY2ggcmVsYXRpb25zaGlwcyBhcmUgZXhwb3J0ZWRcbiAgICAgKiBAcGFyYW0gb3B0aW9ucy5pbmNsdWRlSGVhZGVyIFdoZXRoZXIgdG8gaW5jbHVkZSBjb2x1bW4gaGVhZGVycyAoZGVmYXVsdDogdHJ1ZSlcbiAgICAgKiBAcmV0dXJucyBQcm9taXNlPEJsb2I+IGluIGJyb3dzZXIgZW52aXJvbm1lbnRzIChpZiBvdXRwdXRQYXRoIGlzIG5vdCBwcm92aWRlZCksIFByb21pc2U8dm9pZD4gaW4gTm9kZS5qcyAoaWYgb3V0cHV0UGF0aCBpcyBwcm92aWRlZCkuXG4gICAgICovXG4gICAgYXN5bmMgZXhwb3J0UmVsYXRpb25zaGlwcyhvcHRpb25zKSB7XG4gICAgICAgIHZhciBfYTtcbiAgICAgICAgY29uc3QgZGF0YSA9IHtcbiAgICAgICAgICAgIGluY2x1ZGVfaGVhZGVyOiAoX2EgPSBvcHRpb25zLmluY2x1ZGVIZWFkZXIpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IHRydWUsXG4gICAgICAgIH07XG4gICAgICAgIGlmIChvcHRpb25zLmNvbHVtbnMpIHtcbiAgICAgICAgICAgIGRhdGEuY29sdW1ucyA9IG9wdGlvbnMuY29sdW1ucztcbiAgICAgICAgfVxuICAgICAgICBpZiAob3B0aW9ucy5maWx0ZXJzKSB7XG4gICAgICAgICAgICBkYXRhLmZpbHRlcnMgPSBvcHRpb25zLmZpbHRlcnM7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNsaWVudC5tYWtlUmVxdWVzdChcIlBPU1RcIiwgYGRvY3VtZW50cy8ke29wdGlvbnMuaWR9L3JlbGF0aW9uc2hpcHMvZXhwb3J0YCwgLy8gSUQgaW4gcGF0aFxuICAgICAgICB7XG4gICAgICAgICAgICBkYXRhLCAvLyBFeHBvcnQgb3B0aW9ucyBpbiBib2R5XG4gICAgICAgICAgICByZXNwb25zZVR5cGU6IFwiYXJyYXlidWZmZXJcIixcbiAgICAgICAgICAgIGhlYWRlcnM6IHsgQWNjZXB0OiBcInRleHQvY3N2XCIgfSxcbiAgICAgICAgICAgIHJldHVybkZ1bGxSZXNwb25zZTogZmFsc2UsXG4gICAgICAgIH0pO1xuICAgICAgICAvLyBOb2RlIGVudmlyb25tZW50OiB3cml0ZSB0byBmaWxlIGlmIG91dHB1dFBhdGggaXMgZ2l2ZW5cbiAgICAgICAgaWYgKG9wdGlvbnMub3V0cHV0UGF0aCAmJiB0eXBlb2YgcHJvY2VzcyAhPT0gXCJ1bmRlZmluZWRcIiAmJiAoZnMgPT09IG51bGwgfHwgZnMgPT09IHZvaWQgMCA/IHZvaWQgMCA6IGZzLnByb21pc2VzKSkge1xuICAgICAgICAgICAgYXdhaXQgZnMucHJvbWlzZXMud3JpdGVGaWxlKG9wdGlvbnMub3V0cHV0UGF0aCwgQnVmZmVyLmZyb20ocmVzcG9uc2UpKTtcbiAgICAgICAgICAgIHJldHVybjsgLy8gUmV0dXJuIHZvaWRcbiAgICAgICAgfVxuICAgICAgICAvLyBCcm93c2VyIG9yIE5vZGUgd2l0aG91dCBvdXRwdXRQYXRoOiByZXR1cm4gQmxvYlxuICAgICAgICByZXR1cm4gbmV3IEJsb2IoW3Jlc3BvbnNlXSwgeyB0eXBlOiBcInRleHQvY3N2XCIgfSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEV4cG9ydCByZWxhdGlvbnNoaXBzIGZvciBhIGRvY3VtZW50IGFzIGEgQ1NWIGZpbGUgYW5kIHRyaWdnZXIgZG93bmxvYWQgaW4gdGhlIGJyb3dzZXIuXG4gICAgICpcbiAgICAgKiBOb3RlOiBUaGlzIG1ldGhvZCBvbmx5IHdvcmtzIGluIGJyb3dzZXIgZW52aXJvbm1lbnRzLlxuICAgICAqIE5vdGU6IEFjY2VzcyBjb250cm9sIChzdXBlcnVzZXIvb3duZXIpIGFwcGxpZXMgYmFzZWQgb24gdGhlIHVuZGVybHlpbmcgYGV4cG9ydFJlbGF0aW9uc2hpcHNgIGNhbGwuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gb3B0aW9ucyBFeHBvcnQgY29uZmlndXJhdGlvbiBvcHRpb25zXG4gICAgICogQHBhcmFtIG9wdGlvbnMuZmlsZW5hbWUgVGhlIGRlc2lyZWQgZmlsZW5hbWUgZm9yIHRoZSBkb3dubG9hZGVkIGZpbGUgKGUuZy4sIFwicmVsYXRpb25zaGlwcy5jc3ZcIikuXG4gICAgICogQHBhcmFtIG9wdGlvbnMuaWQgVGhlIElEIG9mIHRoZSBkb2N1bWVudCB3aG9zZSByZWxhdGlvbnNoaXBzIGFyZSB0byBiZSBleHBvcnRlZC5cbiAgICAgKiBAcGFyYW0gb3B0aW9ucy5jb2x1bW5zIE9wdGlvbmFsIGxpc3Qgb2Ygc3BlY2lmaWMgY29sdW1ucyB0byBpbmNsdWRlXG4gICAgICogQHBhcmFtIG9wdGlvbnMuZmlsdGVycyBPcHRpb25hbCBmaWx0ZXJzIHRvIGxpbWl0IHdoaWNoIHJlbGF0aW9uc2hpcHMgYXJlIGV4cG9ydGVkXG4gICAgICogQHBhcmFtIG9wdGlvbnMuaW5jbHVkZUhlYWRlciBXaGV0aGVyIHRvIGluY2x1ZGUgY29sdW1uIGhlYWRlcnMgKGRlZmF1bHQ6IHRydWUpXG4gICAgICovXG4gICAgYXN5bmMgZXhwb3J0UmVsYXRpb25zaGlwc1RvRmlsZShvcHRpb25zKSB7XG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ID09PSBcInVuZGVmaW5lZFwiKSB7XG4gICAgICAgICAgICBjb25zb2xlLndhcm4oXCJleHBvcnRSZWxhdGlvbnNoaXBzVG9GaWxlIGlzIGludGVuZGVkIGZvciBicm93c2VyIGVudmlyb25tZW50cyBvbmx5LlwiKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBibG9iID0gYXdhaXQgdGhpcy5leHBvcnRSZWxhdGlvbnNoaXBzKHtcbiAgICAgICAgICAgIGlkOiBvcHRpb25zLmlkLFxuICAgICAgICAgICAgY29sdW1uczogb3B0aW9ucy5jb2x1bW5zLFxuICAgICAgICAgICAgZmlsdGVyczogb3B0aW9ucy5maWx0ZXJzLFxuICAgICAgICAgICAgaW5jbHVkZUhlYWRlcjogb3B0aW9ucy5pbmNsdWRlSGVhZGVyLFxuICAgICAgICB9KTtcbiAgICAgICAgaWYgKGJsb2IgaW5zdGFuY2VvZiBCbG9iKSB7XG4gICAgICAgICAgICAoMCwgdXRpbHNfMS5kb3dubG9hZEJsb2IpKGJsb2IsIG9wdGlvbnMuZmlsZW5hbWUpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkV4cGVjdGVkIGEgQmxvYiBidXQgcmVjZWl2ZWQgdm9pZC4gRGlkIHlvdSBhY2NpZGVudGFsbHkgcHJvdmlkZSBhbiBvdXRwdXRQYXRoIGluIGEgYnJvd3NlciBjb250ZXh0P1wiKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICAvKipcbiAgICAgKiBEb3dubG9hZCBtdWx0aXBsZSBkb2N1bWVudHMgYXMgYSB6aXAgZmlsZS5cbiAgICAgKlxuICAgICAqIE5vdGU6IEFjY2VzcyBjb250cm9sIGFwcGxpZXMuIE5vbi1zdXBlcnVzZXJzIG1pZ2h0IGJlIHJlc3RyaWN0ZWQgdG8gZXhwb3J0aW5nIG9ubHkgZG9jdW1lbnRzIHRoZXkgb3duIG9yIGhhdmUgYWNjZXNzIHRvLCBhbmQgbWlnaHQgYmUgcmVxdWlyZWQgdG8gcHJvdmlkZSBkb2N1bWVudCBJRHMuIFN1cGVydXNlcnMgY2FuIHR5cGljYWxseSBleHBvcnQgYW55IGRvY3VtZW50cy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSBvcHRpb25zIENvbmZpZ3VyYXRpb24gb3B0aW9ucyBmb3IgdGhlIHppcCBkb3dubG9hZFxuICAgICAqIEBwYXJhbSBvcHRpb25zLmRvY3VtZW50SWRzIE9wdGlvbmFsIGxpc3Qgb2YgZG9jdW1lbnQgSURzIHRvIGluY2x1ZGUuIE1heSBiZSByZXF1aXJlZCBmb3Igbm9uLXN1cGVydXNlcnMuXG4gICAgICogQHBhcmFtIG9wdGlvbnMuc3RhcnREYXRlIE9wdGlvbmFsIGZpbHRlciBmb3IgZG9jdW1lbnRzIGNyZWF0ZWQgb24gb3IgYWZ0ZXIgdGhpcyBkYXRlLlxuICAgICAqIEBwYXJhbSBvcHRpb25zLmVuZERhdGUgT3B0aW9uYWwgZmlsdGVyIGZvciBkb2N1bWVudHMgY3JlYXRlZCBvbiBvciBiZWZvcmUgdGhpcyBkYXRlLlxuICAgICAqIEBwYXJhbSBvcHRpb25zLm91dHB1dFBhdGggT3B0aW9uYWwgcGF0aCB0byBzYXZlIHRoZSB6aXAgZmlsZSAoTm9kZS5qcyBvbmx5KS4gSWYgcHJvdmlkZWQsIHRoZSBmdW5jdGlvbiByZXR1cm5zIHZvaWQuXG4gICAgICogQHJldHVybnMgUHJvbWlzZTxCbG9iPiBpbiBicm93c2VyIGVudmlyb25tZW50cyAoaWYgb3V0cHV0UGF0aCBpcyBub3QgcHJvdmlkZWQpLCBQcm9taXNlPHZvaWQ+IGluIE5vZGUuanMgKGlmIG91dHB1dFBhdGggaXMgcHJvdmlkZWQpLlxuICAgICAqL1xuICAgIGFzeW5jIGRvd25sb2FkWmlwKG9wdGlvbnMpIHtcbiAgICAgICAgdmFyIF9hO1xuICAgICAgICBjb25zdCBwYXJhbXMgPSB7fTtcbiAgICAgICAgaWYgKChfYSA9IG9wdGlvbnMuZG9jdW1lbnRJZHMpID09PSBudWxsIHx8IF9hID09PSB2b2lkIDAgPyB2b2lkIDAgOiBfYS5sZW5ndGgpIHtcbiAgICAgICAgICAgIC8vIFBhc3MgYXMgYXJyYXksIGJhY2tlbmQgZXhwZWN0cyBsaXN0XG4gICAgICAgICAgICBwYXJhbXMuZG9jdW1lbnRfaWRzID0gb3B0aW9ucy5kb2N1bWVudElkcztcbiAgICAgICAgfVxuICAgICAgICBpZiAob3B0aW9ucy5zdGFydERhdGUpIHtcbiAgICAgICAgICAgIHBhcmFtcy5zdGFydF9kYXRlID0gb3B0aW9ucy5zdGFydERhdGUudG9JU09TdHJpbmcoKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAob3B0aW9ucy5lbmREYXRlKSB7XG4gICAgICAgICAgICBwYXJhbXMuZW5kX2RhdGUgPSBvcHRpb25zLmVuZERhdGUudG9JU09TdHJpbmcoKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMuY2xpZW50Lm1ha2VSZXF1ZXN0KFwiR0VUXCIsIFwiZG9jdW1lbnRzL2Rvd25sb2FkX3ppcFwiLCB7XG4gICAgICAgICAgICBwYXJhbXMsXG4gICAgICAgICAgICByZXNwb25zZVR5cGU6IFwiYXJyYXlidWZmZXJcIixcbiAgICAgICAgICAgIGhlYWRlcnM6IHsgQWNjZXB0OiBcImFwcGxpY2F0aW9uL3ppcFwiIH0sIC8vIENvcnJlY3QgbWltZSB0eXBlXG4gICAgICAgICAgICByZXR1cm5GdWxsUmVzcG9uc2U6IGZhbHNlLFxuICAgICAgICB9KTtcbiAgICAgICAgLy8gTm9kZSBlbnZpcm9ubWVudDogd3JpdGUgdG8gZmlsZSBpZiBvdXRwdXRQYXRoIGlzIGdpdmVuXG4gICAgICAgIGlmIChvcHRpb25zLm91dHB1dFBhdGggJiYgdHlwZW9mIHByb2Nlc3MgIT09IFwidW5kZWZpbmVkXCIgJiYgKGZzID09PSBudWxsIHx8IGZzID09PSB2b2lkIDAgPyB2b2lkIDAgOiBmcy5wcm9taXNlcykpIHtcbiAgICAgICAgICAgIGF3YWl0IGZzLnByb21pc2VzLndyaXRlRmlsZShvcHRpb25zLm91dHB1dFBhdGgsIEJ1ZmZlci5mcm9tKHJlc3BvbnNlKSk7XG4gICAgICAgICAgICByZXR1cm47IC8vIFJldHVybiB2b2lkXG4gICAgICAgIH1cbiAgICAgICAgLy8gQnJvd3NlciBvciBOb2RlIHdpdGhvdXQgb3V0cHV0UGF0aDogcmV0dXJuIEJsb2JcbiAgICAgICAgcmV0dXJuIG5ldyBCbG9iKFtyZXNwb25zZV0sIHsgdHlwZTogXCJhcHBsaWNhdGlvbi96aXBcIiB9KTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogRG93bmxvYWQgbXVsdGlwbGUgZG9jdW1lbnRzIGFzIGEgemlwIGZpbGUgYW5kIHRyaWdnZXIgZG93bmxvYWQgaW4gdGhlIGJyb3dzZXIuXG4gICAgICpcbiAgICAgKiBOb3RlOiBUaGlzIG1ldGhvZCBvbmx5IHdvcmtzIGluIGJyb3dzZXIgZW52aXJvbm1lbnRzLlxuICAgICAqIE5vdGU6IEFjY2VzcyBjb250cm9sIGFwcGxpZXMgYmFzZWQgb24gdGhlIHVuZGVybHlpbmcgYGRvd25sb2FkWmlwYCBjYWxsLlxuICAgICAqXG4gICAgICogQHBhcmFtIG9wdGlvbnMgQ29uZmlndXJhdGlvbiBvcHRpb25zIGZvciB0aGUgemlwIGRvd25sb2FkXG4gICAgICogQHBhcmFtIG9wdGlvbnMuZmlsZW5hbWUgVGhlIGRlc2lyZWQgZmlsZW5hbWUgZm9yIHRoZSBkb3dubG9hZGVkIHppcCBmaWxlIChlLmcuLCBcImRvY3VtZW50cy56aXBcIikuXG4gICAgICogQHBhcmFtIG9wdGlvbnMuZG9jdW1lbnRJZHMgT3B0aW9uYWwgbGlzdCBvZiBkb2N1bWVudCBJRHMgdG8gaW5jbHVkZS5cbiAgICAgKiBAcGFyYW0gb3B0aW9ucy5zdGFydERhdGUgT3B0aW9uYWwgZmlsdGVyIGZvciBkb2N1bWVudHMgY3JlYXRlZCBvbiBvciBhZnRlciB0aGlzIGRhdGUuXG4gICAgICogQHBhcmFtIG9wdGlvbnMuZW5kRGF0ZSBPcHRpb25hbCBmaWx0ZXIgZm9yIGRvY3VtZW50cyBjcmVhdGVkIG9uIG9yIGJlZm9yZSB0aGlzIGRhdGUuXG4gICAgICovXG4gICAgYXN5bmMgZG93bmxvYWRaaXBUb0ZpbGUob3B0aW9ucykge1xuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyA9PT0gXCJ1bmRlZmluZWRcIikge1xuICAgICAgICAgICAgY29uc29sZS53YXJuKFwiZG93bmxvYWRaaXBUb0ZpbGUgaXMgaW50ZW5kZWQgZm9yIGJyb3dzZXIgZW52aXJvbm1lbnRzIG9ubHkuXCIpO1xuICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGJsb2IgPSBhd2FpdCB0aGlzLmRvd25sb2FkWmlwKHtcbiAgICAgICAgICAgIGRvY3VtZW50SWRzOiBvcHRpb25zLmRvY3VtZW50SWRzLFxuICAgICAgICAgICAgc3RhcnREYXRlOiBvcHRpb25zLnN0YXJ0RGF0ZSxcbiAgICAgICAgICAgIGVuZERhdGU6IG9wdGlvbnMuZW5kRGF0ZSxcbiAgICAgICAgfSk7XG4gICAgICAgIGlmIChibG9iIGluc3RhbmNlb2YgQmxvYikge1xuICAgICAgICAgICAgKDAsIHV0aWxzXzEuZG93bmxvYWRCbG9iKShibG9iLCBvcHRpb25zLmZpbGVuYW1lKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFeHBlY3RlZCBhIEJsb2IgYnV0IHJlY2VpdmVkIHZvaWQuIERpZCB5b3UgYWNjaWRlbnRhbGx5IHByb3ZpZGUgYW4gb3V0cHV0UGF0aCBpbiBhIGJyb3dzZXIgY29udGV4dD9cIik7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogRXhwb3J0IGRvY3VtZW50cyBtZXRhZGF0YSBhcyBhIENTViBmaWxlIGFuZCB0cmlnZ2VyIGRvd25sb2FkIGluIHRoZSBicm93c2VyLlxuICAgICAqXG4gICAgICogTm90ZTogVGhpcyBtZXRob2Qgb25seSB3b3JrcyBpbiBicm93c2VyIGVudmlyb25tZW50cy5cbiAgICAgKiBOb3RlOiBBY2Nlc3MgY29udHJvbCAoc3VwZXJ1c2VyKSBhcHBsaWVzIGJhc2VkIG9uIHRoZSB1bmRlcmx5aW5nIGBleHBvcnRgIGNhbGwuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gb3B0aW9ucyBFeHBvcnQgY29uZmlndXJhdGlvbiBvcHRpb25zXG4gICAgICogQHBhcmFtIG9wdGlvbnMuZmlsZW5hbWUgVGhlIGRlc2lyZWQgZmlsZW5hbWUgZm9yIHRoZSBkb3dubG9hZGVkIENTViBmaWxlIChlLmcuLCBcImV4cG9ydC5jc3ZcIikuXG4gICAgICogQHBhcmFtIG9wdGlvbnMuY29sdW1ucyBPcHRpb25hbCBsaXN0IG9mIHNwZWNpZmljIGNvbHVtbnMgdG8gaW5jbHVkZVxuICAgICAqIEBwYXJhbSBvcHRpb25zLmZpbHRlcnMgT3B0aW9uYWwgZmlsdGVycyB0byBsaW1pdCB3aGljaCBkb2N1bWVudHMgYXJlIGV4cG9ydGVkXG4gICAgICogQHBhcmFtIG9wdGlvbnMuaW5jbHVkZUhlYWRlciBXaGV0aGVyIHRvIGluY2x1ZGUgY29sdW1uIGhlYWRlcnMgKGRlZmF1bHQ6IHRydWUpXG4gICAgICovXG4gICAgYXN5bmMgZXhwb3J0VG9GaWxlKG9wdGlvbnMpIHtcbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgPT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2FybihcImV4cG9ydFRvRmlsZSBpcyBpbnRlbmRlZCBmb3IgYnJvd3NlciBlbnZpcm9ubWVudHMgb25seS5cIik7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgYmxvYiA9IGF3YWl0IHRoaXMuZXhwb3J0KHtcbiAgICAgICAgICAgIGNvbHVtbnM6IG9wdGlvbnMuY29sdW1ucyxcbiAgICAgICAgICAgIGZpbHRlcnM6IG9wdGlvbnMuZmlsdGVycyxcbiAgICAgICAgICAgIGluY2x1ZGVIZWFkZXI6IG9wdGlvbnMuaW5jbHVkZUhlYWRlcixcbiAgICAgICAgfSk7XG4gICAgICAgIGlmIChibG9iIGluc3RhbmNlb2YgQmxvYikge1xuICAgICAgICAgICAgKDAsIHV0aWxzXzEuZG93bmxvYWRCbG9iKShibG9iLCBvcHRpb25zLmZpbGVuYW1lKTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFeHBlY3RlZCBhIEJsb2IgYnV0IHJlY2VpdmVkIHZvaWQuIERpZCB5b3UgYWNjaWRlbnRhbGx5IHByb3ZpZGUgYW4gb3V0cHV0UGF0aCBpbiBhIGJyb3dzZXIgY29udGV4dD9cIik7XG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogRGVsZXRlIGEgc3BlY2lmaWMgZG9jdW1lbnQgYnkgSUQuIFRoaXMgYWxzbyBkZWxldGVzIGFzc29jaWF0ZWQgY2h1bmtzLlxuICAgICAqXG4gICAgICogTm90ZTogVXNlcnMgY2FuIHR5cGljYWxseSBvbmx5IGRlbGV0ZSBkb2N1bWVudHMgdGhleSBvd24uIFN1cGVydXNlcnMgbWF5IGhhdmUgYnJvYWRlciBhY2Nlc3MuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gaWQgSUQgb2YgZG9jdW1lbnQgdG8gZGVsZXRlXG4gICAgICogQHJldHVybnMgUHJvbWlzZTxXcmFwcGVkQm9vbGVhblJlc3BvbnNlPlxuICAgICAqL1xuICAgIGFzeW5jIGRlbGV0ZShvcHRpb25zKSB7XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5tYWtlUmVxdWVzdChcIkRFTEVURVwiLCBgZG9jdW1lbnRzLyR7b3B0aW9ucy5pZH1gKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IGNodW5rcyBmb3IgYSBzcGVjaWZpYyBkb2N1bWVudC5cbiAgICAgKlxuICAgICAqIE5vdGU6IFVzZXJzIGNhbiBvbmx5IGFjY2VzcyBjaHVua3MgZnJvbSBkb2N1bWVudHMgdGhleSBvd24gb3IgaGF2ZSBhY2Nlc3MgdG8gdGhyb3VnaCBjb2xsZWN0aW9ucy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSBpZCBEb2N1bWVudCBJRCB0byByZXRyaWV2ZSBjaHVua3MgZm9yXG4gICAgICogQHBhcmFtIGluY2x1ZGVWZWN0b3JzIFdoZXRoZXIgdG8gaW5jbHVkZSB2ZWN0b3IgZW1iZWRkaW5ncyBpbiB0aGUgcmVzcG9uc2UgKGRlZmF1bHQ6IGZhbHNlKVxuICAgICAqIEBwYXJhbSBvZmZzZXQgU3BlY2lmaWVzIHRoZSBudW1iZXIgb2Ygb2JqZWN0cyB0byBza2lwLiBEZWZhdWx0cyB0byAwLlxuICAgICAqIEBwYXJhbSBsaW1pdCBTcGVjaWZpZXMgYSBsaW1pdCBvbiB0aGUgbnVtYmVyIG9mIG9iamVjdHMgdG8gcmV0dXJuLCByYW5naW5nIGJldHdlZW4gMSBhbmQgMTAwMC4gRGVmYXVsdHMgdG8gMTAwLlxuICAgICAqIEByZXR1cm5zIFByb21pc2U8V3JhcHBlZENodW5rc1Jlc3BvbnNlPlxuICAgICAqL1xuICAgIGFzeW5jIGxpc3RDaHVua3Mob3B0aW9ucykge1xuICAgICAgICB2YXIgX2EsIF9iLCBfYztcbiAgICAgICAgY29uc3QgcGFyYW1zID0ge1xuICAgICAgICAgICAgLy8gTWFwIHRvIHNuYWtlX2Nhc2UgZm9yIHRoZSBBUElcbiAgICAgICAgICAgIGluY2x1ZGVfdmVjdG9yczogKF9hID0gb3B0aW9ucy5pbmNsdWRlVmVjdG9ycykgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogZmFsc2UsXG4gICAgICAgICAgICBvZmZzZXQ6IChfYiA9IG9wdGlvbnMub2Zmc2V0KSAhPT0gbnVsbCAmJiBfYiAhPT0gdm9pZCAwID8gX2IgOiAwLFxuICAgICAgICAgICAgbGltaXQ6IChfYyA9IG9wdGlvbnMubGltaXQpICE9PSBudWxsICYmIF9jICE9PSB2b2lkIDAgPyBfYyA6IDEwMCxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50Lm1ha2VSZXF1ZXN0KFwiR0VUXCIsIGBkb2N1bWVudHMvJHtvcHRpb25zLmlkfS9jaHVua3NgLCB7XG4gICAgICAgICAgICBwYXJhbXMsXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBMaXN0IGNvbGxlY3Rpb25zIGFzc29jaWF0ZWQgd2l0aCBhIHNwZWNpZmljIGRvY3VtZW50LlxuICAgICAqXG4gICAgICogTm90ZTogVGhpcyBlbmRwb2ludCBtaWdodCBiZSByZXN0cmljdGVkIHRvIHN1cGVydXNlcnMgZGVwZW5kaW5nIG9uIEFQSSBpbXBsZW1lbnRhdGlvbi4gQ2hlY2sgQVBJIGRvY3VtZW50YXRpb24uXG4gICAgICpcbiAgICAgKiBAcGFyYW0gaWQgSUQgb2YgZG9jdW1lbnQgdG8gcmV0cmlldmUgY29sbGVjdGlvbnMgZm9yXG4gICAgICogQHBhcmFtIG9mZnNldCBTcGVjaWZpZXMgdGhlIG51bWJlciBvZiBvYmplY3RzIHRvIHNraXAuIERlZmF1bHRzIHRvIDAuXG4gICAgICogQHBhcmFtIGxpbWl0IFNwZWNpZmllcyBhIGxpbWl0IG9uIHRoZSBudW1iZXIgb2Ygb2JqZWN0cyB0byByZXR1cm4sIHJhbmdpbmcgYmV0d2VlbiAxIGFuZCAxMDAwLiBEZWZhdWx0cyB0byAxMDAuXG4gICAgICogQHJldHVybnMgUHJvbWlzZTxXcmFwcGVkQ29sbGVjdGlvbnNSZXNwb25zZT5cbiAgICAgKi9cbiAgICBhc3luYyBsaXN0Q29sbGVjdGlvbnMob3B0aW9ucykge1xuICAgICAgICB2YXIgX2EsIF9iO1xuICAgICAgICBjb25zdCBwYXJhbXMgPSB7XG4gICAgICAgICAgICBvZmZzZXQ6IChfYSA9IG9wdGlvbnMub2Zmc2V0KSAhPT0gbnVsbCAmJiBfYSAhPT0gdm9pZCAwID8gX2EgOiAwLFxuICAgICAgICAgICAgbGltaXQ6IChfYiA9IG9wdGlvbnMubGltaXQpICE9PSBudWxsICYmIF9iICE9PSB2b2lkIDAgPyBfYiA6IDEwMCxcbiAgICAgICAgfTtcbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50Lm1ha2VSZXF1ZXN0KFwiR0VUXCIsIGBkb2N1bWVudHMvJHtvcHRpb25zLmlkfS9jb2xsZWN0aW9uc2AsIHtcbiAgICAgICAgICAgIHBhcmFtcyxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIERlbGV0ZSBkb2N1bWVudHMgYmFzZWQgb24gbWV0YWRhdGEgZmlsdGVycy5cbiAgICAgKlxuICAgICAqIE5vdGU6IEZvciBub24tc3VwZXJ1c2VycywgZGVsZXRpb24gaXMgaW1wbGljaXRseSBsaW1pdGVkIHRvIGRvY3VtZW50cyBvd25lZCBieSB0aGUgdXNlciwgaW4gYWRkaXRpb24gdG8gdGhlIHByb3ZpZGVkIGZpbHRlcnMuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gZmlsdGVycyBGaWx0ZXJzIHRvIGFwcGx5IHdoZW4gc2VsZWN0aW5nIGRvY3VtZW50cyB0byBkZWxldGUgKGUuZy4sIGB7IFwibWV0YWRhdGEueWVhclwiOiB7IFwiJGx0XCI6IDIwMjAgfSB9YClcbiAgICAgKiBAcmV0dXJucyBQcm9taXNlPFdyYXBwZWRCb29sZWFuUmVzcG9uc2U+XG4gICAgICovXG4gICAgYXN5bmMgZGVsZXRlQnlGaWx0ZXIob3B0aW9ucykge1xuICAgICAgICAvLyBGaWx0ZXJzIGFyZSBzZW50IGluIHRoZSByZXF1ZXN0IGJvZHkgYXMgSlNPTlxuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQubWFrZVJlcXVlc3QoXCJERUxFVEVcIiwgXCJkb2N1bWVudHMvYnktZmlsdGVyXCIsIHtcbiAgICAgICAgICAgIGRhdGE6IG9wdGlvbnMuZmlsdGVycyxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFRyaWdnZXJzIHRoZSBleHRyYWN0aW9uIG9mIGVudGl0aWVzIGFuZCByZWxhdGlvbnNoaXBzIGZyb20gYSBkb2N1bWVudC5cbiAgICAgKlxuICAgICAqIE5vdGU6IFVzZXJzIHR5cGljYWxseSBuZWVkIHRvIG93biB0aGUgZG9jdW1lbnQgdG8gdHJpZ2dlciBleHRyYWN0aW9uLiBTdXBlcnVzZXJzIG1heSBoYXZlIGJyb2FkZXIgYWNjZXNzLlxuICAgICAqIFRoaXMgaXMgb2Z0ZW4gYW4gYXN5bmNocm9ub3VzIHByb2Nlc3MuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gaWQgSUQgb2YgdGhlIGRvY3VtZW50IHRvIGV4dHJhY3QgZnJvbS5cbiAgICAgKiBAcGFyYW0gc2V0dGluZ3MgT3B0aW9uYWwgc2V0dGluZ3MgdG8gb3ZlcnJpZGUgdGhlIGRlZmF1bHQgZXh0cmFjdGlvbiBjb25maWd1cmF0aW9uLlxuICAgICAqIEBwYXJhbSBydW5XaXRoT3JjaGVzdHJhdGlvbiBXaGV0aGVyIHRvIHJ1biB3aXRoIG9yY2hlc3RyYXRpb24gKHJlY29tbWVuZGVkLCBkZWZhdWx0OiB0cnVlKS5cbiAgICAgKiBAcmV0dXJucyBQcm9taXNlPFdyYXBwZWRHZW5lcmljTWVzc2FnZVJlc3BvbnNlPiBpbmRpY2F0aW5nIHRoZSB0YXNrIHdhcyBxdWV1ZWQgb3IgY29tcGxldGVkLlxuICAgICAqL1xuICAgIGFzeW5jIGV4dHJhY3Qob3B0aW9ucykge1xuICAgICAgICBjb25zdCBkYXRhID0ge307XG4gICAgICAgIGlmIChvcHRpb25zLnNldHRpbmdzKSB7XG4gICAgICAgICAgICAvLyBTZW5kIHNldHRpbmdzIGluIHRoZSBib2R5IGFzIHBlciByb3V0ZXJcbiAgICAgICAgICAgIGRhdGEuc2V0dGluZ3MgPSBvcHRpb25zLnNldHRpbmdzO1xuICAgICAgICB9XG4gICAgICAgIGlmIChvcHRpb25zLnJ1bldpdGhPcmNoZXN0cmF0aW9uICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgICAgIC8vIFNlbmQgcnVuV2l0aE9yY2hlc3RyYXRpb24gaW4gdGhlIGJvZHlcbiAgICAgICAgICAgIGRhdGEucnVuX3dpdGhfb3JjaGVzdHJhdGlvbiA9IG9wdGlvbnMucnVuV2l0aE9yY2hlc3RyYXRpb247XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50Lm1ha2VSZXF1ZXN0KFwiUE9TVFwiLCBgZG9jdW1lbnRzLyR7b3B0aW9ucy5pZH0vZXh0cmFjdGAsIHtcbiAgICAgICAgICAgIC8vIERhdGEgZ29lcyBpbiB0aGUgYm9keSBmb3IgUE9TVFxuICAgICAgICAgICAgZGF0YTogZGF0YSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJldHJpZXZlcyB0aGUgZW50aXRpZXMgdGhhdCB3ZXJlIGV4dHJhY3RlZCBmcm9tIGEgZG9jdW1lbnQuXG4gICAgICpcbiAgICAgKiBOb3RlOiBVc2VycyBjYW4gb25seSBhY2Nlc3MgZW50aXRpZXMgZnJvbSBkb2N1bWVudHMgdGhleSBvd24gb3IgaGF2ZSBhY2Nlc3MgdG8gdGhyb3VnaCBjb2xsZWN0aW9ucy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSBpZCBEb2N1bWVudCBJRCB0byByZXRyaWV2ZSBlbnRpdGllcyBmb3JcbiAgICAgKiBAcGFyYW0gb2Zmc2V0IFNwZWNpZmllcyB0aGUgbnVtYmVyIG9mIG9iamVjdHMgdG8gc2tpcC4gRGVmYXVsdHMgdG8gMC5cbiAgICAgKiBAcGFyYW0gbGltaXQgU3BlY2lmaWVzIGEgbGltaXQgb24gdGhlIG51bWJlciBvZiBvYmplY3RzIHRvIHJldHVybiwgcmFuZ2luZyBiZXR3ZWVuIDEgYW5kIDEwMDAuIERlZmF1bHRzIHRvIDEwMC5cbiAgICAgKiBAcGFyYW0gaW5jbHVkZUVtYmVkZGluZ3MgV2hldGhlciB0byBpbmNsdWRlIHZlY3RvciBlbWJlZGRpbmdzIGluIHRoZSByZXNwb25zZSAoZGVmYXVsdDogZmFsc2UpLiBSZW5hbWVkIGZyb20gaW5jbHVkZVZlY3RvcnMgZm9yIGNvbnNpc3RlbmN5IHdpdGggcm91dGVyLlxuICAgICAqIEByZXR1cm5zIFByb21pc2U8V3JhcHBlZEVudGl0aWVzUmVzcG9uc2U+XG4gICAgICovXG4gICAgYXN5bmMgbGlzdEVudGl0aWVzKG9wdGlvbnMpIHtcbiAgICAgICAgdmFyIF9hLCBfYiwgX2M7XG4gICAgICAgIGNvbnN0IHBhcmFtcyA9IHtcbiAgICAgICAgICAgIG9mZnNldDogKF9hID0gb3B0aW9ucy5vZmZzZXQpICE9PSBudWxsICYmIF9hICE9PSB2b2lkIDAgPyBfYSA6IDAsXG4gICAgICAgICAgICBsaW1pdDogKF9iID0gb3B0aW9ucy5saW1pdCkgIT09IG51bGwgJiYgX2IgIT09IHZvaWQgMCA/IF9iIDogMTAwLFxuICAgICAgICAgICAgLy8gTWFwIHRvIHNuYWtlX2Nhc2UgZm9yIHRoZSBBUElcbiAgICAgICAgICAgIGluY2x1ZGVfZW1iZWRkaW5nczogKF9jID0gb3B0aW9ucy5pbmNsdWRlRW1iZWRkaW5ncykgIT09IG51bGwgJiYgX2MgIT09IHZvaWQgMCA/IF9jIDogZmFsc2UsXG4gICAgICAgIH07XG4gICAgICAgIHJldHVybiB0aGlzLmNsaWVudC5tYWtlUmVxdWVzdChcIkdFVFwiLCBgZG9jdW1lbnRzLyR7b3B0aW9ucy5pZH0vZW50aXRpZXNgLCB7XG4gICAgICAgICAgICBwYXJhbXMsXG4gICAgICAgIH0pO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZXRyaWV2ZXMgdGhlIHJlbGF0aW9uc2hpcHMgYmV0d2VlbiBlbnRpdGllcyB0aGF0IHdlcmUgZXh0cmFjdGVkIGZyb20gYSBkb2N1bWVudC5cbiAgICAgKlxuICAgICAqIE5vdGU6IFVzZXJzIGNhbiBvbmx5IGFjY2VzcyByZWxhdGlvbnNoaXBzIGZyb20gZG9jdW1lbnRzIHRoZXkgb3duIG9yIGhhdmUgYWNjZXNzIHRvIHRocm91Z2ggY29sbGVjdGlvbnMuXG4gICAgICpcbiAgICAgKiBAcGFyYW0gaWQgRG9jdW1lbnQgSUQgdG8gcmV0cmlldmUgcmVsYXRpb25zaGlwcyBmb3JcbiAgICAgKiBAcGFyYW0gb2Zmc2V0IFNwZWNpZmllcyB0aGUgbnVtYmVyIG9mIG9iamVjdHMgdG8gc2tpcC4gRGVmYXVsdHMgdG8gMC5cbiAgICAgKiBAcGFyYW0gbGltaXQgU3BlY2lmaWVzIGEgbGltaXQgb24gdGhlIG51bWJlciBvZiBvYmplY3RzIHRvIHJldHVybiwgcmFuZ2luZyBiZXR3ZWVuIDEgYW5kIDEwMDAuIERlZmF1bHRzIHRvIDEwMC5cbiAgICAgKiBAcGFyYW0gZW50aXR5TmFtZXMgT3B0aW9uYWwgZmlsdGVyIGZvciByZWxhdGlvbnNoaXBzIGludm9sdmluZyBzcGVjaWZpYyBlbnRpdHkgbmFtZXMuXG4gICAgICogQHBhcmFtIHJlbGF0aW9uc2hpcFR5cGVzIE9wdGlvbmFsIGZpbHRlciBmb3Igc3BlY2lmaWMgcmVsYXRpb25zaGlwIHR5cGVzLlxuICAgICAqIEByZXR1cm5zIFByb21pc2U8V3JhcHBlZFJlbGF0aW9uc2hpcHNSZXNwb25zZT5cbiAgICAgKi9cbiAgICBhc3luYyBsaXN0UmVsYXRpb25zaGlwcyhvcHRpb25zKSB7XG4gICAgICAgIHZhciBfYSwgX2IsIF9jLCBfZDtcbiAgICAgICAgY29uc3QgcGFyYW1zID0ge1xuICAgICAgICAgICAgb2Zmc2V0OiAoX2EgPSBvcHRpb25zLm9mZnNldCkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogMCxcbiAgICAgICAgICAgIGxpbWl0OiAoX2IgPSBvcHRpb25zLmxpbWl0KSAhPT0gbnVsbCAmJiBfYiAhPT0gdm9pZCAwID8gX2IgOiAxMDAsXG4gICAgICAgIH07XG4gICAgICAgIC8vIEFkZCBvcHRpb25hbCBmaWx0ZXJzIGlmIHByb3ZpZGVkXG4gICAgICAgIGlmICgoX2MgPSBvcHRpb25zLmVudGl0eU5hbWVzKSA9PT0gbnVsbCB8fCBfYyA9PT0gdm9pZCAwID8gdm9pZCAwIDogX2MubGVuZ3RoKSB7XG4gICAgICAgICAgICBwYXJhbXMuZW50aXR5X25hbWVzID0gb3B0aW9ucy5lbnRpdHlOYW1lcztcbiAgICAgICAgfVxuICAgICAgICBpZiAoKF9kID0gb3B0aW9ucy5yZWxhdGlvbnNoaXBUeXBlcykgPT09IG51bGwgfHwgX2QgPT09IHZvaWQgMCA/IHZvaWQgMCA6IF9kLmxlbmd0aCkge1xuICAgICAgICAgICAgcGFyYW1zLnJlbGF0aW9uc2hpcF90eXBlcyA9IG9wdGlvbnMucmVsYXRpb25zaGlwVHlwZXM7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRoaXMuY2xpZW50Lm1ha2VSZXF1ZXN0KFwiR0VUXCIsIGBkb2N1bWVudHMvJHtvcHRpb25zLmlkfS9yZWxhdGlvbnNoaXBzYCwge1xuICAgICAgICAgICAgcGFyYW1zLFxuICAgICAgICB9KTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogVHJpZ2dlcnMgdGhlIGRlZHVwbGljYXRpb24gb2YgZW50aXRpZXMgd2l0aGluIGEgZG9jdW1lbnQuXG4gICAgICpcbiAgICAgKiBOb3RlOiBVc2VycyB0eXBpY2FsbHkgbmVlZCB0byBvd24gdGhlIGRvY3VtZW50IHRvIHRyaWdnZXIgZGVkdXBsaWNhdGlvbi4gU3VwZXJ1c2VycyBtYXkgaGF2ZSBicm9hZGVyIGFjY2Vzcy5cbiAgICAgKiBUaGlzIGlzIG9mdGVuIGFuIGFzeW5jaHJvbm91cyBwcm9jZXNzLlxuICAgICAqXG4gICAgICogQHBhcmFtIGlkIERvY3VtZW50IElEIHRvIGRlZHVwbGljYXRlIGVudGl0aWVzIGZvci5cbiAgICAgKiBAcGFyYW0gc2V0dGluZ3MgT3B0aW9uYWwgc2V0dGluZ3MgdG8gb3ZlcnJpZGUgdGhlIGRlZmF1bHQgZGVkdXBsaWNhdGlvbiBjb25maWd1cmF0aW9uLlxuICAgICAqIEBwYXJhbSBydW5XaXRoT3JjaGVzdHJhdGlvbiBXaGV0aGVyIHRvIHJ1biB3aXRoIG9yY2hlc3RyYXRpb24gKHJlY29tbWVuZGVkLCBkZWZhdWx0OiB0cnVlKS5cbiAgICAgKiBAcmV0dXJucyBQcm9taXNlPFdyYXBwZWRHZW5lcmljTWVzc2FnZVJlc3BvbnNlPiBpbmRpY2F0aW5nIHRoZSB0YXNrIHdhcyBxdWV1ZWQgb3IgY29tcGxldGVkLlxuICAgICAqL1xuICAgIGFzeW5jIGRlZHVwbGljYXRlKG9wdGlvbnMpIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IHt9O1xuICAgICAgICAvLyBSZW1vdmVkIHJ1blR5cGVcbiAgICAgICAgaWYgKG9wdGlvbnMuc2V0dGluZ3MpIHtcbiAgICAgICAgICAgIGRhdGEuc2V0dGluZ3MgPSBvcHRpb25zLnNldHRpbmdzOyAvLyBTZW5kIHNldHRpbmdzIGluIGJvZHlcbiAgICAgICAgfVxuICAgICAgICBpZiAob3B0aW9ucy5ydW5XaXRoT3JjaGVzdHJhdGlvbiAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICAgICAgICBkYXRhLnJ1bl93aXRoX29yY2hlc3RyYXRpb24gPSBvcHRpb25zLnJ1bldpdGhPcmNoZXN0cmF0aW9uOyAvLyBTZW5kIGluIGJvZHlcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQubWFrZVJlcXVlc3QoXCJQT1NUXCIsIGBkb2N1bWVudHMvJHtvcHRpb25zLmlkfS9kZWR1cGxpY2F0ZWAsIHtcbiAgICAgICAgICAgIC8vIERhdGEgZ29lcyBpbiB0aGUgYm9keSBmb3IgUE9TVFxuICAgICAgICAgICAgZGF0YTogZGF0YSxcbiAgICAgICAgfSk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFBlcmZvcm0gYSBzZWFyY2ggcXVlcnkgb24gZG9jdW1lbnQgc3VtbWFyaWVzLlxuICAgICAqXG4gICAgICogTm90ZTogQWNjZXNzIGNvbnRyb2wgKGJhc2VkIG9uIHVzZXIgb3duZXJzaGlwL2NvbGxlY3Rpb24gYWNjZXNzKSBpcyBhcHBsaWVkIHRvIHRoZSBzZWFyY2ggcmVzdWx0cy5cbiAgICAgKlxuICAgICAqIEBwYXJhbSBxdWVyeSBUaGUgc2VhcmNoIHF1ZXJ5IHN0cmluZy5cbiAgICAgKiBAcGFyYW0gc2VhcmNoTW9kZSBUaGUgc2VhcmNoIG1vZGUgdG8gdXNlICgnYmFzaWMnLCAnYWR2YW5jZWQnLCAnY3VzdG9tJykuIERlZmF1bHRzIHRvICdjdXN0b20nLlxuICAgICAqIEBwYXJhbSBzZWFyY2hTZXR0aW5ncyBPcHRpb25hbCBzZXR0aW5ncyB0byBjb25maWd1cmUgdGhlIHNlYXJjaCAoZmlsdGVycywgbGltaXRzLCBoeWJyaWQgc2VhcmNoIG9wdGlvbnMsIGV0Yy4pLlxuICAgICAqIEByZXR1cm5zIFByb21pc2U8V3JhcHBlZERvY3VtZW50U2VhcmNoUmVzcG9uc2U+XG4gICAgICovXG4gICAgYXN5bmMgc2VhcmNoKG9wdGlvbnMpIHtcbiAgICAgICAgdmFyIF9hLCBfYjtcbiAgICAgICAgY29uc3QgZGF0YSA9IHtcbiAgICAgICAgICAgIHF1ZXJ5OiBvcHRpb25zLnF1ZXJ5LFxuICAgICAgICAgICAgLy8gTWFwIHRvIHNuYWtlX2Nhc2UgZm9yIEFQSVxuICAgICAgICAgICAgc2VhcmNoX21vZGU6IChfYSA9IG9wdGlvbnMuc2VhcmNoTW9kZSkgIT09IG51bGwgJiYgX2EgIT09IHZvaWQgMCA/IF9hIDogXCJjdXN0b21cIixcbiAgICAgICAgICAgIHNlYXJjaF9zZXR0aW5nczogKF9iID0gb3B0aW9ucy5zZWFyY2hTZXR0aW5ncykgIT09IG51bGwgJiYgX2IgIT09IHZvaWQgMCA/IF9iIDoge30sIC8vIFNlbmQgZW1wdHkgb2JqZWN0IGlmIHVuZGVmaW5lZFxuICAgICAgICB9O1xuICAgICAgICByZXR1cm4gdGhpcy5jbGllbnQubWFrZVJlcXVlc3QoXCJQT1NUXCIsIFwiZG9jdW1lbnRzL3NlYXJjaFwiLCB7XG4gICAgICAgICAgICBkYXRhOiBkYXRhLCAvLyBVc2UgZGF0YSBmb3IgUE9TVCBib2R5XG4gICAgICAgIH0pO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBJbmdlc3QgYSBzYW1wbGUgZG9jdW1lbnQgaW50byBSMlIuIERvd25sb2FkcyBhIHNhbXBsZSBQREYsIGluZ2VzdHMgaXQsIGFuZCBjbGVhbnMgdXAuXG4gICAgICpcbiAgICAgKiBOb3RlOiBUaGlzIHJlcXVpcmVzIE5vZGUuanMgZW52aXJvbm1lbnQgd2l0aCAnZnMnLCAnYXhpb3MnLCAnb3MnLCAncGF0aCcsICd1dWlkJyBtb2R1bGVzLiBJdCB3aWxsIG5vdCB3b3JrIGRpcmVjdGx5IGluIGEgc3RhbmRhcmQgYnJvd3NlciBlbnZpcm9ubWVudCBkdWUgdG8gZmlsZSBzeXN0ZW0gYWNjZXNzLlxuICAgICAqXG4gICAgICogQHBhcmFtIG9wdGlvbnMgT3B0aW9uYWwgaW5nZXN0aW9uIG9wdGlvbnMuXG4gICAgICogQHBhcmFtIG9wdGlvbnMuaW5nZXN0aW9uTW9kZSBJZiBwcm92aWRlZCwgcGFzc2VzIHRoZSBpbmdlc3Rpb24gbW9kZSAoZS5nLiBcImhpLXJlc1wiKSB0byB0aGUgY3JlYXRlKCkgbWV0aG9kLlxuICAgICAqIEByZXR1cm5zIFByb21pc2U8V3JhcHBlZEluZ2VzdGlvblJlc3BvbnNlPiBUaGUgaW5nZXN0aW9uIHJlc3BvbnNlLlxuICAgICAqL1xuICAgIGFzeW5jIGNyZWF0ZVNhbXBsZShvcHRpb25zKSB7XG4gICAgICAgIC8vIENoZWNrIGlmIGluIE5vZGUuanMgZW52aXJvbm1lbnRcbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIgfHwgIWZzIHx8ICFheGlvc18xLmRlZmF1bHQgfHwgIW9zIHx8ICFwYXRoKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJjcmVhdGVTYW1wbGUgbWV0aG9kIHJlcXVpcmVzIGEgTm9kZS5qcyBlbnZpcm9ubWVudCB3aXRoICdmcycsICdheGlvcycsICdvcycsICdwYXRoJywgJ3V1aWQnIG1vZHVsZXMuXCIpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHNhbXBsZUZpbGVVcmwgPSBcImh0dHBzOi8vcmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbS9TY2lQaGktQUkvUjJSL21haW4vcHkvY29yZS9leGFtcGxlcy9kYXRhL0RlZXBTZWVrX1IxLnBkZlwiO1xuICAgICAgICBjb25zdCBwYXJzZWRVcmwgPSBuZXcgVVJMKHNhbXBsZUZpbGVVcmwpO1xuICAgICAgICBjb25zdCBmaWxlbmFtZSA9IHBhcnNlZFVybC5wYXRobmFtZS5zcGxpdChcIi9cIikucG9wKCkgfHwgXCJzYW1wbGUucGRmXCI7IC8vIERlZmF1bHQgdG8gLnBkZlxuICAgICAgICAvLyBDcmVhdGUgYSB0ZW1wb3JhcnkgZmlsZSBwYXRoIHVzaW5nIE5vZGUuanMgJ29zJyBhbmQgJ3BhdGgnXG4gICAgICAgIGNvbnN0IHRtcERpciA9IG9zLnRtcGRpcigpO1xuICAgICAgICBjb25zdCB0bXBGaWxlUGF0aCA9IHBhdGguam9pbih0bXBEaXIsIGByMnJfc2FtcGxlXyR7RGF0ZS5ub3coKX1fJHtmaWxlbmFtZX1gKTtcbiAgICAgICAgbGV0IGluZ2VzdGlvblJlc3BvbnNlO1xuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8gRG93bmxvYWQgdGhlIGZpbGUgdXNpbmcgYXhpb3NcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYXhpb3NfMS5kZWZhdWx0LmdldChzYW1wbGVGaWxlVXJsLCB7XG4gICAgICAgICAgICAgICAgcmVzcG9uc2VUeXBlOiBcImFycmF5YnVmZmVyXCIsIC8vIEdldCBkYXRhIGFzIEFycmF5QnVmZmVyXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIC8vIFdyaXRlIHRoZSBkb3dubG9hZGVkIGZpbGUgdG8gdGhlIHRlbXBvcmFyeSBsb2NhdGlvbiB1c2luZyBOb2RlLmpzICdmcydcbiAgICAgICAgICAgIGF3YWl0IGZzLnByb21pc2VzLndyaXRlRmlsZSh0bXBGaWxlUGF0aCwgQnVmZmVyLmZyb20ocmVzcG9uc2UuZGF0YSkpOyAvLyBDb252ZXJ0IEFycmF5QnVmZmVyIHRvIEJ1ZmZlclxuICAgICAgICAgICAgLy8gR2VuZXJhdGUgYSBzdGFibGUgZG9jdW1lbnQgSUQgdXNpbmcgdXVpZCB2NVxuICAgICAgICAgICAgY29uc3QgTkFNRVNQQUNFX0ROUyA9IFwiNmJhN2I4MTAtOWRhZC0xMWQxLTgwYjQtMDBjMDRmZDQzMGM4XCI7IC8vIFN0YW5kYXJkIEROUyBuYW1lc3BhY2UgVVVJRFxuICAgICAgICAgICAgY29uc3QgZG9jSWQgPSAoMCwgdXVpZF8xLnY1KShzYW1wbGVGaWxlVXJsLCBOQU1FU1BBQ0VfRE5TKTtcbiAgICAgICAgICAgIGNvbnN0IG1ldGFkYXRhID0geyB0aXRsZTogZmlsZW5hbWUgfTtcbiAgICAgICAgICAgIC8vIEluZ2VzdCB0aGUgZmlsZSBieSBjYWxsaW5nIHRoZSBjcmVhdGUoKSBtZXRob2QsIHBhc3NpbmcgdGhlIGZpbGUgcGF0aFxuICAgICAgICAgICAgaW5nZXN0aW9uUmVzcG9uc2UgPSBhd2FpdCB0aGlzLmNyZWF0ZSh7XG4gICAgICAgICAgICAgICAgZmlsZTogdG1wRmlsZVBhdGgsIC8vIFBhc3MgdGhlIHBhdGggYXMgc3RyaW5nIChOb2RlLmpzIGNvbXBhdGlibGUgcGFydCBvZiBjcmVhdGUpXG4gICAgICAgICAgICAgICAgbWV0YWRhdGEsXG4gICAgICAgICAgICAgICAgaWQ6IGRvY0lkLFxuICAgICAgICAgICAgICAgIGluZ2VzdGlvbk1vZGU6IG9wdGlvbnMgPT09IG51bGwgfHwgb3B0aW9ucyA9PT0gdm9pZCAwID8gdm9pZCAwIDogb3B0aW9ucy5pbmdlc3Rpb25Nb2RlLFxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAvLyBFbnN1cmUgY2xlYW51cCBoYXBwZW5zIGV2ZW4gb24gZXJyb3IgZHVyaW5nIGRvd25sb2FkIG9yIGluZ2VzdGlvblxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIGR1cmluZyBjcmVhdGVTYW1wbGU6XCIsIGVycm9yKTtcbiAgICAgICAgICAgIHRocm93IGVycm9yOyAvLyBSZS10aHJvdyB0aGUgZXJyb3IgYWZ0ZXIgbG9nZ2luZ1xuICAgICAgICB9XG4gICAgICAgIGZpbmFsbHkge1xuICAgICAgICAgICAgLy8gQ2xlYW4gdXA6IHJlbW92ZSB0aGUgdGVtcG9yYXJ5IGZpbGUgdXNpbmcgTm9kZS5qcyAnZnMnXG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgIGF3YWl0IGZzLnByb21pc2VzLnVubGluayh0bXBGaWxlUGF0aCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAodW5saW5rRXJyb3IpIHtcbiAgICAgICAgICAgICAgICAvLyBMb2cgdW5saW5rIGVycm9yIGJ1dCBkb24ndCBvdmVyd3JpdGUgb3JpZ2luYWwgZXJyb3IgaWYgb25lIG9jY3VycmVkXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihgRmFpbGVkIHRvIGRlbGV0ZSB0ZW1wb3JhcnkgZmlsZSAke3RtcEZpbGVQYXRofTpgLCB1bmxpbmtFcnJvcik7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGluZ2VzdGlvblJlc3BvbnNlO1xuICAgIH1cbn1cbmV4cG9ydHMuRG9jdW1lbnRzQ2xpZW50ID0gRG9jdW1lbnRzQ2xpZW50O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZG9jdW1lbnRzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/documents.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/graphs.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/graphs.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.GraphsClient = void 0;\nconst utils_1 = __webpack_require__(/*! ../../utils */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/index.js\");\nlet fs;\nif (typeof window === \"undefined\") {\n    fs = __webpack_require__(/*! fs */ \"fs\");\n}\nclass GraphsClient {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * List graphs with pagination and filtering options.\n     * @param collectionIds Optional list of collection IDs to filter by\n     * @param offset Optional offset for pagination\n     * @param limit Optional limit for pagination\n     * @returns\n     */\n    async list(options) {\n        var _a, _b;\n        const params = {\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options === null || options === void 0 ? void 0 : options.limit) !== null && _b !== void 0 ? _b : 100,\n        };\n        if ((options === null || options === void 0 ? void 0 : options.collectionIds) && options.collectionIds.length > 0) {\n            params.collectionIds = options.collectionIds;\n        }\n        return this.client.makeRequest(\"GET\", \"graphs\", {\n            params,\n        });\n    }\n    /**\n     * Get detailed information about a specific graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @returns\n     */\n    async retrieve(options) {\n        return this.client.makeRequest(\"GET\", `graphs/${options.collectionId}`);\n    }\n    /**\n     * Deletes a graph and all its associated data.\n     *\n     * This endpoint permanently removes the specified graph along with all\n     * entities and relationships that belong to only this graph.\n     *\n     * Entities and relationships extracted from documents are not deleted.\n     * @param collectionId The collection ID corresponding to the graph\n     * @returns\n     */\n    async reset(options) {\n        return this.client.makeRequest(\"POST\", `graphs/${options.collectionId}/reset`);\n    }\n    /**\n     * Update an existing graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param name Optional new name for the graph\n     * @param description Optional new description for the graph\n     * @returns\n     */\n    async update(options) {\n        const data = Object.assign(Object.assign({}, (options.name && { name: options.name })), (options.description && { description: options.description }));\n        return this.client.makeRequest(\"POST\", `graphs/${options.collectionId}`, {\n            data,\n        });\n    }\n    /**\n     * Creates a new entity in the graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param entity Entity to add\n     * @returns\n     */\n    async createEntity(options) {\n        const data = Object.assign(Object.assign(Object.assign({ name: options.name }, (options.description && { description: options.description })), (options.category && { category: options.category })), (options.metadata && { metadata: options.metadata }));\n        return this.client.makeRequest(\"POST\", `graphs/${options.collectionId}/entities`, {\n            data,\n        });\n    }\n    /**\n     * List all entities in a graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 100. Defaults to 100.\n     * @returns\n     */\n    async listEntities(options) {\n        var _a, _b;\n        const params = {\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options === null || options === void 0 ? void 0 : options.limit) !== null && _b !== void 0 ? _b : 100,\n        };\n        return this.client.makeRequest(\"GET\", `graphs/${options.collectionId}/entities`, {\n            params,\n        });\n    }\n    /**\n     * Retrieve an entity from a graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param entityId Entity ID to retrieve\n     * @returns\n     */\n    async getEntity(options) {\n        return this.client.makeRequest(\"GET\", `graphs/${options.collectionId}/entities/${options.entityId}`);\n    }\n    /**\n     * Updates an existing entity in the graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param entityId Entity ID to update\n     * @param entity Entity to update\n     * @returns\n     */\n    async updateEntity(options) {\n        const data = Object.assign(Object.assign(Object.assign(Object.assign({}, (options.name && { name: options.name })), (options.description && { description: options.description })), (options.category && { category: options.category })), (options.metadata && { metadata: options.metadata }));\n        return this.client.makeRequest(\"POST\", `graphs/${options.collectionId}/entities/${options.entityId}`, {\n            data,\n        });\n    }\n    /**\n     * Remove an entity from a graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param entityId Entity ID to remove\n     * @returns\n     */\n    async removeEntity(options) {\n        return this.client.makeRequest(\"DELETE\", `graphs/${options.collectionId}/entities/${options.entityId}`);\n    }\n    /**\n     * Creates a new relationship in the graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param relationship Relationship to add\n     * @returns\n     */\n    async createRelationship(options) {\n        const data = Object.assign(Object.assign({ subject: options.subject, subject_id: options.subjectId, predicate: options.predicate, object: options.object, object_id: options.objectId, description: options.description }, (options.weight && { weight: options.weight })), (options.metadata && { metadata: options.metadata }));\n        return this.client.makeRequest(\"POST\", `graphs/${options.collectionId}/relationships`, {\n            data,\n        });\n    }\n    /**\n     * List all relationships in a graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 100. Defaults to 100.\n     * @returns\n     */\n    async listRelationships(options) {\n        var _a, _b;\n        const params = {\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options === null || options === void 0 ? void 0 : options.limit) !== null && _b !== void 0 ? _b : 100,\n        };\n        return this.client.makeRequest(\"GET\", `graphs/${options.collectionId}/relationships`, {\n            params,\n        });\n    }\n    /**\n     * Retrieve a relationship from a graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param relationshipId Relationship ID to retrieve\n     * @returns\n     */\n    async getRelationship(options) {\n        return this.client.makeRequest(\"GET\", `graphs/${options.collectionId}/relationships/${options.relationshipId}`);\n    }\n    /**\n     * Updates an existing relationship in the graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param relationshipId Relationship ID to update\n     * @param relationship Relationship to update\n     * @returns WrappedRelationshipResponse\n     */\n    async updateRelationship(options) {\n        const data = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, (options.subject && { subject: options.subject })), (options.subjectId && { subject_id: options.subjectId })), (options.predicate && { predicate: options.predicate })), (options.object && { object: options.object })), (options.objectId && { object_id: options.objectId })), (options.description && { description: options.description })), (options.weight && { weight: options.weight })), (options.metadata && { metadata: options.metadata }));\n        return this.client.makeRequest(\"POST\", `graphs/${options.collectionId}/relationships/${options.relationshipId}`, {\n            data,\n        });\n    }\n    /**\n     * Remove a relationship from a graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param relationshipId Entity ID to remove\n     * @returns\n     */\n    async removeRelationship(options) {\n        return this.client.makeRequest(\"DELETE\", `graphs/${options.collectionId}/relationships/${options.relationshipId}`);\n    }\n    /**\n     * Export graph entities as a CSV file with support for filtering and column selection.\n     *\n     * @param options Export configuration options\n     * @param options.outputPath Path where the CSV file should be saved (Node.js only)\n     * @param options.columns Optional list of specific columns to include\n     * @param options.filters Optional filters to limit which documents are exported\n     * @param options.includeHeader Whether to include column headers (default: true)\n     * @returns Promise<Blob> in browser environments, Promise<void> in Node.js\n     */\n    async exportEntities(options) {\n        var _a;\n        const data = {\n            include_header: (_a = options.includeHeader) !== null && _a !== void 0 ? _a : true,\n        };\n        if (options.columns) {\n            data.columns = options.columns;\n        }\n        if (options.filters) {\n            data.filters = options.filters;\n        }\n        const response = await this.client.makeRequest(\"POST\", `graphs/${options.collectionId}/entities/export`, {\n            data,\n            responseType: \"arraybuffer\",\n            headers: { Accept: \"text/csv\" },\n        });\n        // Node environment\n        if (options.outputPath && typeof process !== \"undefined\") {\n            await fs.promises.writeFile(options.outputPath, Buffer.from(response));\n            return;\n        }\n        // Browser\n        return new Blob([response], { type: \"text/csv\" });\n    }\n    /**\n     * Export graph entities as a CSV file and save it to the user's device.\n     * @param filename\n     * @param options\n     */\n    async exportEntitiesToFile(options) {\n        const blob = await this.exportEntities(options);\n        if (blob instanceof Blob) {\n            (0, utils_1.downloadBlob)(blob, options.filename);\n        }\n    }\n    /**\n     * Export graph relationships as a CSV file with support for filtering and column selection.\n     *\n     * @param options Export configuration options\n     * @param options.outputPath Path where the CSV file should be saved (Node.js only)\n     * @param options.columns Optional list of specific columns to include\n     * @param options.filters Optional filters to limit which documents are exported\n     * @param options.includeHeader Whether to include column headers (default: true)\n     * @returns Promise<Blob> in browser environments, Promise<void> in Node.js\n     */\n    async exportRelationships(options) {\n        var _a;\n        const data = {\n            include_header: (_a = options.includeHeader) !== null && _a !== void 0 ? _a : true,\n        };\n        if (options.columns) {\n            data.columns = options.columns;\n        }\n        if (options.filters) {\n            data.filters = options.filters;\n        }\n        const response = await this.client.makeRequest(\"POST\", `graphs/${options.collectionId}/relationships/export`, {\n            data,\n            responseType: \"arraybuffer\",\n            headers: { Accept: \"text/csv\" },\n        });\n        // Node environment\n        if (options.outputPath && typeof process !== \"undefined\") {\n            await fs.promises.writeFile(options.outputPath, Buffer.from(response));\n            return;\n        }\n        // Browser\n        return new Blob([response], { type: \"text/csv\" });\n    }\n    /**\n     * Export graph relationships as a CSV file and save it to the user's device.\n     * @param filename\n     * @param options\n     */\n    async exportRelationshipsToFile(options) {\n        const blob = await this.exportRelationships(options);\n        if (blob instanceof Blob) {\n            (0, utils_1.downloadBlob)(blob, options.filename);\n        }\n    }\n    /**\n     * Export graph communities as a CSV file with support for filtering and column selection.\n     *\n     * @param options Export configuration options\n     * @param options.outputPath Path where the CSV file should be saved (Node.js only)\n     * @param options.columns Optional list of specific columns to include\n     * @param options.filters Optional filters to limit which documents are exported\n     * @param options.includeHeader Whether to include column headers (default: true)\n     * @returns Promise<Blob> in browser environments, Promise<void> in Node.js\n     */\n    async exportCommunities(options) {\n        var _a;\n        const data = {\n            include_header: (_a = options.includeHeader) !== null && _a !== void 0 ? _a : true,\n        };\n        if (options.columns) {\n            data.columns = options.columns;\n        }\n        if (options.filters) {\n            data.filters = options.filters;\n        }\n        const response = await this.client.makeRequest(\"POST\", `graphs/${options.collectionId}/communities/export`, {\n            data,\n            responseType: \"arraybuffer\",\n            headers: { Accept: \"text/csv\" },\n        });\n        // Node environment\n        if (options.outputPath && typeof process !== \"undefined\") {\n            await fs.promises.writeFile(options.outputPath, Buffer.from(response));\n            return;\n        }\n        // Browser\n        return new Blob([response], { type: \"text/csv\" });\n    }\n    /**\n     * Export graph communities as a CSV file and save it to the user's device.\n     * @param filename\n     * @param options\n     */\n    async exportCommunitiesToFile(options) {\n        const blob = await this.exportRelationships(options);\n        if (blob instanceof Blob) {\n            (0, utils_1.downloadBlob)(blob, options.filename);\n        }\n    }\n    /**\n     * Creates a new community in the graph.\n     *\n     * While communities are typically built automatically via the /graphs/{id}/communities/build endpoint,\n     * this endpoint allows you to manually create your own communities.\n     *\n     * This can be useful when you want to:\n     *  - Define custom groupings of entities based on domain knowledge\n     *  - Add communities that weren't detected by the automatic process\n     *  - Create hierarchical organization structures\n     *  - Tag groups of entities with specific metadata\n     *\n     * The created communities will be integrated with any existing automatically detected communities\n     * in the graph's community structure.\n     *\n     * @param collectionId The collection ID corresponding to the graph\n     * @param name Name of the community\n     * @param summary Summary of the community\n     * @param findings Findings or insights about the community\n     * @param rating Rating of the community\n     * @param ratingExplanation Explanation of the community rating\n     * @param attributes Additional attributes to associate with the community\n     * @returns WrappedCommunityResponse\n     */\n    async createCommunity(options) {\n        const data = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({ name: options.name }, (options.summary && { summary: options.summary })), (options.findings && { findings: options.findings })), (options.rating && { rating: options.rating })), (options.ratingExplanation && {\n            rating_explanation: options.ratingExplanation,\n        })), (options.attributes && { attributes: options.attributes }));\n        return this.client.makeRequest(\"POST\", `graphs/${options.collectionId}/communities`, {\n            data,\n        });\n    }\n    /**\n     * List all communities in a graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 100. Defaults to 100.\n     * @returns\n     */\n    async listCommunities(options) {\n        var _a, _b;\n        const params = {\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options === null || options === void 0 ? void 0 : options.limit) !== null && _b !== void 0 ? _b : 100,\n        };\n        return this.client.makeRequest(\"GET\", `graphs/${options.collectionId}/communities`, {\n            params,\n        });\n    }\n    /**\n     * Retrieve a community from a graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param communityId Entity ID to retrieve\n     * @returns\n     */\n    async getCommunity(options) {\n        return this.client.makeRequest(\"GET\", `graphs/${options.collectionId}/communities/${options.communityId}`);\n    }\n    /**\n     * Updates an existing community in the graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param communityId Community ID to update\n     * @param entity Entity to update\n     * @returns WrappedCommunityResponse\n     */\n    async updateCommunity(options) {\n        const data = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, (options.name && { name: options.name })), (options.summary && { summary: options.summary })), (options.findings && { findings: options.findings })), (options.rating && { rating: options.rating })), (options.ratingExplanation && {\n            rating_explanation: options.ratingExplanation,\n        })), (options.attributes && { attributes: options.attributes }));\n        return this.client.makeRequest(\"POST\", `graphs/${options.collectionId}/communities/${options.communityId}`, {\n            data,\n        });\n    }\n    /**\n     * Delete a community in a graph.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param communityId Community ID to delete\n     * @returns\n     */\n    async deleteCommunity(options) {\n        return this.client.makeRequest(\"DELETE\", `graphs/${options.collectionId}/communities/${options.communityId}`);\n    }\n    /**\n     * Adds documents to a graph by copying their entities and relationships.\n     *\n     * This endpoint:\n     *  1. Copies document entities to the graphs_entities table\n     *  2. Copies document relationships to the graphs_relationships table\n     *  3. Associates the documents with the graph\n     *\n     * When a document is added:\n     *  - Its entities and relationships are copied to graph-specific tables\n     *  - Existing entities/relationships are updated by merging their properties\n     *  - The document ID is recorded in the graph's document_ids array\n     *\n     * Documents added to a graph will contribute their knowledge to:\n     *  - Graph analysis and querying\n     *  - Community detection\n     *  - Knowledge graph enrichment\n     *\n     * The user must have access to both the graph and the documents being added.\n     * @param collectionId The collection ID corresponding to the graph\n     * @returns\n     */\n    async pull(options) {\n        return this.client.makeRequest(\"POST\", `graphs/${options.collectionId}/pull`);\n    }\n    /**\n     * Removes a document from a graph and removes any associated entities\n     *\n     * This endpoint:\n     *  1. Removes the document ID from the graph's document_ids array\n     *  2. Optionally deletes the document's copied entities and relationships\n     *\n     * The user must have access to both the graph and the document being removed.\n     * @param collectionId The collection ID corresponding to the graph\n     * @param documentId The document ID to remove\n     * @returns\n     */\n    async removeDocument(options) {\n        return this.client.makeRequest(\"DELETE\", `graphs/${options.collectionId}/documents/${options.documentId}`);\n    }\n    /**\n     * Creates communities in the graph by analyzing entity relationships and similarities.\n     *\n     * Communities are created through the following process:\n     * 1. Analyzes entity relationships and metadata to build a similarity graph\n     * 2. Applies advanced community detection algorithms (e.g. Leiden) to identify densely connected groups\n     * 3. Creates hierarchical community structure with multiple granularity levels\n     * 4. Generates natural language summaries and statistical insights for each community\n     *\n     * The resulting communities can be used to:\n     * - Understand high-level graph structure and organization\n     * - Identify key entity groupings and their relationships\n     * - Navigate and explore the graph at different levels of detail\n     * - Generate insights about entity clusters and their characteristics\n     *\n     * The community detection process is configurable through settings like:\n     * - Community detection algorithm parameters\n     * - Summary generation prompt\n     *\n     * @param options\n     * @returns\n     */\n    async buildCommunities(options) {\n        return this.client.makeRequest(\"POST\", `graphs/${options.collectionId}/communities/build`);\n    }\n}\nexports.GraphsClient = GraphsClient;\n//# sourceMappingURL=graphs.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/graphs.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/indices.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/indices.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.IndiciesClient = void 0;\nclass IndiciesClient {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Create a new vector similarity search index in the database.\n     * @param config Configuration for the vector index.\n     * @param runWithOrchestration Whether to run index creation as an orchestrated task.\n     * @returns\n     */\n    async create(options) {\n        const data = Object.assign({ config: options.config }, (options.runWithOrchestration !== undefined && {\n            run_with_orchestration: options.runWithOrchestration,\n        }));\n        return this.client.makeRequest(\"POST\", `indices`, {\n            data,\n        });\n    }\n    /**\n     * List existing vector similarity search indices with pagination support.\n     * @param filters Filter criteria for indices.\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 100. Defaults to 100.\n     * @returns\n     */\n    async list(options) {\n        var _a, _b;\n        const params = {\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options === null || options === void 0 ? void 0 : options.limit) !== null && _b !== void 0 ? _b : 100,\n        };\n        if (options === null || options === void 0 ? void 0 : options.filters) {\n            params.filters = options.filters;\n        }\n        return this.client.makeRequest(\"GET\", `indices`, {\n            params,\n        });\n    }\n    /**\n     * Get detailed information about a specific vector index.\n     * @param indexName The name of the index to retrieve.\n     * @param tableName The name of the table where the index is stored.\n     * @returns\n     */\n    async retrieve(options) {\n        return this.client.makeRequest(\"GET\", `indices/${options.indexName}/${options.tableName}`);\n    }\n    /**\n     * Delete an existing vector index.\n     * @param indexName The name of the index to delete.\n     * @param tableName The name of the table where the index is stored.\n     * @returns\n     */\n    async delete(options) {\n        return this.client.makeRequest(\"DELETE\", `indices/${options.indexName}/${options.tableName}`);\n    }\n}\nexports.IndiciesClient = IndiciesClient;\n//# sourceMappingURL=indices.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/indices.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/prompts.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/prompts.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PromptsClient = void 0;\nclass PromptsClient {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Create a new prompt with the given configuration.\n     *\n     * This endpoint allows superusers to create a new prompt with a\n     * specified name, template, and input types.\n     * @param name The name of the prompt\n     * @param template The template string for the prompt\n     * @param inputTypes A dictionary mapping input names to their types\n     * @returns\n     */\n    async create(options) {\n        return this.client.makeRequest(\"POST\", \"prompts\", {\n            data: options,\n        });\n    }\n    /**\n     * List all available prompts.\n     *\n     * This endpoint retrieves a list of all prompts in the system.\n     * Only superusers can access this endpoint.\n     * @returns\n     */\n    async list() {\n        return this.client.makeRequest(\"GET\", \"prompts\");\n    }\n    /**\n     * Get a specific prompt by name, optionally with inputs and override.\n     *\n     * This endpoint retrieves a specific prompt and allows for optional\n     * inputs and template override.\n     * Only superusers can access this endpoint.\n     * @param options\n     * @returns\n     */\n    async retrieve(options) {\n        const data = Object.assign(Object.assign({}, (options.inputs && { inputs: options.inputs })), (options.promptOverride && {\n            promptOverride: options.promptOverride,\n        }));\n        return this.client.makeRequest(\"POST\", `prompts/${options.name}`, {\n            params: data,\n        });\n    }\n    /**\n     * Update an existing prompt's template and/or input types.\n     *\n     * This endpoint allows superusers to update the template and input types of an existing prompt.\n     * @param options\n     * @returns\n     */\n    async update(options) {\n        const params = {\n            name: options.name,\n        };\n        if (options.template) {\n            params.template = options.template;\n        }\n        if (options.inputTypes) {\n            params.inputTypes = options.inputTypes;\n        }\n        return this.client.makeRequest(\"PUT\", `prompts/${options.name}`, {\n            data: params,\n        });\n    }\n    /**\n     * Delete a prompt by name.\n     *\n     * This endpoint allows superusers to delete an existing prompt.\n     * @param name The name of the prompt to delete\n     * @returns\n     */\n    async delete(options) {\n        return this.client.makeRequest(\"DELETE\", `prompts/${options.name}`);\n    }\n}\nexports.PromptsClient = PromptsClient;\n//# sourceMappingURL=prompts.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/prompts.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/retrieval.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/retrieval.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.RetrievalClient = void 0;\nconst utils_1 = __webpack_require__(/*! ../../utils */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/index.js\");\nclass RetrievalClient {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Perform a search query on the vector database and knowledge graph and\n     * any other configured search engines.\n     *\n     * This endpoint allows for complex filtering of search results using\n     * PostgreSQL-based queries. Filters can be applied to various fields\n     * such as document_id, and internal metadata values.\n     *\n     * Allowed operators include: `eq`, `neq`, `gt`, `gte`, `lt`, `lte`,\n     * `like`, `ilike`, `in`, and `nin`.\n     * @param query Search query to find relevant documents\n     * @param searchSettings Settings for the search\n     * @returns\n     */\n    async search(options) {\n        const data = Object.assign(Object.assign({ query: options.query }, (options.searchSettings && {\n            search_settings: (0, utils_1.ensureSnakeCase)(options.searchSettings),\n        })), (options.searchMode && {\n            search_mode: options.searchMode,\n        }));\n        return await this.client.makeRequest(\"POST\", \"retrieval/search\", {\n            data: data,\n        });\n    }\n    /**\n     * Execute a RAG (Retrieval-Augmented Generation) query.\n     *\n     * This endpoint combines search results with language model generation.\n     * It supports the same filtering capabilities as the search endpoint,\n     * allowing for precise control over the retrieved context.\n     *\n     * The generation process can be customized using the `rag_generation_config` parameter.\n     * @param query\n     * @param searchSettings Settings for the search\n     * @param ragGenerationConfig Configuration for RAG generation\n     * @param taskPrompt Optional custom prompt to override default\n     * @param includeTitleIfAvailable Include document titles in responses when available\n     * @returns\n     */\n    async rag(options) {\n        const data = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({ query: options.query }, (options.searchMode && {\n            search_mode: options.searchMode,\n        })), (options.searchSettings && {\n            search_settings: (0, utils_1.ensureSnakeCase)(options.searchSettings),\n        })), (options.ragGenerationConfig && {\n            rag_generation_config: (0, utils_1.ensureSnakeCase)(options.ragGenerationConfig),\n        })), (options.taskPrompt && {\n            task_prompt: options.taskPrompt,\n        })), (options.includeTitleIfAvailable !== undefined && {\n            include_title_if_available: options.includeTitleIfAvailable,\n        })), (options.includeWebSearch && {\n            include_web_search: options.includeWebSearch,\n        }));\n        if (options.ragGenerationConfig && options.ragGenerationConfig.stream) {\n            return this.streamRag(data);\n        }\n        else {\n            return await this.client.makeRequest(\"POST\", \"retrieval/rag\", {\n                data: data,\n            });\n        }\n    }\n    async streamRag(ragData) {\n        return this.client.makeRequest(\"POST\", \"retrieval/rag\", {\n            data: ragData,\n            headers: { \"Content-Type\": \"application/json\" },\n            responseType: \"stream\",\n        });\n    }\n    /**\n     * Engage with an intelligent RAG-powered conversational agent for complex\n     * information retrieval and analysis.\n     *\n     * This advanced endpoint combines retrieval-augmented generation (RAG)\n     * with a conversational AI agent to provide detailed, context-aware\n     * responses based on your document collection.\n     *\n     * The agent can:\n     *    - Maintain conversation context across multiple interactions\n     *    - Dynamically search and retrieve relevant information from both\n     *      vector and knowledge graph sources\n     *    - Break down complex queries into sub-questions for comprehensive\n     *      answers\n     *    - Cite sources and provide evidence-based responses\n     *    - Handle follow-up questions and clarifications\n     *    - Navigate complex topics with multi-step reasoning\n     *\n     * This endpoint offers two operating modes:\n     *    - RAG mode: Standard retrieval-augmented generation for answering questions\n     *      based on knowledge base\n     *    - Research mode: Advanced capabilities for deep analysis, reasoning, and computation\n     *\n     * @param message Current message to process\n     * @param messages List of messages to process\n     * @param ragGenerationConfig Configuration for RAG generation in 'rag' mode\n     * @param researchGenerationConfig Configuration for generation in 'research' mode\n     * @param searchMode Search mode to use, either \"basic\", \"advanced\", or \"custom\"\n     * @param searchSettings Settings for the search\n     * @param taskPrompt Optional custom prompt to override default\n     * @param includeTitleIfAvailable Include document titles in responses when available\n     * @param conversationId ID of the conversation\n     * @param tools List of tool configurations (deprecated)\n     * @param ragTools List of tools to enable for RAG mode\n     * @param researchTools List of tools to enable for Research mode\n     * @param maxToolContextLength Maximum context length for tool replies\n     * @param useSystemContext Use system context for generation\n     * @param mode Mode to use, either \"rag\" or \"research\"\n     * @param needsInitialConversationName Whether the conversation needs an initial name\n     * @returns\n     */\n    async agent(options) {\n        const data = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, (options.message && {\n            message: options.message,\n        })), (options.messages && {\n            messages: options.messages,\n        })), (options.searchMode && {\n            search_mode: options.searchMode,\n        })), (options.ragGenerationConfig && {\n            rag_generation_config: (0, utils_1.ensureSnakeCase)(options.ragGenerationConfig),\n        })), (options.researchGenerationConfig && {\n            research_generation_config: (0, utils_1.ensureSnakeCase)(options.researchGenerationConfig),\n        })), (options.searchSettings && {\n            search_settings: (0, utils_1.ensureSnakeCase)(options.searchSettings),\n        })), (options.taskPrompt && {\n            task_prompt: options.taskPrompt,\n        })), (typeof options.includeTitleIfAvailable && {\n            include_title_if_available: options.includeTitleIfAvailable,\n        })), (options.conversationId && {\n            conversation_id: options.conversationId,\n        })), (options.maxToolContextLength && {\n            max_tool_context_length: options.maxToolContextLength,\n        })), (options.tools && {\n            tools: options.tools,\n        })), (options.ragTools && {\n            rag_tools: options.ragTools,\n        })), (options.researchTools && {\n            research_tools: options.researchTools,\n        })), (typeof options.useSystemContext !== undefined && {\n            use_system_context: options.useSystemContext,\n        })), (options.mode && {\n            mode: options.mode,\n        })), (options.needsInitialConversationName && {\n            needsInitialConversationName: options.needsInitialConversationName,\n        }));\n        // Determine if streaming is enabled\n        let isStream = false;\n        if (options.ragGenerationConfig && options.ragGenerationConfig.stream) {\n            isStream = true;\n        }\n        else if (options.researchGenerationConfig &&\n            options.mode === \"research\" &&\n            options.researchGenerationConfig.stream) {\n            isStream = true;\n        }\n        if (isStream) {\n            return this.streamAgent(data);\n        }\n        else {\n            return await this.client.makeRequest(\"POST\", \"retrieval/agent\", {\n                data: data,\n            });\n        }\n    }\n    async streamAgent(agentData) {\n        // Return the raw stream like streamCompletion does\n        return this.client.makeRequest(\"POST\", \"retrieval/agent\", {\n            data: agentData,\n            headers: { \"Content-Type\": \"application/json\" },\n            responseType: \"stream\",\n        });\n    }\n    /**\n     * Generate completions for a list of messages.\n     *\n     * This endpoint uses the language model to generate completions for\n     * the provided messages. The generation process can be customized using\n     * the generation_config parameter.\n     *\n     * The messages list should contain alternating user and assistant\n     * messages, with an optional system message at the start. Each message\n     * should have a 'role' and 'content'.\n     * @param messages List of messages to generate completion for\n     * @returns\n     */\n    async completion(options) {\n        const data = Object.assign({ messages: options.messages }, (options.generationConfig && {\n            generation_config: options.generationConfig,\n        }));\n        if (options.generationConfig && options.generationConfig.stream) {\n            return this.streamCompletion(data);\n        }\n        else {\n            return await this.client.makeRequest(\"POST\", \"retrieval/completion\", {\n                data: data,\n            });\n        }\n    }\n    async streamCompletion(ragData) {\n        return this.client.makeRequest(\"POST\", \"retrieval/completion\", {\n            data: ragData,\n            headers: {\n                \"Content-Type\": \"application/json\",\n            },\n            responseType: \"stream\",\n        });\n    }\n    /**\n     * Generate embeddings for the provided text.\n     *\n     * This endpoint generates vector embeddings that can be used for\n     * semantic similarity comparisons or other vector operations.\n     *\n     * @param text Text to generate embeddings for\n     * @returns Vector embedding of the input text\n     */\n    async embedding(options) {\n        return await this.client.makeRequest(\"POST\", \"retrieval/embedding\", {\n            data: options.text,\n        });\n    }\n}\nexports.RetrievalClient = RetrievalClient;\n//# sourceMappingURL=retrieval.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/retrieval.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/system.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/system.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SystemClient = void 0;\nclass SystemClient {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Check the health of the R2R server.\n     */\n    async health() {\n        return await this.client.makeRequest(\"GET\", \"health\");\n    }\n    /**\n     * Get the configuration settings for the R2R server.\n     * @returns\n     */\n    async settings() {\n        return await this.client.makeRequest(\"GET\", \"system/settings\");\n    }\n    /**\n     * Get statistics about the server, including the start time, uptime,\n     * CPU usage, and memory usage.\n     * @returns\n     */\n    async status() {\n        return await this.client.makeRequest(\"GET\", \"system/status\");\n    }\n}\nexports.SystemClient = SystemClient;\n//# sourceMappingURL=system.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vcjJyLWpzQDAuNC40My9ub2RlX21vZHVsZXMvcjJyLWpzL2Rpc3QvdjMvY2xpZW50cy9zeXN0ZW0uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsb0JBQW9CO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEIiLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHIyci1qc0AwLjQuNDNcXG5vZGVfbW9kdWxlc1xccjJyLWpzXFxkaXN0XFx2M1xcY2xpZW50c1xcc3lzdGVtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5TeXN0ZW1DbGllbnQgPSB2b2lkIDA7XG5jbGFzcyBTeXN0ZW1DbGllbnQge1xuICAgIGNvbnN0cnVjdG9yKGNsaWVudCkge1xuICAgICAgICB0aGlzLmNsaWVudCA9IGNsaWVudDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogQ2hlY2sgdGhlIGhlYWx0aCBvZiB0aGUgUjJSIHNlcnZlci5cbiAgICAgKi9cbiAgICBhc3luYyBoZWFsdGgoKSB7XG4gICAgICAgIHJldHVybiBhd2FpdCB0aGlzLmNsaWVudC5tYWtlUmVxdWVzdChcIkdFVFwiLCBcImhlYWx0aFwiKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0IHRoZSBjb25maWd1cmF0aW9uIHNldHRpbmdzIGZvciB0aGUgUjJSIHNlcnZlci5cbiAgICAgKiBAcmV0dXJuc1xuICAgICAqL1xuICAgIGFzeW5jIHNldHRpbmdzKCkge1xuICAgICAgICByZXR1cm4gYXdhaXQgdGhpcy5jbGllbnQubWFrZVJlcXVlc3QoXCJHRVRcIiwgXCJzeXN0ZW0vc2V0dGluZ3NcIik7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIEdldCBzdGF0aXN0aWNzIGFib3V0IHRoZSBzZXJ2ZXIsIGluY2x1ZGluZyB0aGUgc3RhcnQgdGltZSwgdXB0aW1lLFxuICAgICAqIENQVSB1c2FnZSwgYW5kIG1lbW9yeSB1c2FnZS5cbiAgICAgKiBAcmV0dXJuc1xuICAgICAqL1xuICAgIGFzeW5jIHN0YXR1cygpIHtcbiAgICAgICAgcmV0dXJuIGF3YWl0IHRoaXMuY2xpZW50Lm1ha2VSZXF1ZXN0KFwiR0VUXCIsIFwic3lzdGVtL3N0YXR1c1wiKTtcbiAgICB9XG59XG5leHBvcnRzLlN5c3RlbUNsaWVudCA9IFN5c3RlbUNsaWVudDtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXN5c3RlbS5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/system.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/users.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/users.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.UsersClient = void 0;\nconst utils_1 = __webpack_require__(/*! ../../utils */ \"(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/utils/index.js\");\nlet fs;\nif (typeof window === \"undefined\") {\n    fs = __webpack_require__(/*! fs */ \"fs\");\n}\nclass UsersClient {\n    constructor(client) {\n        this.client = client;\n    }\n    /**\n     * Create a new user.\n     * @param email User's email address\n     * @param password User's password\n     * @param name The name for the new user\n     * @param bio The bio for the new user\n     * @param profilePicture The profile picture for the new user\n     * @param isVerified Whether the user is verified\n     * @returns WrappedUserResponse\n     */\n    async create(options) {\n        const data = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, (options.email && { email: options.email })), (options.password && { password: options.password })), (options.name && { name: options.name })), (options.bio && { bio: options.bio })), (options.profilePicture && {\n            profile_picture: options.profilePicture,\n        })), (options.isVerified !== undefined && {\n            is_verified: options.isVerified,\n        }));\n        return this.client.makeRequest(\"POST\", \"users\", {\n            data: data,\n        });\n    }\n    /**\n     * Send a verification email to a user.\n     * @param email User's email address\n     * @returns WrappedGenericMessageResponse\n     */\n    async sendVerificationEmail(options) {\n        return this.client.makeRequest(\"POST\", \"users/send-verification-email\", {\n            data: options.email,\n            headers: {\n                \"Content-Type\": \"text/plain\",\n            },\n        });\n    }\n    /**\n     * Delete a specific user.\n     * Users can only delete their own account unless they are superusers.\n     * @param id User ID to delete\n     * @param password User's password\n     * @returns\n     */\n    async delete(options) {\n        return this.client.makeRequest(\"DELETE\", `users/${options.id}`, {\n            data: {\n                password: options.password,\n            },\n        });\n    }\n    /**\n     * Verify a user's email address.\n     * @param email User's email address\n     * @param verificationCode Verification code sent to the user's email\n     */\n    async verifyEmail(options) {\n        return this.client.makeRequest(\"POST\", \"users/verify-email\", {\n            data: options,\n        });\n    }\n    /**\n     * Log in a user.\n     * @param email User's email address\n     * @param password User's password\n     * @returns\n     */\n    async login(options) {\n        const response = await this.client.makeRequest(\"POST\", \"users/login\", {\n            data: {\n                username: options.email,\n                password: options.password,\n            },\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\",\n            },\n        });\n        if (response === null || response === void 0 ? void 0 : response.results) {\n            this.client.setTokens(response.results.accessToken.token, response.results.refreshToken.token);\n        }\n        return response;\n    }\n    /**\n     * Log in using an existing access token.\n     * @param accessToken Existing access token\n     * @returns\n     */\n    async loginWithToken(options) {\n        this.client.setTokens(options.accessToken, null);\n        try {\n            const response = await this.client.makeRequest(\"GET\", \"users/me\");\n            return {\n                results: {\n                    access_token: {\n                        token: options.accessToken,\n                        token_type: \"access_token\",\n                    },\n                },\n            };\n        }\n        catch (error) {\n            this.client.setTokens(null, null);\n            throw new Error(\"Invalid token provided\");\n        }\n    }\n    /**\n     * Log out the current user.\n     * @returns\n     */\n    async logout() {\n        const response = await this.client.makeRequest(\"POST\", \"users/logout\");\n        this.client.setTokens(null, null);\n        return response;\n    }\n    /**\n     * Refresh the access token using the refresh token.\n     * @returns\n     */\n    async refreshAccessToken() {\n        const refreshToken = this.client.getRefreshToken();\n        if (!refreshToken) {\n            throw new Error(\"No refresh token available. Please login again.\");\n        }\n        const response = await this.client.makeRequest(\"POST\", \"users/refresh-token\", {\n            data: refreshToken,\n            headers: {\n                \"Content-Type\": \"application/x-www-form-urlencoded\",\n            },\n        });\n        if (response === null || response === void 0 ? void 0 : response.results) {\n            this.client.setTokens(response.results.accessToken.token, response.results.refreshToken.token);\n        }\n        else {\n            throw new Error(\"Invalid response structure\");\n        }\n        return response;\n    }\n    /**\n     * Change the user's password.\n     * @param current_password User's current password\n     * @param new_password User's new password\n     * @returns\n     */\n    async changePassword(options) {\n        return this.client.makeRequest(\"POST\", \"users/change-password\", {\n            data: options,\n        });\n    }\n    async requestPasswordReset(email) {\n        return this.client.makeRequest(\"POST\", \"users/request-password-reset\", {\n            data: email,\n            headers: {\n                \"Content-Type\": \"text/plain\",\n            },\n        });\n    }\n    /**\n     * Reset a user's password using a reset token.\n     * @param reset_token Reset token sent to the user's email\n     * @param new_password New password for the user\n     * @returns\n     */\n    async resetPassword(options) {\n        return this.client.makeRequest(\"POST\", \"users/reset-password\", {\n            data: options,\n        });\n    }\n    /**\n     * List users with pagination and filtering options.\n     * @param ids Optional list of user IDs to filter by\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 100. Defaults to 100.\n     * @returns\n     */\n    async list(options) {\n        var _a, _b;\n        const params = {\n            offset: (_a = options === null || options === void 0 ? void 0 : options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options === null || options === void 0 ? void 0 : options.limit) !== null && _b !== void 0 ? _b : 100,\n        };\n        if (options === null || options === void 0 ? void 0 : options.ids) {\n            params.ids = options.ids;\n        }\n        return this.client.makeRequest(\"GET\", \"users\", {\n            params,\n        });\n    }\n    /**\n     * Get a specific user.\n     * @param id User ID to retrieve\n     * @returns\n     */\n    async retrieve(options) {\n        return this.client.makeRequest(\"GET\", `users/${options.id}`);\n    }\n    /**\n     * Get detailed information about the currently authenticated user.\n     * @returns\n     */\n    async me() {\n        return this.client.makeRequest(\"GET\", `users/me`);\n    }\n    /**\n     * Update a user.\n     * @param id User ID to update\n     * @param email Optional new email for the user\n     * @param is_superuser Optional new superuser status for the user\n     * @param name Optional new name for the user\n     * @param bio Optional new bio for the user\n     * @param profilePicture Optional new profile picture for the user\n     * @returns\n     */\n    async update(options) {\n        const data = Object.assign(Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({}, (options.email && { email: options.email })), (options.isSuperuser !== undefined && {\n            is_superuser: options.isSuperuser,\n        })), (options.name && { name: options.name })), (options.bio && { bio: options.bio })), (options.profilePicture && {\n            profile_picture: options.profilePicture,\n        })), (options.metadata && { metadata: options.metadata }));\n        return this.client.makeRequest(\"POST\", `users/${options.id}`, {\n            data,\n        });\n    }\n    /**\n     * Get all collections associated with a specific user.\n     * @param id User ID to retrieve collections for\n     * @param offset Specifies the number of objects to skip. Defaults to 0.\n     * @param limit Specifies a limit on the number of objects to return, ranging between 1 and 100. Defaults to 100.\n     * @returns\n     */\n    async listCollections(options) {\n        var _a, _b;\n        const params = {\n            offset: (_a = options.offset) !== null && _a !== void 0 ? _a : 0,\n            limit: (_b = options.limit) !== null && _b !== void 0 ? _b : 100,\n        };\n        return this.client.makeRequest(\"GET\", `users/${options.id}/collections`, {\n            params,\n        });\n    }\n    /**\n     * Add a user to a collection.\n     * @param id User ID to add\n     * @param collectionId Collection ID to add the user to\n     * @returns\n     */\n    async addToCollection(options) {\n        return this.client.makeRequest(\"POST\", `users/${options.id}/collections/${options.collectionId}`);\n    }\n    /**\n     * Remove a user from a collection.\n     * @param id User ID to remove\n     * @param collectionId Collection ID to remove the user from\n     * @returns\n     */\n    async removeFromCollection(options) {\n        return this.client.makeRequest(\"DELETE\", `users/${options.id}/collections/${options.collectionId}`);\n    }\n    /**\n     * Export users as a CSV file with support for filtering and column selection.\n     *\n     * @param options Export configuration options\n     * @param options.outputPath Path where the CSV file should be saved (Node.js only)\n     * @param options.columns Optional list of specific columns to include\n     * @param options.filters Optional filters to limit which users are exported\n     * @param options.includeHeader Whether to include column headers (default: true)\n     * @returns Promise<Blob> in browser environments, Promise<void> in Node.js\n     */\n    async export(options = {}) {\n        var _a;\n        const data = {\n            include_header: (_a = options.includeHeader) !== null && _a !== void 0 ? _a : true,\n        };\n        if (options.columns) {\n            data.columns = options.columns;\n        }\n        if (options.filters) {\n            data.filters = options.filters;\n        }\n        const response = await this.client.makeRequest(\"POST\", \"users/export\", {\n            data,\n            responseType: \"arraybuffer\",\n            headers: { Accept: \"text/csv\" },\n        });\n        // Node environment\n        if (options.outputPath && typeof process !== \"undefined\") {\n            await fs.promises.writeFile(options.outputPath, Buffer.from(response));\n            return;\n        }\n        // Browser\n        return new Blob([response], { type: \"text/csv\" });\n    }\n    /**\n     * Export users as a CSV file and save it to the user's device.\n     * @param filename\n     * @param options\n     */\n    async exportToFile(options) {\n        const blob = await this.export(options);\n        if (blob instanceof Blob) {\n            (0, utils_1.downloadBlob)(blob, options.filename);\n        }\n    }\n    /**\n     * Create a new API key for the specified user.\n     * Only superusers or the user themselves may create an API key.\n     * @param id ID of the user for whom to create an API key\n     * @returns WrappedAPIKeyResponse\n     */\n    async createApiKey(options) {\n        const data = Object.assign(Object.assign({}, (options.name && { name: options.name })), (options.description && { description: options.description }));\n        return this.client.makeRequest(\"POST\", `users/${options.id}/api-keys`, {\n            data,\n        });\n    }\n    /**\n     * List all API keys for the specified user.\n     * Only superusers or the user themselves may list the API keys.\n     * @param id ID of the user whose API keys to list\n     * @returns WrappedAPIKeysResponse\n     */\n    async listApiKeys(options) {\n        return this.client.makeRequest(\"GET\", `users/${options.id}/api-keys`);\n    }\n    /**\n     * Delete a specific API key for the specified user.\n     * Only superusers or the user themselves may delete the API key.\n     * @param id ID of the user\n     * @param keyId ID of the API key to delete\n     * @returns WrappedBooleanResponse\n     */\n    async deleteApiKey(options) {\n        return this.client.makeRequest(\"DELETE\", `users/${options.id}/api-keys/${options.keyId}`);\n    }\n    async getLimits(options) {\n        return this.client.makeRequest(\"GET\", `users/${options.id}/limits`);\n    }\n    async oauthGoogleAuthorize() {\n        return this.client.makeRequest(\"GET\", \"users/oauth/google/authorize\");\n    }\n    async oauthGithubAuthorize() {\n        return this.client.makeRequest(\"GET\", \"users/oauth/github/authorize\");\n    }\n    async oauthGoogleCallback(options) {\n        return this.client.makeRequest(\"GET\", \"users/oauth/google/callback\", {\n            params: {\n                code: options.code,\n                state: options.state,\n            },\n        });\n    }\n    async oauthGithubCallback(options) {\n        return this.client.makeRequest(\"GET\", \"users/oauth/github/callback\", {\n            params: {\n                code: options.code,\n                state: options.state,\n            },\n        });\n    }\n}\nexports.UsersClient = UsersClient;\n//# sourceMappingURL=users.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/r2r-js@0.4.43/node_modules/r2r-js/dist/v3/clients/users.js\n");

/***/ })

};
;