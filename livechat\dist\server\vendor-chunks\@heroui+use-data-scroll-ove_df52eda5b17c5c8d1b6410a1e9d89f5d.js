"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@heroui+use-data-scroll-ove_df52eda5b17c5c8d1b6410a1e9d89f5d";
exports.ids = ["vendor-chunks/@heroui+use-data-scroll-ove_df52eda5b17c5c8d1b6410a1e9d89f5d"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@heroui+use-data-scroll-ove_df52eda5b17c5c8d1b6410a1e9d89f5d/node_modules/@heroui/use-data-scroll-overflow/dist/index.mjs":
/*!******************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@heroui+use-data-scroll-ove_df52eda5b17c5c8d1b6410a1e9d89f5d/node_modules/@heroui/use-data-scroll-overflow/dist/index.mjs ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDataScrollOverflow: () => (/* binding */ useDataScrollOverflow)\n/* harmony export */ });\n/* harmony import */ var _heroui_shared_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @heroui/shared-utils */ \"(ssr)/./node_modules/.pnpm/@heroui+shared-utils@2.1.7/node_modules/@heroui/shared-utils/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.0.3_@babel+core@7.2_bea4b8f8670d814873efb69ae1bbe3e5/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n// src/index.ts\n\n\nfunction useDataScrollOverflow(props = {}) {\n  const {\n    domRef,\n    isEnabled = true,\n    overflowCheck = \"vertical\",\n    visibility = \"auto\",\n    offset = 0,\n    onVisibilityChange,\n    updateDeps = []\n  } = props;\n  const visibleRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(visibility);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const el = domRef == null ? void 0 : domRef.current;\n    if (!el || !isEnabled) return;\n    const setAttributes = (direction, hasBefore, hasAfter, prefix, suffix) => {\n      if (visibility === \"auto\") {\n        const both = `${prefix}${(0,_heroui_shared_utils__WEBPACK_IMPORTED_MODULE_1__.capitalize)(suffix)}Scroll`;\n        if (hasBefore && hasAfter) {\n          el.dataset[both] = \"true\";\n          el.removeAttribute(`data-${prefix}-scroll`);\n          el.removeAttribute(`data-${suffix}-scroll`);\n        } else {\n          el.dataset[`${prefix}Scroll`] = hasBefore.toString();\n          el.dataset[`${suffix}Scroll`] = hasAfter.toString();\n          el.removeAttribute(`data-${prefix}-${suffix}-scroll`);\n        }\n      } else {\n        const next = hasBefore && hasAfter ? \"both\" : hasBefore ? prefix : hasAfter ? suffix : \"none\";\n        if (next !== visibleRef.current) {\n          onVisibilityChange == null ? void 0 : onVisibilityChange(next);\n          visibleRef.current = next;\n        }\n      }\n    };\n    const checkOverflow = () => {\n      var _a, _b;\n      const directions = [\n        { type: \"vertical\", prefix: \"top\", suffix: \"bottom\" },\n        { type: \"horizontal\", prefix: \"left\", suffix: \"right\" }\n      ];\n      const listbox = el.querySelector('ul[data-slot=\"list\"]');\n      const scrollHeight = +((_a = listbox == null ? void 0 : listbox.getAttribute(\"data-virtual-scroll-height\")) != null ? _a : el.scrollHeight);\n      const scrollTop = +((_b = listbox == null ? void 0 : listbox.getAttribute(\"data-virtual-scroll-top\")) != null ? _b : el.scrollTop);\n      for (const { type, prefix, suffix } of directions) {\n        if (overflowCheck === type || overflowCheck === \"both\") {\n          const hasBefore = type === \"vertical\" ? scrollTop > offset : el.scrollLeft > offset;\n          const hasAfter = type === \"vertical\" ? scrollTop + el.clientHeight + offset < scrollHeight : el.scrollLeft + el.clientWidth + offset < el.scrollWidth;\n          setAttributes(type, hasBefore, hasAfter, prefix, suffix);\n        }\n      }\n    };\n    const clearOverflow = () => {\n      [\"top\", \"bottom\", \"top-bottom\", \"left\", \"right\", \"left-right\"].forEach((attr) => {\n        el.removeAttribute(`data-${attr}-scroll`);\n      });\n    };\n    el.addEventListener(\"scroll\", checkOverflow, true);\n    if (visibility !== \"auto\") {\n      clearOverflow();\n      if (visibility === \"both\") {\n        el.dataset.topBottomScroll = String(overflowCheck === \"vertical\");\n        el.dataset.leftRightScroll = String(overflowCheck === \"horizontal\");\n      } else {\n        el.dataset.topBottomScroll = \"false\";\n        el.dataset.leftRightScroll = \"false\";\n        [\"top\", \"bottom\", \"left\", \"right\"].forEach((attr) => {\n          el.dataset[`${attr}Scroll`] = String(visibility === attr);\n        });\n      }\n    }\n    return () => {\n      el.removeEventListener(\"scroll\", checkOverflow, true);\n      clearOverflow();\n    };\n  }, [...updateDeps, isEnabled, visibility, overflowCheck, onVisibilityChange, domRef]);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGhlcm91aSt1c2UtZGF0YS1zY3JvbGwtb3ZlX2RmNTJlZGE1YjE3YzVjOGQxYjY0MTBhMWU5ZDg5ZjVkL25vZGVfbW9kdWxlcy9AaGVyb3VpL3VzZS1kYXRhLXNjcm9sbC1vdmVyZmxvdy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNrRDtBQUNSO0FBQzFDLHlDQUF5QztBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKLHFCQUFxQiw2Q0FBTTtBQUMzQixFQUFFLGdEQUFTO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsT0FBTyxFQUFFLGdFQUFVLFNBQVM7QUFDcEQ7QUFDQTtBQUNBLHFDQUFxQyxPQUFPO0FBQzVDLHFDQUFxQyxPQUFPO0FBQzVDLFVBQVU7QUFDVix3QkFBd0IsT0FBTztBQUMvQix3QkFBd0IsT0FBTztBQUMvQixxQ0FBcUMsT0FBTyxHQUFHLE9BQU87QUFDdEQ7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLG1EQUFtRDtBQUM3RCxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsdUJBQXVCO0FBQzFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1DQUFtQyxLQUFLO0FBQ3hDLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsS0FBSztBQUM3QixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBR0UiLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBoZXJvdWkrdXNlLWRhdGEtc2Nyb2xsLW92ZV9kZjUyZWRhNWIxN2M1YzhkMWI2NDEwYTFlOWQ4OWY1ZFxcbm9kZV9tb2R1bGVzXFxAaGVyb3VpXFx1c2UtZGF0YS1zY3JvbGwtb3ZlcmZsb3dcXGRpc3RcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvaW5kZXgudHNcbmltcG9ydCB7IGNhcGl0YWxpemUgfSBmcm9tIFwiQGhlcm91aS9zaGFyZWQtdXRpbHNcIjtcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VEYXRhU2Nyb2xsT3ZlcmZsb3cocHJvcHMgPSB7fSkge1xuICBjb25zdCB7XG4gICAgZG9tUmVmLFxuICAgIGlzRW5hYmxlZCA9IHRydWUsXG4gICAgb3ZlcmZsb3dDaGVjayA9IFwidmVydGljYWxcIixcbiAgICB2aXNpYmlsaXR5ID0gXCJhdXRvXCIsXG4gICAgb2Zmc2V0ID0gMCxcbiAgICBvblZpc2liaWxpdHlDaGFuZ2UsXG4gICAgdXBkYXRlRGVwcyA9IFtdXG4gIH0gPSBwcm9wcztcbiAgY29uc3QgdmlzaWJsZVJlZiA9IHVzZVJlZih2aXNpYmlsaXR5KTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBlbCA9IGRvbVJlZiA9PSBudWxsID8gdm9pZCAwIDogZG9tUmVmLmN1cnJlbnQ7XG4gICAgaWYgKCFlbCB8fCAhaXNFbmFibGVkKSByZXR1cm47XG4gICAgY29uc3Qgc2V0QXR0cmlidXRlcyA9IChkaXJlY3Rpb24sIGhhc0JlZm9yZSwgaGFzQWZ0ZXIsIHByZWZpeCwgc3VmZml4KSA9PiB7XG4gICAgICBpZiAodmlzaWJpbGl0eSA9PT0gXCJhdXRvXCIpIHtcbiAgICAgICAgY29uc3QgYm90aCA9IGAke3ByZWZpeH0ke2NhcGl0YWxpemUoc3VmZml4KX1TY3JvbGxgO1xuICAgICAgICBpZiAoaGFzQmVmb3JlICYmIGhhc0FmdGVyKSB7XG4gICAgICAgICAgZWwuZGF0YXNldFtib3RoXSA9IFwidHJ1ZVwiO1xuICAgICAgICAgIGVsLnJlbW92ZUF0dHJpYnV0ZShgZGF0YS0ke3ByZWZpeH0tc2Nyb2xsYCk7XG4gICAgICAgICAgZWwucmVtb3ZlQXR0cmlidXRlKGBkYXRhLSR7c3VmZml4fS1zY3JvbGxgKTtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBlbC5kYXRhc2V0W2Ake3ByZWZpeH1TY3JvbGxgXSA9IGhhc0JlZm9yZS50b1N0cmluZygpO1xuICAgICAgICAgIGVsLmRhdGFzZXRbYCR7c3VmZml4fVNjcm9sbGBdID0gaGFzQWZ0ZXIudG9TdHJpbmcoKTtcbiAgICAgICAgICBlbC5yZW1vdmVBdHRyaWJ1dGUoYGRhdGEtJHtwcmVmaXh9LSR7c3VmZml4fS1zY3JvbGxgKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc3QgbmV4dCA9IGhhc0JlZm9yZSAmJiBoYXNBZnRlciA/IFwiYm90aFwiIDogaGFzQmVmb3JlID8gcHJlZml4IDogaGFzQWZ0ZXIgPyBzdWZmaXggOiBcIm5vbmVcIjtcbiAgICAgICAgaWYgKG5leHQgIT09IHZpc2libGVSZWYuY3VycmVudCkge1xuICAgICAgICAgIG9uVmlzaWJpbGl0eUNoYW5nZSA9PSBudWxsID8gdm9pZCAwIDogb25WaXNpYmlsaXR5Q2hhbmdlKG5leHQpO1xuICAgICAgICAgIHZpc2libGVSZWYuY3VycmVudCA9IG5leHQ7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9O1xuICAgIGNvbnN0IGNoZWNrT3ZlcmZsb3cgPSAoKSA9PiB7XG4gICAgICB2YXIgX2EsIF9iO1xuICAgICAgY29uc3QgZGlyZWN0aW9ucyA9IFtcbiAgICAgICAgeyB0eXBlOiBcInZlcnRpY2FsXCIsIHByZWZpeDogXCJ0b3BcIiwgc3VmZml4OiBcImJvdHRvbVwiIH0sXG4gICAgICAgIHsgdHlwZTogXCJob3Jpem9udGFsXCIsIHByZWZpeDogXCJsZWZ0XCIsIHN1ZmZpeDogXCJyaWdodFwiIH1cbiAgICAgIF07XG4gICAgICBjb25zdCBsaXN0Ym94ID0gZWwucXVlcnlTZWxlY3RvcigndWxbZGF0YS1zbG90PVwibGlzdFwiXScpO1xuICAgICAgY29uc3Qgc2Nyb2xsSGVpZ2h0ID0gKygoX2EgPSBsaXN0Ym94ID09IG51bGwgPyB2b2lkIDAgOiBsaXN0Ym94LmdldEF0dHJpYnV0ZShcImRhdGEtdmlydHVhbC1zY3JvbGwtaGVpZ2h0XCIpKSAhPSBudWxsID8gX2EgOiBlbC5zY3JvbGxIZWlnaHQpO1xuICAgICAgY29uc3Qgc2Nyb2xsVG9wID0gKygoX2IgPSBsaXN0Ym94ID09IG51bGwgPyB2b2lkIDAgOiBsaXN0Ym94LmdldEF0dHJpYnV0ZShcImRhdGEtdmlydHVhbC1zY3JvbGwtdG9wXCIpKSAhPSBudWxsID8gX2IgOiBlbC5zY3JvbGxUb3ApO1xuICAgICAgZm9yIChjb25zdCB7IHR5cGUsIHByZWZpeCwgc3VmZml4IH0gb2YgZGlyZWN0aW9ucykge1xuICAgICAgICBpZiAob3ZlcmZsb3dDaGVjayA9PT0gdHlwZSB8fCBvdmVyZmxvd0NoZWNrID09PSBcImJvdGhcIikge1xuICAgICAgICAgIGNvbnN0IGhhc0JlZm9yZSA9IHR5cGUgPT09IFwidmVydGljYWxcIiA/IHNjcm9sbFRvcCA+IG9mZnNldCA6IGVsLnNjcm9sbExlZnQgPiBvZmZzZXQ7XG4gICAgICAgICAgY29uc3QgaGFzQWZ0ZXIgPSB0eXBlID09PSBcInZlcnRpY2FsXCIgPyBzY3JvbGxUb3AgKyBlbC5jbGllbnRIZWlnaHQgKyBvZmZzZXQgPCBzY3JvbGxIZWlnaHQgOiBlbC5zY3JvbGxMZWZ0ICsgZWwuY2xpZW50V2lkdGggKyBvZmZzZXQgPCBlbC5zY3JvbGxXaWR0aDtcbiAgICAgICAgICBzZXRBdHRyaWJ1dGVzKHR5cGUsIGhhc0JlZm9yZSwgaGFzQWZ0ZXIsIHByZWZpeCwgc3VmZml4KTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH07XG4gICAgY29uc3QgY2xlYXJPdmVyZmxvdyA9ICgpID0+IHtcbiAgICAgIFtcInRvcFwiLCBcImJvdHRvbVwiLCBcInRvcC1ib3R0b21cIiwgXCJsZWZ0XCIsIFwicmlnaHRcIiwgXCJsZWZ0LXJpZ2h0XCJdLmZvckVhY2goKGF0dHIpID0+IHtcbiAgICAgICAgZWwucmVtb3ZlQXR0cmlidXRlKGBkYXRhLSR7YXR0cn0tc2Nyb2xsYCk7XG4gICAgICB9KTtcbiAgICB9O1xuICAgIGVsLmFkZEV2ZW50TGlzdGVuZXIoXCJzY3JvbGxcIiwgY2hlY2tPdmVyZmxvdywgdHJ1ZSk7XG4gICAgaWYgKHZpc2liaWxpdHkgIT09IFwiYXV0b1wiKSB7XG4gICAgICBjbGVhck92ZXJmbG93KCk7XG4gICAgICBpZiAodmlzaWJpbGl0eSA9PT0gXCJib3RoXCIpIHtcbiAgICAgICAgZWwuZGF0YXNldC50b3BCb3R0b21TY3JvbGwgPSBTdHJpbmcob3ZlcmZsb3dDaGVjayA9PT0gXCJ2ZXJ0aWNhbFwiKTtcbiAgICAgICAgZWwuZGF0YXNldC5sZWZ0UmlnaHRTY3JvbGwgPSBTdHJpbmcob3ZlcmZsb3dDaGVjayA9PT0gXCJob3Jpem9udGFsXCIpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgZWwuZGF0YXNldC50b3BCb3R0b21TY3JvbGwgPSBcImZhbHNlXCI7XG4gICAgICAgIGVsLmRhdGFzZXQubGVmdFJpZ2h0U2Nyb2xsID0gXCJmYWxzZVwiO1xuICAgICAgICBbXCJ0b3BcIiwgXCJib3R0b21cIiwgXCJsZWZ0XCIsIFwicmlnaHRcIl0uZm9yRWFjaCgoYXR0cikgPT4ge1xuICAgICAgICAgIGVsLmRhdGFzZXRbYCR7YXR0cn1TY3JvbGxgXSA9IFN0cmluZyh2aXNpYmlsaXR5ID09PSBhdHRyKTtcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBlbC5yZW1vdmVFdmVudExpc3RlbmVyKFwic2Nyb2xsXCIsIGNoZWNrT3ZlcmZsb3csIHRydWUpO1xuICAgICAgY2xlYXJPdmVyZmxvdygpO1xuICAgIH07XG4gIH0sIFsuLi51cGRhdGVEZXBzLCBpc0VuYWJsZWQsIHZpc2liaWxpdHksIG92ZXJmbG93Q2hlY2ssIG9uVmlzaWJpbGl0eUNoYW5nZSwgZG9tUmVmXSk7XG59XG5leHBvcnQge1xuICB1c2VEYXRhU2Nyb2xsT3ZlcmZsb3dcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@heroui+use-data-scroll-ove_df52eda5b17c5c8d1b6410a1e9d89f5d/node_modules/@heroui/use-data-scroll-overflow/dist/index.mjs\n");

/***/ })

};
;