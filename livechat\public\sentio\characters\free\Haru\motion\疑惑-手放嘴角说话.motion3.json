{"Version": 3, "Meta": {"Duration": 2.03, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 37, "TotalSegmentCount": 120, "TotalPointCount": 313, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 17, 1, 0.167, 17, 0.333, 17, 0.5, 17, 1, 0.667, 17, 0.833, 17, 1, 17, 1, 1.122, 17, 1.244, 0, 1.367, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, -16, 1, 0.167, -16, 0.333, -16, 0.5, -16, 1, 0.667, -16, 0.833, -16, 1, -16, 1, 1.122, -16, 1.244, 0, 1.367, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, -15, 1, 0.167, -15, 0.333, -15, 0.5, -15, 1, 0.667, -15, 0.833, -15, 1, -15, 1, 1.122, -15, 1.244, 9, 1.367, 9, 0, 2.033, 9]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 0, 1, 1, 1, 1.044, 1.008, 1.089, -0.008, 1.133, 0, 0, 1.2, 0, 1, 1.256, -0.008, 1.311, 1.008, 1.367, 1, 0, 2.033, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.667, 0, 0.833, 0, 1, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 0, 1, 1, 1, 1.044, 1.008, 1.089, -0.008, 1.133, 0, 0, 1.2, 0, 1, 1.256, -0.008, 1.311, 1.008, 1.367, 1, 0, 2.033, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.667, 0, 0.833, 0, 1, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.667, 0, 0.833, 0, 1, 0, 1, 1.122, 0, 1.244, 0, 1.367, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, -0.5, 1, 0.333, -0.5, 0.667, -0.5, 1, -0.5, 1, 1.122, -0.5, 1.244, 0, 1.367, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0.79, 1, 0.333, 0.79, 0.667, 0.79, 1, 0.79, 1, 1.122, 0.79, 1.244, 0, 1.367, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.667, 0, 0.833, 0, 1, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.667, 0, 0.833, 0, 1, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.667, 0, 0.833, 0, 1, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.667, 0, 0.833, 0, 1, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.667, 0, 0.833, 0, 1, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0.13, 0, 2.033, 0.13]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0.19, 0, 2.033, 0.19]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0.38, 0, 2.033, 0.38]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0.38, 0, 2.033, 0.38]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.667, 0, 0.833, 0, 1, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.067, 0, 0.133, 0.009, 0.2, 0.036, 1, 0.233, 0.049, 0.267, 0.068, 0.3, 0.092, 1, 0.333, -0.195, 0.367, 0.648, 0.4, 0.894, 1, 0.422, 0.886, 0.444, 0.392, 0.467, 0.384, 0, 0.533, 0.424, 1, 0.556, 0.417, 0.578, 0.007, 0.6, 0, 1, 0.622, 0.014, 0.644, 0.903, 0.667, 0.918, 1, 0.689, 0.903, 0.711, 0.022, 0.733, 0.008, 1, 0.756, 0.009, 0.778, 0.001, 0.8, 0.165, 1, 0.822, 0.334, 0.844, 0.699, 0.867, 0.706, 1, 0.889, 0.695, 0.911, 0.011, 0.933, 0, 1, 0.956, 0.015, 0.978, 0.957, 1, 0.973, 1, 1.022, 0.969, 1.044, 0.844, 1.067, 0.694, 1, 1.1, 0.447, 1.133, 0.288, 1.167, 0.257, 1, 1.222, 0.158, 1.278, 0.157, 1.333, 0.157, 1, 1.356, 0.161, 1.378, 0.4, 1.4, 0.424, 1, 1.422, 0.443, 1.444, 0.439, 1.467, 0.439, 1, 1.489, 0.432, 1.511, 0.007, 1.533, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.667, 0, 0.833, 0, 1, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, -5, 1, 0.167, -5, 0.333, -5, 0.5, -5, 1, 0.667, -5, 0.833, -5, 1, -5, 1, 1.033, -5, 1.067, -5, 1.1, -5, 1, 1.222, -5, 1.344, -3, 1.467, -3, 0, 2.033, -3]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.667, 0, 0.833, 0, 1, 0, 1, 1.033, 0, 1.067, 0, 1.1, 0, 1, 1.256, 0, 1.411, 0, 1.567, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.667, 0, 0.833, 0, 1, 0, 1, 1.033, 0, 1.067, 0, 1.1, 0, 1, 1.222, 0, 1.344, -4, 1.467, -4, 0, 2.033, -4]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.167, 0, 0.333, 0, 0.5, 0, 1, 0.667, 0, 0.833, 0, 1, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamArmLA", "Segments": [0, 0.5, 1, 0.167, 0.5, 0.333, 0.5, 0.5, 0.5, 1, 0.667, 0.5, 0.833, 0.5, 1, 0.5, 0, 2.033, 0.5]}, {"Target": "Parameter", "Id": "ParamArmRA", "Segments": [0, 0.7, 1, 0.167, 0.7, 0.333, 0.7, 0.5, 0.7, 1, 0.667, 0.7, 0.833, 0.7, 1, 0.7, 1, 1.033, 0.7, 1.067, 0.7, 1.1, 0.7, 1, 1.222, 0.7, 1.344, 0.52, 1.467, 0.52, 0, 2.033, 0.52]}, {"Target": "Parameter", "Id": "ParamArmLB", "Segments": [0, 3.4, 1, 0.167, 3.4, 0.333, 3.4, 0.5, 3.4, 1, 0.667, 3.4, 0.833, 3.4, 1, 3.4, 1, 1.122, 3.4, 1.244, 4, 1.367, 4, 0, 2.033, 4]}, {"Target": "Parameter", "Id": "ParamArmRB", "Segments": [0, -1, 0, 2.033, -1]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 2.033, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 2.033, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_B_001", "Segments": [0, 1, 0, 2.03, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_B_001", "Segments": [0, 1, 0, 2.03, 1]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_R_A_001", "Segments": [0, 0, 0, 2.03, 0]}, {"Target": "PartOpacity", "Id": "PARTS_01_ARM_L_A_001", "Segments": [0, 0, 0, 2.03, 0]}]}