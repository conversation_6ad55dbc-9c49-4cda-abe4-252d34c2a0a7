"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ricky0123+vad-web@0.0.22";
exports.ids = ["vendor-chunks/@ricky0123+vad-web@0.0.22"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/asset-path.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/asset-path.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.baseAssetPath = void 0;\n// nextjs@14 bundler may attempt to execute this during SSR and crash\nconst isWeb = typeof window !== \"undefined\" && typeof window.document !== \"undefined\";\nconst currentScript = isWeb\n    ? window.document.currentScript\n    : null;\nlet basePath = \"/\";\nif (currentScript) {\n    basePath = currentScript.src\n        .replace(/#.*$/, \"\")\n        .replace(/\\?.*$/, \"\")\n        .replace(/\\/[^\\/]+$/, \"/\");\n}\nexports.baseAssetPath = basePath;\n//# sourceMappingURL=asset-path.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJpY2t5MDEyMyt2YWQtd2ViQDAuMC4yMi9ub2RlX21vZHVsZXMvQHJpY2t5MDEyMy92YWQtd2ViL2Rpc3QvYXNzZXQtcGF0aC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxxQkFBcUI7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCO0FBQ3JCIiwic291cmNlcyI6WyJEOlxcTExNXFxMZWFybmluZ1xcYWlfY29kaW5nXFxhbnl0aGluZ2NoYXRcXGxpdmVjaGF0XFxub2RlX21vZHVsZXNcXC5wbnBtXFxAcmlja3kwMTIzK3ZhZC13ZWJAMC4wLjIyXFxub2RlX21vZHVsZXNcXEByaWNreTAxMjNcXHZhZC13ZWJcXGRpc3RcXGFzc2V0LXBhdGguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLmJhc2VBc3NldFBhdGggPSB2b2lkIDA7XG4vLyBuZXh0anNAMTQgYnVuZGxlciBtYXkgYXR0ZW1wdCB0byBleGVjdXRlIHRoaXMgZHVyaW5nIFNTUiBhbmQgY3Jhc2hcbmNvbnN0IGlzV2ViID0gdHlwZW9mIHdpbmRvdyAhPT0gXCJ1bmRlZmluZWRcIiAmJiB0eXBlb2Ygd2luZG93LmRvY3VtZW50ICE9PSBcInVuZGVmaW5lZFwiO1xuY29uc3QgY3VycmVudFNjcmlwdCA9IGlzV2ViXG4gICAgPyB3aW5kb3cuZG9jdW1lbnQuY3VycmVudFNjcmlwdFxuICAgIDogbnVsbDtcbmxldCBiYXNlUGF0aCA9IFwiL1wiO1xuaWYgKGN1cnJlbnRTY3JpcHQpIHtcbiAgICBiYXNlUGF0aCA9IGN1cnJlbnRTY3JpcHQuc3JjXG4gICAgICAgIC5yZXBsYWNlKC8jLiokLywgXCJcIilcbiAgICAgICAgLnJlcGxhY2UoL1xcPy4qJC8sIFwiXCIpXG4gICAgICAgIC5yZXBsYWNlKC9cXC9bXlxcL10rJC8sIFwiL1wiKTtcbn1cbmV4cG9ydHMuYmFzZUFzc2V0UGF0aCA9IGJhc2VQYXRoO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXNzZXQtcGF0aC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/asset-path.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/default-model-fetcher.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/default-model-fetcher.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.defaultModelFetcher = void 0;\nconst defaultModelFetcher = (path) => {\n    return fetch(path).then((model) => model.arrayBuffer());\n};\nexports.defaultModelFetcher = defaultModelFetcher;\n//# sourceMappingURL=default-model-fetcher.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJpY2t5MDEyMyt2YWQtd2ViQDAuMC4yMi9ub2RlX21vZHVsZXMvQHJpY2t5MDEyMy92YWQtd2ViL2Rpc3QvZGVmYXVsdC1tb2RlbC1mZXRjaGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0E7QUFDQSwyQkFBMkI7QUFDM0IiLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEByaWNreTAxMjMrdmFkLXdlYkAwLjAuMjJcXG5vZGVfbW9kdWxlc1xcQHJpY2t5MDEyM1xcdmFkLXdlYlxcZGlzdFxcZGVmYXVsdC1tb2RlbC1mZXRjaGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5kZWZhdWx0TW9kZWxGZXRjaGVyID0gdm9pZCAwO1xuY29uc3QgZGVmYXVsdE1vZGVsRmV0Y2hlciA9IChwYXRoKSA9PiB7XG4gICAgcmV0dXJuIGZldGNoKHBhdGgpLnRoZW4oKG1vZGVsKSA9PiBtb2RlbC5hcnJheUJ1ZmZlcigpKTtcbn07XG5leHBvcnRzLmRlZmF1bHRNb2RlbEZldGNoZXIgPSBkZWZhdWx0TW9kZWxGZXRjaGVyO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9ZGVmYXVsdC1tb2RlbC1mZXRjaGVyLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/default-model-fetcher.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/frame-processor.js":
/*!**************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/frame-processor.js ***!
  \**************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\n/*\nSome of this code, together with the default options found in index.ts,\nwere taken (or took inspiration) from https://github.com/snakers4/silero-vad\n*/\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.FrameProcessor = exports.validateOptions = exports.defaultV5FrameProcessorOptions = exports.defaultLegacyFrameProcessorOptions = void 0;\nconst logging_1 = __webpack_require__(/*! ./logging */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/logging.js\");\nconst messages_1 = __webpack_require__(/*! ./messages */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/messages.js\");\nconst RECOMMENDED_FRAME_SAMPLES = [512, 1024, 1536];\nexports.defaultLegacyFrameProcessorOptions = {\n    positiveSpeechThreshold: 0.5,\n    negativeSpeechThreshold: 0.5 - 0.15,\n    preSpeechPadFrames: 1,\n    redemptionFrames: 8,\n    frameSamples: 1536,\n    minSpeechFrames: 3,\n    submitUserSpeechOnPause: false,\n};\nexports.defaultV5FrameProcessorOptions = {\n    positiveSpeechThreshold: 0.5,\n    negativeSpeechThreshold: 0.5 - 0.15,\n    preSpeechPadFrames: 3,\n    redemptionFrames: 24,\n    frameSamples: 512,\n    minSpeechFrames: 9,\n    submitUserSpeechOnPause: false,\n};\nfunction validateOptions(options) {\n    if (!RECOMMENDED_FRAME_SAMPLES.includes(options.frameSamples)) {\n        logging_1.log.warn(\"You are using an unusual frame size\");\n    }\n    if (options.positiveSpeechThreshold < 0 ||\n        options.positiveSpeechThreshold > 1) {\n        logging_1.log.error(\"positiveSpeechThreshold should be a number between 0 and 1\");\n    }\n    if (options.negativeSpeechThreshold < 0 ||\n        options.negativeSpeechThreshold > options.positiveSpeechThreshold) {\n        logging_1.log.error(\"negativeSpeechThreshold should be between 0 and positiveSpeechThreshold\");\n    }\n    if (options.preSpeechPadFrames < 0) {\n        logging_1.log.error(\"preSpeechPadFrames should be positive\");\n    }\n    if (options.redemptionFrames < 0) {\n        logging_1.log.error(\"redemptionFrames should be positive\");\n    }\n}\nexports.validateOptions = validateOptions;\nconst concatArrays = (arrays) => {\n    const sizes = arrays.reduce((out, next) => {\n        out.push(out.at(-1) + next.length);\n        return out;\n    }, [0]);\n    const outArray = new Float32Array(sizes.at(-1));\n    arrays.forEach((arr, index) => {\n        const place = sizes[index];\n        outArray.set(arr, place);\n    });\n    return outArray;\n};\nclass FrameProcessor {\n    constructor(modelProcessFunc, modelResetFunc, options) {\n        this.modelProcessFunc = modelProcessFunc;\n        this.modelResetFunc = modelResetFunc;\n        this.options = options;\n        this.speaking = false;\n        this.redemptionCounter = 0;\n        this.active = false;\n        this.reset = () => {\n            this.speaking = false;\n            this.audioBuffer = [];\n            this.modelResetFunc();\n            this.redemptionCounter = 0;\n        };\n        this.pause = () => {\n            this.active = false;\n            if (this.options.submitUserSpeechOnPause) {\n                return this.endSegment();\n            }\n            else {\n                this.reset();\n                return {};\n            }\n        };\n        this.resume = () => {\n            this.active = true;\n        };\n        this.endSegment = () => {\n            const audioBuffer = this.audioBuffer;\n            this.audioBuffer = [];\n            const speaking = this.speaking;\n            this.reset();\n            const speechFrameCount = audioBuffer.reduce((acc, item) => {\n                return acc + +item.isSpeech;\n            }, 0);\n            if (speaking) {\n                if (speechFrameCount >= this.options.minSpeechFrames) {\n                    const audio = concatArrays(audioBuffer.map((item) => item.frame));\n                    return { msg: messages_1.Message.SpeechEnd, audio };\n                }\n                else {\n                    return { msg: messages_1.Message.VADMisfire };\n                }\n            }\n            return {};\n        };\n        this.process = async (frame) => {\n            if (!this.active) {\n                return {};\n            }\n            const probs = await this.modelProcessFunc(frame);\n            this.audioBuffer.push({\n                frame,\n                isSpeech: probs.isSpeech >= this.options.positiveSpeechThreshold,\n            });\n            if (probs.isSpeech >= this.options.positiveSpeechThreshold &&\n                this.redemptionCounter) {\n                this.redemptionCounter = 0;\n            }\n            if (probs.isSpeech >= this.options.positiveSpeechThreshold &&\n                !this.speaking) {\n                this.speaking = true;\n                return { probs, msg: messages_1.Message.SpeechStart, frame };\n            }\n            if (probs.isSpeech < this.options.negativeSpeechThreshold &&\n                this.speaking &&\n                ++this.redemptionCounter >= this.options.redemptionFrames) {\n                this.redemptionCounter = 0;\n                this.speaking = false;\n                const audioBuffer = this.audioBuffer;\n                this.audioBuffer = [];\n                const speechFrameCount = audioBuffer.reduce((acc, item) => {\n                    return acc + +item.isSpeech;\n                }, 0);\n                if (speechFrameCount >= this.options.minSpeechFrames) {\n                    const audio = concatArrays(audioBuffer.map((item) => item.frame));\n                    return { probs, msg: messages_1.Message.SpeechEnd, audio, frame };\n                }\n                else {\n                    return { probs, msg: messages_1.Message.VADMisfire, frame };\n                }\n            }\n            if (!this.speaking) {\n                while (this.audioBuffer.length > this.options.preSpeechPadFrames) {\n                    this.audioBuffer.shift();\n                }\n            }\n            return { probs, frame };\n        };\n        this.audioBuffer = [];\n        this.reset();\n    }\n}\nexports.FrameProcessor = FrameProcessor;\n//# sourceMappingURL=frame-processor.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/frame-processor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/index.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/index.js ***!
  \****************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.NonRealTimeVAD = exports.Message = exports.FrameProcessor = exports.getDefaultRealTimeVADOptions = exports.MicVAD = exports.DEFAULT_MODEL = exports.AudioNodeVAD = exports.utils = exports.defaultNonRealTimeVADOptions = void 0;\nconst ort = __importStar(__webpack_require__(/*! onnxruntime-web */ \"(ssr)/./node_modules/.pnpm/onnxruntime-web@1.14.0/node_modules/onnxruntime-web/dist/ort-web.node.js\"));\nconst asset_path_1 = __webpack_require__(/*! ./asset-path */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/asset-path.js\");\nconst default_model_fetcher_1 = __webpack_require__(/*! ./default-model-fetcher */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/default-model-fetcher.js\");\nconst frame_processor_1 = __webpack_require__(/*! ./frame-processor */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/frame-processor.js\");\nObject.defineProperty(exports, \"FrameProcessor\", ({ enumerable: true, get: function () { return frame_processor_1.FrameProcessor; } }));\nconst messages_1 = __webpack_require__(/*! ./messages */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/messages.js\");\nObject.defineProperty(exports, \"Message\", ({ enumerable: true, get: function () { return messages_1.Message; } }));\nconst non_real_time_vad_1 = __webpack_require__(/*! ./non-real-time-vad */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/non-real-time-vad.js\");\nconst utils_1 = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/utils.js\");\nexports.defaultNonRealTimeVADOptions = {\n    modelURL: asset_path_1.baseAssetPath + \"silero_vad_legacy.onnx\",\n    modelFetcher: default_model_fetcher_1.defaultModelFetcher,\n};\nclass NonRealTimeVAD extends non_real_time_vad_1.PlatformAgnosticNonRealTimeVAD {\n    static async new(options = {}) {\n        const { modelURL, modelFetcher } = {\n            ...exports.defaultNonRealTimeVADOptions,\n            ...options,\n        };\n        return await this._new(() => modelFetcher(modelURL), ort, options);\n    }\n}\nexports.NonRealTimeVAD = NonRealTimeVAD;\nexports.utils = {\n    audioFileToArray: utils_1.audioFileToArray,\n    minFramesForTargetMS: utils_1.minFramesForTargetMS,\n    arrayBufferToBase64: utils_1.arrayBufferToBase64,\n    encodeWAV: utils_1.encodeWAV,\n};\nvar real_time_vad_1 = __webpack_require__(/*! ./real-time-vad */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/real-time-vad.js\");\nObject.defineProperty(exports, \"AudioNodeVAD\", ({ enumerable: true, get: function () { return real_time_vad_1.AudioNodeVAD; } }));\nObject.defineProperty(exports, \"DEFAULT_MODEL\", ({ enumerable: true, get: function () { return real_time_vad_1.DEFAULT_MODEL; } }));\nObject.defineProperty(exports, \"MicVAD\", ({ enumerable: true, get: function () { return real_time_vad_1.MicVAD; } }));\nObject.defineProperty(exports, \"getDefaultRealTimeVADOptions\", ({ enumerable: true, get: function () { return real_time_vad_1.getDefaultRealTimeVADOptions; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/logging.js":
/*!******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/logging.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.log = exports.LOG_PREFIX = void 0;\nexports.LOG_PREFIX = \"[VAD]\";\nconst levels = [\"error\", \"debug\", \"warn\"];\nfunction getLog(level) {\n    return (...args) => {\n        console[level](exports.LOG_PREFIX, ...args);\n    };\n}\nconst _log = levels.reduce((acc, level) => {\n    acc[level] = getLog(level);\n    return acc;\n}, {});\nexports.log = _log;\n//# sourceMappingURL=logging.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJpY2t5MDEyMyt2YWQtd2ViQDAuMC4yMi9ub2RlX21vZHVsZXMvQHJpY2t5MDEyMy92YWQtd2ViL2Rpc3QvbG9nZ2luZy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxXQUFXLEdBQUcsa0JBQWtCO0FBQ2hDLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLElBQUk7QUFDTCxXQUFXO0FBQ1giLCJzb3VyY2VzIjpbIkQ6XFxMTE1cXExlYXJuaW5nXFxhaV9jb2RpbmdcXGFueXRoaW5nY2hhdFxcbGl2ZWNoYXRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEByaWNreTAxMjMrdmFkLXdlYkAwLjAuMjJcXG5vZGVfbW9kdWxlc1xcQHJpY2t5MDEyM1xcdmFkLXdlYlxcZGlzdFxcbG9nZ2luZy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMubG9nID0gZXhwb3J0cy5MT0dfUFJFRklYID0gdm9pZCAwO1xuZXhwb3J0cy5MT0dfUFJFRklYID0gXCJbVkFEXVwiO1xuY29uc3QgbGV2ZWxzID0gW1wiZXJyb3JcIiwgXCJkZWJ1Z1wiLCBcIndhcm5cIl07XG5mdW5jdGlvbiBnZXRMb2cobGV2ZWwpIHtcbiAgICByZXR1cm4gKC4uLmFyZ3MpID0+IHtcbiAgICAgICAgY29uc29sZVtsZXZlbF0oZXhwb3J0cy5MT0dfUFJFRklYLCAuLi5hcmdzKTtcbiAgICB9O1xufVxuY29uc3QgX2xvZyA9IGxldmVscy5yZWR1Y2UoKGFjYywgbGV2ZWwpID0+IHtcbiAgICBhY2NbbGV2ZWxdID0gZ2V0TG9nKGxldmVsKTtcbiAgICByZXR1cm4gYWNjO1xufSwge30pO1xuZXhwb3J0cy5sb2cgPSBfbG9nO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9bG9nZ2luZy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/logging.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/messages.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/messages.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Message = void 0;\nvar Message;\n(function (Message) {\n    Message[\"AudioFrame\"] = \"AUDIO_FRAME\";\n    Message[\"SpeechStart\"] = \"SPEECH_START\";\n    Message[\"VADMisfire\"] = \"VAD_MISFIRE\";\n    Message[\"SpeechEnd\"] = \"SPEECH_END\";\n    Message[\"SpeechStop\"] = \"SPEECH_STOP\";\n})(Message || (exports.Message = Message = {}));\n//# sourceMappingURL=messages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJpY2t5MDEyMyt2YWQtd2ViQDAuMC4yMi9ub2RlX21vZHVsZXMvQHJpY2t5MDEyMy92YWQtd2ViL2Rpc3QvbWVzc2FnZXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0QsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxjQUFjLGVBQWUsZUFBZTtBQUM3QyIsInNvdXJjZXMiOlsiRDpcXExMTVxcTGVhcm5pbmdcXGFpX2NvZGluZ1xcYW55dGhpbmdjaGF0XFxsaXZlY2hhdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHJpY2t5MDEyMyt2YWQtd2ViQDAuMC4yMlxcbm9kZV9tb2R1bGVzXFxAcmlja3kwMTIzXFx2YWQtd2ViXFxkaXN0XFxtZXNzYWdlcy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbmV4cG9ydHMuTWVzc2FnZSA9IHZvaWQgMDtcbnZhciBNZXNzYWdlO1xuKGZ1bmN0aW9uIChNZXNzYWdlKSB7XG4gICAgTWVzc2FnZVtcIkF1ZGlvRnJhbWVcIl0gPSBcIkFVRElPX0ZSQU1FXCI7XG4gICAgTWVzc2FnZVtcIlNwZWVjaFN0YXJ0XCJdID0gXCJTUEVFQ0hfU1RBUlRcIjtcbiAgICBNZXNzYWdlW1wiVkFETWlzZmlyZVwiXSA9IFwiVkFEX01JU0ZJUkVcIjtcbiAgICBNZXNzYWdlW1wiU3BlZWNoRW5kXCJdID0gXCJTUEVFQ0hfRU5EXCI7XG4gICAgTWVzc2FnZVtcIlNwZWVjaFN0b3BcIl0gPSBcIlNQRUVDSF9TVE9QXCI7XG59KShNZXNzYWdlIHx8IChleHBvcnRzLk1lc3NhZ2UgPSBNZXNzYWdlID0ge30pKTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPW1lc3NhZ2VzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/messages.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/common.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/common.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\n//# sourceMappingURL=common.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJpY2t5MDEyMyt2YWQtd2ViQDAuMC4yMi9ub2RlX21vZHVsZXMvQHJpY2t5MDEyMy92YWQtd2ViL2Rpc3QvbW9kZWxzL2NvbW1vbi5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCIsInNvdXJjZXMiOlsiRDpcXExMTVxcTGVhcm5pbmdcXGFpX2NvZGluZ1xcYW55dGhpbmdjaGF0XFxsaXZlY2hhdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHJpY2t5MDEyMyt2YWQtd2ViQDAuMC4yMlxcbm9kZV9tb2R1bGVzXFxAcmlja3kwMTIzXFx2YWQtd2ViXFxkaXN0XFxtb2RlbHNcXGNvbW1vbi5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbk9iamVjdC5kZWZpbmVQcm9wZXJ0eShleHBvcnRzLCBcIl9fZXNNb2R1bGVcIiwgeyB2YWx1ZTogdHJ1ZSB9KTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbW1vbi5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/index.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/index.js ***!
  \***********************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SileroV5 = exports.SileroLegacy = void 0;\n__exportStar(__webpack_require__(/*! ./common */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/common.js\"), exports);\nvar legacy_1 = __webpack_require__(/*! ./legacy */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/legacy.js\");\nObject.defineProperty(exports, \"SileroLegacy\", ({ enumerable: true, get: function () { return legacy_1.SileroLegacy; } }));\nvar v5_1 = __webpack_require__(/*! ./v5 */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/v5.js\");\nObject.defineProperty(exports, \"SileroV5\", ({ enumerable: true, get: function () { return v5_1.SileroV5; } }));\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/legacy.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/legacy.js ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar _a;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SileroLegacy = void 0;\nconst logging_1 = __webpack_require__(/*! ../logging */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/logging.js\");\nclass SileroLegacy {\n    constructor(ortInstance, _session, _h, _c, _sr) {\n        this.ortInstance = ortInstance;\n        this._session = _session;\n        this._h = _h;\n        this._c = _c;\n        this._sr = _sr;\n        this.reset_state = () => {\n            const zeroes = Array(2 * 64).fill(0);\n            this._h = new this.ortInstance.Tensor(\"float32\", zeroes, [2, 1, 64]);\n            this._c = new this.ortInstance.Tensor(\"float32\", zeroes, [2, 1, 64]);\n        };\n        this.process = async (audioFrame) => {\n            const t = new this.ortInstance.Tensor(\"float32\", audioFrame, [\n                1,\n                audioFrame.length,\n            ]);\n            const inputs = {\n                input: t,\n                h: this._h,\n                c: this._c,\n                sr: this._sr,\n            };\n            const out = await this._session.run(inputs);\n            this._h = out[\"hn\"];\n            this._c = out[\"cn\"];\n            const [isSpeech] = out[\"output\"]?.data;\n            const notSpeech = 1 - isSpeech;\n            return { notSpeech, isSpeech };\n        };\n    }\n}\nexports.SileroLegacy = SileroLegacy;\n_a = SileroLegacy;\nSileroLegacy.new = async (ortInstance, modelFetcher) => {\n    logging_1.log.debug(\"initializing vad\");\n    const modelArrayBuffer = await modelFetcher();\n    const _session = await ortInstance.InferenceSession.create(modelArrayBuffer);\n    // @ts-ignore\n    const _sr = new ortInstance.Tensor(\"int64\", [16000n]);\n    const zeroes = Array(2 * 64).fill(0);\n    const _h = new ortInstance.Tensor(\"float32\", zeroes, [2, 1, 64]);\n    const _c = new ortInstance.Tensor(\"float32\", zeroes, [2, 1, 64]);\n    logging_1.log.debug(\"vad is initialized\");\n    const model = new _a(ortInstance, _session, _h, _c, _sr);\n    return model;\n};\n//# sourceMappingURL=legacy.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/legacy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/v5.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/v5.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar _a;\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.SileroV5 = void 0;\nconst logging_1 = __webpack_require__(/*! ../logging */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/logging.js\");\nfunction getNewState(ortInstance) {\n    const zeroes = Array(2 * 128).fill(0);\n    return new ortInstance.Tensor(\"float32\", zeroes, [2, 1, 128]);\n}\nclass SileroV5 {\n    constructor(_session, _state, _sr, ortInstance) {\n        this._session = _session;\n        this._state = _state;\n        this._sr = _sr;\n        this.ortInstance = ortInstance;\n        this.reset_state = () => {\n            this._state = getNewState(this.ortInstance);\n        };\n        this.process = async (audioFrame) => {\n            const t = new this.ortInstance.Tensor(\"float32\", audioFrame, [\n                1,\n                audioFrame.length,\n            ]);\n            const inputs = {\n                input: t,\n                state: this._state,\n                sr: this._sr,\n            };\n            const out = await this._session.run(inputs);\n            // @ts-ignore\n            this._state = out[\"stateN\"];\n            // @ts-ignore\n            const [isSpeech] = out[\"output\"]?.data;\n            const notSpeech = 1 - isSpeech;\n            return { notSpeech, isSpeech };\n        };\n    }\n}\nexports.SileroV5 = SileroV5;\n_a = SileroV5;\nSileroV5.new = async (ortInstance, modelFetcher) => {\n    logging_1.log.debug(\"Loading VAD...\");\n    const modelArrayBuffer = await modelFetcher();\n    const _session = await ortInstance.InferenceSession.create(modelArrayBuffer);\n    // @ts-ignore\n    const _sr = new ortInstance.Tensor(\"int64\", [16000n]);\n    const _state = getNewState(ortInstance);\n    logging_1.log.debug(\"...finished loading VAD\");\n    return new _a(_session, _state, _sr, ortInstance);\n};\n//# sourceMappingURL=v5.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/v5.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/non-real-time-vad.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/non-real-time-vad.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.PlatformAgnosticNonRealTimeVAD = exports.defaultNonRealTimeVADOptions = void 0;\nconst frame_processor_1 = __webpack_require__(/*! ./frame-processor */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/frame-processor.js\");\nconst messages_1 = __webpack_require__(/*! ./messages */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/messages.js\");\nconst models_1 = __webpack_require__(/*! ./models */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/index.js\");\nconst resampler_1 = __webpack_require__(/*! ./resampler */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/resampler.js\");\nexports.defaultNonRealTimeVADOptions = {\n    ...frame_processor_1.defaultLegacyFrameProcessorOptions,\n    ortConfig: undefined,\n};\nclass PlatformAgnosticNonRealTimeVAD {\n    static async _new(modelFetcher, ort, options = {}) {\n        const fullOptions = {\n            ...exports.defaultNonRealTimeVADOptions,\n            ...options,\n        };\n        if (fullOptions.ortConfig !== undefined) {\n            fullOptions.ortConfig(ort);\n        }\n        const vad = new this(modelFetcher, ort, fullOptions);\n        await vad.init();\n        return vad;\n    }\n    constructor(modelFetcher, ort, options) {\n        this.modelFetcher = modelFetcher;\n        this.ort = ort;\n        this.options = options;\n        this.init = async () => {\n            const model = await models_1.SileroLegacy.new(this.ort, this.modelFetcher);\n            this.frameProcessor = new frame_processor_1.FrameProcessor(model.process, model.reset_state, {\n                frameSamples: this.options.frameSamples,\n                positiveSpeechThreshold: this.options.positiveSpeechThreshold,\n                negativeSpeechThreshold: this.options.negativeSpeechThreshold,\n                redemptionFrames: this.options.redemptionFrames,\n                preSpeechPadFrames: this.options.preSpeechPadFrames,\n                minSpeechFrames: this.options.minSpeechFrames,\n                submitUserSpeechOnPause: this.options.submitUserSpeechOnPause,\n            });\n            this.frameProcessor.resume();\n        };\n        this.run = async function* (inputAudio, sampleRate) {\n            const resamplerOptions = {\n                nativeSampleRate: sampleRate,\n                targetSampleRate: 16000,\n                targetFrameSize: this.options.frameSamples,\n            };\n            const resampler = new resampler_1.Resampler(resamplerOptions);\n            let start = 0;\n            let end = 0;\n            let frameIndex = 0;\n            for await (const frame of resampler.stream(inputAudio)) {\n                const { msg, audio } = await this.frameProcessor.process(frame);\n                switch (msg) {\n                    case messages_1.Message.SpeechStart:\n                        start = (frameIndex * this.options.frameSamples) / 16;\n                        break;\n                    case messages_1.Message.SpeechEnd:\n                        end = ((frameIndex + 1) * this.options.frameSamples) / 16;\n                        yield { audio, start, end };\n                        break;\n                    default:\n                        break;\n                }\n                frameIndex++;\n            }\n            const { msg, audio } = this.frameProcessor.endSegment();\n            if (msg == messages_1.Message.SpeechEnd) {\n                yield {\n                    audio,\n                    start,\n                    end: (frameIndex * this.options.frameSamples) / 16,\n                };\n            }\n        };\n        (0, frame_processor_1.validateOptions)(options);\n    }\n}\nexports.PlatformAgnosticNonRealTimeVAD = PlatformAgnosticNonRealTimeVAD;\n//# sourceMappingURL=non-real-time-vad.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/non-real-time-vad.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/real-time-vad.js":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/real-time-vad.js ***!
  \************************************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (this && this.__importStar) || function (mod) {\n    if (mod && mod.__esModule) return mod;\n    var result = {};\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n    __setModuleDefault(result, mod);\n    return result;\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.AudioNodeVAD = exports.MicVAD = exports.getDefaultRealTimeVADOptions = exports.ort = exports.DEFAULT_MODEL = void 0;\nconst ortInstance = __importStar(__webpack_require__(/*! onnxruntime-web */ \"(ssr)/./node_modules/.pnpm/onnxruntime-web@1.14.0/node_modules/onnxruntime-web/dist/ort-web.node.js\"));\nconst default_model_fetcher_1 = __webpack_require__(/*! ./default-model-fetcher */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/default-model-fetcher.js\");\nconst frame_processor_1 = __webpack_require__(/*! ./frame-processor */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/frame-processor.js\");\nconst logging_1 = __webpack_require__(/*! ./logging */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/logging.js\");\nconst messages_1 = __webpack_require__(/*! ./messages */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/messages.js\");\nconst models_1 = __webpack_require__(/*! ./models */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/models/index.js\");\nconst resampler_1 = __webpack_require__(/*! ./resampler */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/resampler.js\");\nexports.DEFAULT_MODEL = \"legacy\";\nexports.ort = ortInstance;\nconst workletFile = \"vad.worklet.bundle.min.js\";\nconst sileroV5File = \"silero_vad_v5.onnx\";\nconst sileroLegacyFile = \"silero_vad_legacy.onnx\";\nconst getDefaultRealTimeVADOptions = (model) => {\n    const frameProcessorOptions = model === \"v5\"\n        ? frame_processor_1.defaultV5FrameProcessorOptions\n        : frame_processor_1.defaultLegacyFrameProcessorOptions;\n    return {\n        ...frameProcessorOptions,\n        onFrameProcessed: (probabilities) => { },\n        onVADMisfire: () => {\n            logging_1.log.debug(\"VAD misfire\");\n        },\n        onSpeechStart: () => {\n            logging_1.log.debug(\"Detected speech start\");\n        },\n        onSpeechEnd: () => {\n            logging_1.log.debug(\"Detected speech end\");\n        },\n        baseAssetPath: \"https://cdn.jsdelivr.net/npm/@ricky0123/vad-web@0.0.22/dist/\",\n        onnxWASMBasePath: \"https://cdn.jsdelivr.net/npm/onnxruntime-web@1.14.0/dist/\",\n        stream: undefined,\n        ortConfig: undefined,\n        model: exports.DEFAULT_MODEL,\n        workletOptions: {},\n    };\n};\nexports.getDefaultRealTimeVADOptions = getDefaultRealTimeVADOptions;\nclass MicVAD {\n    static async new(options = {}) {\n        const fullOptions = {\n            ...(0, exports.getDefaultRealTimeVADOptions)(options.model ?? exports.DEFAULT_MODEL),\n            ...options,\n        };\n        (0, frame_processor_1.validateOptions)(fullOptions);\n        let stream;\n        if (fullOptions.stream === undefined)\n            stream = await navigator.mediaDevices.getUserMedia({\n                audio: {\n                    ...fullOptions.additionalAudioConstraints,\n                    channelCount: 1,\n                    echoCancellation: true,\n                    autoGainControl: true,\n                    noiseSuppression: true,\n                },\n            });\n        else\n            stream = fullOptions.stream;\n        const audioContext = new AudioContext();\n        const sourceNode = new MediaStreamAudioSourceNode(audioContext, {\n            mediaStream: stream,\n        });\n        const audioNodeVAD = await AudioNodeVAD.new(audioContext, fullOptions);\n        audioNodeVAD.receive(sourceNode);\n        return new MicVAD(fullOptions, audioContext, stream, audioNodeVAD, sourceNode);\n    }\n    constructor(options, audioContext, stream, audioNodeVAD, sourceNode, listening = false) {\n        this.options = options;\n        this.audioContext = audioContext;\n        this.stream = stream;\n        this.audioNodeVAD = audioNodeVAD;\n        this.sourceNode = sourceNode;\n        this.listening = listening;\n        this.pause = () => {\n            this.audioNodeVAD.pause();\n            this.listening = false;\n        };\n        this.start = () => {\n            this.audioNodeVAD.start();\n            this.listening = true;\n        };\n        this.destroy = () => {\n            if (this.listening) {\n                this.pause();\n            }\n            if (this.options.stream === undefined) {\n                this.stream.getTracks().forEach((track) => track.stop());\n            }\n            this.sourceNode.disconnect();\n            this.audioNodeVAD.destroy();\n            this.audioContext.close();\n        };\n    }\n}\nexports.MicVAD = MicVAD;\nclass AudioNodeVAD {\n    static async new(ctx, options = {}) {\n        const fullOptions = {\n            ...(0, exports.getDefaultRealTimeVADOptions)(options.model ?? exports.DEFAULT_MODEL),\n            ...options,\n        };\n        (0, frame_processor_1.validateOptions)(fullOptions);\n        exports.ort.env.wasm.wasmPaths = fullOptions.onnxWASMBasePath;\n        if (fullOptions.ortConfig !== undefined) {\n            fullOptions.ortConfig(exports.ort);\n        }\n        const modelFile = fullOptions.model === \"v5\" ? sileroV5File : sileroLegacyFile;\n        const modelURL = fullOptions.baseAssetPath + modelFile;\n        const modelFactory = fullOptions.model === \"v5\" ? models_1.SileroV5.new : models_1.SileroLegacy.new;\n        let model;\n        try {\n            model = await modelFactory(exports.ort, () => (0, default_model_fetcher_1.defaultModelFetcher)(modelURL));\n        }\n        catch (e) {\n            console.error(`Encountered an error while loading model file ${modelURL}`);\n            throw e;\n        }\n        const frameProcessor = new frame_processor_1.FrameProcessor(model.process, model.reset_state, {\n            frameSamples: fullOptions.frameSamples,\n            positiveSpeechThreshold: fullOptions.positiveSpeechThreshold,\n            negativeSpeechThreshold: fullOptions.negativeSpeechThreshold,\n            redemptionFrames: fullOptions.redemptionFrames,\n            preSpeechPadFrames: fullOptions.preSpeechPadFrames,\n            minSpeechFrames: fullOptions.minSpeechFrames,\n            submitUserSpeechOnPause: fullOptions.submitUserSpeechOnPause,\n        });\n        const audioNodeVAD = new AudioNodeVAD(ctx, fullOptions, frameProcessor);\n        await audioNodeVAD.setupAudioNode();\n        return audioNodeVAD;\n    }\n    constructor(ctx, options, frameProcessor) {\n        this.ctx = ctx;\n        this.options = options;\n        this.bufferIndex = 0;\n        this.pause = () => {\n            const ev = this.frameProcessor.pause();\n            this.handleFrameProcessorEvent(ev);\n        };\n        this.start = () => {\n            this.frameProcessor.resume();\n        };\n        this.receive = (node) => {\n            node.connect(this.audioNode);\n        };\n        this.processFrame = async (frame) => {\n            const ev = await this.frameProcessor.process(frame);\n            this.handleFrameProcessorEvent(ev);\n        };\n        this.handleFrameProcessorEvent = (ev) => {\n            if (ev.probs !== undefined) {\n                this.options.onFrameProcessed(ev.probs, ev.frame);\n            }\n            switch (ev.msg) {\n                case messages_1.Message.SpeechStart:\n                    this.options.onSpeechStart();\n                    break;\n                case messages_1.Message.VADMisfire:\n                    this.options.onVADMisfire();\n                    break;\n                case messages_1.Message.SpeechEnd:\n                    this.options.onSpeechEnd(ev.audio);\n                    break;\n            }\n        };\n        this.destroy = () => {\n            if (this.audioNode instanceof AudioWorkletNode) {\n                this.audioNode.port.postMessage({\n                    message: messages_1.Message.SpeechStop,\n                });\n            }\n            this.audioNode.disconnect();\n            this.gainNode?.disconnect();\n        };\n        this.frameProcessor = frameProcessor;\n    }\n    async setupAudioNode() {\n        const hasAudioWorklet = \"audioWorklet\" in this.ctx && typeof AudioWorkletNode === \"function\";\n        if (hasAudioWorklet) {\n            try {\n                const workletURL = this.options.baseAssetPath + workletFile;\n                await this.ctx.audioWorklet.addModule(workletURL);\n                const workletOptions = this.options.workletOptions ?? {};\n                workletOptions.processorOptions = {\n                    ...(workletOptions.processorOptions ?? {}),\n                    frameSamples: this.options.frameSamples,\n                };\n                this.audioNode = new AudioWorkletNode(this.ctx, \"vad-helper-worklet\", workletOptions);\n                this.audioNode.port.onmessage = async (ev) => {\n                    switch (ev.data?.message) {\n                        case messages_1.Message.AudioFrame:\n                            let buffer = ev.data.data;\n                            if (!(buffer instanceof ArrayBuffer)) {\n                                buffer = new ArrayBuffer(ev.data.data.byteLength);\n                                new Uint8Array(buffer).set(new Uint8Array(ev.data.data));\n                            }\n                            const frame = new Float32Array(buffer);\n                            await this.processFrame(frame);\n                            break;\n                    }\n                };\n                return;\n            }\n            catch (e) {\n                console.log(\"AudioWorklet setup failed, falling back to ScriptProcessor\", e);\n            }\n        }\n        // Initialize resampler for ScriptProcessor\n        this.resampler = new resampler_1.Resampler({\n            nativeSampleRate: this.ctx.sampleRate,\n            targetSampleRate: 16000,\n            targetFrameSize: this.options.frameSamples ?? 480,\n        });\n        // Fallback to ScriptProcessor\n        const bufferSize = 4096; // Increased for more stable processing\n        this.audioNode = this.ctx.createScriptProcessor(bufferSize, 1, 1);\n        // Create a gain node with zero gain to handle the audio chain\n        this.gainNode = this.ctx.createGain();\n        this.gainNode.gain.value = 0;\n        let processingAudio = false;\n        this.audioNode.onaudioprocess = async (e) => {\n            if (processingAudio)\n                return;\n            processingAudio = true;\n            try {\n                const input = e.inputBuffer.getChannelData(0);\n                const output = e.outputBuffer.getChannelData(0);\n                output.fill(0);\n                // Process through resampler\n                if (this.resampler) {\n                    const frames = this.resampler.process(input);\n                    for (const frame of frames) {\n                        await this.processFrame(frame);\n                    }\n                }\n            }\n            catch (error) {\n                console.error(\"Error processing audio:\", error);\n            }\n            finally {\n                processingAudio = false;\n            }\n        };\n        // Connect the audio chain\n        this.audioNode.connect(this.gainNode);\n        this.gainNode.connect(this.ctx.destination);\n    }\n}\nexports.AudioNodeVAD = AudioNodeVAD;\n//# sourceMappingURL=real-time-vad.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/real-time-vad.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/resampler.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/resampler.js ***!
  \********************************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Resampler = void 0;\nconst logging_1 = __webpack_require__(/*! ./logging */ \"(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/logging.js\");\nclass Resampler {\n    constructor(options) {\n        this.options = options;\n        this.process = (audioFrame) => {\n            const outputFrames = [];\n            for (const sample of audioFrame) {\n                this.inputBuffer.push(sample);\n                while (this.hasEnoughDataForFrame()) {\n                    const outputFrame = this.generateOutputFrame();\n                    outputFrames.push(outputFrame);\n                }\n            }\n            return outputFrames;\n        };\n        this.stream = async function* (audioInput) {\n            for (const sample of audioInput) {\n                this.inputBuffer.push(sample);\n                while (this.hasEnoughDataForFrame()) {\n                    const outputFrame = this.generateOutputFrame();\n                    yield outputFrame;\n                }\n            }\n        };\n        if (options.nativeSampleRate < 16000) {\n            logging_1.log.error(\"nativeSampleRate is too low. Should have 16000 = targetSampleRate <= nativeSampleRate\");\n        }\n        this.inputBuffer = [];\n    }\n    hasEnoughDataForFrame() {\n        return ((this.inputBuffer.length * this.options.targetSampleRate) /\n            this.options.nativeSampleRate >=\n            this.options.targetFrameSize);\n    }\n    generateOutputFrame() {\n        const outputFrame = new Float32Array(this.options.targetFrameSize);\n        let outputIndex = 0;\n        let inputIndex = 0;\n        while (outputIndex < this.options.targetFrameSize) {\n            let sum = 0;\n            let num = 0;\n            while (inputIndex <\n                Math.min(this.inputBuffer.length, ((outputIndex + 1) * this.options.nativeSampleRate) /\n                    this.options.targetSampleRate)) {\n                const value = this.inputBuffer[inputIndex];\n                if (value !== undefined) {\n                    sum += value;\n                    num++;\n                }\n                inputIndex++;\n            }\n            outputFrame[outputIndex] = sum / num;\n            outputIndex++;\n        }\n        this.inputBuffer = this.inputBuffer.slice(inputIndex);\n        return outputFrame;\n    }\n}\nexports.Resampler = Resampler;\n//# sourceMappingURL=resampler.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/resampler.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/utils.js":
/*!****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/utils.js ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.audioFileToArray = exports.encodeWAV = exports.arrayBufferToBase64 = exports.minFramesForTargetMS = void 0;\nfunction minFramesForTargetMS(targetDuration, frameSamples, sr = 16000) {\n    return Math.ceil((targetDuration * sr) / 1000 / frameSamples);\n}\nexports.minFramesForTargetMS = minFramesForTargetMS;\nfunction arrayBufferToBase64(buffer) {\n    const bytes = new Uint8Array(buffer);\n    const len = bytes.byteLength;\n    const binary = new Array(len);\n    for (var i = 0; i < len; i++) {\n        const byte = bytes[i];\n        if (byte === undefined) {\n            break;\n        }\n        binary[i] = String.fromCharCode(byte);\n    }\n    return btoa(binary.join(\"\"));\n}\nexports.arrayBufferToBase64 = arrayBufferToBase64;\n/*\nThis rest of this was mostly copied from https://github.com/linto-ai/WebVoiceSDK\n*/\nfunction encodeWAV(samples, format = 3, sampleRate = 16000, numChannels = 1, bitDepth = 32) {\n    var bytesPerSample = bitDepth / 8;\n    var blockAlign = numChannels * bytesPerSample;\n    var buffer = new ArrayBuffer(44 + samples.length * bytesPerSample);\n    var view = new DataView(buffer);\n    /* RIFF identifier */\n    writeString(view, 0, \"RIFF\");\n    /* RIFF chunk length */\n    view.setUint32(4, 36 + samples.length * bytesPerSample, true);\n    /* RIFF type */\n    writeString(view, 8, \"WAVE\");\n    /* format chunk identifier */\n    writeString(view, 12, \"fmt \");\n    /* format chunk length */\n    view.setUint32(16, 16, true);\n    /* sample format (raw) */\n    view.setUint16(20, format, true);\n    /* channel count */\n    view.setUint16(22, numChannels, true);\n    /* sample rate */\n    view.setUint32(24, sampleRate, true);\n    /* byte rate (sample rate * block align) */\n    view.setUint32(28, sampleRate * blockAlign, true);\n    /* block align (channel count * bytes per sample) */\n    view.setUint16(32, blockAlign, true);\n    /* bits per sample */\n    view.setUint16(34, bitDepth, true);\n    /* data chunk identifier */\n    writeString(view, 36, \"data\");\n    /* data chunk length */\n    view.setUint32(40, samples.length * bytesPerSample, true);\n    if (format === 1) {\n        // Raw PCM\n        floatTo16BitPCM(view, 44, samples);\n    }\n    else {\n        writeFloat32(view, 44, samples);\n    }\n    return buffer;\n}\nexports.encodeWAV = encodeWAV;\nfunction interleave(inputL, inputR) {\n    var length = inputL.length + inputR.length;\n    var result = new Float32Array(length);\n    var index = 0;\n    var inputIndex = 0;\n    while (index < length) {\n        result[index++] = inputL[inputIndex];\n        result[index++] = inputR[inputIndex];\n        inputIndex++;\n    }\n    return result;\n}\nfunction writeFloat32(output, offset, input) {\n    for (var i = 0; i < input.length; i++, offset += 4) {\n        output.setFloat32(offset, input[i], true);\n    }\n}\nfunction floatTo16BitPCM(output, offset, input) {\n    for (var i = 0; i < input.length; i++, offset += 2) {\n        var s = Math.max(-1, Math.min(1, input[i]));\n        output.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7fff, true);\n    }\n}\nfunction writeString(view, offset, string) {\n    for (var i = 0; i < string.length; i++) {\n        view.setUint8(offset + i, string.charCodeAt(i));\n    }\n}\nasync function audioFileToArray(audioFileData) {\n    const ctx = new OfflineAudioContext(1, 1, 44100);\n    const reader = new FileReader();\n    let audioBuffer = null;\n    await new Promise((res) => {\n        reader.addEventListener(\"loadend\", (ev) => {\n            const audioData = reader.result;\n            ctx.decodeAudioData(audioData, (buffer) => {\n                audioBuffer = buffer;\n                ctx\n                    .startRendering()\n                    .then((renderedBuffer) => {\n                    console.log(\"Rendering completed successfully\");\n                    res();\n                })\n                    .catch((err) => {\n                    console.error(`Rendering failed: ${err}`);\n                });\n            }, (e) => {\n                console.log(`Error with decoding audio data: ${e}`);\n            });\n        });\n        reader.readAsArrayBuffer(audioFileData);\n    });\n    if (audioBuffer === null) {\n        throw Error(\"some shit\");\n    }\n    let _audioBuffer = audioBuffer;\n    let out = new Float32Array(_audioBuffer.length);\n    for (let i = 0; i < _audioBuffer.length; i++) {\n        for (let j = 0; j < _audioBuffer.numberOfChannels; j++) {\n            // @ts-ignore\n            out[i] += _audioBuffer.getChannelData(j)[i];\n        }\n    }\n    return { audio: out, sampleRate: _audioBuffer.sampleRate };\n}\nexports.audioFileToArray = audioFileToArray;\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ricky0123+vad-web@0.0.22/node_modules/@ricky0123/vad-web/dist/utils.js\n");

/***/ })

};
;