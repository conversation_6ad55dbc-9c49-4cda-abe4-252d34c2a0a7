exports.id=875,exports.ids=[875],exports.modules={74277:(e,t,r)=>{var s={"./en.json":[78552,552],"./zh.json":[30545,545]};function n(e){if(!r.o(s,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=s[e],n=t[0];return r.e(t[1]).then(()=>r.t(n,19))}n.keys=()=>Object.keys(s),n.id=74277,e.exports=n},4991:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"00710cb852dc46949f6337cc8e295416e52998adcf":()=>s.Y,"400e6bc43a9f5734884e1c8701d037dde4cf563a27":()=>s.k});var s=r(32802)},39243:(e,t,r)=>{Promise.resolve().then(r.bind(r,50719)),Promise.resolve().then(r.bind(r,74574))},98915:(e,t,r)=>{Promise.resolve().then(r.bind(r,74287)),Promise.resolve().then(r.bind(r,10245))},33907:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,39585,23)),Promise.resolve().then(r.t.bind(r,77465,23)),Promise.resolve().then(r.t.bind(r,13905,23)),Promise.resolve().then(r.t.bind(r,2208,23)),Promise.resolve().then(r.t.bind(r,50547,23)),Promise.resolve().then(r.t.bind(r,43748,23)),Promise.resolve().then(r.t.bind(r,29627,23))},94155:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,27353,23)),Promise.resolve().then(r.t.bind(r,1,23)),Promise.resolve().then(r.t.bind(r,32713,23)),Promise.resolve().then(r.t.bind(r,35944,23)),Promise.resolve().then(r.t.bind(r,90403,23)),Promise.resolve().then(r.t.bind(r,55420,23)),Promise.resolve().then(r.t.bind(r,51195,23))},65852:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,35155,23))},23996:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,97539,23))},74287:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>i});var s=r(15238),n=r(67433),o=r(58755),l=r(78320),a=r(59579);function i({children:e}){let t=(0,l.useRouter)();return(0,s.jsxs)(n.M,{navigate:t.push,children:[(0,s.jsx)(o.tE,{placement:"top-right",toastProps:{timeout:3e3}}),(0,s.jsx)(a.v,{children:e})]})}},59579:(e,t,r)=>{"use strict";r.d(t,{J:()=>a,v:()=>i});var s=r(15238),n=r(423),o=r(37251);let l=(0,n.createContext)({pipeline:null,setPipeline:()=>{},selectedModel:"null",setSelectedModel:()=>{},isAuthenticated:!1,login:async()=>({success:!1,userRole:"user"}),loginWithToken:async()=>({success:!1,userRole:"user"}),logout:async()=>{},unsetCredentials:async()=>{},register:async()=>{},authState:{isAuthenticated:!1,email:null,userRole:null,userId:null},getClient:()=>null,client:null,viewMode:"user",setViewMode:()=>{},isSuperUser:()=>!1,createUser:async()=>{throw Error("createUser is not implemented in the default context")}}),a=()=>{let e=(0,n.useContext)(l);if(!e)throw Error("useUser must be used within a UserProvider");return e},i=({children:e})=>{let[t,a]=(0,n.useState)(null),[i,c]=(0,n.useState)(null),[u,d]=(0,n.useState)("null"),[h,m]=(0,n.useState)("user"),[v,f]=(0,n.useState)({isAuthenticated:!1,email:null,userRole:null,userId:null}),g=(0,n.useCallback)(()=>"admin"===v.userRole&&"admin"===h,[v.userRole,h]),[b,w]=(0,n.useState)(null),k=(0,n.useCallback)(async(e,t,r)=>{let s=new o.r2rClient(r);try{let r=await s.users.login({email:e,password:t}),n=r.results.accessToken?.token||r.results.access_token?.token,o=r.results.refreshToken?.token||r.results.refresh_token?.token;if(!n)throw Error("No access token received from server");localStorage.setItem("livechatAccessToken",n),o&&localStorage.setItem("livechatRefreshToken",o),s.setTokens(n,o||""),a(s);let l=await s.users.me();if(!l.results)throw Error("Failed to get user information");let i="user";try{await s.system.settings(),i="admin"}catch(e){e instanceof Error&&"status"in e&&403===e.status||console.error("Unexpected error when checking user role:",e)}return f({isAuthenticated:!0,email:l.results.email,userRole:i,userId:l.results.id}),w(Date.now()),{success:!0,userRole:i}}catch(e){throw console.error("Login error:",e),e}},[]),y=(0,n.useCallback)(async(e,t)=>{let r=new o.r2rClient(t);try{r.setTokens(e,"");let t=await r.users.me();if(!t.results)throw Error("Failed to get user information");localStorage.setItem("livechatAccessToken",e),a(r);let s="user";try{await r.system.settings(),s="admin"}catch(e){e instanceof Error&&"status"in e&&403===e.status||console.error("Unexpected error when checking user role:",e)}return f({isAuthenticated:!0,email:t.results.email,userRole:s,userId:t.results.id}),{success:!0,userRole:s}}catch(e){throw console.error("Token login error:",e),localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),e}},[]),x=(0,n.useCallback)(async()=>{try{t&&await t.users.logout()}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),a(null),f({isAuthenticated:!1,email:null,userRole:null,userId:null}),w(null)}},[t]),p=(0,n.useCallback)(async()=>{localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),a(null),f({isAuthenticated:!1,email:null,userRole:null,userId:null}),w(null)},[]),P=(0,n.useCallback)(async(e,t,r)=>{let s=new o.r2rClient(r);try{await s.users.create({email:e,password:t}),await k(e,t,r)}catch(e){throw console.error("Registration error:",e),e}},[k]),A=(0,n.useCallback)(()=>t,[t]),E=(0,n.useCallback)(async(e,r,s=!1)=>{if(!t)throw Error("No authenticated client available");try{await t.users.create({email:e,password:r})}catch(e){throw console.error("Create user error:",e),e}},[t]);return(0,n.useEffect)(()=>{(async()=>{let e=localStorage.getItem("livechatAccessToken");if(e)try{let{loadChatConfig:t,getDeploymentUrl:s}=await r.e(536).then(r.bind(r,59536)),n=await t(),o=s(n);await y(e,o)}catch(e){console.error("Auto-login failed:",e),localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken")}})()},[y]),(0,s.jsx)(l.Provider,{value:{pipeline:i,setPipeline:c,selectedModel:u,setSelectedModel:d,isAuthenticated:v.isAuthenticated,login:k,loginWithToken:y,logout:x,unsetCredentials:p,register:P,authState:v,getClient:A,client:t,viewMode:h,setViewMode:m,isSuperUser:g,createUser:E},children:e})}},42221:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m,metadata:()=>h});var s=r(18582),n=r(5767),o=r(46099),l=r.n(o),a=r(71517),i=r(32328),c=r(87390),u=r(50719),d=function(e){return e.FREEDOM="Freedom",e}({});(function(e){return e.DIALOGUE="DIALOGUE",e.IMMSERSIVE="IMMSERSIVE",e})({}).DIALOGUE,d.FREEDOM,r(32239);let h={title:"沐光而行",icons:"favicon.icon"};async function m({children:e}){let t=await (0,i.A)(),r=await (0,c.A)();return(0,s.jsxs)("html",{lang:t,className:"dark",children:[(0,s.jsx)("head",{children:(0,s.jsx)("script",{src:"/sentio/core/live2dcubismcore.min.js"})}),(0,s.jsx)("body",{className:(0,n.A)(l().className),children:(0,s.jsx)(a.A,{messages:r,children:(0,s.jsx)(u.Providers,{children:(0,s.jsx)("main",{children:e})})})})]})}},35453:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(18582),n=r(54582);function o(){return(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen bg-gray-100",children:(0,s.jsxs)("div",{className:"max-w-md p-6 bg-white rounded-lg shadow-lg",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-gray-800 mb-4",children:"Not Found"}),(0,s.jsx)("p",{className:"text-lg text-gray-600 mb-6",children:"Could not find requested resource"}),(0,s.jsx)(n.default,{href:"/",className:"inline-block px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-300",children:"Return Home"})]})})}},50719:(e,t,r)=>{"use strict";r.d(t,{Providers:()=>s});let s=(0,r(29622).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\LLM\\Learning\\ai_coding\\anythingchat\\livechat\\app\\providers.tsx","Providers")},42245:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(14011),n=r(32802);let o=(0,s.A)(async()=>{let e=await (0,n.Y)();return{locale:e,messages:(await r(74277)(`./${e}.json`)).default}})},32802:(e,t,r)=>{"use strict";r.d(t,{Y:()=>i,k:()=>c});var s=r(60475);r(58957);var n=r(31862);let o=["zh","en"];var l=r(66633);let a="NEXT_LOCALE";async function i(){let e=await (0,n.UL)(),t=e.get(a)?.value;return t&&o.includes(t)?t:"zh"}async function c(e){(await (0,n.UL)()).set(a,e)}(0,l.D)([i,c]),(0,s.A)(i,"00710cb852dc46949f6337cc8e295416e52998adcf",null),(0,s.A)(c,"400e6bc43a9f5734884e1c8701d037dde4cf563a27",null)},66965:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(54759);let n=async e=>[{type:"image/x-icon",sizes:"256x256",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},32239:()=>{}};