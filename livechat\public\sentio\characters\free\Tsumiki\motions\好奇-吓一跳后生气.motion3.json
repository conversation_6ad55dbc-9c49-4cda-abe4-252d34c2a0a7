{"Version": 3, "Meta": {"Duration": 19.8, "Fps": 30.0, "FadeInTime": 1.0, "FadeOutTime": 0.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 45, "TotalSegmentCount": 712, "TotalPointCount": 2091, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.411, 0, 0.822, -30, 1.233, -30, 1, 1.444, -30, 1.656, -30, 1.867, -30, 1, 1.978, -30, 2.089, 0, 2.2, 0, 1, 2.356, 0, 2.511, 0, 2.667, 0, 1, 2.878, 0, 3.089, 30, 3.3, 30, 1, 3.667, 30, 4.033, 30, 4.4, 30, 1, 4.589, 30, 4.778, 30, 4.967, 30, 1, 5.278, 30, 5.589, 18.994, 5.9, 0, 1, 6.089, -11.532, 6.278, -16, 6.467, -16, 1, 6.956, -16, 7.444, 0, 7.933, 0, 1, 8.178, 0, 8.422, 0, 8.667, 0, 1, 8.811, 0, 8.956, 30, 9.1, 30, 1, 10.5, 30, 11.9, 30, 13.3, 30, 1, 13.467, 30, 13.633, 30, 13.8, 30, 1, 14, 30, 14.2, 0, 14.4, 0, 1, 15, 0, 15.6, 0, 16.2, 0, 1, 16.344, 0, 16.489, 0, 16.633, 0, 1, 16.767, 0, 16.9, 0, 17.033, 0, 1, 17.267, 0, 17.5, 0, 17.733, 0, 1, 17.856, 0, 17.978, 0, 18.1, 0, 1, 18.656, 0, 19.211, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.411, 0, 0.822, -30, 1.233, -30, 1, 1.444, -30, 1.656, -30, 1.867, -30, 1, 1.978, -30, 2.089, 30, 2.2, 30, 1, 2.356, 30, 2.511, 30, 2.667, 30, 1, 2.878, 30, 3.089, 30, 3.3, 30, 1, 3.667, 30, 4.033, 30, 4.4, 30, 1, 4.589, 30, 4.778, 30, 4.967, 30, 1, 5.278, 30, 5.589, 23.307, 5.9, 0, 1, 6.089, -14.151, 6.278, -30, 6.467, -30, 1, 6.956, -30, 7.444, 0, 7.933, 0, 1, 8.178, 0, 8.422, 0, 8.667, 0, 1, 8.811, 0, 8.956, 30, 9.1, 30, 1, 10.5, 30, 11.9, 30, 13.3, 30, 1, 13.467, 30, 13.633, 30, 13.8, 30, 1, 14, 30, 14.2, 0, 14.4, 0, 1, 15, 0, 15.6, 0, 16.2, 0, 1, 16.344, 0, 16.489, -30, 16.633, -30, 1, 16.767, -30, 16.9, -30, 17.033, -30, 1, 17.267, -30, 17.5, 0, 17.733, 0, 1, 17.856, 0, 17.978, 0, 18.1, 0, 1, 18.656, 0, 19.211, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.244, 0, 0.489, 12, 0.733, 12, 1, 0.9, 12, 1.067, 10.982, 1.233, 0, 1, 1.356, -8.054, 1.478, -30, 1.6, -30, 1, 1.689, -30, 1.778, -30, 1.867, -30, 1, 1.978, -30, 2.089, 0, 2.2, 0, 1, 2.356, 0, 2.511, 0, 2.667, 0, 1, 2.878, 0, 3.089, 30, 3.3, 30, 1, 3.667, 30, 4.033, 30, 4.4, 30, 1, 4.589, 30, 4.778, 30, 4.967, 30, 1, 5.278, 30, 5.589, 0, 5.9, 0, 1, 6.578, 0, 7.256, 3, 7.933, 3, 1, 8.178, 3, 8.422, 3, 8.667, 3, 1, 8.811, 3, 8.956, -14, 9.1, -14, 1, 10.5, -14, 11.9, -14, 13.3, -14, 1, 13.467, -14, 13.633, -14, 13.8, -14, 1, 14, -14, 14.2, 0, 14.4, 0, 1, 15.511, 0, 16.622, 0, 17.733, 0, 1, 17.856, 0, 17.978, 0, 18.1, 0, 1, 18.2, 0, 18.3, 0, 18.4, 0, 1, 18.611, 0, 18.822, 7, 19.033, 7, 1, 19.144, 7, 19.256, 7, 19.367, 7, 1, 19.5, 7, 19.633, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.244, 1, 0.489, 1.2, 0.733, 1.2, 1, 0.9, 1.2, 1.067, 1, 1.233, 1, 1, 1.444, 1, 1.656, 1, 1.867, 1, 1, 1.978, 1, 2.089, 1.2, 2.2, 1.2, 1, 2.244, 1.2, 2.289, 0, 2.333, 0, 1, 2.389, 0, 2.444, 1.2, 2.5, 1.2, 1, 2.556, 1.2, 2.611, 0, 2.667, 0, 1, 2.767, 0, 2.867, 1.2, 2.967, 1.2, 1, 3.078, 1.2, 3.189, 1.2, 3.3, 1.2, 1, 3.489, 1.2, 3.678, 1.2, 3.867, 1.2, 1, 4, 1.2, 4.133, 1.2, 4.267, 1.2, 1, 4.311, 1.2, 4.356, 0, 4.4, 0, 1, 4.689, 0, 4.978, 0, 5.267, 0, 1, 5.478, 0, 5.689, 0, 5.9, 0, 1, 6.089, 0, 6.278, 0, 6.467, 0, 1, 6.578, 0, 6.689, 0, 6.8, 0, 1, 6.911, 0, 7.022, 0.8, 7.133, 0.8, 1, 7.256, 0.8, 7.378, 0, 7.5, 0, 1, 7.644, 0, 7.789, 1.2, 7.933, 1.2, 1, 8.178, 1.2, 8.422, 1.198, 8.667, 1, 1, 8.811, 0.883, 8.956, 0, 9.1, 0, 1, 10.233, 0, 11.367, 0, 12.5, 0, 1, 12.667, 0, 12.833, 0.8, 13, 0.8, 1, 13.511, 0.8, 14.022, 0.8, 14.533, 0.8, 1, 14.6, 0.8, 14.667, 0, 14.733, 0, 1, 14.8, 0, 14.867, 0.749, 14.933, 0.8, 1, 15.156, 0.969, 15.378, 1, 15.6, 1, 1, 15.689, 1, 15.778, 1, 15.867, 1, 1, 15.978, 1, 16.089, 0, 16.2, 0, 1, 16.344, 0, 16.489, 0, 16.633, 0, 1, 16.767, 0, 16.9, 0, 17.033, 0, 1, 17.267, 0, 17.5, 1, 17.733, 1, 1, 17.856, 1, 17.978, 0, 18.1, 0, 1, 18.2, 0, 18.3, 1, 18.4, 1, 1, 18.556, 1, 18.711, 0, 18.867, 0, 1, 18.989, 0, 19.111, 0, 19.233, 0, 1, 19.411, 0, 19.589, 1, 19.767, 1, 0, 19.8, 1]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 1.1, 0, 2.2, 0, 3.3, 0, 1, 3.856, 0, 4.411, 1, 4.967, 1, 1, 5.467, 1, 5.967, 0, 6.467, 0, 1, 10.222, 0, 13.978, 0, 17.733, 0, 1, 17.856, 0, 17.978, 0, 18.1, 0, 1, 18.356, 0, 18.611, 1, 18.867, 1, 1, 18.989, 1, 19.111, 1, 19.233, 1, 1, 19.322, 1, 19.411, 0, 19.5, 0, 1, 19.589, 0, 19.678, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.244, 1, 0.489, 1.2, 0.733, 1.2, 1, 0.9, 1.2, 1.067, 1, 1.233, 1, 1, 1.444, 1, 1.656, 1, 1.867, 1, 1, 1.978, 1, 2.089, 1.2, 2.2, 1.2, 1, 2.244, 1.2, 2.289, 0, 2.333, 0, 1, 2.389, 0, 2.444, 1.2, 2.5, 1.2, 1, 2.556, 1.2, 2.611, 0, 2.667, 0, 1, 2.767, 0, 2.867, 1.2, 2.967, 1.2, 1, 3.078, 1.2, 3.189, 1.2, 3.3, 1.2, 1, 3.489, 1.2, 3.678, 1.2, 3.867, 1.2, 1, 4, 1.2, 4.133, 1.2, 4.267, 1.2, 1, 4.311, 1.2, 4.356, 0, 4.4, 0, 1, 4.689, 0, 4.978, 0, 5.267, 0, 1, 5.478, 0, 5.689, 0, 5.9, 0, 1, 6.089, 0, 6.278, 0, 6.467, 0, 1, 6.578, 0, 6.689, 0, 6.8, 0, 1, 6.911, 0, 7.022, 0.8, 7.133, 0.8, 1, 7.256, 0.8, 7.378, 0, 7.5, 0, 1, 7.644, 0, 7.789, 1.2, 7.933, 1.2, 1, 8.178, 1.2, 8.422, 1.198, 8.667, 1, 1, 8.811, 0.883, 8.956, 0, 9.1, 0, 1, 10.233, 0, 11.367, 0, 12.5, 0, 1, 12.667, 0, 12.833, 0.8, 13, 0.8, 1, 13.511, 0.8, 14.022, 0.8, 14.533, 0.8, 1, 14.6, 0.8, 14.667, 0, 14.733, 0, 1, 14.8, 0, 14.867, 0.749, 14.933, 0.8, 1, 15.156, 0.969, 15.378, 1, 15.6, 1, 1, 15.689, 1, 15.778, 1, 15.867, 1, 1, 15.978, 1, 16.089, 0, 16.2, 0, 1, 16.344, 0, 16.489, 0, 16.633, 0, 1, 16.767, 0, 16.9, 0, 17.033, 0, 1, 17.267, 0, 17.5, 1, 17.733, 1, 1, 17.856, 1, 17.978, 0, 18.1, 0, 1, 18.2, 0, 18.3, 1, 18.4, 1, 1, 18.556, 1, 18.711, 0, 18.867, 0, 1, 18.989, 0, 19.111, 0, 19.233, 0, 1, 19.411, 0, 19.589, 1, 19.767, 1, 0, 19.8, 1]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 1.1, 0, 2.2, 0, 3.3, 0, 1, 3.667, 0, 4.033, 1, 4.4, 1, 1, 4.589, 1, 4.778, 1, 4.967, 1, 1, 5.467, 1, 5.967, 0, 6.467, 0, 1, 10.222, 0, 13.978, 0, 17.733, 0, 1, 17.856, 0, 17.978, 0, 18.1, 0, 1, 18.356, 0, 18.611, 1, 18.867, 1, 1, 18.989, 1, 19.111, 1, 19.233, 1, 1, 19.322, 1, 19.411, 0, 19.5, 0, 1, 19.589, 0, 19.678, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeForm", "Segments": [0, 0, 1, 0.733, 0, 1.467, 1, 2.2, 1, 1, 2.356, 1, 2.511, 1, 2.667, 1, 1, 2.878, 1, 3.089, 1, 3.3, 1, 1, 3.667, 1, 4.033, -1, 4.4, -1, 1, 4.589, -1, 4.778, -1, 4.967, -1, 1, 5.956, -1, 6.944, -1, 7.933, -1, 1, 8.178, -1, 8.422, -1, 8.667, -1, 1, 11.689, -1, 14.711, 0, 17.733, 0, 1, 17.856, 0, 17.978, 0, 18.1, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamTear", "Segments": [0, 0, 1, 0.733, 0, 1.467, 0, 2.2, 0, 1, 2.356, 0, 2.511, 0, 2.667, 0, 1, 2.878, 0, 3.089, 0, 3.3, 0, 1, 3.667, 0, 4.033, 0, 4.4, 0, 1, 4.589, 0, 4.778, 0, 4.967, 0, 1, 5.689, 0, 6.411, 1, 7.133, 1, 1, 9.6, 1, 12.067, 1, 14.533, 1, 1, 14.667, 1, 14.8, 0, 14.933, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.411, 0, 0.822, 0.77, 1.233, 0.77, 1, 1.356, 0.77, 1.478, 0.77, 1.6, 0.77, 1, 1.689, 0.77, 1.778, 0.77, 1.867, 0.77, 1, 1.978, 0.77, 2.089, -0.04, 2.2, -0.04, 1, 2.356, -0.04, 2.511, -0.04, 2.667, -0.04, 1, 2.878, -0.04, 3.089, 1, 3.3, 1, 1, 3.489, 1, 3.678, -0.24, 3.867, -0.24, 1, 4, -0.24, 4.133, -0.24, 4.267, -0.24, 1, 4.311, -0.24, 4.356, 0, 4.4, 0, 1, 4.589, 0, 4.778, -0.54, 4.967, -0.54, 1, 5.956, -0.54, 6.944, 0, 7.933, 0, 1, 8.178, 0, 8.422, 0, 8.667, 0, 1, 10.378, 0, 12.089, -0.15, 13.8, -0.15, 1, 14, -0.15, 14.2, 0.2, 14.4, 0.2, 1, 14.611, 0.2, 14.822, -0.93, 15.033, -0.93, 1, 15.111, -0.93, 15.189, -0.93, 15.267, -0.93, 1, 15.467, -0.93, 15.667, 0, 15.867, 0, 1, 17.167, 0, 18.467, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, -0.5, 1, 0.411, -0.5, 0.822, 0.06, 1.233, 0.06, 1, 1.356, 0.06, 1.478, 0.06, 1.6, 0.06, 1, 1.689, 0.06, 1.778, 0.06, 1.867, 0.06, 1, 1.978, 0.06, 2.089, -0.22, 2.2, -0.22, 1, 2.356, -0.22, 2.511, -0.22, 2.667, -0.22, 1, 2.878, -0.22, 3.089, 1, 3.3, 1, 1, 3.489, 1, 3.678, 1, 3.867, 1, 1, 4, 1, 4.133, 1, 4.267, 1, 1, 4.311, 1, 4.356, 0.183, 4.4, 0, 1, 4.589, -0.779, 4.778, -1, 4.967, -1, 1, 5.956, -1, 6.944, -0.5, 7.933, -0.5, 1, 8.178, -0.5, 8.422, -0.5, 8.667, -0.5, 1, 10.378, -0.5, 12.089, -0.88, 13.8, -0.88, 1, 14, -0.88, 14.2, -0.591, 14.4, -0.5, 1, 14.611, -0.404, 14.822, -0.41, 15.033, -0.41, 1, 15.111, -0.41, 15.189, -0.41, 15.267, -0.41, 1, 15.467, -0.41, 15.667, 0, 15.867, 0, 1, 17.167, 0, 18.467, -0.5, 19.767, -0.5, 0, 19.8, -0.5]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 1, 1, 0.244, 1, 0.489, 1, 0.733, 1, 1, 0.9, 1, 1.067, 1, 1.233, 1, 1, 1.356, 1, 1.478, 1, 1.6, 1, 1, 1.689, 1, 1.778, 1, 1.867, 1, 1, 1.978, 1, 2.089, 0, 2.2, 0, 1, 2.356, 0, 2.511, 0, 2.667, 0, 1, 2.878, 0, 3.089, 0, 3.3, 0, 1, 3.667, 0, 4.033, 0, 4.4, 0, 1, 4.589, 0, 4.778, 0, 4.967, 0, 1, 5.278, 0, 5.589, 1, 5.9, 1, 1, 6.433, 1, 6.967, 1, 7.5, 1, 1, 7.644, 1, 7.789, 0, 7.933, 0, 1, 8.178, 0, 8.422, 0, 8.667, 0, 1, 8.811, 0, 8.956, 0, 9.1, 0, 1, 9.178, 0, 9.256, 1, 9.333, 1, 1, 10.822, 1, 12.311, 1, 13.8, 1, 1, 15.789, 1, 17.778, 1, 19.767, 1, 0, 19.8, 1]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.733, 0, 1.467, 0, 2.2, 0, 1, 2.244, 0, 2.289, -1, 2.333, -1, 1, 2.389, -1, 2.444, 0, 2.5, 0, 1, 2.556, 0, 2.611, -1, 2.667, -1, 1, 2.767, -1, 2.867, 0, 2.967, 0, 1, 3.078, 0, 3.189, 0, 3.3, 0, 1, 3.489, 0, 3.678, 0, 3.867, 0, 1, 4, 0, 4.133, 0, 4.267, 0, 1, 4.311, 0, 4.356, -1, 4.4, -1, 1, 4.689, -1, 4.978, -1, 5.267, -1, 1, 5.478, -1, 5.689, -1, 5.9, -1, 1, 6.089, -1, 6.278, -1, 6.467, -1, 1, 6.578, -1, 6.689, -1, 6.8, -1, 1, 6.911, -1, 7.022, -0.32, 7.133, -0.32, 1, 7.256, -0.32, 7.378, -1, 7.5, -1, 1, 7.644, -1, 7.789, -0.58, 7.933, -0.58, 1, 8.178, -0.58, 8.422, -0.58, 8.667, -0.58, 1, 8.811, -0.58, 8.956, -1, 9.1, -1, 1, 9.878, -1, 10.656, -1, 11.433, -1, 1, 11.5, -1, 11.567, -0.77, 11.633, -0.77, 1, 11.7, -0.77, 11.767, -1, 11.833, -1, 1, 12.056, -1, 12.278, -1, 12.5, -1, 1, 12.667, -1, 12.833, 0, 13, 0, 1, 13.511, 0, 14.022, 0, 14.533, 0, 1, 14.6, 0, 14.667, -1, 14.733, -1, 1, 14.8, -1, 14.867, 0, 14.933, 0, 1, 15.244, 0, 15.556, 0, 15.867, 0, 1, 15.978, 0, 16.089, -1, 16.2, -1, 1, 16.344, -1, 16.489, -1, 16.633, -1, 1, 16.767, -1, 16.9, -1, 17.033, -1, 1, 17.267, -1, 17.5, 0, 17.733, 0, 1, 17.856, 0, 17.978, -1, 18.1, -1, 1, 18.2, -1, 18.3, 0.2, 18.4, 0.2, 1, 18.556, 0.2, 18.711, -1, 18.867, -1, 1, 18.989, -1, 19.111, -1, 19.233, -1, 1, 19.411, -1, 19.589, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.733, 0, 1.467, 0, 2.2, 0, 1, 2.244, 0, 2.289, -1, 2.333, -1, 1, 2.389, -1, 2.444, 0, 2.5, 0, 1, 2.556, 0, 2.611, -1, 2.667, -1, 1, 2.767, -1, 2.867, 0, 2.967, 0, 1, 3.078, 0, 3.189, 0, 3.3, 0, 1, 3.489, 0, 3.678, 0, 3.867, 0, 1, 4, 0, 4.133, 0, 4.267, 0, 1, 4.311, 0, 4.356, -1, 4.4, -1, 1, 4.689, -1, 4.978, -1, 5.267, -1, 1, 5.478, -1, 5.689, -1, 5.9, -1, 1, 6.089, -1, 6.278, -1, 6.467, -1, 1, 6.578, -1, 6.689, -1, 6.8, -1, 1, 6.911, -1, 7.022, -0.32, 7.133, -0.32, 1, 7.256, -0.32, 7.378, -1, 7.5, -1, 1, 7.644, -1, 7.789, -0.58, 7.933, -0.58, 1, 8.178, -0.58, 8.422, -0.58, 8.667, -0.58, 1, 8.811, -0.58, 8.956, -1, 9.1, -1, 1, 9.878, -1, 10.656, -1, 11.433, -1, 1, 11.5, -1, 11.567, -0.77, 11.633, -0.77, 1, 11.7, -0.77, 11.767, -1, 11.833, -1, 1, 12.056, -1, 12.278, -1, 12.5, -1, 1, 12.667, -1, 12.833, 0, 13, 0, 1, 13.511, 0, 14.022, 0, 14.533, 0, 1, 14.6, 0, 14.667, -1, 14.733, -1, 1, 14.8, -1, 14.867, 0, 14.933, 0, 1, 15.244, 0, 15.556, 0, 15.867, 0, 1, 15.978, 0, 16.089, -1, 16.2, -1, 1, 16.344, -1, 16.489, -1, 16.633, -1, 1, 16.767, -1, 16.9, -1, 17.033, -1, 1, 17.267, -1, 17.5, 0, 17.733, 0, 1, 17.856, 0, 17.978, -1, 18.1, -1, 1, 18.2, -1, 18.3, 0.2, 18.4, 0.2, 1, 18.556, 0.2, 18.711, -1, 18.867, -1, 1, 18.989, -1, 19.111, -1, 19.233, -1, 1, 19.411, -1, 19.589, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 1, 1.1, 0, 2.2, 0, 3.3, 0, 1, 3.489, 0, 3.678, -1, 3.867, -1, 1, 4, -1, 4.133, -1, 4.267, -1, 1, 4.811, -1, 5.356, -1, 5.9, -1, 1, 6.311, -1, 6.722, -1, 7.133, -1, 1, 9.189, -1, 11.244, -1, 13.3, -1, 1, 13.878, -1, 14.456, 0, 15.033, 0, 1, 16.611, 0, 18.189, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 1, 1.1, 0, 2.2, 0, 3.3, 0, 1, 3.489, 0, 3.678, -1, 3.867, -1, 1, 4, -1, 4.133, -1, 4.267, -1, 1, 4.811, -1, 5.356, -1, 5.9, -1, 1, 6.311, -1, 6.722, -1, 7.133, -1, 1, 9.189, -1, 11.244, -1, 13.3, -1, 1, 13.878, -1, 14.456, 0, 15.033, 0, 1, 16.611, 0, 18.189, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 1, 1.1, 0, 2.2, 0, 3.3, 0, 1, 3.489, 0, 3.678, 1, 3.867, 1, 1, 4, 1, 4.133, 1, 4.267, 1, 1, 4.311, 1, 4.356, -1, 4.4, -1, 1, 4.9, -1, 5.4, 1, 5.9, 1, 1, 6.311, 1, 6.722, 1, 7.133, 1, 1, 7.256, 1, 7.378, 1, 7.5, 1, 1, 7.644, 1, 7.789, -1, 7.933, -1, 1, 8.178, -1, 8.422, -1, 8.667, -1, 1, 10.211, -1, 11.756, -1, 13.3, -1, 1, 13.878, -1, 14.456, -0.616, 15.033, 0, 1, 15.7, 0.711, 16.367, 1, 17.033, 1, 1, 17.267, 1, 17.5, 1, 17.733, 1, 1, 17.856, 1, 17.978, 1, 18.1, 1, 1, 18.356, 1, 18.611, 0, 18.867, 0, 1, 18.989, 0, 19.111, 0, 19.233, 0, 1, 19.411, 0, 19.589, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 1, 0.889, 0, 1.778, 0, 2.667, 0, 1, 2.878, 0, 3.089, 0, 3.3, 0, 1, 3.489, 0, 3.678, 1, 3.867, 1, 1, 4, 1, 4.133, 1, 4.267, 1, 1, 4.311, 1, 4.356, -1, 4.4, -1, 1, 4.9, -1, 5.4, 1, 5.9, 1, 1, 6.311, 1, 6.722, 1, 7.133, 1, 1, 7.256, 1, 7.378, 1, 7.5, 1, 1, 7.644, 1, 7.789, -1, 7.933, -1, 1, 8.178, -1, 8.422, -1, 8.667, -1, 1, 10.211, -1, 11.756, -1, 13.3, -1, 1, 13.878, -1, 14.456, -0.616, 15.033, 0, 1, 15.7, 0.711, 16.367, 1, 17.033, 1, 1, 17.267, 1, 17.5, 1, 17.733, 1, 1, 17.856, 1, 17.978, 1, 18.1, 1, 1, 18.356, 1, 18.611, 0, 18.867, 0, 1, 18.989, 0, 19.111, 0, 19.233, 0, 1, 19.411, 0, 19.589, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 1, 0.889, 0, 1.778, 0, 2.667, 0, 1, 2.878, 0, 3.089, -1, 3.3, -1, 1, 3.489, -1, 3.678, -1, 3.867, -1, 1, 4, -1, 4.133, -1, 4.267, -1, 1, 4.311, -1, 4.356, -1.5, 4.4, -1.5, 1, 4.9, -1.5, 5.4, -1, 5.9, -1, 1, 6.433, -1, 6.967, -1, 7.5, -1, 1, 7.644, -1, 7.789, -1.5, 7.933, -1.5, 1, 8.178, -1.5, 8.422, -1.5, 8.667, -1.5, 1, 10.211, -1.5, 11.756, -1.5, 13.3, -1.5, 1, 13.878, -1.5, 14.456, -1.39, 15.033, -1, 1, 15.222, -0.872, 15.411, -0.6, 15.6, -0.6, 1, 15.944, -0.6, 16.289, -0.6, 16.633, -0.6, 1, 16.767, -0.6, 16.9, -0.6, 17.033, -0.6, 1, 17.267, -0.6, 17.5, 0, 17.733, 0, 1, 17.856, 0, 17.978, 0, 18.1, 0, 1, 18.656, 0, 19.211, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 1, 0.889, 0, 1.778, 0, 2.667, 0, 1, 2.878, 0, 3.089, -1, 3.3, -1, 1, 3.489, -1, 3.678, -1, 3.867, -1, 1, 4, -1, 4.133, -1, 4.267, -1, 1, 4.311, -1, 4.356, -1.5, 4.4, -1.5, 1, 4.9, -1.5, 5.4, -1, 5.9, -1, 1, 6.433, -1, 6.967, -1, 7.5, -1, 1, 7.644, -1, 7.789, -1.5, 7.933, -1.5, 1, 8.178, -1.5, 8.422, -1.5, 8.667, -1.5, 1, 10.211, -1.5, 11.756, -1.5, 13.3, -1.5, 1, 13.878, -1.5, 14.456, -1.39, 15.033, -1, 1, 15.222, -0.872, 15.411, -0.6, 15.6, -0.6, 1, 15.944, -0.6, 16.289, -0.6, 16.633, -0.6, 1, 16.767, -0.6, 16.9, -0.6, 17.033, -0.6, 1, 17.267, -0.6, 17.5, 0, 17.733, 0, 1, 17.856, 0, 17.978, 0, 18.1, 0, 1, 18.656, 0, 19.211, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 1, 0.622, 1, 1.244, 1, 1.867, 1, 1, 1.978, 1, 2.089, -1, 2.2, -1, 1, 2.356, -1, 2.511, -1, 2.667, -1, 1, 2.878, -1, 3.089, 1, 3.3, 1, 1, 3.622, 1, 3.944, 1, 4.267, 1, 1, 4.311, 1, 4.356, -1, 4.4, -1, 1, 4.9, -1, 5.4, -1, 5.9, -1, 1, 6.2, -1, 6.5, -0.09, 6.8, -0.09, 1, 6.911, -0.09, 7.022, -1, 7.133, -1, 1, 7.256, -1, 7.378, -1, 7.5, -1, 1, 7.644, -1, 7.789, 0, 7.933, 0, 1, 8.178, 0, 8.422, -1, 8.667, -1, 1, 8.811, -1, 8.956, -1, 9.1, -1, 1, 10.5, -1, 11.9, -1, 13.3, -1, 1, 13.667, -1, 14.033, -0.14, 14.4, -0.14, 1, 14.8, -0.14, 15.2, -0.32, 15.6, -0.32, 1, 15.944, -0.32, 16.289, 0.43, 16.633, 0.43, 1, 16.767, 0.43, 16.9, 0.4, 17.033, 0.4, 1, 17.267, 0.4, 17.5, 1, 17.733, 1, 1, 17.856, 1, 17.978, 1, 18.1, 1, 1, 18.656, 1, 19.211, 1, 19.767, 1, 0, 19.8, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 1, 0.622, 0, 1.244, 0, 1.867, 0, 1, 1.978, 0, 2.089, 1, 2.2, 1, 1, 2.356, 1, 2.511, 1, 2.667, 1, 1, 2.878, 1, 3.089, 1, 3.3, 1, 1, 3.622, 1, 3.944, 1, 4.267, 1, 1, 4.311, 1, 4.356, 1, 4.4, 1, 1, 4.9, 1, 5.4, 0, 5.9, 0, 1, 6.2, 0, 6.5, 0.67, 6.8, 0.67, 1, 6.911, 0.67, 7.022, 0, 7.133, 0, 1, 7.256, 0, 7.378, 0, 7.5, 0, 1, 7.644, 0, 7.789, 1, 7.933, 1, 1, 8.178, 1, 8.422, 1, 8.667, 1, 1, 8.811, 1, 8.956, 0, 9.1, 0, 1, 10.5, 0, 11.9, 0, 13.3, 0, 1, 13.467, 0, 13.633, 1, 13.8, 1, 1, 14, 1, 14.2, 0, 14.4, 0, 1, 14.8, 0, 15.2, 0, 15.6, 0, 1, 15.944, 0, 16.289, 0.81, 16.633, 0.81, 1, 16.767, 0.81, 16.9, 0.826, 17.033, 0.67, 1, 17.267, 0.396, 17.5, 0, 17.733, 0, 1, 17.856, 0, 17.978, 0, 18.1, 0, 1, 18.656, 0, 19.211, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamCheek_01", "Segments": [0, 0, 1, 0.889, 0, 1.778, 0, 2.667, 0, 1, 2.878, 0, 3.089, 1, 3.3, 1, 1, 3.667, 1, 4.033, 1, 4.4, 1, 1, 4.589, 1, 4.778, 1, 4.967, 1, 1, 8.711, 1, 12.456, 1, 16.2, 1, 1, 16.344, 1, 16.489, 0, 16.633, 0, 1, 16.767, 0, 16.9, 0, 17.033, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamCheek_02", "Segments": [0, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamCheek_03", "Segments": [0, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamCheek_04", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.9, 0, 1.067, 0, 1.233, 0, 1, 1.356, 0, 1.478, 0, 1.6, 0, 1, 1.689, 0, 1.778, 0, 1.867, 0, 1, 1.978, 0, 2.089, 0, 2.2, 0, 1, 2.356, 0, 2.511, 0, 2.667, 0, 1, 2.878, 0, 3.089, 1, 3.3, 1, 1, 3.667, 1, 4.033, 1, 4.4, 1, 1, 4.589, 1, 4.778, 1, 4.967, 1, 1, 8.322, 1, 11.678, 1, 15.033, 1, 1, 15.222, 1, 15.411, 0, 15.6, 0, 1, 15.8, 0, 16, 0, 16.2, 0, 1, 16.711, 0, 17.222, 0, 17.733, 0, 1, 17.856, 0, 17.978, 0, 18.1, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.9, 0, 1.067, -3, 1.233, -3, 1, 1.356, -3, 1.478, -3, 1.6, -3, 1, 1.689, -3, 1.778, -3, 1.867, -3, 1, 1.978, -3, 2.089, -10, 2.2, -10, 1, 2.567, -10, 2.933, -10, 3.3, -10, 1, 3.667, -10, 4.033, -10, 4.4, -10, 1, 4.589, -10, 4.778, -10, 4.967, -10, 1, 5.278, -10, 5.589, 1, 5.9, 1, 1, 6.311, 1, 6.722, 0, 7.133, 0, 1, 7.4, 0, 7.667, 0, 7.933, 0, 1, 8.178, 0, 8.422, 0, 8.667, 0, 1, 8.744, 0, 8.822, 10, 8.9, 10, 1, 10.533, 10, 12.167, 10, 13.8, 10, 1, 14, 10, 14.2, 0, 14.4, 0, 1, 15, 0, 15.6, 0, 16.2, 0, 1, 16.711, 0, 17.222, 0, 17.733, 0, 1, 17.856, 0, 17.978, 0, 18.1, 0, 1, 18.656, 0, 19.211, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.9, 0, 1.067, -10, 1.233, -10, 1, 1.356, -10, 1.478, -10, 1.6, -10, 1, 1.689, -10, 1.778, -10, 1.867, -10, 1, 1.978, -10, 2.089, 0, 2.2, 0, 1, 2.567, 0, 2.933, 0, 3.3, 0, 1, 3.667, 0, 4.033, -10, 4.4, -10, 1, 4.589, -10, 4.778, 1, 4.967, 1, 1, 5.278, 1, 5.589, 1, 5.9, 1, 1, 6.311, 1, 6.722, 0, 7.133, 0, 1, 7.4, 0, 7.667, 0, 7.933, 0, 1, 8.178, 0, 8.422, 0, 8.667, 0, 1, 9.589, 0, 10.511, 0, 11.433, 0, 1, 11.5, 0, 11.567, 2, 11.633, 2, 1, 11.7, 2, 11.767, 0, 11.833, 0, 1, 13.289, 0, 14.744, 0, 16.2, 0, 1, 16.344, 0, 16.489, -4, 16.633, -4, 1, 16.767, -4, 16.9, -3.906, 17.033, -3, 1, 17.267, -1.414, 17.5, 0, 17.733, 0, 1, 17.856, 0, 17.978, 0, 18.1, 0, 1, 18.656, 0, 19.211, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.9, 0, 1.067, -3, 1.233, -3, 1, 1.444, -3, 1.656, -3, 1.867, -3, 1, 1.978, -3, 2.089, 10, 2.2, 10, 1, 2.356, 10, 2.511, 8.527, 2.667, 4, 1, 2.711, 2.706, 2.756, 0, 2.8, 0, 1, 2.867, 0, 2.933, 10, 3, 10, 1, 3.067, 10, 3.133, 0, 3.2, 0, 1, 3.233, 0, 3.267, 10, 3.3, 10, 1, 3.356, 10, 3.411, 0, 3.467, 0, 1, 3.533, 0, 3.6, 10, 3.667, 10, 1, 3.733, 10, 3.8, 0, 3.867, 0, 1, 3.922, 0, 3.978, 10, 4.033, 10, 1, 4.111, 10, 4.189, 7.135, 4.267, 0, 1, 4.311, -4.077, 4.356, -7, 4.4, -7, 1, 4.767, -7, 5.133, 4, 5.5, 4, 1, 6.1, 4, 6.7, 0, 7.3, 0, 1, 7.511, 0, 7.722, 0, 7.933, 0, 1, 8.178, 0, 8.422, 0, 8.667, 0, 1, 9.589, 0, 10.511, 0, 11.433, 0, 1, 11.567, 0, 11.7, 0, 11.833, 0, 1, 12.489, 0, 13.144, 0, 13.8, 0, 1, 13.911, 0, 14.022, 2, 14.133, 2, 1, 14.222, 2, 14.311, 0, 14.4, 0, 1, 15, 0, 15.6, 0, 16.2, 0, 1, 16.711, 0, 17.222, 0, 17.733, 0, 1, 17.856, 0, 17.978, 0, 18.1, 0, 1, 18.356, 0, 18.611, 0, 18.867, 0, 1, 18.989, 0, 19.111, 0, 19.233, 0, 1, 19.411, 0, 19.589, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 0.9, 0, 1.067, 0, 1.233, 0, 1, 1.356, 0, 1.478, 0, 1.6, 0, 1, 1.689, 0, 1.778, 0, 1.867, 0, 1, 1.978, 0, 2.089, 0, 2.2, 0, 1, 2.356, 0, 2.511, 0, 2.667, 0, 1, 2.878, 0, 3.089, 0, 3.3, 0, 1, 3.667, 0, 4.033, 0, 4.4, 0, 1, 4.589, 0, 4.778, 0, 4.967, 0, 1, 9.9, 0, 14.833, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "Param<PERSON><PERSON><PERSON>", "Segments": [0, 0, 1, 0.411, 0, 0.822, 1, 1.233, 1, 1, 1.444, 1, 1.656, 1, 1.867, 1, 1, 1.978, 1, 2.089, 0, 2.2, 0, 1, 2.356, 0, 2.511, 0, 2.667, 0, 1, 2.878, 0, 3.089, -1, 3.3, -1, 1, 3.667, -1, 4.033, -1, 4.4, -1, 1, 4.589, -1, 4.778, 0, 4.967, 0, 1, 7.122, 0, 9.278, 0, 11.433, 0, 1, 11.5, 0, 11.567, 0, 11.633, 0, 1, 11.7, 0, 11.767, 0, 11.833, 0, 1, 13.289, 0, 14.744, 0, 16.2, 0, 1, 16.344, 0, 16.489, 0.06, 16.633, 0.06, 1, 16.767, 0.06, 16.9, 0, 17.033, 0, 1, 17.267, 0, 17.5, 0, 17.733, 0, 1, 17.856, 0, 17.978, 0, 18.1, 0, 1, 18.656, 0, 19.211, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamBustY", "Segments": [0, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamTie", "Segments": [0, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamSkirt", "Segments": [0, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 1, 1.189, 0, 2.378, 0, 3.567, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamHairSide", "Segments": [0, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamHairAho", "Segments": [0, 0, 1, 0.411, 0, 0.822, 0.51, 1.233, 0.51, 1, 1.356, 0.51, 1.478, -0.32, 1.6, -0.32, 1, 1.689, -0.32, 1.778, 0, 1.867, 0, 1, 1.978, 0, 2.089, -1, 2.2, -1, 1, 2.289, -1, 2.378, 0, 2.467, 0, 1, 2.556, 0, 2.644, -1, 2.733, -1, 1, 2.833, -1, 2.933, 1, 3.033, 1, 1, 3.122, 1, 3.211, -1, 3.3, -1, 1, 3.356, -1, 3.411, 1, 3.467, 1, 1, 3.533, 1, 3.6, -1, 3.667, -1, 1, 3.733, -1, 3.8, 1, 3.867, 1, 1, 3.922, 1, 3.978, -1, 4.033, -1, 1, 4.111, -1, 4.189, 0, 4.267, 0, 1, 4.367, 0, 4.467, -0.56, 4.567, -0.56, 1, 4.7, -0.56, 4.833, 0.33, 4.967, 0.33, 1, 5.167, 0.33, 5.367, -0.32, 5.567, -0.32, 1, 5.844, -0.32, 6.122, 0, 6.4, 0, 1, 6.522, 0, 6.644, -1, 6.767, -1, 1, 6.889, -1, 7.011, -0.456, 7.133, 0, 1, 7.256, 0.456, 7.378, 0.52, 7.5, 0.52, 1, 7.644, 0.52, 7.789, -1, 7.933, -1, 1, 8.078, -1, 8.222, -1, 8.367, -1, 1, 8.467, -1, 8.567, -0.289, 8.667, 0, 1, 8.811, 0.417, 8.956, 0.45, 9.1, 0.45, 1, 9.278, 0.45, 9.456, -1, 9.633, -1, 1, 9.756, -1, 9.878, 0, 10, 0, 1, 10.1, 0, 10.2, -1, 10.3, -1, 1, 10.378, -1, 10.456, 0, 10.533, 0, 1, 10.622, 0, 10.711, -1, 10.8, -1, 1, 10.889, -1, 10.978, 0, 11.067, 0, 1, 11.156, 0, 11.244, -1, 11.333, -1, 1, 11.433, -1, 11.533, 0, 11.633, 0, 1, 12.044, 0, 12.456, 0, 12.867, 0, 1, 13.178, 0, 13.489, 1, 13.8, 1, 1, 14.244, 1, 14.689, 0, 15.133, 0, 1, 15.489, 0, 15.844, 0, 16.2, 0, 1, 16.344, 0, 16.489, 1, 16.633, 1, 1, 16.767, 1, 16.9, 0.451, 17.033, 0, 1, 17.267, -0.789, 17.5, -1, 17.733, -1, 1, 18.111, -1, 18.489, 1, 18.867, 1, 1, 19.167, 1, 19.467, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamHairTair", "Segments": [0, 0, 1, 0.622, 0, 1.244, 0, 1.867, 0, 1, 1.978, 0, 2.089, 1, 2.2, 1, 1, 2.489, 1, 2.778, 0, 3.067, 0, 1, 4.589, 0, 6.111, 0, 7.633, 0, 1, 7.733, 0, 7.833, 0.5, 7.933, 0.5, 1, 8.078, 0.5, 8.222, 0.454, 8.367, 0.27, 1, 8.467, 0.142, 8.567, 0, 8.667, 0, 1, 10.378, 0, 12.089, 0, 13.8, 0, 1, 15.789, 0, 17.778, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamRibonL", "Segments": [0, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamRibonR", "Segments": [0, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamArm", "Segments": [0, 0, 1, 0.411, 0, 0.822, -1, 1.233, -1, 1, 1.444, -1, 1.656, -1, 1.867, -1, 1, 1.978, -1, 2.089, 1, 2.2, 1, 1, 2.4, 1, 2.6, 1, 2.8, 1, 1, 2.867, 1, 2.933, 1, 3, 1, 1, 3.067, 1, 3.133, 1, 3.2, 1, 1, 3.233, 1, 3.267, 1, 3.3, 1, 1, 3.356, 1, 3.411, 1, 3.467, 1, 1, 3.533, 1, 3.6, 1, 3.667, 1, 1, 3.733, 1, 3.8, 1, 3.867, 1, 1, 3.922, 1, 3.978, 1, 4.033, 1, 1, 4.111, 1, 4.189, 1, 4.267, 1, 1, 4.811, 1, 5.356, -1, 5.9, -1, 1, 6.433, -1, 6.967, -1, 7.5, -1, 1, 7.644, -1, 7.789, 0.505, 7.933, 0.53, 1, 9.889, 0.866, 11.844, 1, 13.8, 1, 1, 14, 1, 14.2, 0, 14.4, 0, 1, 16.189, 0, 17.978, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamArmL", "Segments": [0, 0, 1, 0.622, 0, 1.244, 0, 1.867, 0, 1, 1.978, 0, 2.089, 0.5, 2.2, 0.5, 1, 2.4, 0.5, 2.6, 0, 2.8, 0, 1, 2.867, 0, 2.933, 0.5, 3, 0.5, 1, 3.067, 0.5, 3.133, 0, 3.2, 0, 1, 3.233, 0, 3.267, 0.5, 3.3, 0.5, 1, 3.356, 0.5, 3.411, 0, 3.467, 0, 1, 3.533, 0, 3.6, 0.5, 3.667, 0.5, 1, 3.733, 0.5, 3.8, 0, 3.867, 0, 1, 3.922, 0, 3.978, 0.5, 4.033, 0.5, 1, 4.111, 0.5, 4.189, 0, 4.267, 0, 1, 4.811, 0, 5.356, 0, 5.9, 0, 1, 6.433, 0, 6.967, 0, 7.5, 0, 1, 7.844, 0, 8.189, 0, 8.533, 0, 1, 8.722, 0, 8.911, -1, 9.1, -1, 1, 10.5, -1, 11.9, -1, 13.3, -1, 1, 13.467, -1, 13.633, -1, 13.8, -1, 1, 14, -1, 14.2, 0, 14.4, 0, 1, 16.189, 0, 17.978, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 0, 1, 0.622, 0, 1.244, 0, 1.867, 0, 1, 2.178, 0, 2.489, 0.5, 2.8, 0.5, 1, 2.867, 0.5, 2.933, 0, 3, 0, 1, 3.067, 0, 3.133, 0.5, 3.2, 0.5, 1, 3.233, 0.5, 3.267, 0, 3.3, 0, 1, 3.356, 0, 3.411, 0.5, 3.467, 0.5, 1, 3.533, 0.5, 3.6, 0, 3.667, 0, 1, 3.733, 0, 3.8, 0.5, 3.867, 0.5, 1, 3.922, 0.5, 3.978, 0, 4.033, 0, 1, 4.111, 0, 4.189, 0.5, 4.267, 0.5, 1, 4.811, 0.5, 5.356, 0, 5.9, 0, 1, 6.433, 0, 6.967, 0, 7.5, 0, 1, 7.844, 0, 8.189, 0, 8.533, 0, 1, 8.722, 0, 8.911, -1, 9.1, -1, 1, 10.5, -1, 11.9, -1, 13.3, -1, 1, 13.467, -1, 13.633, -1, 13.8, -1, 1, 14, -1, 14.2, 0, 14.4, 0, 1, 16.189, 0, 17.978, 0, 19.767, 0, 0, 19.8, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0.5, 1, 6.589, 0.5, 13.178, 0.5, 19.767, 0.5, 0, 19.8, 0.5]}, {"Target": "Parameter", "Id": "ParamLegL", "Segments": [0, 0, 1, 0.333, 0, 0.667, -1, 1, -1, 1, 1.289, -1, 1.578, -1, 1.867, -1, 1, 1.978, -1, 2.089, 1, 2.2, 1, 1, 2.311, 1, 2.422, 1, 2.533, 1, 1, 2.622, 1, 2.711, 0.76, 2.8, 0.76, 1, 2.867, 0.76, 2.933, 1, 3, 1, 1, 3.067, 1, 3.133, 0.77, 3.2, 0.77, 1, 3.233, 0.77, 3.267, 1, 3.3, 1, 1, 3.356, 1, 3.411, 0.84, 3.467, 0.84, 1, 3.533, 0.84, 3.6, 1, 3.667, 1, 1, 3.733, 1, 3.8, 0.72, 3.867, 0.72, 1, 3.922, 0.72, 3.978, 1, 4.033, 1, 1, 4.111, 1, 4.189, 0.85, 4.267, 0.85, 1, 4.311, 0.85, 4.356, 1, 4.4, 1, 1, 4.589, 1, 4.778, 1.026, 4.967, 0.84, 1, 5.278, 0.533, 5.589, 0, 5.9, 0, 0, 19.8, 0]}]}