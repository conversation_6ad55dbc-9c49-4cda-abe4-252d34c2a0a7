{"Version": 3, "Meta": {"Duration": 2.333, "Fps": 30.0, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 25, "TotalSegmentCount": 69, "TotalPointCount": 170, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.811, 0, 1.022, 5, 1.233, 5, 1, 1.489, 5, 1.744, 0, 2, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.2, 0, 0.4, 19, 0.6, 19, 1, 0.811, 19, 1.022, -8, 1.233, -8, 1, 1.489, -8, 1.744, 0, 2, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.411, 0, 0.822, 15, 1.233, 15, 1, 1.489, 15, 1.744, 12, 2, 12, 0, 2.333, 12]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.411, 1, 0.822, 1, 1.233, 1, 1, 1.289, 1, 1.344, 0, 1.4, 0, 1, 1.544, 0, 1.689, 0.95, 1.833, 0.95, 0, 2.333, 0.95]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.411, 1, 0.822, 1, 1.233, 1, 1, 1.289, 1, 1.344, 0, 1.4, 0, 1, 1.544, 0, 1.689, 0.96, 1.833, 0.96, 0, 2.333, 0.96]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0.05, 0.6, 0.05, 1, 0.811, 0.05, 1.022, -0.23, 1.233, -0.23, 1, 1.489, -0.23, 1.744, 0, 2, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.2, 0, 0.4, -0.71, 0.6, -0.71, 1, 0.811, -0.71, 1.022, 0.25, 1.233, 0.25, 1, 1.489, 0.25, 1.744, 0, 2, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0.79, 0.6, 0.79, 1, 0.811, 0.79, 1.022, 0.79, 1.233, 0.79, 1, 1.489, 0.79, 1.744, 0.54, 2, 0.54, 0, 2.333, 0.54]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.2, 0, 0.4, 1, 0.6, 1, 1, 0.811, 1, 1.022, 1, 1.233, 1, 1, 1.489, 1, 1.744, 0.56, 2, 0.56, 0, 2.333, 0.56]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamMouthForm", "Segments": [0, 1, 0, 2.333, 1]}, {"Target": "Parameter", "Id": "ParamMouthOpenY", "Segments": [0, 0, 0, 0.4, 0, 0, 0.467, 0.008, 0, 0.6, 0.008, 1, 0.622, 0.01, 0.644, 0.139, 0.667, 0.141, 1, 0.733, 0.142, 0.8, 0.007, 0.867, 0.008, 1, 0.933, 0.001, 1, 0.941, 1.067, 0.933, 1, 1.089, 0.923, 1.111, 0.262, 1.133, 0.251, 1, 1.178, 0.247, 1.222, 0.709, 1.267, 0.706, 1, 1.422, 0.703, 1.578, 0.011, 1.733, 0.008, 0, 1.8, 0.008, 0, 1.867, 0, 0, 2.267, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamTere", "Segments": [0, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamArmR", "Segments": [0, 10, 0, 2.333, 10]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 10, 1, 0.2, 10, 0.4, -1.171, 0.6, -5, 1, 0.811, -9.041, 1.022, -9, 1.233, -9, 1, 1.489, -9, 1.744, -7, 2, -7, 0, 2.333, -7]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.2, 0, 0.4, 0, 0.6, 0, 1, 0.811, 0, 1.022, -4, 1.233, -4, 1, 1.489, -4, 1.744, -3, 2, -3, 0, 2.333, -3]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 2.333, 0]}, {"Target": "Parameter", "Id": "ParamRibbon", "Segments": [0, 0, 0, 2.333, 0]}]}