(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{3767:()=>{},2208:()=>{},43974:(e,t,r)=>{Promise.resolve().then(r.bind(r,69392))},69392:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(41473),l=r(75664),o=r(86037);function a(){let e=(0,l.useRouter)(),{isAuthenticated:t}=(0,o.J)();return(0,s.useEffect)(()=>{t?e.push("/sentio"):e.push("/auth/login")},[t,e]),null}},86037:(e,t,r)=>{"use strict";r.d(t,{J:()=>n,v:()=>u});var s=r(12389),l=r(41473),o=r(90959);let a=(0,l.createContext)({pipeline:null,setPipeline:()=>{},selectedModel:"null",setSelectedModel:()=>{},isAuthenticated:!1,login:async()=>({success:!1,userRole:"user"}),loginWithToken:async()=>({success:!1,userRole:"user"}),logout:async()=>{},unsetCredentials:async()=>{},register:async()=>{},authState:{isAuthenticated:!1,email:null,userRole:null,userId:null},getClient:()=>null,client:null,viewMode:"user",setViewMode:()=>{},isSuperUser:()=>!1,createUser:async()=>{throw Error("createUser is not implemented in the default context")}}),n=()=>{let e=(0,l.useContext)(a);if(!e)throw Error("useUser must be used within a UserProvider");return e},u=e=>{let{children:t}=e,[n,u]=(0,l.useState)(null),[c,i]=(0,l.useState)(null),[h,d]=(0,l.useState)("null"),[m,v]=(0,l.useState)("user"),[k,w]=(0,l.useState)({isAuthenticated:!1,email:null,userRole:null,userId:null}),g=(0,l.useCallback)(()=>"admin"===k.userRole&&"admin"===m,[k.userRole,m]),[f,y]=(0,l.useState)(null),S=(0,l.useCallback)(async(e,t,r)=>{let s=new o.r2rClient(r);try{var l,a,n,c;let r=await s.users.login({email:e,password:t}),o=(null===(l=r.results.accessToken)||void 0===l?void 0:l.token)||(null===(a=r.results.access_token)||void 0===a?void 0:a.token),i=(null===(n=r.results.refreshToken)||void 0===n?void 0:n.token)||(null===(c=r.results.refresh_token)||void 0===c?void 0:c.token);if(!o)throw Error("No access token received from server");localStorage.setItem("livechatAccessToken",o),i&&localStorage.setItem("livechatRefreshToken",i),s.setTokens(o,i||""),u(s);let h=await s.users.me();if(!h.results)throw Error("Failed to get user information");let d="user";try{await s.system.settings(),d="admin"}catch(e){e instanceof Error&&"status"in e&&403===e.status||console.error("Unexpected error when checking user role:",e)}return w({isAuthenticated:!0,email:h.results.email,userRole:d,userId:h.results.id}),y(Date.now()),{success:!0,userRole:d}}catch(e){throw console.error("Login error:",e),e}},[]),C=(0,l.useCallback)(async(e,t)=>{let r=new o.r2rClient(t);try{r.setTokens(e,"");let t=await r.users.me();if(!t.results)throw Error("Failed to get user information");localStorage.setItem("livechatAccessToken",e),u(r);let s="user";try{await r.system.settings(),s="admin"}catch(e){e instanceof Error&&"status"in e&&403===e.status||console.error("Unexpected error when checking user role:",e)}return w({isAuthenticated:!0,email:t.results.email,userRole:s,userId:t.results.id}),{success:!0,userRole:s}}catch(e){throw console.error("Token login error:",e),localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),e}},[]),I=(0,l.useCallback)(async()=>{try{n&&await n.users.logout()}catch(e){console.error("Logout error:",e)}finally{localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),u(null),w({isAuthenticated:!1,email:null,userRole:null,userId:null}),y(null)}},[n]),T=(0,l.useCallback)(async()=>{localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken"),u(null),w({isAuthenticated:!1,email:null,userRole:null,userId:null}),y(null)},[]),A=(0,l.useCallback)(async(e,t,r)=>{let s=new o.r2rClient(r);try{await s.users.create({email:e,password:t}),await S(e,t,r)}catch(e){throw console.error("Registration error:",e),e}},[S]),R=(0,l.useCallback)(()=>n,[n]),p=(0,l.useCallback)(async function(e,t){if(arguments.length>2&&void 0!==arguments[2]&&arguments[2],!n)throw Error("No authenticated client available");try{await n.users.create({email:e,password:t})}catch(e){throw console.error("Create user error:",e),e}},[n]);return(0,l.useEffect)(()=>{(async()=>{let e=localStorage.getItem("livechatAccessToken");if(e)try{let{loadChatConfig:t,getDeploymentUrl:s}=await r.e(111).then(r.bind(r,12492)),l=await t(),o=s(l);await C(e,o)}catch(e){console.error("Auto-login failed:",e),localStorage.removeItem("livechatAccessToken"),localStorage.removeItem("livechatRefreshToken")}})()},[C]),(0,s.jsx)(a.Provider,{value:{pipeline:c,setPipeline:i,selectedModel:h,setSelectedModel:d,isAuthenticated:k.isAuthenticated,login:S,loginWithToken:C,logout:I,unsetCredentials:T,register:A,authState:k,getClient:R,client:n,viewMode:m,setViewMode:v,isSuperUser:g,createUser:p},children:t})}}},e=>{var t=t=>e(e.s=t);e.O(0,[78,663,61,358],()=>t(43974)),_N_E=e.O()}]);